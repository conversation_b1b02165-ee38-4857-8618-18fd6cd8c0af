package startup

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
)

func InitMonitorMetrics() {
	err := metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricNamespaceLoad, Labels: []string{"namespace", "load_status", "data_level"}})
	if err != nil {
		return
	}
	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricReqStatus, Labels: []string{"url", "rsp_status"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricCdtDataSyncCount, Labels: []string{"sync_type", "sync_status", "store"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricLineStatus, Labels: []string{"url", "rsp_status", "line"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricProductStatus, Labels: []string{"url", "rsp_status", "product"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricPickupGroupStatus, Labels: []string{"url", "rsp_status", "pickup_group"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricLaneStatus, Labels: []string{
		"url",
		"rsp_status",
		"can_pickup",
		"can_cod_pickup",
		"can_deliver",
		"can_cod_deliver",
		"can_trade_in",
		"payment_method",
		"err_code",
		"lane",
		"line",
		"product",
	}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricSiteServiceableCheck, Labels: []string{"site_id", "query_type"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{
		Name: constant.MetricsElectronicFenceReport,
		Labels: []string{
			"line_id",
			"pickup_result",
			"deliver_result",
			"whether_success",
			"ret_code",
		}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{
		Name: constant.MetricsEFenceAndServiceCheckDiffReport,
		Labels: []string{
			"line_id",
			"pickup_can_diff",
			"pickup_can_cod_diff",
			"deliver_can_diff",
			"deliver_can_cod_diff",
		}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsCDTMaxReport, Labels: []string{"product_id", "region"}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsCDTMinReport, Labels: []string{"product_id", "region"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricServiceAbleByLine, Labels: []string{
		"func",
		"line_id",
		"error_code",
		"error_message",
	}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricTimeServiceFunc, Labels: []string{
		"func",
		"region",
		"product_id",
		"update_event",
		"lane_code",
		"error_code",
		"error_message",
	}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricLocalcacheCount, Labels: []string{
		"namespace",
	}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricLocalcacheTime, Labels: []string{
		"namespace",
	}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricsBranchStatusReport, Labels: []string{
		"field",
		"status",
		"region",
		"supply_type",
	}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricsBatchItemCdtReport, Labels: []string{
		"func", // CalcNonAutoCdt / CalcAutoCdt
		"region",
		"product_id",
		"update_event",
		"lane_code",
		"error_code",
		"error_message",
	}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricsCmpItemCdtReport, Labels: []string{
		"region",
		"is_diff",
	}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsSyncItemCdtDataReport, Labels: []string{"product_id", "cdt_type", "location_type", "rule_id"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricsCheckAbilityResultReport, Labels: []string{"check_result", "check_result_type", "type_marker"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricsMatchOptimalStationResultReport, Labels: []string{"station_id"}})
	if err != nil {
		return
	}

	err = metrics.CreateSummary(metrics.SummaryOpts{Name: constant.MetricsEFenceCacheMeshCount, Labels: []string{"region", "zone_id"}})
	if err != nil {
		return
	}

	err = metrics.CreateSummary(metrics.SummaryOpts{Name: constant.MetricsEFenceCacheMeshCountByFullTab, Labels: []string{"region", "zone_key"}})
	if err != nil {
		return
	}

	err = metrics.CreateSummary(metrics.SummaryOpts{Name: constant.MetricsEFenceCacheMeshSuccessCount, Labels: []string{}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricInstallationLineStatus, Labels: []string{
		"rsp_status",
		"can_deliver",
		"can_cod_deliver",
		"can_trade_in",
		"err_code",
		"line",
		"product",
	}})
}

func InitTaskMonitorMetrics() {
	err := metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricNamespaceLoad, Labels: []string{"namespace", "load_status", "data_level"}})
	if err != nil {
		return
	}
	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricReqStatus, Labels: []string{"url", "rsp_status"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricCdtDataSyncCount, Labels: []string{"sync_type", "sync_status", "store"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.CheckCdtAutoUpdateTaskUpdated, Labels: []string{"auto_update_rule_id", "next_update_time", "region"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.SyncAutoUpdateTaskStatus, Labels: []string{"sync_type", "sync_status"}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricDbRowStatus, Labels: []string{"table", "count", "baseline"}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsEDDPrecisionReport, Labels: []string{"product_id", "update_times"}})
	if err != nil {
		return
	}
	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricsEDDPrecisionCounterReport, Labels: []string{"product_id", "is_accurate", "is_precise", "is_strict_precise"}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsEDDReport, Labels: []string{"product_id", "update_event"}})
	if err != nil {
		return
	}
	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricsEDDCounterReport, Labels: []string{"product_id", "update_event"}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsRealTimeReport, Labels: []string{"product_id", "update_event"}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsEDDDelayQueueReport, Labels: []string{"region", "queue"}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricsEDDDiffReport, Labels: []string{"product_id", "update_event", "data_sdk_error", "old_edd_error", "edd_min_diff", "edd_max_diff"}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsSyncItemCdtDataReport, Labels: []string{"product_id", "cdt_type", "location_type", "rule_id"}})
	if err != nil {
		return
	}

	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsStationUpdateVolumeCostTime, Help: "", Labels: []string{"type", "region"}})
	if err != nil {
		logger.LogErrorf("metrics.CreateGauge error", err)
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricsMatchOptimalStationResultReport, Labels: []string{"station_id"}})
	if err != nil {
		return
	}

	err = metrics.CreateSummary(metrics.SummaryOpts{Name: constant.MetricsEFenceCacheMeshCount, Labels: []string{"region", "zone_id"}})
	if err != nil {
		return
	}

	err = metrics.CreateSummary(metrics.SummaryOpts{Name: constant.MetricsEFenceCacheMeshCountByFullTab, Labels: []string{"region", "zone_key"}})
	if err != nil {
		return
	}

	err = metrics.CreateSummary(metrics.SummaryOpts{Name: constant.MetricsEFenceCacheMeshSuccessCount, Labels: []string{}})
	if err != nil {
		return
	}

	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.EddPushMonitor, Labels: []string{"region", "status", "field", "update_type", "product_id", "event", "retry_times"}})
	if err != nil {
		return
	}

}

func InitApiMonitorMetrics() {
	err := metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricDataFailureNotify, Labels: []string{"file_url", "update_time", "region", "code", "error", "rule_id", "file_url_list"}})
	if err != nil {
		return
	}
	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricChangeReportStatus, Labels: []string{"url", "status"}})
	if err != nil {
		return
	}
	err = metrics.CreateCounter(metrics.CounterOpts{Name: constant.MetricVolumeDataFailureNotify, Labels: []string{"file_url", "update_time", "region", "code", "error", "rule_id"}})
	if err != nil {
		return
	}
	err = metrics.CreateGauge(metrics.GaugeOpts{Name: constant.MetricsSyncItemCdtDataReport, Labels: []string{"product_id", "cdt_type", "location_type", "rule_id"}})
	if err != nil {
		return
	}
}
