//go:generate wire
//go:build wireinject
// +build wireinject

package grpc_api

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/branch"
	cacheCacheApi "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/delivery_instruction"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/geo_distance"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/health"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/installation"
	oneApiPackageLimit "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/one_api"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/package_limit"
	parcel_library3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/pickup_configuration"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/pis_branch"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/serviceable_area"
	site_serviceable_area2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/sync_item_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/tw_store"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/address_revision_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	cdt_ab_test2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf_detail"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/geo_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	line_package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	parcel_library2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	product_package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/postal_code_to_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/service_point_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	siteBasicConfSiteServiceableAreaBasicConf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	siteServiceableCepRangeSiteServiceableAreaCepRange "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	siteServiceableLocationSiteServiceableAreaLocation "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	siteServiceablePostcodeSiteServiceableAreaPostcode "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/hd_station_cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/station_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station_buyer_repo"
	tpl_id_line_id_ref3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
	tw_store3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tw_store"
	branch2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/branch"
	autoUpdateDataAutoUpdateRule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_data"
	automated_volume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_ab_test"
	cdtCalculationAutoUpdateRule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_calculation"
	manual_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_method"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/geo_distance_service"
	installation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/installation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/map_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/match"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/calculate_weight"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/product_package_limit"
	basic_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_conf"
	collect_deliver_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/station"
	tpl_id_line_id_ref2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/tpl_id_line_id_ref"
	tw_store2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/tw_store"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spex_service"
	"github.com/google/wire"
)

func InitGRPCService() *GRPCService {
	wire.Build(
		wire.Struct(new(GRPCService), "*"),
		health.HealthServiceProviderSet,
		package_limit.GrpcPackageLimitControllerProviderSet,
		line_package_limit.LinePackageLimitServiceProviderSet,
		line_package_limit2.LinePackageLimitDAOProviderSet,
		parcel_library.LogisticParcelLibraryServiceProviderSet,
		parcel_library2.LogisticParcelLibraryDaoProviderSet,
		product_package_limit.ProductPackageLimitServiceProviderSet,
		product_package_limit2.ProductPackageLimitDAOProviderSet,
		calculate_weight.CalculateWeightServiceProviderSet,
		serviceable_area.GrpcServiceableAreaControllerProviderSet,
		serviceable_core_logic.ServiceableCheckerServiceProviderSet,
		basic_conf.LineBasicServiceableConfProviderSet,
		basic_location.LineBasicServiceableLocationProviderSet,
		basic_postcode.LineBasicServiceablePostcodeProviderSet,
		scenario_conf.LineCommonServiceableScenarioConfProviderSet,
		operation_conf.LogisticLineOperationServiceableConfTabProviderSet,
		operation_location.LogisticLineOperationServiceableLocationTabProviderSet,
		operation_postcode.LogisticLineOperationServiceablePostcodeTabProviderSet,
		route.LogisticLineServiceableRouteTabProviderSet,
		area_location_ref.LogisticLineServiceableAreaLocationRefTabProviderSet,
		collect_deliver_group.LineCollectDeliverGroupConfProviderSet,
		collect_deliver_group_ref.LineBasicServiceableGroupRefProviderSet,
		cep_range.LineBasicServiceableCepRangeProviderSet,
		operation_route.LineOperationRouteTabProviderSet,
		effective_rule.ServiceableEffectiveRuleProviderSet,
		product_serviceable_zone.ProductServiceableCepGroupDaoProviderSet,
		shop_serviceable_zone.ShopServiceableZoneDaoProviderSet,
		line_toggle.EFenceLineToggleDaoDaoSet,
		location_whitelist.EFenceLocationWhiteListDaoSet,
		mesh.EFenceMeshDaoSet,
		polygon.EFencePolygonDaoSet,
		predefined_route_model.LogisticLinePredefinedRouteDaoProviderSet,
		basic_conf2.LineBasicServiceableConfManagerProviderSet,
		collect_deliver_group2.CollectDeliverGroupServiceProviderSet,
		e_fence.EFenceServiceSet,
		cacheCacheApi.CacheApiServiceProviderSet,
		pickup_configuration.PickupWindowControllerProviderSet,
		tpl_id_line_id_ref.TplIDLineIDRefControllerProviderSet,
		tpl_id_line_id_ref2.TPLIDLineIDRefServiceProviderSet,
		tpl_id_line_id_ref3.TPLIDLineIDRefDAOProviderSet,
		cdt_calculation.CdtCalculationControllerProviderSet,
		cdtCalculationAutoUpdateRule.CdtCalculationServiceProviderSet,
		auto_update_rule.CdtAutoUpdateRuleDAOProviderSet,
		auto_update_data.CdtAutoUpdateDataDAOProviderSet,
		lane_auto_update_data.CdtAutoUpdateDataDAOProviderSet,
		manual_update_rule.CdtAutoUpdateRuleDAOProviderSet,
		lane_manual_update_rule.CdtAutoUpdateRuleDAOProviderSet,
		manual_manipulation_rule.CdtManualManipulationRuleDAOProviderSet,
		lane_manual_manipulation_rule.CdtManualManipulationRuleDAOProviderSet,
		sls_holiday.SlsHolidayDAOProviderSet,
		lps_holiday.LPSHolidayDAOProviderSet,
		edd_history.EDDHistoryDAOProviderSet,
		cdt_ab_test.CdtAbTestRuleServiceProviderSet,
		cdt_ab_test2.CdtAbTestRuleDaoProviderSet,
		aggregate_masked_channel_cdt.AggregateMaskedChannelCdtProviderSet,
		automated_volume_generation_data.AutomatedVolumeGenerationDataProviderSet,
		automated_volume.AutomatedVolumeGenerationRuleProviderSet,
		branch.BranchControllerProviderSet,
		branch2.BranchServiceProviderSet,
		branch_info.BranchDaoProviderSet,
		branch_group.BranchGroupDaoProviderSet,
		s3_service.S3ServiceProviderSet,
		spex_service.SpexServiceProviderSet,
		lcs_service.LcsServiceProviderSet,
		postal_code_to_geo.BranchGroupDaoProviderSet,
		branch_task_record.BranchTaskRecordDaoProviderSet,
		station_conf.LogisticStationDaoProviderSet,
		station.StationServiceProviderSet,
		match.HomeDeliveryServiceProviderSet,
		hd_distance_repo.HdDistanceDaoProviderSet,
		hd_distance_cache_repo.HdDistanceCacheDaoProviderSet,
		service_point_geo.ServicePointDaoProviderSet,
		map_service.MapServiceProviderSet,
		google_result_repo.GoogleResultDaoProviderSet,
		google_result_cache_repo.GoogleResultCacheDaoProviderSet,
		station_buyer_repo.StationBuyerProviderSet,
		address_revision_repo.AddressRevisionDaoProviderSet,
		hd_station_cache.LogisticStationCacheProviderSet,
		oneApiPackageLimit.GrpcOneApiControllerProviderSet,
		tw_store.TWStoreProviderSet,
		tw_store2.TWStoreServiceProviderSet,
		tw_store3.TWStoreDAOProviderSet,
		scene_serviceable_area.GrpcSceneServiceableAreaControllerProviderSet,
		site_serviceable_area.SiteServiceableAreaServiceProviderSet,
		siteBasicConfSiteServiceableAreaBasicConf.LogisticSiteServiceableAreaBasocConfDaoProviderSet,
		siteServiceableLocationSiteServiceableAreaLocation.LogisticSiteServiceableAreaCepRangeDaoProviderSet,
		siteServiceablePostcodeSiteServiceableAreaPostcode.LogisticSiteServiceableAreaPostcodeDaoProviderSet,
		siteServiceableCepRangeSiteServiceableAreaCepRange.LogisticSiteServiceableAreaCepRangeDaoProviderSet,
		site_serviceable_area2.SiteServiceableAreaControllerProviderSet,
		pis_branch.PISBranchControllerProviderSet,
		delivery_instruction.LcosDeliveryInstructionServiceProviderSet,
		delivery_method.DeliveryMethodServiceProviderSet,
		delivery_conf.DeliveryInstructionBasicConfDAOProviderSet,
		delivery_conf_detail.DeliveryInstructionBasicConfDetailDAOProviderSet,
		delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeDAOProviderSet,
		delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationDAOProviderSet,
		sync_item_cdt.SyncItemCdtSerivceProviderSet,
		autoUpdateDataAutoUpdateRule.CdtAutoUpdateDataDAOProviderSet,
		manual_update_rule2.CdtManualUpdateRuleDAOProviderSet,
		automated_volume2.AutomatedVolumeGenerationRuleServiceProviderSet,
		parcel_library3.GrpcParcelLibraryControllerProviderSet,
		installation.LcosInstallationServiceProviderSet,
		installation2.LcosInstallationServiceProviderSet,
		geo_distance.GeoDistanceCalculateControllerProviderSet,
		geo_distance_service.LogisticProductGeoDistanceServiceProviderSet,
		geo_distance_repo.LogisticProductGeoDistanceConfRepoProviderSet,
	)

	return nil
}
