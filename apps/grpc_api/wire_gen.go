// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package grpc_api

import (
	branch2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/branch"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/delivery_instruction"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/geo_distance"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/health"
	installation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/installation"
	package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/one_api"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/package_limit"
	parcel_library3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/pickup_configuration"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/pis_branch"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/serviceable_area"
	site_serviceable_area2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/sync_item_cdt"
	tpl_id_line_id_ref3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/tpl_id_line_id_ref"
	tw_store3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/tw_store"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/address_revision_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf_detail"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/geo_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/postal_code_to_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/service_point_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/hd_station_cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/station_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station_buyer_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tw_store"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/branch"
	auto_update_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_data"
	automated_volume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/automated_volume"
	cdt_ab_test2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_ab_test"
	auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_calculation"
	manual_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_method"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/geo_distance_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/installation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/map_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/match"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/calculate_weight"
	line_package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/line_package_limit"
	parcel_library2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	product_package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/product_package_limit"
	basic_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_conf"
	collect_deliver_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/station"
	tpl_id_line_id_ref2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/tpl_id_line_id_ref"
	tw_store2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/tw_store"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spex_service"
)

// Injectors from wire.go:

func InitGRPCService() *GRPCService {
	healthService := health.NewHealthService()
	linePackageLimitDAO := line_package_limit.NewLinePackageLimitDAO()
	logisticParcelLibraryDao := parcel_library.NewLogisticParcelLibraryDao()
	logisticParcelLibraryService := parcel_library2.NewLogisticParcelLibraryService(logisticParcelLibraryDao)
	linePackageLimitService := line_package_limit2.NewLinePackageLimitService(linePackageLimitDAO, logisticParcelLibraryService)
	productPackageLimitDAO := product_package_limit.NewProductPackageLimitDAO()
	productPackageLimitService := product_package_limit2.NewProductPackageLimitService(productPackageLimitDAO, logisticParcelLibraryService)
	calculateWeightService := calculate_weight.NewCalculateWeightService(productPackageLimitDAO, logisticParcelLibraryService)
	grpcPackageLimitController := package_limit.NewGrpcPackageLimitController(linePackageLimitService, productPackageLimitService, calculateWeightService)
	lineBasicServiceableConfDAO := basic_conf.NewLineBasicServiceableConfDAO()
	lineBasicServiceableLocationDAO := basic_location.NewLineBasicServiceableLocationDAO()
	lineBasicServiceablePostcodeDAO := basic_postcode.NewLineBasicServiceablePostcodeDAO()
	lineCommonServiceableScenarioConfDAO := scenario_conf.NewLineCommonServiceableScenarioConfDAO()
	logisticLineOperationServiceableConfTabDAO := operation_conf.NewLogisticLineOperationServiceableConfTabDAO()
	logisticLineOperationServiceableLocationTabDAO := operation_location.NewLogisticLineOperationServiceableLocationTabDAO()
	logisticLineOperationServiceablePostcodeTabDAO := operation_postcode.NewLogisticLineOperationServiceablePostcodeTabDAO()
	logisticLineServiceableRouteTabDAO := route.NewLogisticLineServiceableRouteTabDAO()
	logisticLineServiceableAreaLocationRefTabDAO := area_location_ref.NewLogisticLineServiceableAreaLocationRefTabDAO()
	lineCollectDeliverGroupConfDao := collect_deliver_group.NewLineCollectDeliverGroupConfDao()
	lineBasicServiceableGroupRefDao := collect_deliver_group_ref.NewLineBaseServiceableGroupRefDao()
	lineServiceableCepRangeDaoInterfaceImpl := cep_range.NewLineServiceableCepRangeDAO()
	lineOperationRouteTabDAO := operation_route.NewLineOperationRouteTabDAO()
	effectiveRuleDAO := effective_rule.NewEffectiveRuleDAO()
	productServiceableZoneDao := product_serviceable_zone.NewProductServiceableZoneDao()
	shopServiceableZoneDao := shop_serviceable_zone.NewShopServiceableZoneDao()
	eFenceLineToggleDao := line_toggle.NewEFenceLineToggleDao()
	eFenceLocationWhitelistDao := location_whitelist.NewEFenceLocationWhitelistDao()
	eFenceMeshDao := mesh.NewEFenceMeshDao()
	eFencePolygonDao := polygon.NewEFencePolygonDao()
	logisticLinePredefinedRouteDaoImpl := predefined_route_model.NewLogisticLinePredefinedRouteDao()
	serviceableCheckerService := serviceable_core_logic.NewServiceableCheckerService(lineBasicServiceableConfDAO, lineBasicServiceableLocationDAO, lineBasicServiceablePostcodeDAO, lineCommonServiceableScenarioConfDAO, logisticLineOperationServiceableConfTabDAO, logisticLineOperationServiceableLocationTabDAO, logisticLineOperationServiceablePostcodeTabDAO, logisticLineServiceableRouteTabDAO, logisticLineServiceableAreaLocationRefTabDAO, lineCollectDeliverGroupConfDao, lineBasicServiceableGroupRefDao, lineServiceableCepRangeDaoInterfaceImpl, lineOperationRouteTabDAO, effectiveRuleDAO, productServiceableZoneDao, shopServiceableZoneDao, eFenceLineToggleDao, eFenceLocationWhitelistDao, eFenceMeshDao, eFencePolygonDao, logisticLinePredefinedRouteDaoImpl)
	collectDeliverGroupService := collect_deliver_group2.NewCollectDeliverGroupService(lineBasicServiceableGroupRefDao, lineCollectDeliverGroupConfDao)
	lineBasicServiceableConfService := basic_conf2.NewLineBasicServiceableConfService(lineBasicServiceableConfDAO, lineCommonServiceableScenarioConfDAO, logisticLineOperationServiceableConfTabDAO, lineBasicServiceableGroupRefDao, collectDeliverGroupService, lineBasicServiceableLocationDAO, lineBasicServiceablePostcodeDAO)
	eFenceService := e_fence.NewEFenceService(eFencePolygonDao, eFenceMeshDao, eFenceLocationWhitelistDao, eFenceLineToggleDao)
	grpcServiceableAreaController := serviceable_area.NewGrpcServiceableAreaController(serviceableCheckerService, lineBasicServiceableConfService, eFenceService)
	cacheApiService := cache_api.CacheApiService()
	pickupWindowController := pickup_configuration.NewPickupWindowController()
	tplIDLineIDRefDao := tpl_id_line_id_ref.NewTPLIDLineIDRefDAO()
	tplIDLineIDRefService := tpl_id_line_id_ref2.NewTplIDLineIDRefService(tplIDLineIDRefDao)
	tplIDLineIDRefController := tpl_id_line_id_ref3.NewTplIDLineIDRefController(tplIDLineIDRefService)
	cdtAutoUpdateRuleDao := auto_update_rule.NewCdtAutoUpdateRuleDao()
	laneCdtAutoUpdateDataDao := lane_auto_update_data.NewLaneCdtAutoUpdateDataDao()
	cdtAutoUpdateDataDao := auto_update_data.NewCdtAutoUpdateDataDao(laneCdtAutoUpdateDataDao)
	laneCdtManualUpdateRuleDao := lane_manual_update_rule.NewLaneCdtManualUpdateRuleDao()
	cdtManualUpdateRuleDao := manual_update_rule.NewCdtManualUpdateRuleDao(laneCdtManualUpdateRuleDao)
	laneCdtManualManipulationRuleDao := lane_manual_manipulation_rule.NewLaneCdtManualManipulationRuleDao()
	cdtManualManipulationRuleDao := manual_manipulation_rule.NewCdtManualManipulationRuleDao(laneCdtManualManipulationRuleDao)
	slsHolidayDao := sls_holiday.NewSlsHolidayDao()
	lpsHolidayDao := lps_holiday.NewLPSHolidayDAO()
	eddHistoryDao := edd_history.NewEddHistoryDao()
	cdtAbTestRuleDao := cdt_ab_test.NewCdtAbTestRuleDao()
	aggregateMaskedChannelCdtDao := aggregate_masked_channel_cdt.NewAggregateMaskedChannelCdtDao()
	cdtAbTestService := cdt_ab_test2.NewCdtAbTestService(cdtAbTestRuleDao, cdtAutoUpdateRuleDao, cdtManualUpdateRuleDao, aggregateMaskedChannelCdtDao)
	automatedVolumeGenerationDataDao := automated_volume_generation_data.NewAutomatedVolumeGenerationDataDao()
	automatedVolumeGenerationRuleDao := automated_volume.NewAutomatedVolumeGenerationRuleDao()
	cdtCalculationService := auto_update_rule2.NewCdtCalculationService(cdtAutoUpdateRuleDao, cdtAutoUpdateDataDao, cdtManualUpdateRuleDao, cdtManualManipulationRuleDao, slsHolidayDao, lpsHolidayDao, tplIDLineIDRefDao, eddHistoryDao, cdtAbTestService, automatedVolumeGenerationDataDao, automatedVolumeGenerationRuleDao)
	cdtCalculationController := cdt_calculation.NewCdtCalculationController(cdtCalculationService)
	branchDao := branch_info.NewBranchDao()
	branchGroupDao := branch_group.NewBranchGroupDao()
	s3Service := s3_service.NewS3Service()
	spexService := spex_service.NewSpexService()
	lcsService := lcs_service.NewLcsService()
	postalCodeToGeoDao := postal_code_to_geo.NewPostalCodeToGenDao()
	branchTaskDao := branch_task_record.NewBranchTaskDao()
	stationDao := station_conf.NewLogisticStationDao()
	branchService := branch.NewBranchService(branchDao, branchGroupDao, s3Service, spexService, lcsService, postalCodeToGeoDao, branchTaskDao, stationDao)
	hdDistanceDao := hd_distance_repo.NewHdDistanceDao()
	hdDistanceCacheDao := hd_distance_cache_repo.NewHdDistanceCacheDao()
	servicePointDao := service_point_geo.NewServicePointDao()
	googleResultDao := google_result_repo.NewGoogleResultDao()
	mapService := map_service.NewMapService(googleResultDao)
	googleResultCacheDao := google_result_cache_repo.NewGoogleResultCacheDao()
	stationBuyerDao := station_buyer_repo.NewStationBuyerDao()
	addressRevisionDao := address_revision_repo.NewAddressRevisionDao()
	hdStationCacheManagerImpl := hd_station_cache.NewHDStationCacheManagerImpl()
	homeDeliveryService := match.NewHomeDeliveryService(hdDistanceDao, hdDistanceCacheDao, servicePointDao, mapService, googleResultCacheDao, s3Service, stationDao, stationBuyerDao, addressRevisionDao, hdStationCacheManagerImpl)
	stationServiceImpl := station.NewStationServiceImpl(homeDeliveryService, hdStationCacheManagerImpl, stationDao, s3Service, addressRevisionDao)
	branchController := branch2.NewBranchController(branchService, stationServiceImpl, homeDeliveryService)
	thirdPartConvenienceStoreDao := tw_store.NewThirdPartConvenienceStoreDAO()
	twStoreService := tw_store2.NewTWStoreService(thirdPartConvenienceStoreDao, s3Service)
	twStoreController := tw_store3.NewTwStoreController(twStoreService)
	siteServiceableAreaBasicConfDao := site_serviceable_area_basic_conf.NewSiteServiceableAreaBasicConfDAO()
	siteServiceableAreaLocationDao := site_serviceable_area_location.NewSiteServiceableAreaLocationDAO()
	siteServiceableAreaPostcodeDao := site_serviceable_area_postcode.NewSiteServiceableAreaPostcodeDAO()
	siteServiceableAreaCepRangeDao := site_serviceable_area_cep_range.NewSiteServiceableAreaCepRangeDAO()
	siteServiceableAreaService := site_serviceable_area.NewSiteServiceableAreaService(siteServiceableAreaBasicConfDao, siteServiceableAreaLocationDao, siteServiceableAreaPostcodeDao, siteServiceableAreaCepRangeDao)
	grpcSceneServiceableAreaController := scene_serviceable_area.NewGrpcSceneServiceableAreaController(serviceableCheckerService, siteServiceableAreaService, logisticParcelLibraryService)
	grpcOneApiController := package_limit2.NewGrpcOneApiController(pickupWindowController, grpcServiceableAreaController, grpcPackageLimitController, branchController, twStoreController, grpcSceneServiceableAreaController, logisticParcelLibraryService)
	siteServiceableAreaController := site_serviceable_area2.NewSiteServiceableAreaController(siteServiceableAreaService)
	pisBranchController := pis_branch.NewPISBranchController(branchController)
	deliveryInstructionBasicConfTab := delivery_conf.NewDeliveryInstructionBasicConfTab()
	deliveryInstructionBasicConfDetailTab := delivery_conf_detail.NewDeliveryInstructionBasicConfDetailTab()
	deliveryInstructionWhitelistCepRangeDao := delivery_instruction_whitelist_cep_range.NewDeliveryInstructionWhitelistCepRangeDAO()
	deliveryInstructionWhitelistLocationDao := delivery_instruction_whitelist_location.NewDeliveryInstructionWhitelistLocationDAO()
	deliveryMethod := delivery_method.NewDeliveryMethod(deliveryInstructionBasicConfTab, deliveryInstructionBasicConfDetailTab, deliveryInstructionWhitelistCepRangeDao, deliveryInstructionWhitelistLocationDao)
	deliveryInstructionController := delivery_instruction.NewDeliveryInstructionController(deliveryMethod)
	autoUpdateDataService := auto_update_rule3.NewAutoUpdateDataService(cdtAutoUpdateDataDao, laneCdtAutoUpdateDataDao, cdtAutoUpdateRuleDao)
	manualUpdateService := manual_update_rule2.NewManualManipulationService(cdtManualUpdateRuleDao, laneCdtManualUpdateRuleDao, cdtAbTestRuleDao)
	automatedVolume := automated_volume2.NewAutomatedVolumeGenerationRule(automatedVolumeGenerationRuleDao, automatedVolumeGenerationDataDao)
	syncItemCdtService := sync_item_cdt.NewSyncItemCdtService(autoUpdateDataService, manualUpdateService, automatedVolume)
	grpcParcelLibraryController := parcel_library3.NewGrpcParcelLibraryController(logisticParcelLibraryService)
	installationService := installation.NewInstallationMethod(lpsHolidayDao)
	installationController := installation2.NewInstallationController(installationService)
	logisticProductGeoDistanceConfRepoImpl := geo_distance_repo.NewLogisticProductGeoDistanceConfRepo()
	logisticProductGeoDistanceServiceImpl := geo_distance_service.NewLogisticProductGeoDistanceService(logisticProductGeoDistanceConfRepoImpl)
	geoDistanceCalculateController := geo_distance.NewGeoDistanceCalculateController(logisticProductGeoDistanceServiceImpl)
	grpcService := &GRPCService{
		healthService:              healthService,
		weightLimitService:         grpcPackageLimitController,
		serviceableService:         grpcServiceableAreaController,
		cacheApiService:            cacheApiService,
		pickupWindowService:        pickupWindowController,
		tplIDLineIDRefService:      tplIDLineIDRefController,
		cdtCalculationService:      cdtCalculationController,
		branchService:              branchController,
		oneApiService:              grpcOneApiController,
		twStore:                    twStoreController,
		siteServiceableArea:        siteServiceableAreaController,
		sceneServiceableArea:       grpcSceneServiceableAreaController,
		pisBranchService:           pisBranchController,
		deliveryInstructionService: deliveryInstructionController,
		syncItemCdtService:         syncItemCdtService,
		parcelLibraryService:       grpcParcelLibraryController,
		installationService:        installationController,
		geoDistanceService:         geoDistanceCalculateController,
	}
	return grpcService
}
