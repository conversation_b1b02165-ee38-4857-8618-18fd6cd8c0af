// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package mono_sa

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	parcel_library2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area"
)

// Injectors from wire.go:

func InitMonoService() *MonoService {
	lineBasicServiceableConfDAO := basic_conf.NewLineBasicServiceableConfDAO()
	lineBasicServiceableLocationDAO := basic_location.NewLineBasicServiceableLocationDAO()
	lineBasicServiceablePostcodeDAO := basic_postcode.NewLineBasicServiceablePostcodeDAO()
	lineCommonServiceableScenarioConfDAO := scenario_conf.NewLineCommonServiceableScenarioConfDAO()
	logisticLineOperationServiceableConfTabDAO := operation_conf.NewLogisticLineOperationServiceableConfTabDAO()
	logisticLineOperationServiceableLocationTabDAO := operation_location.NewLogisticLineOperationServiceableLocationTabDAO()
	logisticLineOperationServiceablePostcodeTabDAO := operation_postcode.NewLogisticLineOperationServiceablePostcodeTabDAO()
	logisticLineServiceableRouteTabDAO := route.NewLogisticLineServiceableRouteTabDAO()
	logisticLineServiceableAreaLocationRefTabDAO := area_location_ref.NewLogisticLineServiceableAreaLocationRefTabDAO()
	lineCollectDeliverGroupConfDao := collect_deliver_group.NewLineCollectDeliverGroupConfDao()
	lineBasicServiceableGroupRefDao := collect_deliver_group_ref.NewLineBaseServiceableGroupRefDao()
	lineServiceableCepRangeDaoInterfaceImpl := cep_range.NewLineServiceableCepRangeDAO()
	lineOperationRouteTabDAO := operation_route.NewLineOperationRouteTabDAO()
	effectiveRuleDAO := effective_rule.NewEffectiveRuleDAO()
	productServiceableZoneDao := product_serviceable_zone.NewProductServiceableZoneDao()
	shopServiceableZoneDao := shop_serviceable_zone.NewShopServiceableZoneDao()
	eFenceLineToggleDao := line_toggle.NewEFenceLineToggleDao()
	eFenceLocationWhitelistDao := location_whitelist.NewEFenceLocationWhitelistDao()
	eFenceMeshDao := mesh.NewEFenceMeshDao()
	eFencePolygonDao := polygon.NewEFencePolygonDao()
	logisticLinePredefinedRouteDaoImpl := predefined_route_model.NewLogisticLinePredefinedRouteDao()
	serviceableCheckerService := serviceable_core_logic.NewServiceableCheckerService(lineBasicServiceableConfDAO, lineBasicServiceableLocationDAO, lineBasicServiceablePostcodeDAO, lineCommonServiceableScenarioConfDAO, logisticLineOperationServiceableConfTabDAO, logisticLineOperationServiceableLocationTabDAO, logisticLineOperationServiceablePostcodeTabDAO, logisticLineServiceableRouteTabDAO, logisticLineServiceableAreaLocationRefTabDAO, lineCollectDeliverGroupConfDao, lineBasicServiceableGroupRefDao, lineServiceableCepRangeDaoInterfaceImpl, lineOperationRouteTabDAO, effectiveRuleDAO, productServiceableZoneDao, shopServiceableZoneDao, eFenceLineToggleDao, eFenceLocationWhitelistDao, eFenceMeshDao, eFencePolygonDao, logisticLinePredefinedRouteDaoImpl)
	siteServiceableAreaBasicConfDao := site_serviceable_area_basic_conf.NewSiteServiceableAreaBasicConfDAO()
	siteServiceableAreaLocationDao := site_serviceable_area_location.NewSiteServiceableAreaLocationDAO()
	siteServiceableAreaPostcodeDao := site_serviceable_area_postcode.NewSiteServiceableAreaPostcodeDAO()
	siteServiceableAreaCepRangeDao := site_serviceable_area_cep_range.NewSiteServiceableAreaCepRangeDAO()
	siteServiceableAreaService := site_serviceable_area.NewSiteServiceableAreaService(siteServiceableAreaBasicConfDao, siteServiceableAreaLocationDao, siteServiceableAreaPostcodeDao, siteServiceableAreaCepRangeDao)
	logisticParcelLibraryDao := parcel_library.NewLogisticParcelLibraryDao()
	logisticParcelLibraryService := parcel_library2.NewLogisticParcelLibraryService(logisticParcelLibraryDao)
	grpcSceneServiceableAreaController := scene_serviceable_area.NewGrpcSceneServiceableAreaController(serviceableCheckerService, siteServiceableAreaService, logisticParcelLibraryService)
	monoService := &MonoService{
		SceneServiceableArea: grpcSceneServiceableAreaController,
	}
	return monoService
}
