//go:generate wire
//go:build wireinject
// +build wireinject

package mono_sa

import (
	"github.com/google/wire"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	parcel_library2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	siteBasicConfSiteServiceableAreaBasicConf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	siteServiceableCepRangeSiteServiceableAreaCepRange "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	siteServiceableLocationSiteServiceableAreaLocation "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	siteServiceablePostcodeSiteServiceableAreaPostcode "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area"
)

func InitMonoService() *MonoService {
	wire.Build(
		wire.Struct(new(MonoService), "*"),
		parcel_library.LogisticParcelLibraryServiceProviderSet,
		parcel_library2.LogisticParcelLibraryDaoProviderSet,
		scene_serviceable_area.GrpcSceneServiceableAreaControllerProviderSet,
		serviceable_core_logic.ServiceableCheckerServiceProviderSet,
		site_serviceable_area.SiteServiceableAreaServiceProviderSet,
		basic_conf.LineBasicServiceableConfProviderSet,
		basic_location.LineBasicServiceableLocationProviderSet,
		basic_postcode.LineBasicServiceablePostcodeProviderSet,
		scenario_conf.LineCommonServiceableScenarioConfProviderSet,
		operation_conf.LogisticLineOperationServiceableConfTabProviderSet,
		operation_location.LogisticLineOperationServiceableLocationTabProviderSet,
		operation_postcode.LogisticLineOperationServiceablePostcodeTabProviderSet,
		route.LogisticLineServiceableRouteTabProviderSet,
		area_location_ref.LogisticLineServiceableAreaLocationRefTabProviderSet,
		collect_deliver_group.LineCollectDeliverGroupConfProviderSet,
		collect_deliver_group_ref.LineBasicServiceableGroupRefProviderSet,
		cep_range.LineBasicServiceableCepRangeProviderSet,
		operation_route.LineOperationRouteTabProviderSet,
		effective_rule.ServiceableEffectiveRuleProviderSet,
		product_serviceable_zone.ProductServiceableCepGroupDaoProviderSet,
		shop_serviceable_zone.ShopServiceableZoneDaoProviderSet,
		line_toggle.EFenceLineToggleDaoDaoSet,
		location_whitelist.EFenceLocationWhiteListDaoSet,
		mesh.EFenceMeshDaoSet,
		polygon.EFencePolygonDaoSet,
		siteBasicConfSiteServiceableAreaBasicConf.LogisticSiteServiceableAreaBasocConfDaoProviderSet,
		siteServiceableLocationSiteServiceableAreaLocation.LogisticSiteServiceableAreaCepRangeDaoProviderSet, // todo
		siteServiceablePostcodeSiteServiceableAreaPostcode.LogisticSiteServiceableAreaPostcodeDaoProviderSet,
		siteServiceableCepRangeSiteServiceableAreaCepRange.LogisticSiteServiceableAreaCepRangeDaoProviderSet,
		predefined_route_model.LogisticLinePredefinedRouteDaoProviderSet,
	)
	return nil
}
