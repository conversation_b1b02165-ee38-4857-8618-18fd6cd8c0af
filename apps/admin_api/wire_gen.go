// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package admin_api

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/branch"
	aggregate_masked_channel_cdt3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/aggregate_masked_channel_cdt"
	auto_update_rule4 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/auto_update_rule"
	automated_volume3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/automated_volume"
	cdt_ab_test3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/cdt_ab_test"
	cdt_overview2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/cdt_overview"
	edd_auto_update_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_auto_update_rule"
	edd_delay_queue2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_delay_queue"
	edd_history3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_model_conf"
	edd_update_rule_conf3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_update_rule_conf"
	edd_update_task_conf3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edt_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/forecast_task_result"
	manual_manipulation_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/manual_manipulation_rule"
	manual_update_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/manual_update_rule"
	data_version3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/data_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/delivery_instruction/delivery_instruction_conf"
	delivery_instruction_whitelist_cep_range3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/delivery_instruction/delivery_instruction_whitelist_cep_range"
	delivery_instruction_whitelist_location3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/delivery_instruction/delivery_instruction_whitelist_location"
	e_fence2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/e_fence"
	edt_config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/edt_config"
	edt_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/geo_distance_controller"
	line_package_limit3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/line_package_limit"
	parcel_library3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/parcel_library"
	product_package_limit3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/product_package_limit"
	holidays2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/holidays"
	open_logistic_pickup_config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/open_logistic_pickup_config"
	open_logistic_timeslot3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/open_logistic_timeslot"
	pickup_config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/pickup_config"
	pickup_group3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/pickup_group"
	pickup_window2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/pickup_window"
	recurring_holidays2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/recurring_holidays"
	timeslot3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/timeslot"
	task_configuration3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/sa_task/task_configuration"
	task_record3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/sa_task/task_record"
	scheduled3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/scheduled"
	area_route_serviceable2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/area_route_serviceable"
	basic_cep_range2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_cep_range"
	basic_conf3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_conf"
	basic_location3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_location"
	basic_postcode3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_postcode"
	card_delivery_address3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/card_delivery_address"
	collect_deliver_group3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/collect_deliver_group"
	operation_cep_range2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_cep_range"
	operation_conf3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_conf"
	operation_location3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_location"
	operation_postcode3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_postcode"
	operation_route3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_route"
	predefined_route_serviceable2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/predefined_route_serviceable"
	product_serviceable_zone3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/zone_serviceable/product_serviceable_zone"
	shop_serviceable_zone3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/zone_serviceable/shop_serviceable_zone"
	effective_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area_rule/effective_rule"
	initialized_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area_rule/initialized_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_basic_conf"
	site_serviceable_area_cep_range3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_cep_range"
	site_serviceable_area_location3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_location"
	site_serviceable_area_postcode3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_postcode"
	order_account_mapping3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/spx_serviceable_area/order_account_mapping"
	spx_serviceable_area2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/spx_serviceable_area/spx_serviceable_area"
	station2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/station"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/sync_item_card_cdt"
	time_experiment2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/time_experiment"
	tpl_id_line_id_ref3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/address_revision_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/algo_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_result"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_preview_task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_rule_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/product_ctime"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/data_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf_detail"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/geo_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/recurring_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/postal_code_to_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sa_task/task_configuration"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sa_task/task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/service_point_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/initialized_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_compare"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/hd_station_cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/station_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station_buyer_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/time_experiment_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/branch"
	aggregate_masked_channel_cdt2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/aggregate_masked_channel_cdt"
	algo_model_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/algo_model_conf"
	auto_update_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_data"
	auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_rule"
	automated_volume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/automated_volume"
	cdt_ab_test2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_ab_test"
	auto_update_rule5 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_overview"
	edd_auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_delay_queue"
	edd_forecast_result2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_forecast_result"
	edd_forecast_task2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_forecast_task"
	edd_history2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_history"
	edd_update_rule_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_update_rule_conf"
	edd_update_task_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_update_task_conf"
	manual_manipulation_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_manipulation_rule"
	manual_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_update_rule"
	data_version2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/data_version"
	delivery_instruction_whitelist_cep_range2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_instruction_whitelist_cep_range"
	delivery_instruction_whitelist_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_method"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/edt_config_service"
	edt_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/geo_distance_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/map_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/match"
	line_package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/line_package_limit"
	parcel_library2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	product_package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/holidays"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/open_logistic_pickup_config"
	open_logistic_timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/open_logistic_timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_config"
	pickup_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_window"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/recurring_holidays"
	timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/timeslot"
	task_configuration2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/sa_task/task_configuration"
	task_record2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/sa_task/task_record"
	scheduled2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_cep_range"
	basic_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_conf"
	basic_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_location"
	basic_postcode2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_postcode"
	collect_deliver_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/common_card_delivery_address"
	card_delivery_address2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_cep_range"
	operation_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_conf"
	operation_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_location"
	operation_postcode2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_postcode"
	operation_route2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/predefined_route_serviceable"
	product_serviceable_zone2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/zone_serviceable/product_serviceable_zone"
	shop_serviceable_zone2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area_rule"
	site_serviceable_area_basic_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_basic_conf"
	site_serviceable_area_cep_range2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_cep_range"
	site_serviceable_area_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_location"
	site_serviceable_area_postcode2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_postcode"
	order_account_mapping2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area"
	spx_serviceable_area_compare2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area_compare"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/station"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/time_experiment"
	tpl_id_line_id_ref2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spex_service"
)

// Injectors from wire.go:

func InitAdminAPIService() *APIService {
	linePackageLimitDAO := line_package_limit.NewLinePackageLimitDAO()
	logisticParcelLibraryDao := parcel_library.NewLogisticParcelLibraryDao()
	logisticParcelLibraryService := parcel_library2.NewLogisticParcelLibraryService(logisticParcelLibraryDao)
	linePackageLimitService := line_package_limit2.NewLinePackageLimitService(linePackageLimitDAO, logisticParcelLibraryService)
	adminLinePackageLimitController := line_package_limit3.NewAdminLinePackageLimitController(linePackageLimitService)
	productPackageLimitDAO := product_package_limit.NewProductPackageLimitDAO()
	productPackageLimitService := product_package_limit2.NewProductPackageLimitService(productPackageLimitDAO, logisticParcelLibraryService)
	adminProductPackageLimitController := product_package_limit3.NewAdminProductPackageLimitController(productPackageLimitService)
	lineBasicServiceableConfDAO := basic_conf.NewLineBasicServiceableConfDAO()
	lineCommonServiceableScenarioConfDAO := scenario_conf.NewLineCommonServiceableScenarioConfDAO()
	logisticLineOperationServiceableConfTabDAO := operation_conf.NewLogisticLineOperationServiceableConfTabDAO()
	lineBasicServiceableGroupRefDao := collect_deliver_group_ref.NewLineBaseServiceableGroupRefDao()
	lineCollectDeliverGroupConfDao := collect_deliver_group.NewLineCollectDeliverGroupConfDao()
	collectDeliverGroupService := collect_deliver_group2.NewCollectDeliverGroupService(lineBasicServiceableGroupRefDao, lineCollectDeliverGroupConfDao)
	lineBasicServiceableLocationDAO := basic_location.NewLineBasicServiceableLocationDAO()
	lineBasicServiceablePostcodeDAO := basic_postcode.NewLineBasicServiceablePostcodeDAO()
	lineBasicServiceableConfService := basic_conf2.NewLineBasicServiceableConfService(lineBasicServiceableConfDAO, lineCommonServiceableScenarioConfDAO, logisticLineOperationServiceableConfTabDAO, lineBasicServiceableGroupRefDao, collectDeliverGroupService, lineBasicServiceableLocationDAO, lineBasicServiceablePostcodeDAO)
	lineServiceableCepRangeDaoInterfaceImpl := cep_range.NewLineServiceableCepRangeDAO()
	scheduledDao := scheduled.NewScheduledDao()
	s3Service := s3_service.NewS3Service()
	scheduledService := scheduled2.NewScheduledService(scheduledDao, s3Service)
	lineBasicServiceableCepRangeService := basic_cep_range.NewLineBasicServiceableCepRangeService(lineServiceableCepRangeDaoInterfaceImpl, scheduledService)
	lineOperationServiceableCepRangeService := operation_cep_range.NewLineOperationServiceableCepRangeService(lineServiceableCepRangeDaoInterfaceImpl, scheduledService)
	orderAccountMappingDao := order_account_mapping.NewOrderAccountMappingDao()
	orderAccountMappingService := order_account_mapping2.NewOrderAccountMappingService(orderAccountMappingDao)
	spxServiceableAreaVersionDao := spx_serviceable_area_version.NewSpxServiceableAreaVersionDao()
	spxServiceableAreaDataDao := spx_serviceable_area_data.NewSpxServiceableAreaDataDao()
	spxServiceableAreaCompareTaskDao := spx_serviceable_area_compare.NewSpxServiceableAreaCompareTaskDao()
	spxServiceableAreaService := spx_serviceable_area.NewSpxServiceableAreaService(orderAccountMappingService, spxServiceableAreaVersionDao, spxServiceableAreaDataDao, spxServiceableAreaCompareTaskDao)
	cardDeliveryAddressChangeVersionDao := card_delivery_address.NewCardDeliveryAddressChangeVersionDao()
	cardDeliveryAddressService := card_delivery_address2.NewCardDeliveryAddressService(cardDeliveryAddressChangeVersionDao)
	lineBasicServiceableLocationService := basic_location2.NewLineBasicServiceableLocationService(lineBasicServiceableLocationDAO, scheduledService, lineBasicServiceableConfDAO, orderAccountMappingService, spxServiceableAreaService, cardDeliveryAddressService)
	lineBasicServiceablePostcodeService := basic_postcode2.NewLineBasicServiceablePostcodeService(lineBasicServiceablePostcodeDAO, scheduledService, lineBasicServiceableConfDAO, orderAccountMappingService, spxServiceableAreaService)
	logisticLineOperationServiceablePostcodeTabDAO := operation_postcode.NewLogisticLineOperationServiceablePostcodeTabDAO()
	lineOperationServiceablePostcodeService := operation_postcode2.NewLineOperationServiceablePostcodeService(logisticLineOperationServiceablePostcodeTabDAO, lineBasicServiceablePostcodeDAO, scheduledService)
	logisticLineOperationServiceableLocationTabDAO := operation_location.NewLogisticLineOperationServiceableLocationTabDAO()
	lineOperationServiceableLocationService := operation_location2.NewLineOperationServiceableLocationService(logisticLineOperationServiceableLocationTabDAO, lineBasicServiceableLocationDAO, scheduledService)
	logisticLineServiceableRouteTabDAO := route.NewLogisticLineServiceableRouteTabDAO()
	logisticLineServiceableAreaTabDAO := area.NewLogisticLineServiceableAreaTabDAO()
	lineServiceableRouteService := area_route_serviceable.NewLineServiceableRouteService(logisticLineServiceableRouteTabDAO, logisticLineServiceableAreaTabDAO, lineCollectDeliverGroupConfDao, scheduledService)
	adminLineBasicServiceableConfController := basic_conf3.NewAdminLineBasicServiceableConfController(lineBasicServiceableConfService, lineBasicServiceableCepRangeService, lineOperationServiceableCepRangeService, lineBasicServiceableLocationService, lineBasicServiceablePostcodeService, lineOperationServiceablePostcodeService, lineOperationServiceableLocationService, lineServiceableRouteService)
	adminLineBasicServiceableLocationController := basic_location3.NewAdminLineBasicServiceableLocationController(lineBasicServiceableLocationService)
	adminLineBasicServiceablePostcodeController := basic_postcode3.NewAdminLineBasicServiceablePostcodeController(lineBasicServiceablePostcodeService)
	adminLineOperationServiceablePostcodeController := operation_postcode3.NewAdminLineOperationServiceablePostcodeController(lineOperationServiceablePostcodeService)
	lineOperationServiceableConfService := operation_conf2.NewLineOperationServiceableConfService(logisticLineOperationServiceableConfTabDAO, lineCommonServiceableScenarioConfDAO, lineBasicServiceableConfDAO)
	adminLineOperationServiceableConfController := operation_conf3.NewAdminLineOperationServiceableConfController(lineOperationServiceableConfService)
	adminLineOperationServiceableLocationController := operation_location3.NewAdminLineOperationServiceableLocationController(lineOperationServiceableLocationService)
	logisticLineServiceableAreaLocationRefTabDAO := area_location_ref.NewLogisticLineServiceableAreaLocationRefTabDAO()
	lineServiceableAreaService := area_route_serviceable.NewLineServiceableAreaService(logisticLineServiceableAreaTabDAO, logisticLineServiceableAreaLocationRefTabDAO)
	adminLineServiceableAreaController := area_route_serviceable2.NewAdminLineServiceableAreaController(lineServiceableAreaService)
	adminLineServiceableRouteController := area_route_serviceable2.NewAdminLineServiceableRouteController(lineServiceableRouteService)
	lineOperationRouteTabDAO := operation_route.NewLineOperationRouteTabDAO()
	lineOperationRouteService := operation_route2.NewLineOperationRouteService(lineOperationRouteTabDAO, logisticLineServiceableAreaTabDAO, lineCollectDeliverGroupConfDao, lineBasicServiceableConfDAO, logisticLineServiceableRouteTabDAO, scheduledService)
	adminLineOperationRouteController := operation_route3.NewAdminLineOperationRouteController(lineOperationRouteService)
	collectDeliverGroupController := collect_deliver_group3.NewCollectDeliverGroupController(collectDeliverGroupService)
	adminLineBasicServiceableCepRangeController := basic_cep_range2.NewAdminLineBasicServiceableCepRangeController(lineBasicServiceableCepRangeService)
	adminLineOperationServiceableCepRangeController := operation_cep_range2.NewAdminLineOperationServiceableCepRangeController(lineOperationServiceableCepRangeService)
	logisticHolidayDao := holiday.NewLogisticHolidayDAO()
	recurringHolidayDao := recurring_holiday.NewRecurringHolidayDAO()
	pickupGroupDao := pickup_group.NewPickupGroupDAO()
	logisticHolidaysService := holidays.NewLogisticHolidaysService(logisticHolidayDao, recurringHolidayDao, pickupGroupDao)
	lineHolidaysController := holidays2.NewLineHolidaysController(logisticHolidaysService)
	recurringHolidaysService := recurring_holidays.NewRecurringHolidaysService(recurringHolidayDao, logisticHolidayDao, pickupGroupDao)
	lineRecurringHolidaysController := recurring_holidays2.NewLineRecurringHolidaysController(recurringHolidaysService)
	pickupConfigurationDao := pickup_config.NewPickupConfigurationDAO()
	pickupTimeslotDao := timeslot.NewPickupTimeslotDAO()
	pickupTimeslotService := timeslot2.NewPickupTimeslotService(pickupTimeslotDao, pickupGroupDao, pickupConfigurationDao)
	pickupConfigService := pickup_configuration.NewLinePickupConfigService(pickupConfigurationDao, pickupGroupDao, lineBasicServiceableConfDAO, pickupTimeslotService)
	pickupConfigController := pickup_config2.NewPickupConfigController(pickupConfigService)
	linePickupTimeslotController := timeslot3.NewLinePickupTimeslotController(pickupTimeslotService)
	pickupGroupService := pickup_group2.NewPickupGroupService(pickupGroupDao, lineBasicServiceableConfDAO)
	pickupGroupController := pickup_group3.NewPickupGroupController(pickupGroupService)
	openLogisticPickupConfigurationDao := open_logistic_pickup_config.NewOpenLogisticPickupConfigurationDAO()
	openLogisticPickupConfigService := open_logistic_pickup_configuration.NewOpenLogisticPickupConfigService(openLogisticPickupConfigurationDao, pickupGroupDao, lineBasicServiceableConfDAO)
	openLogisticPickupConfigController := open_logistic_pickup_config2.NewOpenLogisticPickupConfigController(openLogisticPickupConfigService)
	openLogisticPickupTimeslotDao := open_logistic_timeslot.NewOpenLogisticPickupTimeslotDAO()
	openLogisticPickupTimeslotService := open_logistic_timeslot2.NewOpenLogisticPickupTimeslotService(openLogisticPickupTimeslotDao, pickupGroupDao)
	openLogisticPickupTimeslotController := open_logistic_timeslot3.NewOpenLogisticPickupTimeslotController(openLogisticPickupTimeslotService)
	branchDao := branch_info.NewBranchDao()
	branchGroupDao := branch_group.NewBranchGroupDao()
	spexService := spex_service.NewSpexService()
	lcsService := lcs_service.NewLcsService()
	postalCodeToGeoDao := postal_code_to_geo.NewPostalCodeToGenDao()
	branchTaskDao := branch_task_record.NewBranchTaskDao()
	stationDao := station_conf.NewLogisticStationDao()
	branchService := branch.NewBranchService(branchDao, branchGroupDao, s3Service, spexService, lcsService, postalCodeToGeoDao, branchTaskDao, stationDao)
	branchController := controller.NewBranchController(branchService)
	cdtAutoUpdateRuleDao := auto_update_rule.NewCdtAutoUpdateRuleDao()
	laneCdtAutoUpdateDataDao := lane_auto_update_data.NewLaneCdtAutoUpdateDataDao()
	cdtAutoUpdateDataDao := auto_update_data.NewCdtAutoUpdateDataDao(laneCdtAutoUpdateDataDao)
	laneCdtManualUpdateRuleDao := lane_manual_update_rule.NewLaneCdtManualUpdateRuleDao()
	cdtManualUpdateRuleDao := manual_update_rule.NewCdtManualUpdateRuleDao(laneCdtManualUpdateRuleDao)
	productCtimeDao := product_ctime.NewProductCtimeDao()
	eddAutoUpdateRuleDao := edd_auto_update_rule.NewEddAutoUpdateRuleDao()
	cdtAbTestRuleDao := cdt_ab_test.NewCdtAbTestRuleDao()
	autoUpdateService := auto_update_rule2.NewAutoUpdateService(cdtAutoUpdateRuleDao, cdtAutoUpdateDataDao, cdtManualUpdateRuleDao, laneCdtManualUpdateRuleDao, productCtimeDao, eddAutoUpdateRuleDao, cdtAbTestRuleDao)
	autoUpdateDataService := auto_update_rule3.NewAutoUpdateDataService(cdtAutoUpdateDataDao, laneCdtAutoUpdateDataDao, cdtAutoUpdateRuleDao)
	cdtAutoRuleController := auto_update_rule4.NewCdtAutoRuleController(autoUpdateService, autoUpdateDataService)
	laneCdtManualManipulationRuleDao := lane_manual_manipulation_rule.NewLaneCdtManualManipulationRuleDao()
	cdtManualManipulationRuleDao := manual_manipulation_rule.NewCdtManualManipulationRuleDao(laneCdtManualManipulationRuleDao)
	manualManipulationService := manual_manipulation_rule2.NewManualManipulationService(cdtManualManipulationRuleDao, laneCdtManualManipulationRuleDao)
	cdtManualManipulationController := manual_manipulation_rule3.NewCdtManualManipulationController(manualManipulationService)
	manualUpdateService := manual_update_rule2.NewManualManipulationService(cdtManualUpdateRuleDao, laneCdtManualUpdateRuleDao, cdtAbTestRuleDao)
	cdtManualUpdateController := manual_update_rule3.NewCdtManualUpdateController(manualUpdateService)
	slsHolidayDao := sls_holiday.NewSlsHolidayDao()
	lpsHolidayDao := lps_holiday.NewLPSHolidayDAO()
	tplIDLineIDRefDao := tpl_id_line_id_ref.NewTPLIDLineIDRefDAO()
	eddHistoryDao := edd_history.NewEddHistoryDao()
	aggregateMaskedChannelCdtDao := aggregate_masked_channel_cdt.NewAggregateMaskedChannelCdtDao()
	cdtAbTestService := cdt_ab_test2.NewCdtAbTestService(cdtAbTestRuleDao, cdtAutoUpdateRuleDao, cdtManualUpdateRuleDao, aggregateMaskedChannelCdtDao)
	automatedVolumeGenerationDataDao := automated_volume_generation_data.NewAutomatedVolumeGenerationDataDao()
	automatedVolumeGenerationRuleDao := automated_volume.NewAutomatedVolumeGenerationRuleDao()
	cdtCalculationService := auto_update_rule5.NewCdtCalculationService(cdtAutoUpdateRuleDao, cdtAutoUpdateDataDao, cdtManualUpdateRuleDao, cdtManualManipulationRuleDao, slsHolidayDao, lpsHolidayDao, tplIDLineIDRefDao, eddHistoryDao, cdtAbTestService, automatedVolumeGenerationDataDao, automatedVolumeGenerationRuleDao)
	cdtRuleOverviewService := cdt_overview.NewCdtRuleOverviewService(cdtManualManipulationRuleDao, cdtManualUpdateRuleDao, laneCdtManualUpdateRuleDao, cdtAutoUpdateRuleDao, cdtCalculationService)
	cdtRuleOverviewController := cdt_overview2.NewCdtRuleOverviewController(cdtRuleOverviewService)
	tplIDLineIDRefService := tpl_id_line_id_ref2.NewTplIDLineIDRefService(tplIDLineIDRefDao)
	tplIDLineIDRefController := tpl_id_line_id_ref3.NewTplIDLineIDRefController(tplIDLineIDRefService)
	siteServiceableAreaBasicConfDao := site_serviceable_area_basic_conf.NewSiteServiceableAreaBasicConfDAO()
	siteServiceableAreaBasicConfService := site_serviceable_area_basic_conf2.NewSiteServiceableAreaBasicConfService(siteServiceableAreaBasicConfDao)
	adminSiteServiceableAreaBasicConfController := site_service_area_basic_conf.NewAdminSiteServiceableAreaBasicConfController(siteServiceableAreaBasicConfService)
	siteServiceableAreaLocationDao := site_serviceable_area_location.NewSiteServiceableAreaLocationDAO()
	siteServiceableAreaLocationService := site_serviceable_area_location2.NewSiteServiceableAreaLocationService(siteServiceableAreaBasicConfDao, siteServiceableAreaLocationDao)
	adminSiteServiceableAreaLocationController := site_serviceable_area_location3.NewAdminSiteServiceableAreaLocationController(siteServiceableAreaLocationService)
	siteServiceableAreaPostcodeDao := site_serviceable_area_postcode.NewSiteServiceableAreaPostcodeDAO()
	siteServiceableAreaPostcodeService := site_serviceable_area_postcode2.NewSiteServiceableAreaPostcodeService(siteServiceableAreaBasicConfDao, siteServiceableAreaPostcodeDao)
	adminSiteServiceableAreaPostcodeController := site_serviceable_area_postcode3.NewAdminSiteServiceableAreaPostcodeController(siteServiceableAreaPostcodeService)
	siteServiceableAreaCepRangeDao := site_serviceable_area_cep_range.NewSiteServiceableAreaCepRangeDAO()
	siteServiceableAreaCepRangeService := site_serviceable_area_cep_range2.NewSiteServiceableAreaCepRangeService(siteServiceableAreaBasicConfDao, siteServiceableAreaCepRangeDao)
	adminSiteServiceableAreaCepRangeController := site_serviceable_area_cep_range3.NewAdminSiteServiceableAreaCepRangeController(siteServiceableAreaCepRangeService)
	logisticSATaskConfigurationDao := task_configuration.NewLogisticSATaskConfigurationDao()
	saTaskConfigurationService := task_configuration2.NewSATaskConfigurationService(logisticSATaskConfigurationDao, lineBasicServiceablePostcodeDAO)
	adminSATaskConfigurationController := task_configuration3.NewAdminSATaskConfigurationController(saTaskConfigurationService)
	logisticSATaskRecordDao := task_record.NewLogisticSATaskRecordDao()
	saTaskRecordService := task_record2.NewSATaskRecordService(logisticSATaskRecordDao, s3Service)
	adminSATaskRecordController := task_record3.NewAdminSATaskRecordController(saTaskRecordService, lineBasicServiceablePostcodeService)
	dataVersionDao := data_version.NewDataVersionDao()
	dataVersionService := data_version2.NewDataVersionService(dataVersionDao)
	dataVersionController := data_version3.NewDataVersionController(dataVersionService)
	initializedRuleDAO := initialized_rule.NewInitializedRuleDAO()
	effectiveRuleDAO := effective_rule.NewEffectiveRuleDAO()
	serviceableAreaRuleService := serviceable_area_rule.NewServiceableAreaRuleService(initializedRuleDAO, effectiveRuleDAO)
	serviceableInitializedRuleController := initialized_rule2.NewServiceableInitializedRuleController(serviceableAreaRuleService)
	serviceableEffectiveRuleController := effective_rule2.NewServiceableEffectiveRuleController(serviceableAreaRuleService)
	eddUpdateRuleConfigDao := edd_update_rule_conf.NewEDDUpdateRuleConfigDao()
	eddUpdateRuleConf := edd_update_rule_conf2.NewEDDUpdateRuleConf(eddUpdateRuleConfigDao)
	eddUpdateRuleConfController := edd_update_rule_conf3.NewEDDUpdateRuleConfController(eddUpdateRuleConf)
	eddUpdateTaskConfigDao := edd_update_task_conf.NewEDDUpdateTaskConfigDao()
	eddUpdatePreviewTaskRecordDao := edd_update_preview_task_record.NewEDDUpdatePreviewTaskRecordDao()
	eddUpdateTaskConf := edd_update_task_conf2.NewEDDUpdateTaskConf(eddUpdateTaskConfigDao, eddHistoryDao, s3Service, eddUpdatePreviewTaskRecordDao)
	eddUpdateTaskConfController := edd_update_task_conf3.NewEDDUpdateTaskConfController(eddUpdateTaskConf)
	eddHistoryConf := edd_history2.NewEDDHistoryConf(eddHistoryDao)
	eddHistoryController := edd_history3.NewEDDHistoryController(eddHistoryConf)
	eddForecastTaskDao := edd_forecast_task.NewEDDForecastRuleDao()
	eddAutoUpdateRule := edd_auto_update_rule2.NewEDDAutoUpdateRule(eddAutoUpdateRuleDao, eddForecastTaskDao, cdtAutoUpdateRuleDao)
	eddAutoUpdateRuleConfController := edd_auto_update_rule3.NewEDDAutoUpdateRuleConfController(eddAutoUpdateRule)
	algoModelConfigDao := algo_model_conf.NewAlgoModelConfigDao()
	algoModelConfService := algo_model_conf2.NewAlgoModelConfService(algoModelConfigDao)
	eddModelConfController := edd_model_conf.NewEDDModelConfController(algoModelConfService)
	edtModelConfController := edt_model_conf.NewEDTModelConfController(algoModelConfService)
	scheduledController := scheduled3.NewAdminScheduledController(scheduledService, lineBasicServiceableLocationService, lineBasicServiceablePostcodeService)
	eddForecastResultDao := edd_forecast_result.NewEDDForecastResultDao()
	eddForecastTask := edd_forecast_task2.NewEDDForecastTask(eddForecastTaskDao, eddForecastResultDao, cdtAutoUpdateRuleDao, eddAutoUpdateRuleDao)
	eddForecastTaskController := forecast_task.NewEDDForecastTaskController(eddForecastTask, autoUpdateService, eddAutoUpdateRule)
	eddForecastTaskResult := edd_forecast_result2.NewEDDForecastTaskResult(eddForecastResultDao, eddForecastTaskDao)
	eddForecastTaskResultController := forecast_task_result.NewEDDForecastTaskResultController(eddForecastTaskResult)
	eddWaybillDelayQueue := edd_delay_queue.NewEddWaybillDelayQueue()
	eddDelayQueueDebugController := edd_delay_queue2.NewEddDelayQueueDebugController(eddWaybillDelayQueue)
	deliveryInstructionBasicConfTab := delivery_conf.NewDeliveryInstructionBasicConfTab()
	deliveryInstructionBasicConfDetailTab := delivery_conf_detail.NewDeliveryInstructionBasicConfDetailTab()
	deliveryInstructionWhitelistCepRangeDao := delivery_instruction_whitelist_cep_range.NewDeliveryInstructionWhitelistCepRangeDAO()
	deliveryInstructionWhitelistLocationDao := delivery_instruction_whitelist_location.NewDeliveryInstructionWhitelistLocationDAO()
	deliveryMethod := delivery_method.NewDeliveryMethod(deliveryInstructionBasicConfTab, deliveryInstructionBasicConfDetailTab, deliveryInstructionWhitelistCepRangeDao, deliveryInstructionWhitelistLocationDao)
	deliveryInstructionConfController := delivery_instruction_conf.NewAdminDeliveryInstructionConfController(deliveryMethod)
	deliveryInstructionWhitelistCepRangeService := delivery_instruction_whitelist_cep_range2.NewDeliveryInstructionWhitelistCepRangeService(deliveryInstructionWhitelistCepRangeDao, deliveryInstructionWhitelistLocationDao)
	adminDeliveryInstructionWhitelistCepRangeController := delivery_instruction_whitelist_cep_range3.NewAdminDeliveryInstructionWhitelistCepRangeController(deliveryInstructionWhitelistCepRangeService)
	deliveryInstructionWhitelistLocationService := delivery_instruction_whitelist_location2.NewDeliveryInstructionWhitelistLocationService(deliveryInstructionWhitelistLocationDao, deliveryInstructionWhitelistCepRangeDao)
	deliveryInstructionWhitelistLocationController := delivery_instruction_whitelist_location3.NewAdminDeliveryInstructionWhitelistLocationController(deliveryInstructionWhitelistLocationService)
	spxServiceableAreaComapreService := spx_serviceable_area_compare2.NewSpxServiceableAreaCompareService(spxServiceableAreaCompareTaskDao, spxServiceableAreaVersionDao)
	spxServiceableAreaController := spx_serviceable_area2.NewSpxServiceableAreaController(spxServiceableAreaService, spxServiceableAreaComapreService)
	orderAccountMappingController := order_account_mapping3.NewOrderAccountMappingController(orderAccountMappingService)
	pickupWindowService := pickup_window.NewPickupWindowService(logisticHolidayDao, recurringHolidayDao)
	pickupWindowController := pickup_window2.NewPickupWindowController(pickupWindowService)
	cdtAbTestController := cdt_ab_test3.NewCdtAbTestController(cdtAbTestService)
	automatedVolume := automated_volume2.NewAutomatedVolumeGenerationRule(automatedVolumeGenerationRuleDao, automatedVolumeGenerationDataDao)
	automatedVolumeRuleController := automated_volume3.NewAutomatedVolumeRuleController(automatedVolume)
	aggregateMaskedChannelCdt := aggregate_masked_channel_cdt2.NewAggregateMaskedChannelCdt(aggregateMaskedChannelCdtDao, cdtAbTestRuleDao)
	aggregateMaskedChannelCdtController := aggregate_masked_channel_cdt3.NewAdminAggregateMaskedChannelCdtController(aggregateMaskedChannelCdt)
	syncItemCardCdtController := sync_item_card_cdt.NewSyncItemCardCdtController(autoUpdateDataService, manualUpdateService)
	hdDistanceDao := hd_distance_repo.NewHdDistanceDao()
	hdDistanceCacheDao := hd_distance_cache_repo.NewHdDistanceCacheDao()
	servicePointDao := service_point_geo.NewServicePointDao()
	googleResultDao := google_result_repo.NewGoogleResultDao()
	mapService := map_service.NewMapService(googleResultDao)
	googleResultCacheDao := google_result_cache_repo.NewGoogleResultCacheDao()
	stationBuyerDao := station_buyer_repo.NewStationBuyerDao()
	addressRevisionDao := address_revision_repo.NewAddressRevisionDao()
	hdStationCacheManagerImpl := hd_station_cache.NewHDStationCacheManagerImpl()
	homeDeliveryService := match.NewHomeDeliveryService(hdDistanceDao, hdDistanceCacheDao, servicePointDao, mapService, googleResultCacheDao, s3Service, stationDao, stationBuyerDao, addressRevisionDao, hdStationCacheManagerImpl)
	stationServiceImpl := station.NewStationServiceImpl(homeDeliveryService, hdStationCacheManagerImpl, stationDao, s3Service, addressRevisionDao)
	redisVolumeServiceImpl := station.NewRedisVolumeServiceImpl()
	stationVolumeServiceImpl := station.NewStationVolumeServiceImpl(stationServiceImpl, stationDao, redisVolumeServiceImpl, spexService)
	stationController := station2.NewStationController(stationServiceImpl, stationVolumeServiceImpl)
	productServiceableZoneDao := product_serviceable_zone.NewProductServiceableZoneDao()
	productServiceableZoneService := product_serviceable_zone2.NewProductServiceableZoneService(productServiceableZoneDao)
	productServiceableZoneController := product_serviceable_zone3.NewProductServiceableZoneController(productServiceableZoneService)
	shopServiceableZoneDao := shop_serviceable_zone.NewShopServiceableZoneDao()
	shopServiceableZoneService := shop_serviceable_zone2.NewShopServiceableZoneService(productServiceableZoneDao, shopServiceableZoneDao)
	shopServiceableZoneController := shop_serviceable_zone3.NewShopServiceableZoneController(shopServiceableZoneService)
	eFencePolygonDao := polygon.NewEFencePolygonDao()
	eFenceMeshDao := mesh.NewEFenceMeshDao()
	eFenceLocationWhitelistDao := location_whitelist.NewEFenceLocationWhitelistDao()
	eFenceLineToggleDao := line_toggle.NewEFenceLineToggleDao()
	eFenceService := e_fence.NewEFenceService(eFencePolygonDao, eFenceMeshDao, eFenceLocationWhitelistDao, eFenceLineToggleDao)
	eFenceController := e_fence2.NewEFenceController(eFenceService)
	edtRuleRepoImpl := edt_rule.NewEdtRuleRepoImpl()
	validatorManagerImpl := edt_rule2.NewValidatorManagerImpl(edtRuleRepoImpl)
	edtRuleServiceImpl := edt_rule2.NewEdtRuleServiceImpl(edtRuleRepoImpl, validatorManagerImpl)
	edtRuleControllerImpl := edt_rule3.NewEdtRuleControllerImpl(edtRuleServiceImpl)
	edtConfigDao := edt_config.NewEdtConfigDao()
	edtConfigService := edt_config_service.NewEdtConfigService(edtConfigDao)
	edtConfigController := edt_config2.NewEdtConfigController(edtConfigService)
	timeExperimentRepoImpl := time_experiment_repo.NewTimeExperimentRepoImpl()
	timeExperimentServiceImpl := time_experiment.NewTimeExperimentServiceImpl(timeExperimentRepoImpl)
	timeExperimentControllerImpl := time_experiment2.NewTimeExperimentControllerImpl(timeExperimentServiceImpl)
	commonCardDeliveryAddressService := common_card_delivery_address.NewCommonCardDeliveryAddressService(cardDeliveryAddressChangeVersionDao, lineBasicServiceableLocationService, lineBasicServiceablePostcodeService, lineBasicServiceableConfDAO)
	cardDeliveryAddressController := card_delivery_address3.NewCardDeliveryAddressController(lineBasicServiceableLocationService, cardDeliveryAddressService, commonCardDeliveryAddressService)
	logisticParcelLibraryController := parcel_library3.NewLogisticParcelLibraryController(logisticParcelLibraryService)
	logisticProductGeoDistanceConfRepoImpl := geo_distance_repo.NewLogisticProductGeoDistanceConfRepo()
	logisticProductGeoDistanceServiceImpl := geo_distance_service.NewLogisticProductGeoDistanceService(logisticProductGeoDistanceConfRepoImpl)
	logisticProductGeoDistanceControllerImpl := geo_distance_controller.NewLogisticProductGeoDistanceController(logisticProductGeoDistanceServiceImpl)
	logisticLinePredefinedRouteDaoImpl := predefined_route_model.NewLogisticLinePredefinedRouteDao()
	logisticLinePredefinedRouteServiceImpl := predefined_route_serviceable.NewLogisticLinePredefinedRouteService(logisticLinePredefinedRouteDaoImpl, logisticLineServiceableAreaTabDAO)
	logisticPredefinedRouteControllerImpl := predefined_route_serviceable2.NewLogisticPredefinedRouteController(logisticLinePredefinedRouteServiceImpl)
	apiService := &APIService{
		LinePackageLimitController:                     adminLinePackageLimitController,
		ProductPackageLimitController:                  adminProductPackageLimitController,
		LineBasicServiceableConfController:             adminLineBasicServiceableConfController,
		LineBasicServiceableLocationController:         adminLineBasicServiceableLocationController,
		LineBasicServiceablePostcodeController:         adminLineBasicServiceablePostcodeController,
		LineOperationServiceablePostcodeController:     adminLineOperationServiceablePostcodeController,
		LineOperationServiceableConfController:         adminLineOperationServiceableConfController,
		LineOperationServiceableLocationController:     adminLineOperationServiceableLocationController,
		LineServiceableAreaController:                  adminLineServiceableAreaController,
		LineServiceableRouteController:                 adminLineServiceableRouteController,
		LineOperationRouteController:                   adminLineOperationRouteController,
		CollectDeliverGroupController:                  collectDeliverGroupController,
		LineBasicServiceableCepRangeController:         adminLineBasicServiceableCepRangeController,
		LineOperationServiceableCepRangeController:     adminLineOperationServiceableCepRangeController,
		HolidaysController:                             lineHolidaysController,
		RecurringHolidaysController:                    lineRecurringHolidaysController,
		PickupConfigController:                         pickupConfigController,
		PickupTimeslotController:                       linePickupTimeslotController,
		PickupGroupController:                          pickupGroupController,
		OpenLogisticPickupConfigController:             openLogisticPickupConfigController,
		OpenLogisticPickupTimeslotController:           openLogisticPickupTimeslotController,
		BranchController:                               branchController,
		CdtAutoUpdateRuleController:                    cdtAutoRuleController,
		CdtManualManipulationRuleController:            cdtManualManipulationController,
		CdtManualUpdateRuleController:                  cdtManualUpdateController,
		CdtRuleOverviewController:                      cdtRuleOverviewController,
		TplIDLineIDRefController:                       tplIDLineIDRefController,
		SiteServiceableAreaBasicConfController:         adminSiteServiceableAreaBasicConfController,
		SiteServiceableAreaLocationController:          adminSiteServiceableAreaLocationController,
		SiteServiceableAreaPostcodeController:          adminSiteServiceableAreaPostcodeController,
		SiteServiceableAreaCepRangeController:          adminSiteServiceableAreaCepRangeController,
		SATaskConfigurationController:                  adminSATaskConfigurationController,
		SATaskRecordController:                         adminSATaskRecordController,
		DataVersionController:                          dataVersionController,
		ServiceableInitializedRuleController:           serviceableInitializedRuleController,
		ServiceableEffectiveRuleController:             serviceableEffectiveRuleController,
		EDDUpdateRuleConfController:                    eddUpdateRuleConfController,
		EDDUpdateTaskConfController:                    eddUpdateTaskConfController,
		EDDHistoryController:                           eddHistoryController,
		EDDAutoUpdateController:                        eddAutoUpdateRuleConfController,
		EDDModelConfController:                         eddModelConfController,
		EDTModelConfController:                         edtModelConfController,
		AdminScheduledController:                       scheduledController,
		EDDForecastTaskController:                      eddForecastTaskController,
		EDDForecastTaskResultController:                eddForecastTaskResultController,
		EDDDelayQueueDebugController:                   eddDelayQueueDebugController,
		DeliveryInstructionConfController:              deliveryInstructionConfController,
		DeliveryInstructionWhitelistCepRangeController: adminDeliveryInstructionWhitelistCepRangeController,
		DeliveryInstructionWhitelistLocationController: deliveryInstructionWhitelistLocationController,
		SpxServiceableAreaController:                   spxServiceableAreaController,
		OrderAccountMappingController:                  orderAccountMappingController,
		PickupWindowController:                         pickupWindowController,
		CdtAbTestController:                            cdtAbTestController,
		AutomatedVolumeRuleController:                  automatedVolumeRuleController,
		AggregateMaskedChannelCdtController:            aggregateMaskedChannelCdtController,
		SyncItemCardCdtController:                      syncItemCardCdtController,
		StationController:                              stationController,
		ProductServiceableZoneController:               productServiceableZoneController,
		ShopServiceableZoneController:                  shopServiceableZoneController,
		EFenceController:                               eFenceController,
		EdtRuleController:                              edtRuleControllerImpl,
		EdtConfigController:                            edtConfigController,
		TimeExperimentController:                       timeExperimentControllerImpl,
		CardDeliveryAddressController:                  cardDeliveryAddressController,
		ParcelLibraryController:                        logisticParcelLibraryController,
		GeoDistanceController:                          logisticProductGeoDistanceControllerImpl,
		PredefinedRouteController:                      logisticPredefinedRouteControllerImpl,
	}
	return apiService
}
