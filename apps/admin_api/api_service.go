package admin_api

import (
	branchController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/branch"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/cdt_overview"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_delay_queue"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_update_rule_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edt_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/forecast_task_result"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/manual_manipulation_rule"
	manual_manipulation_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/data_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/delivery_instruction/delivery_instruction_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/edt_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/geo_distance_controller"
	linePackageLimitController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/parcel_library"
	productPackageLimitController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/product_package_limit"
	holidays2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/holidays"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/open_logistic_pickup_config"
	open_logistic_timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/open_logistic_timeslot"
	holidays5 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/pickup_config"
	pickup_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/pickup_window"
	holidays4 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/recurring_holidays"
	timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/timeslot"
	task_configuration2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/sa_task/task_configuration"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/sa_task/task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/scheduled"
	lineServiceableAreaRouteController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_cep_range"
	lineBasicServiceableConfController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_conf"
	lineBasicServiceableLocationController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_location"
	lineBasicServiceablePostcodeController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/card_delivery_address"
	collectDeliverGroupController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/collect_deliver_group"
	cepRangeOperationServiceableController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_cep_range"
	lineOperationServiceableConfController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_conf"
	lineOperationServiceableLocationController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_location"
	lineOperationServiceablePostcodeController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_postcode"
	lineOperationRouteController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/predefined_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/zone_serviceable/shop_serviceable_zone"
	SAEffectiveRuleController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area_rule/effective_rule"
	SAInitializedRuleController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area_rule/initialized_rule"
	basic_conf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/spx_serviceable_area/spx_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/station"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/sync_item_card_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/time_experiment"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/tpl_id_line_id_ref"
)

type APIService struct {
	LinePackageLimitController linePackageLimitController.AdminLinePackageLimitController

	ProductPackageLimitController productPackageLimitController.AdminProductPackageLimitController

	LineBasicServiceableConfController lineBasicServiceableConfController.AdminLineBasicServiceableConfController

	LineBasicServiceableLocationController lineBasicServiceableLocationController.AdminLineBasicServiceableLocationController

	LineBasicServiceablePostcodeController lineBasicServiceablePostcodeController.AdminLineBasicServiceablePostcodeController

	LineOperationServiceablePostcodeController lineOperationServiceablePostcodeController.AdminLineOperationServiceablePostcodeController

	LineOperationServiceableConfController lineOperationServiceableConfController.AdminLineOperationServiceableConfController

	LineOperationServiceableLocationController lineOperationServiceableLocationController.AdminLineOperationServiceableLocationController

	LineServiceableAreaController lineServiceableAreaRouteController.AdminLineServiceableAreaController

	LineServiceableRouteController lineServiceableAreaRouteController.AdminLineServiceableRouteController

	LineOperationRouteController lineOperationRouteController.AdminLineOperationRouteController

	CollectDeliverGroupController collectDeliverGroupController.CollectDeliverGroupController

	LineBasicServiceableCepRangeController basic_cep_range.AdminLineBasicServiceableCepRangeController

	LineOperationServiceableCepRangeController cepRangeOperationServiceableController.AdminLineOperationServiceableCepRangeController

	HolidaysController holidays2.AdminLineHolidaysController

	RecurringHolidaysController holidays4.AdminLineRecurringHolidaysController

	PickupConfigController holidays5.AdminPickupConfigController

	PickupTimeslotController timeslot2.AdminPickupTimeslotController

	PickupGroupController pickup_group2.AdminPickupGroupController

	OpenLogisticPickupConfigController open_logistic_pickup_config.AdminOpenLogisticPickupConfigController

	OpenLogisticPickupTimeslotController open_logistic_timeslot2.AdminOpenLogisticPickupTimeslotController

	BranchController branchController.BranchController

	CdtAutoUpdateRuleController auto_update_rule.AdminCdtAutoRuleController

	CdtManualManipulationRuleController manual_manipulation_rule.AdminCdtManualManipulationRuleController

	CdtManualUpdateRuleController manual_manipulation_rule2.AdminCdtManualUpdateRuleController

	CdtRuleOverviewController cdt_overview.AdminCdtRuleOverviewController

	TplIDLineIDRefController tpl_id_line_id_ref.AdminTplIDLineIDRefController

	SiteServiceableAreaBasicConfController basic_conf.AdminSiteServiceableAreaBasicConfControllerInterface

	SiteServiceableAreaLocationController site_serviceable_area_location.AdminSiteServiceableAreaLocationControllerInterface

	SiteServiceableAreaPostcodeController site_serviceable_area_postcode.AdminSiteServiceableAreaPostcodeControllerInterface

	SiteServiceableAreaCepRangeController site_serviceable_area_cep_range.AdminSiteServiceableAreaCepRangeControllerInterface

	SATaskConfigurationController task_configuration2.AdminSATaskConfigurationControllerInterface

	SATaskRecordController task_record.AdminSATaskRecordControllerInterface

	DataVersionController data_version.DataVersionController

	ServiceableInitializedRuleController SAInitializedRuleController.AdminServiceableInitializedRuleController

	ServiceableEffectiveRuleController SAEffectiveRuleController.AdminServiceableEffectiveRuleController

	EDDUpdateRuleConfController edd_update_rule_conf.AdminEDDUpdateRuleConfController

	EDDUpdateTaskConfController edd_update_task_conf.AdminEDDUpdateTaskConfController

	EDDHistoryController edd_history.AdminEDDHistoryController

	EDDAutoUpdateController edd_auto_update_rule.AdminEDDAutoUpdateRuleController

	EDDModelConfController edd_model_conf.AdminEDDModelConfController

	EDTModelConfController edt_model_conf.AdminEDTModelConfController

	AdminScheduledController scheduled.AdminScheduledController

	EDDForecastTaskController forecast_task.AdminEDDForecastTaskController

	EDDForecastTaskResultController forecast_task_result.AdminEDDForecastTaskResultController

	EDDDelayQueueDebugController edd_delay_queue.EddDelayQueueDebugController

	DeliveryInstructionConfController delivery_instruction_conf.AdminDeliveryInstructionConfController

	DeliveryInstructionWhitelistCepRangeController delivery_instruction_whitelist_cep_range.AdminDeliveryInstructionWhitelistCepRangeControllerInterface

	DeliveryInstructionWhitelistLocationController delivery_instruction_whitelist_location.AdminDeliveryInstructionWhitelistLocationControllerInterface

	SpxServiceableAreaController spx_serviceable_area.SpxServiceableAreaController

	OrderAccountMappingController order_account_mapping.OrderAccountMappingController

	PickupWindowController pickup_window.PickupWindowController

	CdtAbTestController cdt_ab_test.CdtAbTestController

	AutomatedVolumeRuleController automated_volume.AdminAutomatedVolumeRuleController

	AggregateMaskedChannelCdtController aggregate_masked_channel_cdt.AdminAggregateMaskedChannelCdtController

	SyncItemCardCdtController sync_item_card_cdt.SyncItemCardCdtController

	StationController station.StationController

	ProductServiceableZoneController product_serviceable_zone.ProductServiceableZoneController

	ShopServiceableZoneController shop_serviceable_zone.ShopServiceableZoneController

	EFenceController         e_fence.EFenceController
	EdtRuleController        edt_rule.EdtRuleController
	EdtConfigController      edt_config.EdtConfigController
	TimeExperimentController time_experiment.TimeExperimentController

	CardDeliveryAddressController card_delivery_address.CardDeliveryAddressController

	ParcelLibraryController parcel_library.LogisticParcelLibraryController
	GeoDistanceController   geo_distance_controller.LogisticProductGeoDistanceController

	PredefinedRouteController predefined_route_serviceable.LogisticPredefinedRouteController
}
