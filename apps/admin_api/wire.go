//go:generate wire
//go:build wireinject
// +build wireinject

package admin_api

import (
	branchController "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/branch"
	aggregate_masked_channel_cdt2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/auto_update_rule"
	automated_volume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/automated_volume"
	cdt_ab_test3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/cdt_overview"
	edd_auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_delay_queue"
	edd_history2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_update_rule_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/edt_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/forecast_task_result"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/manual_manipulation_rule"
	manual_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/data_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/delivery_instruction/delivery_instruction_conf"
	delivery_instruction_whitelist_cep_range2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/delivery_instruction/delivery_instruction_whitelist_cep_range"
	delivery_instruction_whitelist_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/edt_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/geo_distance_controller"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/line_package_limit"
	parcel_library3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/holidays"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/open_logistic_pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/open_logistic_timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/pickup_config"
	pickup_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/pickup_window"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/recurring_holidays"
	timeslot3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/sa_task/task_configuration"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/sa_task/task_record"
	scheduled3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/scheduled"
	area_route_serviceable4 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/area_route_serviceable"
	basic_cep_range2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_conf"
	basic_location3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_location"
	basic_postcode3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/basic_serviceable/basic_postcode"
	card_delivery_address3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/card_delivery_address"
	collect_deliver_group3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/collect_deliver_group"
	operation_cep_range2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_cep_range"
	operation_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_conf"
	operation_location3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_location"
	operation_postcode3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/operation_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/predefined_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area/zone_serviceable/shop_serviceable_zone"
	effective_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area_rule/effective_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/serviceable_area_rule/initialized_rule"
	siteServiceableAreaBasicConfSiteServiceAreaBasicConf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/site_serviceable_area/site_serviceable_area_postcode"
	order_account_mapping3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/spx_serviceable_area/order_account_mapping"
	spx_serviceable_area2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/spx_serviceable_area/spx_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/station"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/sync_item_card_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/time_experiment"
	tpl_id_line_id_ref2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/address_revision_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	algo_model_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/algo_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	auto_update_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_result"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_preview_task_record"
	edd_update_rule_conf3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_rule_conf"
	edd_update_task_conf3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	manual_manipulation_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/product_ctime"
	data_version3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/data_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf_detail"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	edt_config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_config"
	edt_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/geo_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	line_package_limit3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	parcel_library2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	product_package_limit3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/holiday"
	open_logistic_pickup_config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_pickup_config"
	open_logistic_timeslot3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_timeslot"
	pickup_config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/recurring_holiday"
	timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/postal_code_to_geo"
	task_configuration3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sa_task/task_configuration"
	task_record3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sa_task/task_record"
	scheduled2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/service_point_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	operation_route3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	basic_conf3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	card_delivery_address2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	collect_deliver_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	operation_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	operation_postcode2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	product_serviceable_zone3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	shop_serviceable_zone3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	initialized_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/initialized_rule"
	siteBasicConfSiteServiceableAreaBasicConf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	siteServiceableCepRangeSiteServiceableAreaCepRange "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	siteServiceableLocationSiteServiceableAreaLocation "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	siteServiceablePostcodeSiteServiceableAreaPostcode "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_holiday"
	order_account_mapping2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_compare"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/hd_station_cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/station_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station_buyer_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/time_experiment_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/branch"
	aggregate_masked_channel_cdt3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/algo_model_conf"
	autoUpdateDataAutoUpdateRule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_data"
	auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_rule"
	automated_volume3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/automated_volume"
	cdt_ab_test2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_ab_test"
	cdtCalculationAutoUpdateRule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_calculation"
	cdt_overview2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_overview"
	edd_auto_update_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_auto_update_rule"
	edd_delay_queue2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_delay_queue"
	edd_forecast_result2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_forecast_result"
	edd_forecast_task2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_forecast_task"
	edd_history3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_history"
	edd_update_rule_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_update_rule_conf"
	edd_update_task_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_update_task_conf"
	manual_manipulation_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_manipulation_rule"
	manual_update_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_update_rule"
	data_version2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/data_version"
	delivery_instruction_whitelist_cep_range3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_instruction_whitelist_cep_range"
	delivery_instruction_whitelist_location3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_method"
	e_fence2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/edt_config_service"
	edt_rule4 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/geo_distance_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/map_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/match"
	line_package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	product_package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/product_package_limit"
	holidays2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/holidays"
	openLogisticPickupConfigOpenLogisticPickupConfiguration "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/open_logistic_pickup_config"
	open_logistic_timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/open_logistic_timeslot"
	pickupConfigPickupConfiguration "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_config"
	pickup_group3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_group"
	pickup_window2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_window"
	recurring_holidays2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/recurring_holidays"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/timeslot"
	task_configuration2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/sa_task/task_configuration"
	task_record2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/sa_task/task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	area_route_serviceable3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_cep_range"
	basic_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_conf"
	basic_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_location"
	basic_postcode2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/common_card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_cep_range"
	operation_conf3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_postcode"
	operation_route2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_route"
	predefined_route_serviceable2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/predefined_route_serviceable"
	product_serviceable_zone2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/zone_serviceable/product_serviceable_zone"
	shop_serviceable_zone2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_basic_conf"
	site_serviceable_area_cep_range2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_cep_range"
	site_serviceable_area_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_location"
	site_serviceable_area_postcode2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area"
	spx_serviceable_area_compare2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area_compare"
	station4 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/station"
	time_experiment2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/time_experiment"
	tpl_id_line_id_ref3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spex_service"
	"github.com/google/wire"
)

func InitAdminAPIService() *APIService {
	wire.Build(
		wire.Struct(new(APIService), "*"),
		line_package_limit.AdminLinePackageLimitProviderSet,
		line_package_limit2.LinePackageLimitServiceProviderSet,
		line_package_limit3.LinePackageLimitDAOProviderSet,
		parcel_library.LogisticParcelLibraryServiceProviderSet,
		parcel_library2.LogisticParcelLibraryDaoProviderSet,
		product_package_limit.AdminProductPackageLimitProviderSet,
		product_package_limit2.ProductPackageLimitServiceProviderSet,
		product_package_limit3.ProductPackageLimitDAOProviderSet,
		basic_conf.AdminLineBasicServiceableConfProviderSet,
		basic_conf2.LineBasicServiceableConfManagerProviderSet,
		basic_conf3.LineBasicServiceableConfProviderSet,
		scenario_conf.LineCommonServiceableScenarioConfProviderSet,
		operation_conf.LogisticLineOperationServiceableConfTabProviderSet,
		collect_deliver_group_ref.LineBasicServiceableGroupRefProviderSet,
		collect_deliver_group.CollectDeliverGroupServiceProviderSet,
		collect_deliver_group2.LineCollectDeliverGroupConfProviderSet,
		basic_location.LineBasicServiceableLocationProviderSet,
		basic_postcode.LineBasicServiceablePostcodeProviderSet,
		basic_cep_range.LineBasicServiceableCepRangeServiceProviderSet,
		cep_range.LineBasicServiceableCepRangeProviderSet,
		scheduled.ScheduledServiceProviderSet,
		scheduled2.ScheduledDaoSet,
		s3_service.S3ServiceProviderSet,
		operation_cep_range.LineOperationServiceableCepRangeServiceProviderSet,
		basic_location2.LineBasicServiceableLocationServiceProviderSet,
		order_account_mapping.OrderAccountMappingProviderSet,
		order_account_mapping2.OrderAccountMappingDaoProviderSet,
		spx_serviceable_area.SpxServiceableAreaServiceProviderSet,
		spx_serviceable_area_version.SpxServiceableAreaVersionDaoProvierSet,
		spx_serviceable_area_data.SpxServiceableAreaDataDaoProviderSet,
		spx_serviceable_area_compare.SpxServiceableAreaCompareTaskDaoProviderSet,
		card_delivery_address.CardDeliveryAddressServiceProviderSet,
		card_delivery_address2.CardDeliveryAddressChangeVersionDaoProviderSet,
		basic_postcode2.LineBasicServiceablePostcodeServiceProviderSet,
		operation_postcode.LineOperationServiceablePostcodeServiceProviderSet,
		operation_postcode2.LogisticLineOperationServiceablePostcodeTabProviderSet,
		operation_location.LineOperationServiceableLocationServiceProviderSet,
		operation_location2.LogisticLineOperationServiceableLocationTabProviderSet,
		area_route_serviceable3.LineServiceableRouteServiceProviderSet,
		route.LogisticLineServiceableRouteTabProviderSet,
		area.LogisticLineServiceableAreaTabProviderSet,
		basic_location3.AdminLineBasicServiceableLocationProviderSet,
		basic_postcode3.AdminLineBasicServiceablePostcodeProviderSet,
		operation_postcode3.AdminLineOperationServiceablePostcodeProviderSet,
		operation_conf2.AdminLineOperationServiceableConfProviderSet,
		operation_conf3.LineOperationServiceableConfServiceProviderSet,
		operation_location3.AdminLineOperationServiceableLocationProviderSet,
		area_route_serviceable4.AdminLineServiceableAreaProviderSet,
		area_route_serviceable3.LineServiceableAreaServiceProviderSet,
		area_location_ref.LogisticLineServiceableAreaLocationRefTabProviderSet,
		area_route_serviceable4.AdminLineServiceableRouteProviderSet,
		operation_route.AdminLineOperationRouteProviderSet,
		operation_route2.LineOperationRouteServiceProviderSet,
		operation_route3.LineOperationRouteTabProviderSet,
		collect_deliver_group3.CollectDeliverGroupControllerProviderSet,
		basic_cep_range2.AdminLineBasicServiceableCepRangeProviderSet,
		operation_cep_range2.AdminLineOperationServiceableCepRangeProviderSet,
		holidays.AdminLineHolidaysProviderSet,
		holidays2.LineHolidaysServiceProviderSet,
		holiday.LineHolidayDAOProviderSet,
		recurring_holiday.LineRecurringHolidayDAOProviderSet,
		pickup_group.PickupGroupDAOProviderSet,
		recurring_holidays.AdminLineRecurringHolidaysProviderSet,
		recurring_holidays2.LineRecurringHolidaysServiceProviderSet,
		pickup_config.AdminPickupConfigProviderSet,
		pickupConfigPickupConfiguration.LinePickupConfigServiceProviderSet,
		pickup_config2.PickupConfigDAOProviderSet,
		timeslot.PickupTimeslotServiceProviderSet,
		timeslot2.PickupTimeslotDAOProviderSet,
		timeslot3.AdminLinePickupTimeslotProviderSet,
		pickup_group2.AdminPickupGroupProviderSet,
		pickup_group3.PickupGroupServiceProviderSet,
		open_logistic_pickup_config.AdminOpenLogisticPickupConfigProviderSet,
		openLogisticPickupConfigOpenLogisticPickupConfiguration.OpenLogisticPickupConfigServiceProviderSet,
		open_logistic_pickup_config2.OpenLogisticPickupConfigDAOProviderSet,
		open_logistic_timeslot.AdminOpenLogisticPickupTimeslotProviderSet,
		open_logistic_timeslot2.OpenLogisticPickupTimeslotServiceProviderSet,
		open_logistic_timeslot3.OpenLogisticPickupTimeslotDAOProviderSet,
		branchController.AdminBranchControllerProviderSet,
		branch.BranchServiceProviderSet,
		branch_info.BranchDaoProviderSet,
		branch_group.BranchGroupDaoProviderSet,
		spex_service.SpexServiceProviderSet,
		lcs_service.LcsServiceProviderSet,
		postal_code_to_geo.BranchGroupDaoProviderSet,
		branch_task_record.BranchTaskRecordDaoProviderSet,
		station_conf.LogisticStationDaoProviderSet,
		auto_update_rule.CdtAutoRuleServiceProviderSet,
		auto_update_rule2.CdtAutoUpdateRuleDAOProviderSet,
		auto_update_rule3.CdtAutoUpdateRuleDAOProviderSet,
		auto_update_data.CdtAutoUpdateDataDAOProviderSet,
		lane_auto_update_data.CdtAutoUpdateDataDAOProviderSet,
		manual_update_rule.CdtAutoUpdateRuleDAOProviderSet,
		lane_manual_update_rule.CdtAutoUpdateRuleDAOProviderSet,
		product_ctime.ProductCtimeDAOProviderSet,
		edd_auto_update_rule.EddAutoUpdateRuleDaoProviderSet,
		cdt_ab_test.CdtAbTestRuleDaoProviderSet,
		autoUpdateDataAutoUpdateRule.CdtAutoUpdateDataDAOProviderSet,
		manual_manipulation_rule.CdtAutoRuleServiceProviderSet,
		manual_manipulation_rule2.CdtManualManipulationRuleDAOProviderSet,
		manual_manipulation_rule3.CdtManualManipulationRuleDAOProviderSet,
		lane_manual_manipulation_rule.CdtManualManipulationRuleDAOProviderSet,
		manual_update_rule2.CdtManualUpdateRuleServiceProviderSet,
		manual_update_rule3.CdtManualUpdateRuleDAOProviderSet,
		cdt_overview.CdtOverviewServiceProviderSet,
		cdt_overview2.CdtRuleOverviceServiceDaoProviderSet,
		cdtCalculationAutoUpdateRule.CdtCalculationServiceProviderSet,
		sls_holiday.SlsHolidayDAOProviderSet,
		lps_holiday.LPSHolidayDAOProviderSet,
		tpl_id_line_id_ref.TPLIDLineIDRefDAOProviderSet,
		edd_history.EDDHistoryDAOProviderSet,
		cdt_ab_test2.CdtAbTestRuleServiceProviderSet,
		aggregate_masked_channel_cdt.AggregateMaskedChannelCdtProviderSet,
		automated_volume_generation_data.AutomatedVolumeGenerationDataProviderSet,
		automated_volume.AutomatedVolumeGenerationRuleProviderSet,
		tpl_id_line_id_ref2.TplIDLineIDRefProviderSet,
		tpl_id_line_id_ref3.TPLIDLineIDRefServiceProviderSet,
		siteServiceableAreaBasicConfSiteServiceAreaBasicConf.AdminSiteServiceableAreaBasicConfProviderSet,
		site_serviceable_area_basic_conf.SiteServiceableAreaBasicConfServiceProviderSet,
		siteBasicConfSiteServiceableAreaBasicConf.LogisticSiteServiceableAreaBasocConfDaoProviderSet,
		site_serviceable_area_location.AdminSiteServiceableAreaLocationProviderSet,
		site_serviceable_area_location2.SiteServiceableAreaLocationServiceProviderSet,
		siteServiceableLocationSiteServiceableAreaLocation.LogisticSiteServiceableAreaCepRangeDaoProviderSet,
		site_serviceable_area_postcode.AdminSiteServiceableAreaPostcodeProviderSet,
		site_serviceable_area_postcode2.SiteServiceableAreaPostcodeServiceProviderSet,
		siteServiceablePostcodeSiteServiceableAreaPostcode.LogisticSiteServiceableAreaPostcodeDaoProviderSet,
		site_serviceable_area_cep_range.AdminSiteServiceableAreaCepRangeProviderSet,
		site_serviceable_area_cep_range2.SiteServiceableAreaCepRangeServiceProviderSet,
		siteServiceableCepRangeSiteServiceableAreaCepRange.LogisticSiteServiceableAreaCepRangeDaoProviderSet,
		task_configuration.AdminSATaskConfProviderSet,
		task_configuration2.SATaskConfigurationServiceProviderSet,
		task_configuration3.LogisticSATaskConfigruationProviderSet,
		task_record.AdminSATaskRecordProviderSet,
		task_record2.SATaskRecordServiceProviderSet,
		task_record3.LogisticSATaskRecordDaoProviderSet,
		data_version.DataVersionControllerSet,
		data_version2.DataVersionServiceSet,
		data_version3.DataVersionDaoSet,
		initialized_rule.AdminServiceableInitializedRuleControllerProviderSet,
		serviceable_area_rule.ServiceableAreaRuleServiceProviderSet,
		initialized_rule2.ServiceableInitializedRuleDAOProviderSet,
		effective_rule.ServiceableEffectiveRuleProviderSet,
		effective_rule2.AdminServiceableEffectiveRuleControllerProviderSet,
		edd_update_rule_conf.EDDUpdateRuleConfControllerProviderSet,
		edd_update_rule_conf2.EDDUpdateRuleConfServiceProviderSet,
		edd_update_rule_conf3.EDDUpdateRuleConfDAOProviderSet,
		edd_update_task_conf.EDDUpdateTaskConfControllerProviderSet,
		edd_update_task_conf2.EDDUpdateTaskConfServiceProviderSet,
		edd_update_task_conf3.EDDUpdateTaskConfDAOProviderSet,
		edd_update_preview_task_record.EDDUpdatePreviewTaskRecordDAOProviderSet,
		edd_history2.EDDHistoryControllerProviderSet,
		edd_history3.EDDHistoryServiceProviderSet,
		edd_auto_update_rule2.EDDAutoUpdateRuleControllerProviderSet,
		edd_auto_update_rule3.EDDAutoUpdateRuleServiceProviderSet,
		edd_forecast_task.EDDForecastTaskProviderSet,
		edd_model_conf.EDDModelConfControllerProviderSet,
		algo_model_conf.AlgoModelConfServiceProviderSet,
		algo_model_conf2.AlgoModelConfDAOProviderSet,
		edt_model_conf.EDTModelConfControllerProviderSet,
		scheduled3.ScheduledControllerProviderSet,
		forecast_task.EDDForecastTaskControllerProviderSet,
		edd_forecast_task2.EDDForecastTaskServiceProviderSet,
		edd_forecast_result.EDDForecastResultProviderSet,
		forecast_task_result.EDDForecastTaskResultControllerProviderSet,
		edd_forecast_result2.EDDForecastTaskResultServiceProviderSet,
		edd_delay_queue.EddDelayQueueDebugControllerProviderSet,
		edd_delay_queue2.EddWaybillDelayQueueProviderSet,
		delivery_instruction_conf.DeliveryInstructionConfControllerProviderSet,
		delivery_method.DeliveryMethodServiceProviderSet,
		delivery_conf.DeliveryInstructionBasicConfDAOProviderSet,
		delivery_conf_detail.DeliveryInstructionBasicConfDetailDAOProviderSet,
		delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeDAOProviderSet,
		delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationDAOProviderSet,
		delivery_instruction_whitelist_cep_range2.AdminDeliveryInstructionWhitelistCepRangeProviderSet,
		delivery_instruction_whitelist_cep_range3.DeliveryInstructionWhitelistCepRangeServiceProviderSet,
		delivery_instruction_whitelist_location2.DeliveryInstructionWhitelistLocationProviderSet,
		delivery_instruction_whitelist_location3.DeliveryInstructionWhitelistLocationServiceProviderSet,
		spx_serviceable_area2.SpxServiceableAreaControllerProviderSet,
		spx_serviceable_area_compare2.SpxServiceableAreaCompareServiceProviderSet,
		order_account_mapping3.OrderAccountMappingControllerProviderSet,
		pickup_window.PickupWindowControllerProviderSet,
		pickup_window2.PickupWindowServiceProviderSet,
		cdt_ab_test3.CdtAbTestControllerProviderSet,
		automated_volume2.AutomatedVolumeRuleControllerProviderSet,
		automated_volume3.AutomatedVolumeGenerationRuleServiceProviderSet,
		aggregate_masked_channel_cdt2.AggregateMaskedChannelCdtControllerProviderSet,
		aggregate_masked_channel_cdt3.AggregateMaskedChannelCdtServiceProviderSet,
		sync_item_card_cdt.SyncItemCardCdtProviderSet,
		station.StationControllerProviderSet,
		station4.StationServiceProviderSet,
		match.HomeDeliveryServiceProviderSet,
		hd_distance_repo.HdDistanceDaoProviderSet,
		hd_distance_cache_repo.HdDistanceCacheDaoProviderSet,
		service_point_geo.ServicePointDaoProviderSet,
		map_service.MapServiceProviderSet,
		google_result_repo.GoogleResultDaoProviderSet,
		google_result_cache_repo.GoogleResultCacheDaoProviderSet,
		station_buyer_repo.StationBuyerProviderSet,
		address_revision_repo.AddressRevisionDaoProviderSet,
		hd_station_cache.LogisticStationCacheProviderSet,
		station4.StationVolumeServiceProviderSet,
		station4.StationRedisCounterProviderSet,
		product_serviceable_zone.ProductServiceableZoneControllerProviderSet,
		product_serviceable_zone2.ProductSerivceableCepGroupServiceProviderSet,
		product_serviceable_zone3.ProductServiceableCepGroupDaoProviderSet,
		shop_serviceable_zone.ShopServiceableZoneControllerProviderSet,
		shop_serviceable_zone2.ShopServiceableZoneServiceProviderSet,
		shop_serviceable_zone3.ShopServiceableZoneDaoProviderSet,
		e_fence.EFenceControllerProviderSet,
		e_fence2.EFenceServiceSet,
		polygon.EFencePolygonDaoSet,
		mesh.EFenceMeshDaoSet,
		location_whitelist.EFenceLocationWhiteListDaoSet,
		line_toggle.EFenceLineToggleDaoDaoSet,
		edt_rule.EdtRuleControllerProviderSet,
		edt_rule4.EdtRuleServiceProviderSet,
		edt_rule3.EdtRuleRepoProviderSet,
		edt_rule4.ValidatorManagerProviderSet,
		edt_config.EdtConfigControllerProviderSet,
		edt_config_service.EdtConfigServiceProviderSet,
		edt_config2.EdtConfigDaoProviderSet,
		time_experiment.TimeExperimentControllerProviderSet,
		time_experiment2.TimeExperimentServiceProviderSet,
		time_experiment_repo.TimeExperimentRepoProviderSet,
		card_delivery_address3.CardDeliveryAddressControllerProviderSet,
		common_card_delivery_address.CommonCardDeliveryAddressServiceProviderSet,
		parcel_library3.LogisticParcelLibraryControllerProviderSet,
		geo_distance_controller.LogisticProductGeoDistanceControllerProviderSet,
		geo_distance_service.LogisticProductGeoDistanceServiceProviderSet,
		geo_distance_repo.LogisticProductGeoDistanceConfRepoProviderSet,
		predefined_route_serviceable.LogisticPredefinedRouteControllerProviderSet,
		predefined_route_serviceable2.LogisticLinePredefinedRouteServiceProviderSet,
		predefined_route_model.LogisticLinePredefinedRouteDaoProviderSet,
	)

	return nil
}
