package common

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	scorm "git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/go/scormv2/clause"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"github.com/go-sql-driver/mysql"
)

/*
* @Author: yajun.han
* @Date: 2020/8/13 3:51 下午
* @Name：common
* @Description:
 */

type NullType byte

const (
	_ NullType = iota
	// IsNull the same as `is null`
	IsNull
	// IsNotNull the same as `is not null`
	IsNotNull
)

func GetAllDataBatch(ctx utils.LCOSContext, md interface{}, result interface{}, batchSize int) *lcos_error.LCOSError {
	if reflect.TypeOf(result).Kind() != reflect.Ptr || reflect.TypeOf(result).Elem().Kind() != reflect.Slice {
		logger.LogErrorf("unsupported type of result")
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, "unsupported type of result")
	}
	totalNum := 0
	temResult := reflect.New(reflect.TypeOf(result).Elem()).Interface()
	d := ctx.ReadDB().Model(md).FindInBatches(temResult, int(batchSize), func(tx scorm.SQLCommon, batch int) error {
		totalNum += int(tx.RowsAffected())
		if reflect.TypeOf(result).Elem().Kind() == reflect.Slice && reflect.TypeOf(temResult).Elem().Kind() == reflect.Slice {
			vr := reflect.ValueOf(result).Elem()
			vt := reflect.ValueOf(temResult).Elem()
			if vt.Len() > 0 {
				vr = reflect.AppendSlice(vr, vt)
				reflect.ValueOf(result).Elem().Set(vr)
			}
		} else {
			return errors.New("unsupported type")
		}
		return nil
	})
	if d.GetError() != nil {
		logger.LogErrorf("get all data batch fail, error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	logger.LogInfof("get all data batch succ, data rows:%d", totalNum)
	return nil
}

// GetAllDataBatchWithDB
// for load data from other db
func GetAllDataBatchWithDB(db scorm.SQLCommon, md interface{}, result interface{}, batchSize int) *lcos_error.LCOSError {
	if reflect.TypeOf(result).Kind() != reflect.Ptr || reflect.TypeOf(result).Elem().Kind() != reflect.Slice {
		logger.LogErrorf("unsupported type of result")
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, "unsupported type of result")
	}
	totalNum := 0
	temResult := reflect.New(reflect.TypeOf(result).Elem()).Interface()
	d := db.Model(md).FindInBatches(temResult, int(batchSize), func(tx scorm.SQLCommon, batch int) error {
		totalNum += int(tx.RowsAffected())
		if reflect.TypeOf(result).Elem().Kind() == reflect.Slice && reflect.TypeOf(temResult).Elem().Kind() == reflect.Slice {
			vr := reflect.ValueOf(result).Elem()
			vt := reflect.ValueOf(temResult).Elem()
			if vt.Len() > 0 {
				vr = reflect.AppendSlice(vr, vt)
				reflect.ValueOf(result).Elem().Set(vr)
			}
		} else {
			return errors.New("unsupported type")
		}
		return nil
	})
	if d.GetError() != nil {
		logger.LogErrorf("get all data batch fail, error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	logger.LogInfof("get all data batch succ, data rows:%d", totalNum)
	return nil
}

func HandAllDataBatch(ctx utils.LCOSContext, md interface{}, result interface{}, batchSize int, selectCol []string, handler func(interface{}) error) *lcos_error.LCOSError {
	totalNum := 0
	d := ctx.ReadDB().Select(selectCol).Model(md).FindInBatches(result, int(batchSize), func(tx scorm.SQLCommon, batch int) error {
		totalNum += int(tx.RowsAffected())
		err := handler(result)
		if err != nil {
			return err
		}
		return nil
	})
	if d.GetError() != nil {
		logger.LogErrorf("handle all data batch fail, error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	logger.LogInfof("handle all data batch succ, data rows:%d", totalNum)
	return nil
}

// todo: not be used
func SelectAllDataBatch(ctx utils.LCOSContext, md interface{}, result interface{}, selectCol []string, batchSize int) *lcos_error.LCOSError {
	if reflect.TypeOf(result).Kind() != reflect.Ptr || reflect.TypeOf(result).Elem().Kind() != reflect.Slice {
		logger.LogErrorf("unsupported type of result")
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, "unsupported type of result")
	}
	totalNum := 0
	temResult := reflect.New(reflect.TypeOf(result).Elem()).Interface()
	d := ctx.ReadDB().Select(selectCol).Model(md).FindInBatches(temResult, int(batchSize), func(tx scorm.SQLCommon, batch int) error {
		totalNum += int(tx.RowsAffected())
		if reflect.TypeOf(result).Elem().Kind() == reflect.Slice && reflect.TypeOf(temResult).Elem().Kind() == reflect.Slice {
			vr := reflect.ValueOf(result).Elem()
			vt := reflect.ValueOf(temResult).Elem()
			if vt.Len() > 0 {
				vr = reflect.AppendSlice(vr, vt)
				reflect.ValueOf(result).Elem().Set(vr)
			}
		} else {
			return errors.New("unsupported type")
		}
		return nil
	})
	if d.GetError() != nil {
		logger.LogErrorf("get all data batch fail, error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	logger.LogInfof("get all data batch succ, data rows:%d", totalNum)
	return nil
}

func GetAllDataBatchWithTName(ctx utils.LCOSContext, tableName string, result interface{}, batchSize int) *lcos_error.LCOSError {
	if reflect.TypeOf(result).Kind() != reflect.Ptr || reflect.TypeOf(result).Elem().Kind() != reflect.Slice {
		logger.LogErrorf("unsupported type of result")
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, "unsupported type of result")
	}
	totalNum := 0
	temResult := reflect.New(reflect.TypeOf(result).Elem()).Interface()
	d := ctx.ReadDB().Table(tableName).FindInBatches(temResult, int(batchSize), func(tx scorm.SQLCommon, batch int) error {
		totalNum += int(tx.RowsAffected())
		if reflect.TypeOf(result).Elem().Kind() == reflect.Slice && reflect.TypeOf(temResult).Elem().Kind() == reflect.Slice {
			vr := reflect.ValueOf(result).Elem()
			vt := reflect.ValueOf(temResult).Elem()
			if vt.Len() > 0 {
				vr = reflect.AppendSlice(vr, vt)
				reflect.ValueOf(result).Elem().Set(vr)
			}
		} else {
			return errors.New("unsupported type")
		}
		return nil
	})
	if d.GetError() != nil {
		logger.LogErrorf("get all data batch with table name fail, error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	logger.LogInfof("get all data batch with table name succ, table_name:%s, data rows:%d", tableName, totalNum)
	return nil
}

func GetAllData(ctx utils.LCOSContext, md interface{}, result interface{}) *lcos_error.LCOSError {
	d := ctx.ReadDB().Model(md).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("get all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

/*
和 GetAllData 相比，只增加了按照id逆序的逻辑，其他完全相同
*/
//todo: not be used
func GetAllDataReversed(ctx utils.LCOSContext, md interface{}, result interface{}) *lcos_error.LCOSError {
	d := ctx.ReadDB().Model(md).Order("id desc").Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("get all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// todo: not be used
func GetPartitionData(ctx utils.LCOSContext, tableName string, result interface{}) *lcos_error.LCOSError {
	d := ctx.ReadDB().Table(tableName).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("get partition data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func SearchAllDataByPage(ctx utils.LCOSContext, md interface{}, result interface{}, page uint32, count uint32, searchData map[string]interface{}) (uint32, *lcos_error.LCOSError) {
	query := ctx.ReadDB().Model(md).Where(searchData)

	var total int64
	d := query.Count(&total)

	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	d = query.Offset(int((page - 1) * count)).Limit(int(count)).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data list fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	return uint32(total), nil
}

func SearchAllDataByPageWithComplex(ctx utils.LCOSContext, md interface{}, result interface{}, page uint32, count uint32, searchData map[string]interface{}) (uint32, *lcos_error.LCOSError) {
	cond, valList, _ := whereBuild(searchData)
	query := ctx.ReadDB().Model(md).Where(cond, valList...)
	var total int64
	d := query.Count(&total)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	d = query.Offset(int((page - 1) * count)).Limit(int(count)).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data list fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	return uint32(total), nil
}

// SearchAllDataByPageOrdered 通过order的分页接口
func SearchAllDataByPageOrdered(ctx utils.LCOSContext, md interface{}, result interface{}, page uint32, count uint32, searchData map[string]interface{}, order string) (uint32, *lcos_error.LCOSError) {
	cond, valList, _ := whereBuild(searchData)
	query := ctx.ReadDB().Debug().Model(md).Where(cond, valList...).Order(order)

	var total int64
	d := query.Count(&total)

	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	d = query.Offset(int((page - 1) * count)).Limit(int(count)).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data list fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	return uint32(total), nil
}

// SearchAllDataByPageOrderedForGroup 通过order的分页接口支持group by
func SearchAllDataByPageOrderedForGroup(ctx utils.LCOSContext, md interface{}, result interface{}, page uint32, count uint32, searchData map[string]interface{}, order string, groupName string) (uint32, *lcos_error.LCOSError) {
	cond, valList, _ := whereBuild(searchData)
	query := ctx.ReadDB().Debug().Model(md).Where(cond, valList...).Group(groupName).Order(order)

	var total int64
	d := query.Count(&total)

	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	d = query.Offset(int((page - 1) * count)).Limit(int(count)).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data list fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	return uint32(total), nil
}

/*
按照mtime逆序输出
*/
func SearchAllDataByPageReversed(ctx utils.LCOSContext, md interface{}, result interface{}, page uint32, count uint32, searchData map[string]interface{}) (uint32, *lcos_error.LCOSError) {
	query := ctx.ReadDB().Model(md).Where(searchData)

	var total int64
	d := query.Count(&total)

	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	d = query.Order("mtime DESC").Offset(int((page - 1) * count)).Limit(int(count)).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data list fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	return uint32(total), nil
}

func SearchAllPartitionDataByPage(ctx utils.LCOSContext, tableName string, result interface{}, page uint32, count uint32, searchData map[string]interface{}) (uint32, *lcos_error.LCOSError) {
	query := ctx.ReadDB().Table(tableName).Where(searchData)

	var total int64
	d := query.Count(&total)

	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v, tablename=%s", d.GetError(), tableName)
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	d = query.Offset(int((page - 1) * count)).Limit(int(count)).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data list fail| err=%v, tablename=%s", d.GetError(), tableName)
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	return uint32(total), nil
}

// 通用查询接口
func SearchAllPartitionDataByPageGeneral(ctx utils.LCOSContext, tableName string, result interface{}, page uint32, count uint32, searchData map[string]interface{}, order string) (uint32, *lcos_error.LCOSError) {
	cond, valList, _ := whereBuild(searchData)
	query := ctx.ReadDB().Table(tableName).Where(cond, valList...).Order(order)

	var total int64
	d := query.Count(&total)

	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v, tablename=%s", d.GetError(), tableName)
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	d = query.Offset(int((page - 1) * count)).Limit(int(count)).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data list fail| err=%v, tablename=%s", d.GetError(), tableName)
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	return uint32(total), nil
}

func SearchAllData(ctx utils.LCOSContext, md interface{}, result interface{}, searchData map[string]interface{}) *lcos_error.LCOSError {
	d := ctx.ReadDB().Model(md).Where(searchData).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func SearchAllDataWithDbConn(ctx context.Context, dbConn *scorm.OrmDB, md interface{}, result interface{}, searchData map[string]interface{}) *lcos_error.LCOSError {
	d := dbConn.Model(md).Where(searchData).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func SearchAllDataWithComplex(ctx utils.LCOSContext, md interface{}, result interface{}, searchData map[string]interface{}) *lcos_error.LCOSError {
	cond, valList, _ := whereBuild(searchData)
	d := ctx.ReadDB().Model(md).Where(cond, valList...).Find(result)
	if d.GetError() != nil {
		logger.LogErrorf("search all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// temResult 取地址，results取地址
func SearchAllDataWithBatch(ctx utils.LCOSContext, md interface{}, result interface{}, searchData map[string]interface{}, batchSize int) *lcos_error.LCOSError {
	if reflect.TypeOf(result).Kind() != reflect.Ptr || reflect.TypeOf(result).Elem().Kind() != reflect.Slice {
		logger.LogErrorf("unsupported type of result")
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, "unsupported type of result")
	}
	totalNum := 0
	temResult := reflect.New(reflect.TypeOf(result).Elem()).Interface()
	d := ctx.ReadDB().Model(md).Where(searchData).FindInBatches(temResult, batchSize, func(tx scorm.SQLCommon, batch int) error {
		totalNum += int(tx.RowsAffected())
		if reflect.TypeOf(result).Elem().Kind() == reflect.Slice && reflect.TypeOf(temResult).Elem().Kind() == reflect.Slice {
			vr := reflect.ValueOf(result).Elem()
			vt := reflect.ValueOf(temResult).Elem()
			if vt.Len() > 0 {
				vr = reflect.AppendSlice(vr, vt)
				reflect.ValueOf(result).Elem().Set(vr)
			}
		} else {
			return errors.New("unsupported type")
		}
		return nil
	})
	if d.GetError() != nil {
		logger.LogErrorf("get all data batch fail, error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	logger.LogInfof("get all data batch succ, data rows:%d", totalNum)
	return nil
}

func SearchTableAllDataWithBatch(ctx utils.LCOSContext, tableName string, result interface{}, searchData map[string]interface{}, batchSize int) *lcos_error.LCOSError {
	if reflect.TypeOf(result).Kind() != reflect.Ptr || reflect.TypeOf(result).Elem().Kind() != reflect.Slice {
		logger.LogErrorf("unsupported type of result")
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, "unsupported type of result")
	}
	totalNum := 0
	temResult := reflect.New(reflect.TypeOf(result).Elem()).Interface()
	d := ctx.ReadDB().Table(tableName).Where(searchData).FindInBatches(temResult, batchSize, func(tx scorm.SQLCommon, batch int) error {
		totalNum += int(tx.RowsAffected())
		if reflect.TypeOf(result).Elem().Kind() == reflect.Slice && reflect.TypeOf(temResult).Elem().Kind() == reflect.Slice {
			vr := reflect.ValueOf(result).Elem()
			vt := reflect.ValueOf(temResult).Elem()
			if vt.Len() > 0 {
				vr = reflect.AppendSlice(vr, vt)
				reflect.ValueOf(result).Elem().Set(vr)
			}
		} else {
			return errors.New("unsupported type")
		}
		return nil
	})
	if d.GetError() != nil {
		logger.LogErrorf("get all data batch fail, error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	logger.LogInfof("get all data batch succ, data rows:%d", totalNum)
	return nil
}

func SearchAllPartitionData(ctx utils.LCOSContext, tableName string, result interface{}, searchData map[string]interface{}) *lcos_error.LCOSError {
	d := ctx.ReadDB().Table(tableName).Where(searchData).Find(result)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "SearchAllPartitionData list fail| err=%v", d.GetError())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// 通用的分表搜索，支持各种匹配方式
func SearchAllPartitionDataGeneral(ctx utils.LCOSContext, tableName string, result interface{}, searchData map[string]interface{}) *lcos_error.LCOSError {
	cond, valList, _ := whereBuild(searchData)
	d := ctx.ReadDB().Table(tableName).Where(cond, valList...).Find(result)

	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "SearchAllPartitionData list fail| err=%v", d.GetError())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func GetDataById(ctx utils.LCOSContext, result interface{}, id uint64) *lcos_error.LCOSError {
	d := ctx.ReadDB().Model(result).Where("id = ?", id).Take(result)
	if d.GetError() != nil {
		logger.LogErrorf("get data error:%s, id:%d", d.GetError().Error(), id)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func GetLast(ctx utils.LCOSContext, model interface{}, result interface{}, searchData map[string]interface{}) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(searchData)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.ReadDB().Model(model).Where(cond, valList...).Last(result)
	if d.GetError() != nil {
		if d.GetError() != scorm.ErrRecordNotFound {
			logger.LogErrorf("get data error:%s", d.GetError().Error())
			return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
		} else {
			return lcos_error.NewLCOSError(lcos_error.RecordNotFoundErrorCode, d.GetError().Error())
		}
	}
	return nil
}

func GetDataByUniqKey(ctx utils.LCOSContext, result interface{}, uniqKeyMap map[string]interface{}) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(uniqKeyMap)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.ReadDB().Model(result).Where(cond, valList...).Take(result)
	if d.GetError() != nil {
		logger.LogErrorf("get data error:%s, params:%v", d.GetError().Error(), uniqKeyMap)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func GetPartitionDataById(ctx utils.LCOSContext, tableName string, result interface{}, id uint64) *lcos_error.LCOSError {
	d := ctx.ReadDB().Table(tableName).Where("id = ?", id).Take(result)
	if d.GetError() != nil {
		logger.LogErrorf("get data error:%s, id:%d", d.GetError().Error(), id)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func GetDataByGeneralWithLimit(ctx utils.LCOSContext, tableName string, condition map[string]interface{}, result interface{}, limit int) *lcos_error.LCOSError {
	var fErr error
	if condition == nil || len(condition) == 0 {
		d := ctx.ReadDB().Table(tableName).Limit(limit).Find(result)
		fErr = d.GetError()
	} else {
		cond, valList, err := whereBuild(condition)
		if err != nil {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
		}
		d := ctx.ReadDB().Table(tableName).Where(cond, valList...).Limit(limit).Find(result)
		fErr = d.GetError()
	}

	if fErr != nil {
		errMsg := fmt.Sprintf("cannot get data, error=%s", fErr.Error())
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, errMsg)
	}
	return nil
}

func BatchCreateDataWithLimit(ctx utils.LCOSContext, data interface{}, limit int) *lcos_error.LCOSError {
	value := reflect.ValueOf(data)
	if value.Len() == 0 {
		return nil
	}

	newCtx := scorm.BindContext(ctx, ctx.WriteDB())
	err := scorm.PropagationRequired(newCtx, func(c context.Context) (e error) {
		tx := scorm.Context(c)
		for i := 0; i <= value.Len()/limit; i++ {
			begin := i * limit
			end := begin + limit
			if end > value.Len() {
				end = value.Len()
			}
			if begin >= end {
				continue
			}
			insertDatas := value.Slice(begin, end)
			d := tx.Model(insertDatas.Index(0).Interface()).Create(insertDatas.Interface())
			if d.GetError() != nil {
				logger.LogErrorf("batch insert data failed[%v]", d.GetError())
				return d.GetError()
			}
		}

		return nil
	})

	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	return nil
}

func BatchCreateDataWithTr(ctx utils.LCOSContext, data interface{}, limit int) *lcos_error.LCOSError {
	value := reflect.ValueOf(data)
	if value.Len() == 0 {
		return nil
	}

	newCtx := scorm.BindContext(ctx, ctx.WriteDB())
	err := scorm.PropagationRequired(newCtx, func(c context.Context) (e error) {
		tx := scorm.Context(c)
		d := tx.CreateInBatches(data, limit)
		if d.GetError() != nil {
			logger.LogErrorf("batch insert data failed[%v]", d.GetError())
			return d.GetError()
		}
		return nil
	})
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}

	return nil
}

// BatchCreateCepRangeDataWithTr 单独给cep range使用的接口
func BatchCreateCepRangeDataWithTr(ctx utils.LCOSContext, data interface{}, limit int) *lcos_error.LCOSError {
	value := reflect.ValueOf(data)
	if value.Len() == 0 {
		return nil
	}

	newCtx := scorm.BindContext(ctx, ctx.WriteDB())
	err := scorm.PropagationRequired(newCtx, func(c context.Context) (e error) {
		tx := scorm.Context(c)
		d := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "line_id"}, {Name: "collect_deliver_group_id"}, {Name: "initial"}, {Name: "final"}}, // on duplidate update
			DoUpdates: clause.Assignments(map[string]interface{}{"mtime": scorm.Expr("VALUES(mtime)"), "can_pickup": scorm.Expr("VALUES(can_pickup)"), "can_cod_pickup": scorm.Expr("VALUES(can_cod_pickup)"), "can_deliver": scorm.Expr("VALUES(can_deliver)"), "can_cod_deliver": scorm.Expr("VALUES(can_cod_deliver)")}),
		}).CreateInBatches(data, limit)
		if d.GetError() != nil {
			logger.LogErrorf("batch insert data failed[%v]", d.GetError())
			return d.GetError()
		}
		return nil
	})
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}

	return nil
}

func BatchCreatePartitionDataWithLimit(ctx utils.LCOSContext, tableName string, data interface{}, limit int) *lcos_error.LCOSError {
	value := reflect.ValueOf(data)
	if value.Len() == 0 {
		return nil
	}

	newCtx := scorm.BindContext(ctx, ctx.WriteDB())
	err := scorm.PropagationRequired(newCtx, func(c context.Context) (e error) {
		tx := scorm.Context(c)
		for i := 0; i <= value.Len()/limit; i++ {
			begin := i * limit
			end := begin + limit
			if end > value.Len() {
				end = value.Len()
			}
			insertDatas := value.Slice(begin, end)
			d := tx.Table(tableName).Create(insertDatas.Interface())
			if d.GetError() != nil {
				logger.LogErrorf("batch insert data failed[%v]", d.GetError())
				return d.GetError()
			}
		}
		return nil
	})

	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}

	return nil
}

// BatchCreatePartitionDataForServicePostcodeWithLimit 单独为服务范围postcode上传提供的接口，增加on duplicate update，减少delete数据的时间
func BatchCreatePartitionDataForServicePostcodeWithLimit(ctx utils.LCOSContext, tableName string, data interface{}, limit int) *lcos_error.LCOSError {
	d := ctx.WriteDB().Table(tableName).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "line_id"}, {Name: "postcode"}, {Name: "collect_deliver_group_id"}}, // on duplidate update
		DoUpdates: clause.Assignments(map[string]interface{}{
			"mtime": scorm.Expr("VALUES(mtime)"), "can_pickup": scorm.Expr("VALUES(can_pickup)"),
			"can_cod_pickup": scorm.Expr("VALUES(can_cod_pickup)"), "can_deliver": scorm.Expr("VALUES(can_deliver)"),
			"can_cod_deliver": scorm.Expr("VALUES(can_cod_deliver)"), "support_trade_in": scorm.Expr("VALUES(support_trade_in)")}),
	}).CreateInBatches(data, 4*limit)
	if d.GetError() != nil {
		logger.LogErrorf("batch insert data failed[%v]", d.GetError())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// BatchCreatePartitionDataForServiceLocationWithLimit 单独为服务范围location上传提供的接口，增加on duplicate update，减少delete数据的时间
func BatchCreatePartitionDataForServiceLocationWithLimit(ctx utils.LCOSContext, tableName string, data interface{}, limit int) *lcos_error.LCOSError {
	d := ctx.WriteDB().Table(tableName).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "line_id"}, {Name: "location_id"}, {Name: "collect_deliver_group_id"}}, // on duplidate update
		DoUpdates: clause.Assignments(map[string]interface{}{
			"mtime": scorm.Expr("VALUES(mtime)"), "can_pickup": scorm.Expr("VALUES(can_pickup)"),
			"can_cod_pickup": scorm.Expr("VALUES(can_cod_pickup)"), "can_deliver": scorm.Expr("VALUES(can_deliver)"),
			"can_cod_deliver": scorm.Expr("VALUES(can_cod_deliver)"), "state": scorm.Expr("VALUES(state)"),
			"city": scorm.Expr("VALUES(city)"), "district": scorm.Expr("VALUES(district)"), "street": scorm.Expr("VALUES(street)"),
			"support_trade_in": scorm.Expr("VALUES(support_trade_in)")}),
	}).CreateInBatches(data, 4*limit)
	if d.GetError() != nil {
		logger.LogErrorf("batch insert data failed[%v]", d.GetError())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func CreateData(ctx utils.LCOSContext, data interface{}) *lcos_error.LCOSError {
	d := ctx.WriteDB().Model(data).Create(data)
	if d.GetError() != nil {
		logger.LogErrorf("create data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func CreateOrUpdateData(ctx utils.LCOSContext, data interface{}) *lcos_error.LCOSError {
	err := ctx.WriteDB().Model(data).Clauses(clause.OnConflict{
		UpdateAll: true,
	}).Create(data).GetError()
	if err != nil {
		logger.CtxLogErrorf(ctx, "create data error:%s", err.Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	return nil
}

func CreateDataAndCheckDuplicate(ctx utils.LCOSContext, data interface{}) (bool, *lcos_error.LCOSError) {
	err := ctx.WriteDB().Model(data).Create(data).GetError()
	if err != nil {
		duplicate := false
		mysqlErr := &mysql.MySQLError{}
		if errors.As(err, &mysqlErr) {
			duplicate = mysqlErr.Number == constant.MySqlDuplicateErrorCode // MySQL错误码，破坏唯一约束
		}
		return duplicate, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	return false, nil
}

// 根据传入的batchSize，批量创建数据
func BatchCreateData(ctx utils.LCOSContext, data interface{}, batchSize int) *lcos_error.LCOSError {
	d := ctx.WriteDB().Model(data).CreateInBatches(data, batchSize)
	if d.GetError() != nil {
		logger.LogErrorf("create data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// 分批创建或更新数据（如果唯一键冲突则更新）
func BatchCreateOrUpdateData(ctx utils.LCOSContext, data interface{}, batchSize int) *lcos_error.LCOSError {
	err := ctx.WriteDB().Clauses(clause.OnConflict{UpdateAll: true}).CreateInBatches(data, batchSize).GetError()
	if err != nil {
		logger.CtxLogErrorf(ctx, "create or update data error: %s", err.Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	return nil
}

func BatchCreateDataAndCheckDuplicate(ctx utils.LCOSContext, data interface{}, batchSize int) (bool, *lcos_error.LCOSError) {
	err := ctx.WriteDB().Model(data).CreateInBatches(data, batchSize).GetError()
	if err != nil {
		duplicate := false
		mysqlErr := &mysql.MySQLError{}
		if errors.As(err, &mysqlErr) {
			duplicate = mysqlErr.Number == constant.MySqlDuplicateErrorCode // MySQL错误码，破坏唯一约束
		}
		return duplicate, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	return false, nil
}

// 分表需要指定tablename
func CreatePartitionData(ctx utils.LCOSContext, tableName string, data interface{}) *lcos_error.LCOSError {
	d := ctx.WriteDB().Table(tableName).Create(data)
	if d.GetError() != nil {
		logger.LogErrorf("create %s data error:%s", tableName, d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// 分表需要指定tablename，批量创建
func BatchCreatePartitionData(ctx utils.LCOSContext, tableName string, data interface{}, batchSize int) *lcos_error.LCOSError {
	d := ctx.WriteDB().Table(tableName).CreateInBatches(data, batchSize)
	if d.GetError() != nil {
		logger.LogErrorf("create %s data error:%s", tableName, d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

/*
如果传入的结构体中没有主键id，则创建对应数据，否则更新
*/
func UpdateOrCreateData(ctx utils.LCOSContext, data interface{}) *lcos_error.LCOSError {
	d := ctx.WriteDB().Model(data).Save(data)
	if d.GetError() != nil {
		logger.LogErrorf("update data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

/*
如果传入的结构体中没有主键id，会报错，否则更新数据
*/
func UpdateData(ctx utils.LCOSContext, data interface{}) *lcos_error.LCOSError {
	d := ctx.WriteDB().Model(data).Updates(data)
	if d.GetError() != nil {
		logger.LogErrorf("update data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func UpdateDataById(ctx utils.LCOSContext, md interface{}, id uint64, updatedData map[string]interface{}) *lcos_error.LCOSError {
	d := ctx.WriteDB().Model(md).Where("id = ?", id).Updates(updatedData)
	if d.GetError() != nil {
		logger.LogErrorf("update data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// UpdateDataFieldsById 基于主键更新所有选定字段。如果updateFields是结构体，则更新除了id的所有字段
func UpdateDataFieldsById(ctx utils.LCOSContext, md interface{}, id uint64, updateFields interface{}) *lcos_error.LCOSError {
	d := ctx.WriteDB().Model(md).Select("*").Omit("id").Where("id = ?", id).Updates(updateFields)
	if d.GetError() != nil {
		logger.LogErrorf("update data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func UpdateDataByParams(ctx utils.LCOSContext, md interface{}, queryParams map[string]interface{}, updatedData map[string]interface{}) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(queryParams)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.WriteDB().Model(md).Where(cond, valList...).Updates(updatedData)
	if d.GetError() != nil {
		logger.LogErrorf("update data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// Notice: 如果updatedData使用的是结构体，则会更新除了id以外的所有字段（包含零值）
func UpdateDataByParamsAndCheckAffected(ctx utils.LCOSContext, md interface{}, queryParams map[string]interface{}, updatedData interface{}) (bool, *lcos_error.LCOSError) {
	cond, valList, err := whereBuild(queryParams)
	if err != nil {
		return false, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.WriteDB().Model(md).Select("*").Omit("id").Where(cond, valList...).Updates(updatedData)
	if d.GetError() != nil {
		mysqlErr := &mysql.MySQLError{}
		duplicate := errors.As(d.GetError(), &mysqlErr) && mysqlErr.Number == constant.MySqlDuplicateErrorCode
		logger.LogErrorf("update data error:%s", d.GetError().Error())
		return duplicate, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	if d.RowsAffected() == 0 {
		logger.LogErrorf("update data error:no rows affected")
		return false, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, "no rows affected")
	}
	return false, nil
}

func UpdateDataWithMap(ctx utils.LCOSContext, model interface{}, date map[string]interface{}) *lcos_error.LCOSError {
	d := ctx.WriteDB().Model(model).Updates(date)
	if d.GetError() != nil {
		logger.LogErrorf("update data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	if d.RowsAffected() == 0 {
		return lcos_error.NewLCOSError(lcos_error.IdNotExit, "update failed,id not found")
	}
	return nil
}

func UpdatePartitionData(ctx utils.LCOSContext, tableName string, data interface{}) *lcos_error.LCOSError {
	d := ctx.WriteDB().Table(tableName).Save(data)
	if d.GetError() != nil {
		logger.LogErrorf("update %s data error:%s", tableName, d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func DeleteDataById(ctx utils.LCOSContext, md interface{}, id uint64) *lcos_error.LCOSError {
	d := ctx.WriteDB().Model(md).Where("id = ?", id).Delete(md)
	if d.GetError() != nil {
		logger.LogErrorf("delete partition data by id error:%s, id:%d", d.GetError().Error(), id)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func DeletePartitionDataById(ctx utils.LCOSContext, md interface{}, tableName string, id uint64) *lcos_error.LCOSError {
	d := ctx.WriteDB().Table(tableName).Where("id = ?", id).Delete(md)
	if d.GetError() != nil {
		logger.LogErrorf("delete partition data by id error:%s, id:%d", d.GetError().Error(), id)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func DeletePartitionDataByParams(ctx utils.LCOSContext, md interface{}, tableName string, query map[string]interface{}) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(query)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.WriteDB().Table(tableName).Where(cond, valList...).Delete(md)
	if d.GetError() != nil {
		logger.LogErrorf("delete partition data by query error:%s, query:%v", d.GetError().Error(), query)
		return lcos_error.NewLCOSError(lcos_error.DBWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func DeleteDataByParams(ctx utils.LCOSContext, md interface{}, query map[string]interface{}) *lcos_error.LCOSError {
	if _, err := DeleteDataByParamsWithLimit(ctx, md, query, 0); err != nil {
		return err
	}
	return nil
}

func DeleteDataByParamsWithLimit(ctx utils.LCOSContext, md interface{}, query map[string]interface{}, limit int) (int64, *lcos_error.LCOSError) {
	cond, valList, err := whereBuild(query)
	if err != nil {
		return 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.WriteDB().Model(md).Where(cond, valList...)
	if limit == 0 {
		d = d.Delete(md)
	} else {
		d = d.Limit(limit).Delete(md)
	}
	if d.GetError() != nil {
		logger.LogErrorf("delete data by query error:%s, query:%v", d.GetError().Error(), query)
		return 0, lcos_error.NewLCOSError(lcos_error.DBWriteErrorCode, d.GetError().Error())
	}
	return d.RowsAffected(), nil
}

func DeletePartitionDataByParamsWithTableName(ctx utils.LCOSContext, tableName string, md interface{}, query map[string]interface{}) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(query)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.WriteDB().Table(tableName).Where(cond, valList...).Delete(md)
	if d.GetError() != nil {
		logger.LogErrorf("delete data by query error:%s, query:%v", d.GetError().Error(), query)
		return lcos_error.NewLCOSError(lcos_error.DBWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func DeleteDataByMap(ctx utils.LCOSContext, condition map[string]interface{}, md interface{}) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(condition)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.WriteDB().Model(md).Where(cond, valList...).Delete(md)
	if d.GetError() != nil {
		logger.LogErrorf("delete data by condition error:%s, condition:%d", d.GetError().Error(), md)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func DeleteDataByMapGeneral(ctx utils.LCOSContext, tableName string, condition map[string]interface{}, md interface{}) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(condition)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.WriteDB().Table(tableName).Where(cond, valList...).Delete(md)
	if d.GetError() != nil {
		logger.LogErrorf("delete data by condition error:%s, condition:%d", d.GetError().Error(), md)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func DeleteDataByMapGeneralWithLimit(ctx utils.LCOSContext, tableName string, condition map[string]interface{}, md interface{}, limit int) (int64, *lcos_error.LCOSError) {
	cond, valList, err := whereBuild(condition)
	if err != nil {
		return 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	// use dry run to generate raw sql
	dryRunGenerator := ctx.WriteDB().Session(&scorm.Session{DryRun: true}).Table(tableName).Where(cond, valList...).Delete(md)
	sql := dryRunGenerator.GetStatement().SQL.String() + fmt.Sprintf(" LIMIT %d", limit)

	vars := dryRunGenerator.GetStatement().Vars
	d := ctx.WriteDB().Exec(sql, vars...)
	if d.GetError() != nil {
		errMsg := fmt.Sprintf("cannot delete data, error=%s", d.GetError().Error())
		logger.CtxLogErrorf(ctx, errMsg)
		return 0, lcos_error.NewLCOSError(lcos_error.DBWriteErrorCode, errMsg)
	}
	rowsAffected := d.RowsAffected()
	logger.CtxLogInfof(ctx, "delete table:%s,execute sql:%s, rows affected:%v", tableName, sql, rowsAffected)

	return rowsAffected, nil
}

// Notice: 如果deleteData使用的是结构体，则会更新除了id以外的所有字段（包含零值）
func DeleteDataByParamsAndCheckAffected(ctx utils.LCOSContext, md interface{}, queryParams map[string]interface{}) (bool, *lcos_error.LCOSError) {
	cond, valList, err := whereBuild(queryParams)
	if err != nil {
		return false, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.WriteDB().Model(md).Where(cond, valList...).Delete(md)
	if d.GetError() != nil {
		mysqlErr := &mysql.MySQLError{}
		duplicate := errors.As(d.GetError(), &mysqlErr) && mysqlErr.Number == constant.MySqlDuplicateErrorCode
		logger.LogErrorf("delete data error:%s", d.GetError().Error())
		return duplicate, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	if d.RowsAffected() == 0 {
		logger.LogErrorf("delete data error:no rows affected")
		return false, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, "no rows affected")
	}
	return false, nil
}

func SearchDistinctColumnsByParams(ctx utils.LCOSContext, md interface{}, result interface{}, searchData map[string]interface{}, columns ...string) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(searchData)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	if err = ctx.ReadDB().Model(md).Where(cond, valList...).Distinct().Select(columns).Find(result).GetError(); err != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	return nil
}

// SearchAllDataGeneral 通用的查询方法，reversed为true将按照mtime逆序返回
func SearchAllDataGeneral(ctx utils.LCOSContext, md interface{}, result interface{}, searchData map[string]interface{}, reversed bool) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(searchData)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	var d scorm.SQLCommon
	if reversed {
		d = ctx.ReadDB().Model(md).Where(cond, valList...).Order("mtime DESC").Find(result)
	} else {
		d = ctx.ReadDB().Model(md).Where(cond, valList...).Find(result)
	}

	if d.GetError() != nil {
		logger.LogErrorf("search all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// SearchAllDataInBatchesGeneral 通用的分批查询方法
func SearchAllDataInBatchesGeneral(ctx utils.LCOSContext, md interface{}, dest interface{}, searchData map[string]interface{}, batchSize int, fn func(tx scorm.SQLCommon, batch int) error) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(searchData)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	if err = ctx.ReadDB().Model(md).Where(cond, valList...).FindInBatches(dest, batchSize, fn).GetError(); err != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	return nil
}

// CheckQueryExist check whether query exist
func CheckQueryExist(ctx utils.LCOSContext, tableName string, condition map[string]interface{}, md interface{}) (bool, *lcos_error.LCOSError) {
	cond, valList, err := whereBuild(condition)
	if err != nil {
		return false, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.ReadDB().Table(tableName).Where(cond, valList...).First(md)
	if d.GetError() != nil {
		if d.GetError() == scorm.ErrRecordNotFound {
			return false, nil
		} else {
			return false, lcos_error.NewLCOSError(lcos_error.DBReadErrorCode, d.GetError().Error())
		}
	}
	return true, nil
}

// SearchAllDataGeneralOrdered 通用的查询方法，增加自定义排序的功能
// order -> "mtime DESC" 按mtime逆序
func SearchAllDataGeneralOrdered(ctx utils.LCOSContext, md interface{}, result interface{}, searchData map[string]interface{}, order string) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(searchData)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	var d scorm.SQLCommon
	if order == "" {
		d = ctx.ReadDB().Model(md).Where(cond, valList...).Find(result)
	} else {
		d = ctx.ReadDB().Model(md).Where(cond, valList...).Order(order).Find(result)
	}

	if d.GetError() != nil {
		logger.LogErrorf("search all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// CountByCondition 通用的查询方法，仅查询出符合条件的行数量
func CountByCondition(ctx utils.LCOSContext, md interface{}, searchData map[string]interface{}) (int64, *lcos_error.LCOSError) {
	query := ctx.ReadDB().Model(md).Where(searchData)

	var total int64
	d := query.Count(&total)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v", d.GetError())
		return 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return total, nil
}

// SearchAllSlsDataGeneralOrdered 通用的查询方法，增加自定义排序的功能
// order -> "mtime DESC" 按mtime逆序
// todo: not be used
func SearchAllSlsDataGeneralOrdered(ctx utils.LCOSContext, region string, md interface{}, result interface{}, searchData map[string]interface{}, order string) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(searchData)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	var d scorm.SQLCommon
	if order == "" {
		d = ctx.ReadSlsDB(region).Model(md).Where(cond, valList...).Find(result)
	} else {
		d = ctx.ReadSlsDB(region).Model(md).Where(cond, valList...).Order(order).Find(result)
	}

	if d.GetError() != nil {
		logger.LogErrorf("search all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// SelectDistinctKeyList 根据条件选择distinct的列
// todo: not be used
func SelectDistinctKeyList(ctx utils.LCOSContext, model interface{}, results interface{}, uniqueKey string, searchData map[string]interface{}) *lcos_error.LCOSError {
	cond, valList, err := whereBuild(searchData)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	d := ctx.ReadDB().Model(model).Where(cond, valList...).Distinct().Pluck(uniqueKey, results)

	if d.GetError() != nil {
		logger.LogErrorf("search all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

// SearchAllBranchDataGeneral 通用的查询方法，reversed为true将按照mtime逆序返回
func SearchAllBranchDataGeneral(ctx utils.LCOSContext, region string, md interface{}, result interface{}, searchData map[string]interface{}, reversed bool) *lcos_error.LCOSError {
	dbConn, dbErr := ctx.ReadBranchDB(region)
	if dbErr != nil {
		logger.CtxLogErrorf(ctx, "Get ReadBranchDB db conn fail, region: %s, err: %s", region, dbErr.Msg)
		return dbErr
	}

	cond, valList, err := whereBuild(searchData)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	var d scorm.SQLCommon
	if reversed {
		d = dbConn.Model(md).Where(cond, valList...).Order("mtime DESC").Find(result)
	} else {
		d = dbConn.Model(md).Where(cond, valList...).Find(result)
	}

	if d.GetError() != nil {
		logger.LogErrorf("search all data error:%s", d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func singleWhereBuild(k string, v interface{}) (whereSQL string, val interface{}, err error) {
	ks := strings.Split(k, " ")
	if len(ks) > 3 {
		return "", nil, fmt.Errorf("Error in query condition: %s. ", k)
	}
	switch len(ks) {
	case 1:
		//fmt.Println(reflect.TypeOf(v))
		switch v := v.(type) {
		case NullType:
			if v == IsNotNull {
				whereSQL += fmt.Sprint(k, " IS NOT NULL")
			} else {
				whereSQL += fmt.Sprint(k, " IS NULL")
			}
		default:
			whereSQL += fmt.Sprint(k, "=?")
		}
	case 2:
		k = ks[0]
		switch ks[1] {
		case "=":
			whereSQL += fmt.Sprint(k, "=?")
		case ">":
			whereSQL += fmt.Sprint(k, ">?")
		case ">=":
			whereSQL += fmt.Sprint(k, ">=?")
		case "<":
			whereSQL += fmt.Sprint(k, "<?")
		case "<=":
			whereSQL += fmt.Sprint(k, "<=?")
		case "!=":
			whereSQL += fmt.Sprint(k, "!=?")
		case "<>":
			whereSQL += fmt.Sprint(k, "!=?")
		case "in":
			whereSQL += fmt.Sprint(k, " in (?)")
		case "like":
			whereSQL += fmt.Sprint(k, " like ?")
		}
	case 3:
		k = ks[0]
		expression := fmt.Sprintf("%s %s", ks[1], ks[2])
		switch expression {
		case "not in":
			whereSQL += fmt.Sprint(k, " not in (?)")
		}
	}
	return whereSQL, v, nil
}

// sql build where
func whereBuild(where map[string]interface{}) (string, []interface{}, error) {
	var whereSQL string
	var valList []interface{}
	for k, v := range where {
		if whereSQL != "" {
			whereSQL += " AND "
		}
		// 对于OR逻辑的兼容
		if k == "_OR_" {
			tmpWhereSql := ""
			if orQuery, ok := v.(map[string]interface{}); ok {
				for orK, orV := range orQuery {
					if tmpWhereSql != "" {
						tmpWhereSql += " OR "
					}
					singleWhereSql, singleVal, err := singleWhereBuild(orK, orV)
					if err != nil {
						return "", nil, err
					}
					tmpWhereSql += singleWhereSql
					valList = append(valList, singleVal)
				}
			}
			tmpWhereSql = "(" + tmpWhereSql + ")"
			whereSQL += tmpWhereSql
		} else {
			tmpWhereSql, tmpVal, err := singleWhereBuild(k, v)
			if err != nil {
				return "", nil, err
			}
			whereSQL += tmpWhereSql
			valList = append(valList, tmpVal)
		}
	}
	return whereSQL, valList, nil
}

func BuildSql(where map[string]interface{}) (string, []interface{}, error) {
	return whereBuild(where)
}
