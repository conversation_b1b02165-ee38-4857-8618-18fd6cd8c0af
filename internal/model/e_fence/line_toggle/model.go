package line_toggle

type EFenceLineToggleTab struct {
	Id               int64  `gorm:"column:id;primary_key" json:"id"`
	LineId           string `gorm:"column:line_id" json:"line_id"`
	LineName         string `gorm:"column:line_name" json:"line_name"`
	SupportLmHubZone int32  `gorm:"column:support_lm_hub_zone" json:"support_lm_hub_zone"`
	CheckWith        int32  `gorm:"column:check_with" json:"check_with"`
	Operator         string `gorm:"column:operator" json:"operator"`
	Region           string `gorm:"column:region" json:"region"`

	LayerId string `gorm:"column:layer_id" json:"layer_id"`

	Ctime int64 `gorm:"column:ctime" json:"ctime"`
	Mtime int64 `gorm:"column:mtime" json:"mtime"`
}

func (e *EFenceLineToggleTab) TableName() string {
	return EFenceLineToggleTabName()
}

func EFenceLineToggleTabName() string {
	return "e_fence_line_toggle_tab"
}

func GetLineToggleUniqKey(lineId string) string {
	return lineId
}
