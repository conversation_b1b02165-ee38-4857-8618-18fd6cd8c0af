package line_toggle

import (
	"strings"

	"git.garena.com/shopee/bg-logistics/go/scormv2/clause"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

type EFenceLineToggleDao interface {
	ListLineToggle(ctx utils.LCOSContext, queryMap map[string]interface{}, page int, count int) ([]*EFenceLineToggleTab, int64, *lcos_error.LCOSError)
	InsertOrUpdateLineToggle(ctx utils.LCOSContext, data *EFenceLineToggleTab) *lcos_error.LCOSError
	DeleteLineToggle(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError
	GetLineConfFromCache(ctx utils.LCOSContext, lineId string) (*EFenceLineToggleTab, *lcos_error.LCOSError)
}

type eFenceLineToggleDao struct {
}

func NewEFenceLineToggleDao() *eFenceLineToggleDao {
	return &eFenceLineToggleDao{}
}

func (e *eFenceLineToggleDao) ListLineToggle(ctx utils.LCOSContext, queryMap map[string]interface{}, page int, count int) ([]*EFenceLineToggleTab, int64, *lcos_error.LCOSError) {
	query := ctx.ReadDB().Table(EFenceLineToggleTabName()).Where(queryMap)

	var total int64
	var result []*EFenceLineToggleTab
	d := query.Count(&total)
	if d.GetError() != nil {
		return nil, 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	query = query.Offset((page - 1) * count).Limit(count).Order("id DESC").Find(&result)
	if query.GetError() != nil {
		return nil, 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, query.GetError().Error())
	}
	return result, total, nil
}

func (e *eFenceLineToggleDao) InsertOrUpdateLineToggle(ctx utils.LCOSContext, data *EFenceLineToggleTab) *lcos_error.LCOSError {
	db := ctx.WriteDB().Table(data.TableName())
	db = db.Clauses(
		clause.OnConflict{
			Columns:   []clause.Column{{Name: "line_id"}, {Name: "region"}},
			DoUpdates: clause.AssignmentColumns([]string{"support_lm_hub_zone", "check_with", "operator", "mtime", "layer_id"}),
		}).Create(data)

	if db.GetError() != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return nil
}

func (e *eFenceLineToggleDao) DeleteLineToggle(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError {
	db := ctx.WriteDB().Table(EFenceLineToggleTabName()).Where("line_id = ? and region = ?", lineId, strings.ToUpper(ctx.GetCountry()))
	db = db.Delete(&EFenceLineToggleTab{})
	if db.GetError() != nil {
		lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}
	return nil
}

func (e *eFenceLineToggleDao) GetLineConfFromCache(ctx utils.LCOSContext, lineId string) (*EFenceLineToggleTab, *lcos_error.LCOSError) {
	queryExec := localcache.NewLocalCacheQueryExecutor()
	operationConfData, err := queryExec.Find(ctx, constant.EFenceLineToggleNamespace, GetLineToggleUniqKey(lineId))
	if err == localcache.ErrKeyNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	data := operationConfData.(*EFenceLineToggleTab)
	return data, nil
}
