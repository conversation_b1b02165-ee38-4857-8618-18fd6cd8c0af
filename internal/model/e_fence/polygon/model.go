package polygon

import (
	"fmt"
	eFenceConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/e_fence"
	"strings"
)

const (
	EFencePolygonTabNamePrefix = "e_fence_polygon_tab"
)

func GetEFencePolygonTableName(region string) string {
	if !eFenceConstant.IsValidRegion(region) {
		region = "SG" // 防止SQL注入，对region进行白名单过滤
	}
	return fmt.Sprintf("%s_%s", EFencePolygonTabNamePrefix, strings.ToLower(region))
}

// 这个数据量不算大，仍然按市场分表，原因是这个表的数据要与mesh表数据在一个事务中更新，锁表时间相对长，如果各个市场都公用一个表，则可能会有较多的锁等待情况；同时也为了电子围栏将来可能的市场拆分减少开发量
type EFencePolygonTab struct {
	ID           uint64 `gorm:"column:id;primary_key" json:"id"`
	Region       string `gorm:"column:region_code" json:"region_code"`
	ZoneId       string `gorm:"column:zone_id" json:"zone_id"`
	ZoneName     string `gorm:"column:zone_name" json:"zone_name"`
	StationId    string `gorm:"column:station_id" json:"station_id"`
	StationName  string `gorm:"column:station_name" json:"station_name"`
	StationType  int32  `gorm:"column:station_type" json:"station_type"`
	Geometry     string `gorm:"column:geo_metry" json:"geo_metry"`
	ZoneArea     string `gorm:"column:zone_area" json:"zone_area"`
	Operator     string `gorm:"column:operator" json:"operator"`
	DataVersion  string `gorm:"column:data_version" json:"data_version"`
	HandleStatus int32  `gorm:"column:handle_status" json:"handle_status"`

	LayerId   string `gorm:"column:layer_id" json:"layer_id"`
	LayerName string `gorm:"column:layer_name" json:"layer_name"`

	CTime uint32 `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	MTime uint32 `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

func (t *EFencePolygonTab) TableName() string {
	return GetEFencePolygonTableName(t.Region)
}

// 用于批量更新数据的结构体
type UpdateZoneFields struct {
	ZoneID       string `json:"zone_id"`
	DataVersion  string `json:"data_version"`
	Operator     string `json:"operator"`
	HandleStatus int32  `json:"handle_status"`
}
