package polygon

import (
	"fmt"
	scorm "git.garena.com/shopee/bg-logistics/go/scormv2"
	eFenceConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/e_fence"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2/clause"
	"git.garena.com/shopee/bg-logistics/logistics/geopolygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
)

type EFencePolygonDao interface {
	// 前端分页查询，返回正在生效的多边形信息
	ListEFencePolygonByPage(ctx utils.LCOSContext, region string, queryMap map[string]interface{}, page uint32, count uint32) ([]*EFencePolygonTab, uint32, *lcos_error.LCOSError)
	// 一次性查询所有符合条件的数据
	ListAllEFencePolygon(ctx utils.LCOSContext, region string, queryMap map[string]interface{}) ([]*EFencePolygonTab, *lcos_error.LCOSError)
	// 写入一条多边形信息，如果zone_id+data_version 重复，则更新mtime
	BatchInsertOrUpdateEFencePolygon(ctx utils.LCOSContext, region string, polygonList []*EFencePolygonTab) *lcos_error.LCOSError
	// 查询一组指定的zone_id+data_version数据
	GetEFencePolygonsByZoneIdAndVersion(ctx utils.LCOSContext, region string, zoneQueryCondition []*schema.EFenceZoneInfo) ([]*EFencePolygonTab, *lcos_error.LCOSError)
	GetEFencePolygonsByVersion(ctx utils.LCOSContext, region string, version []string) ([]*EFencePolygonTab, *lcos_error.LCOSError)
	GetOneEFencePolygonByZoneIdAndVersion(ctx utils.LCOSContext, region string, zoneId string, version string) (*EFencePolygonTab, *lcos_error.LCOSError)
	UpdateEFencePolygonsByVersion(ctx utils.LCOSContext, region string, version string, handleStatus int32, operator string) *lcos_error.LCOSError
	GetPolygonInfoFromCache(ctx utils.LCOSContext, region, zoneId, version, layerId string) (*geopolygon.PolyDetail, bool, *lcos_error.LCOSError)
	ListAllEFencePolygonByLimit(ctx utils.LCOSContext, region string, queryMap map[string]interface{}, limit int) ([]*EFencePolygonTab, *lcos_error.LCOSError)

	GetEFencePolygonsByZoneIdLayerIdAndVersion(ctx utils.LCOSContext, region string, zoneQueryCondition []*schema.EFenceZoneInfo) ([]*EFencePolygonTab, *lcos_error.LCOSError)
	GetEFencePolygonsByVersionAndLayer(ctx utils.LCOSContext, region string, zoneQueryCondition []*schema.VersionItem) ([]*EFencePolygonTab, *lcos_error.LCOSError)
	GetOneEFencePolygonByZoneIdLayerIdAndVersion(ctx utils.LCOSContext, region string, zoneId string, version, layerId string) (*EFencePolygonTab, *lcos_error.LCOSError)
	UpdateEFencePolygonsByVersionAndLayerId(ctx utils.LCOSContext, region string, version string, handleStatus int32, operator string) *lcos_error.LCOSError
	UpdatePolygonStatus(ctx utils.LCOSContext, region, zoneId, version, layerId string, status int32) *lcos_error.LCOSError
	GetAllLayer(ctx utils.LCOSContext, region string) ([]*EFencePolygonTab, *lcos_error.LCOSError)
	GetLayerNameByLayerId(ctx utils.LCOSContext, region string, layerId string) (*EFencePolygonTab, *lcos_error.LCOSError)
}

type eFencePolygonDao struct {
}

func NewEFencePolygonDao() *eFencePolygonDao {
	return &eFencePolygonDao{}
}

func EFencePolyKey(region string, zoneId string, version string, layerId string) string {
	return fmt.Sprintf("%s%s%s_%s", strings.ToLower(region), zoneId, version, layerId)
}

func (e *eFencePolygonDao) ListEFencePolygonByPage(ctx utils.LCOSContext, region string, queryMap map[string]interface{}, page uint32, count uint32) ([]*EFencePolygonTab, uint32, *lcos_error.LCOSError) {
	res := []*EFencePolygonTab{}
	tableName := GetEFencePolygonTableName(region)
	total, err := common.SearchAllPartitionDataByPage(ctx, tableName, &res, page, count, queryMap)

	return res, total, err
}

func (e *eFencePolygonDao) ListAllEFencePolygon(ctx utils.LCOSContext, region string, queryMap map[string]interface{}) ([]*EFencePolygonTab, *lcos_error.LCOSError) {
	res := []*EFencePolygonTab{}
	tableName := GetEFencePolygonTableName(region)
	// 当前应用场景中不需要查询 geometry，这个字段太长了，最好是跳过
	selectCol := []string{"id", "region_code", "zone_id", "zone_name", "station_id", "station_name", "station_type", "operator", "data_version", "handle_status", "ctime", "mtime", "layer_id", "layer_name"}

	db := ctx.ReadDB().Table(tableName).Select(selectCol).Where(queryMap).Find(&res)
	if db.GetError() != nil {
		logger.CtxLogErrorf(ctx, "ListAllEFencePolygon list fail| err=%v", db.GetError())
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return res, nil
}

func (e *eFencePolygonDao) ListAllEFencePolygonByLimit(ctx utils.LCOSContext, region string, queryMap map[string]interface{}, limit int) ([]*EFencePolygonTab, *lcos_error.LCOSError) {
	res := []*EFencePolygonTab{}
	tableName := GetEFencePolygonTableName(region)
	selectCol := []string{"id", "region_code", "zone_id", "zone_name", "station_id", "station_name", "station_type", "operator", "data_version", "handle_status", "ctime", "mtime", "layer_id", "layer_name"}

	db := ctx.ReadDB().Table(tableName).Select(selectCol).Where(queryMap).Limit(limit).Find(&res)
	if db.GetError() != nil {
		logger.CtxLogErrorf(ctx, "ListAllEFencePolygon list fail| err=%v", db.GetError())
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return res, nil
}

func (e *eFencePolygonDao) BatchInsertOrUpdateEFencePolygon(ctx utils.LCOSContext, region string, polygonList []*EFencePolygonTab) *lcos_error.LCOSError {
	tableName := GetEFencePolygonTableName(region)
	batchSize := 10 // Geometry列的值比较大，因此控制一下写入批量

	// 如果数据已经存在，则仅更新mtime
	db := ctx.WriteDB().Table(tableName).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "zone_id"}, {Name: "data_version"}, {Name: "layer_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"mtime"}),
	}).CreateInBatches(polygonList, batchSize)

	if db.GetError() != nil {
		logger.LogErrorf("create %s data error:%s", tableName, db.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return nil
}

func (e *eFencePolygonDao) GetEFencePolygonsByZoneIdAndVersion(ctx utils.LCOSContext, region string, zoneQueryCondition []*schema.EFenceZoneInfo) ([]*EFencePolygonTab, *lcos_error.LCOSError) {
	var polygons []*EFencePolygonTab

	// 构建查询条件
	var conditions []interface{}
	for _, zoneData := range zoneQueryCondition {
		if zoneData == nil {
			continue
		}
		condition := []interface{}{zoneData.ZoneId, zoneData.DataVersion}
		conditions = append(conditions, condition)
	}

	// 执行查询
	tableName := GetEFencePolygonTableName(region)
	db := ctx.WriteDB().Table(tableName)
	db = db.Where("(zone_id, data_version) IN (?)", conditions).Find(&polygons)
	if db.GetError() != nil {
		logger.LogErrorf("can not find %s data error:%s|condition[%+v]", tableName, db.GetError().Error(), zoneQueryCondition)
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return polygons, nil
}

func (e *eFencePolygonDao) GetEFencePolygonsByZoneIdLayerIdAndVersion(ctx utils.LCOSContext, region string, zoneQueryCondition []*schema.EFenceZoneInfo) ([]*EFencePolygonTab, *lcos_error.LCOSError) {
	var polygons []*EFencePolygonTab

	// 构建查询条件
	var conditions []interface{}
	for _, zoneData := range zoneQueryCondition {
		if zoneData == nil {
			continue
		}
		condition := []interface{}{zoneData.ZoneId, zoneData.DataVersion, zoneData.LayerId}
		conditions = append(conditions, condition)
	}

	// 执行查询
	tableName := GetEFencePolygonTableName(region)
	db := ctx.ReadDB().Table(tableName)
	db = db.Where("(zone_id, data_version, layer_id) IN (?)", conditions).Find(&polygons)
	if db.GetError() != nil {
		logger.LogErrorf("can not find %s data error:%s|condition[%+v]", tableName, db.GetError().Error(), zoneQueryCondition)
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return polygons, nil
}

func (e *eFencePolygonDao) GetEFencePolygonsByVersion(ctx utils.LCOSContext, region string, version []string) ([]*EFencePolygonTab, *lcos_error.LCOSError) {
	var polygons []*EFencePolygonTab

	// 执行查询
	tableName := GetEFencePolygonTableName(region)
	db := ctx.WriteDB().Table(tableName)
	db = db.Where("data_version IN (?)", version).Find(&polygons)
	if db.GetError() != nil {
		logger.LogErrorf("can not find %s data error:%s|condition[%+v]|version[%s]", tableName, db.GetError().Error(), version)
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return polygons, nil
}

func (e *eFencePolygonDao) GetEFencePolygonsByVersionAndLayer(ctx utils.LCOSContext, region string, zoneQueryCondition []*schema.VersionItem) ([]*EFencePolygonTab, *lcos_error.LCOSError) {
	var polygons []*EFencePolygonTab

	// 构建查询条件
	var conditions []interface{}
	for _, zoneData := range zoneQueryCondition {
		if zoneData == nil {
			continue
		}
		condition := []interface{}{zoneData.DataVersion}
		conditions = append(conditions, condition)
	}

	// 执行查询
	tableName := GetEFencePolygonTableName(region)
	db := ctx.ReadDB().Table(tableName)
	db = db.Where("(data_version) IN (?)", conditions).Find(&polygons)
	if db.GetError() != nil {
		logger.LogErrorf("can not find %s data error:%s|condition[%+v]", tableName, db.GetError().Error(), conditions)
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return polygons, nil
}

func (e *eFencePolygonDao) GetOneEFencePolygonByZoneIdAndVersion(ctx utils.LCOSContext, region string, zoneId string, version string) (*EFencePolygonTab, *lcos_error.LCOSError) {
	var polygon *EFencePolygonTab

	// 执行查询
	tableName := GetEFencePolygonTableName(region)
	db := ctx.ReadDB().Table(tableName)
	db.Where("zone_id = ? AND data_version = ?", zoneId, version).First(&polygon)

	if db.GetError() != nil {
		logger.LogErrorf("can not find %s data error:%s|zone_id[%s]|version[%s]", tableName, db.GetError().Error(), zoneId, version)
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return polygon, nil
}

func (e *eFencePolygonDao) GetOneEFencePolygonByZoneIdLayerIdAndVersion(ctx utils.LCOSContext, region string, zoneId string, version, layerId string) (*EFencePolygonTab, *lcos_error.LCOSError) {
	var polygon *EFencePolygonTab

	// 执行查询
	tableName := GetEFencePolygonTableName(region)
	db := ctx.ReadDB().Table(tableName)
	db.Where("zone_id = ? AND data_version = ? AND layer_id = ?", zoneId, version, layerId).First(&polygon)

	if db.GetError() != nil {
		logger.LogErrorf("can not find %s data error:%s|zone_id[%s]|version[%s]|layerId[%s]", tableName, db.GetError().Error(), zoneId, version, layerId)
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return polygon, nil
}

func (e *eFencePolygonDao) UpdateEFencePolygonsByVersion(ctx utils.LCOSContext, region string, version string, handleStatus int32, operator string) *lcos_error.LCOSError {
	// 构建更新条件
	condition := &EFencePolygonTab{DataVersion: version}

	// 构建更新字段
	updates := EFencePolygonTab{
		HandleStatus: handleStatus,
		Operator:     operator,
		MTime:        utils.GetTimestamp(ctx),
	}

	tableName := GetEFencePolygonTableName(region)
	db := ctx.WriteDB().Table(tableName).Where(condition).UpdateColumns(updates)
	if db.GetError() != nil {
		logger.LogErrorf("update %s data handle_status error:%s|version[%s]|handleStatus[%d]|operator[%s]", tableName, db.GetError().Error(), version, handleStatus, operator)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	if db.RowsAffected() == 0 {
		logger.LogErrorf("no data to update:%s|version[%s]|handleStatus[%d]", tableName, version, handleStatus)
		return nil
	}

	return nil
}

func (e *eFencePolygonDao) UpdateEFencePolygonsByVersionAndLayerId(ctx utils.LCOSContext, region string, version string, handleStatus int32, operator string) *lcos_error.LCOSError {
	// 构建更新条件
	condition := &EFencePolygonTab{DataVersion: version}

	// 构建更新字段
	updates := EFencePolygonTab{
		HandleStatus: handleStatus,
		Operator:     operator,
		MTime:        utils.GetTimestamp(ctx),
	}

	tableName := GetEFencePolygonTableName(region)
	db := ctx.WriteDB().Table(tableName).Where(condition).UpdateColumns(updates)
	if db.GetError() != nil {
		logger.LogErrorf("update %s data handle_status error:%s|version[%s]|handleStatus[%d]|operator[%s]", tableName, db.GetError().Error(), version, handleStatus, operator)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	if db.RowsAffected() == 0 {
		logger.LogErrorf("no data to update:%s|version[%s]|handleStatus[%d]", tableName, version, handleStatus)
		return nil
	}

	return nil
}

func (e *eFencePolygonDao) GetPolygonInfoFromCache(ctx utils.LCOSContext, region, zoneId, version, layerId string) (*geopolygon.PolyDetail, bool, *lcos_error.LCOSError) {
	queryExec := localcache.NewLocalCacheQueryExecutor()
	operationConfData, err := queryExec.Find(ctx, constant.EFencePolygonNamespace, EFencePolyKey(region, zoneId, version, layerId))
	if err == localcache.ErrKeyNotFound {
		return nil, false, nil
	}
	if err != nil {
		return nil, false, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	data := operationConfData.(*geopolygon.PolyDetail)
	return data, true, nil
}

func (e *eFencePolygonDao) UpdatePolygonStatus(ctx utils.LCOSContext, region, zoneId, version, layerId string, status int32) *lcos_error.LCOSError {
	tableName := GetEFencePolygonTableName(region)
	updates := EFencePolygonTab{
		HandleStatus: status,
		MTime:        utils.GetTimestamp(ctx),
	}
	db := ctx.WriteDB().Table(tableName).Where("zone_id = ? and data_version = ? and layer_id = ? ", zoneId, version, layerId).UpdateColumns(updates)
	if db.GetError() != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}
	return nil
}

func (e *eFencePolygonDao) GetAllLayer(ctx utils.LCOSContext, region string) ([]*EFencePolygonTab, *lcos_error.LCOSError) {
	tableName := GetEFencePolygonTableName(region)

	res := make([]*EFencePolygonTab, 0)
	db := ctx.WriteDB().Table(tableName).Where("region_code = ? AND handle_status = ?", region, eFenceConstant.ZoneHandleStatusInUsage).Distinct("layer_id", "layer_name")
	db = db.Find(&res)
	if db.GetError() != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}
	return res, nil
}

func (e *eFencePolygonDao) GetLayerNameByLayerId(ctx utils.LCOSContext, region string, layerId string) (*EFencePolygonTab, *lcos_error.LCOSError) {
	var res *EFencePolygonTab
	queryMap := map[string]interface{}{
		"layer_id":      layerId,
		"handle_status": eFenceConstant.ZoneHandleStatusInUsage,
	}
	tableName := GetEFencePolygonTableName(region)
	// 当前应用场景中不需要查询 geometry，这个字段太长了，最好是跳过
	selectCol := []string{"layer_id", "layer_name"}

	db := ctx.ReadDB().Table(tableName).Select(selectCol).Where(queryMap).First(&res)
	if db.GetError() != nil {
		if db.GetError() == scorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.CtxLogErrorf(ctx, "GetLayerNameByLayerId list fail| err=%v", db.GetError())
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return res, nil
}
