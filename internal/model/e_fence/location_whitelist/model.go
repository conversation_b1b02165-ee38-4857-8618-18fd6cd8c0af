package location_whitelist

import (
	"fmt"
)

type EFenceLocationWhitelistTab struct {
	Id         int64  `gorm:"column:id;primary_key" json:"id"`
	LocationId int64  `gorm:"column:location_id" json:"location_id"`
	Operator   string `gorm:"column:operator" json:"operator"`
	State      string `gorm:"column:state" json:"state"`
	City       string `gorm:"column:city" json:"city"`
	District   string `gorm:"column:district" json:"district"`
	Street     string `gorm:"column:street" json:"street"`
	Region     string `gorm:"column:region" json:"region"`
	Ctime      int64  `gorm:"column:ctime" json:"ctime"`
	Mtime      int64  `gorm:"column:mtime" json:"mtime"`

	LayerId string `gorm:"column:layer_id" json:"layer_id"`
}

func (e *EFenceLocationWhitelistTab) TableName() string {
	return EFenceLocationWhitelistTabName()
}

func EFenceLocationWhitelistTabName() string {
	return "e_fence_location_whitelist_tab"
}

func GetLocationWhitelistUniqKey(locationId int64, layerId string) string {
	return fmt.Sprintf("%d_%s", locationId, layerId)
}

func GetLocationWhitelistUniqKeyStruct(locationId int64, layerId string) string {
	return fmt.Sprintf("%d_%s", locationId, layerId)
}

type WhitelistUniqKey struct {
	LocationId int
	LayerId    string
}
