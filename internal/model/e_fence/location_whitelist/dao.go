package location_whitelist

import (
	"strings"

	"git.garena.com/shopee/bg-logistics/go/scormv2/clause"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

type EFenceLocationWhitelistDao interface {
	ListLocationWhitelistByPage(ctx utils.LCOSContext, page, count int, queryMap map[string]interface{}) ([]*EFenceLocationWhitelistTab, int64, *lcos_error.LCOSError)
	QueryLocationWhitelist(ctx utils.LCOSContext, queryMap map[string]interface{}) ([]*EFenceLocationWhitelistTab, *lcos_error.LCOSError)
	InsertOrUpdateWhitelist(ctx utils.LCOSContext, data *EFenceLocationWhitelistTab) *lcos_error.LCOSError
	DeleteByLocationId(ctx utils.LCOSContext, whitelistUniqKey *WhitelistUniqKey) *lcos_error.LCOSError
	GetLocationWhitelistFromCache(ctx utils.LCOSContext, locationId int64, layerId string) (*EFenceLocationWhitelistTab, bool, *lcos_error.LCOSError)
}

type eFenceLocationWhitelistDao struct {
}

func NewEFenceLocationWhitelistDao() *eFenceLocationWhitelistDao {
	return &eFenceLocationWhitelistDao{}
}

func (e *eFenceLocationWhitelistDao) ListLocationWhitelistByPage(ctx utils.LCOSContext, page, count int, queryMap map[string]interface{}) ([]*EFenceLocationWhitelistTab, int64, *lcos_error.LCOSError) {
	var result []*EFenceLocationWhitelistTab
	query := ctx.ReadDB().Table(EFenceLocationWhitelistTabName()).Where(queryMap).Order("location_id")

	var total int64
	d := query.Count(&total)
	if d.GetError() != nil {
		return nil, 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	d = query.Offset((page - 1) * count).Limit(int(count)).Find(&result)
	if d.GetError() != nil {
		return nil, 0, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}

	return result, total, nil
}

func (e *eFenceLocationWhitelistDao) QueryLocationWhitelist(ctx utils.LCOSContext, queryMap map[string]interface{}) ([]*EFenceLocationWhitelistTab, *lcos_error.LCOSError) {
	var result []*EFenceLocationWhitelistTab
	query := ctx.ReadDB().Table(EFenceLocationWhitelistTabName()).Where(queryMap).Order("location_id").Find(&result)
	if query.GetError() != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, query.GetError().Error())
	}

	return result, nil
}

func (e *eFenceLocationWhitelistDao) InsertOrUpdateWhitelist(ctx utils.LCOSContext, data *EFenceLocationWhitelistTab) *lcos_error.LCOSError {
	db := ctx.WriteDB().Table(data.TableName())
	db = db.Clauses(
		clause.OnConflict{
			Columns:   []clause.Column{{Name: "location_id"}, {Name: "region"}, {Name: "layer_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"operator", "state", "city", "district", "street", "mtime"}),
		}).Create(data)

	if db.GetError() != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}

	return nil
}

func (e *eFenceLocationWhitelistDao) DeleteByLocationId(ctx utils.LCOSContext, whitelistUniqKey *WhitelistUniqKey) *lcos_error.LCOSError {
	db := ctx.WriteDB().Table(EFenceLocationWhitelistTabName()).Where("location_id = ? and region = ? and layer_id = ?", whitelistUniqKey.LocationId, strings.ToUpper(ctx.GetCountry()), whitelistUniqKey.LayerId)
	db = db.Delete(&EFenceLocationWhitelistTab{})
	if db.GetError() != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}
	return nil
}

func (e *eFenceLocationWhitelistDao) GetLocationWhitelistFromCache(ctx utils.LCOSContext, locationId int64, layerId string) (*EFenceLocationWhitelistTab, bool, *lcos_error.LCOSError) {
	queryExec := localcache.NewLocalCacheQueryExecutor()
	operationConfData, err := queryExec.Find(ctx, constant.EFenceWhitelistNamespace, GetLocationWhitelistUniqKey(locationId, layerId))
	if err == localcache.ErrKeyNotFound {
		return nil, false, nil
	}
	if err != nil {
		return nil, false, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	data := operationConfData.(*EFenceLocationWhitelistTab)
	return data, true, nil
}
