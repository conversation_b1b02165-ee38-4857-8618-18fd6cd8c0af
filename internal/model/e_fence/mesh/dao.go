package mesh

import (
	"fmt"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	scorm "git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/go/scormv2/clause"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
)

type EFenceMeshDao interface {
	// 一次性查询所有符合条件的数据
	LoadEFenceMeshByZoneInfo(ctx utils.LCOSContext, region string, zoneId string, dataVersion string, layerId string, resultMap map[string]interface{}) (int, *lcos_error.LCOSError)
	LoadAllEFenceMesh(ctx utils.LCOSContext, region string, zoneInfoMap map[string]int, resultMap map[string]interface{}) (int, *lcos_error.LCOSError)
	// 写入一条多边形信息，如果zone_id+data_version 重复，则更新mtime
	BatchInsertEFenceMesh(ctx utils.LCOSContext, region string, meshList []*EFenceMeshTab) *lcos_error.LCOSError
	GetMeshFromCache(ctx utils.LCOSContext, geoHash, region string) (ZoneMeshInfoList, bool, *lcos_error.LCOSError)
	GetMeshIdList(ctx utils.LCOSContext, region string, queryMap map[string]interface{}) ([]int64, *lcos_error.LCOSError)
	ListMesh(ctx utils.LCOSContext, region string, queryMap map[string]interface{}) ([]*EFenceMeshTab, *lcos_error.LCOSError)
	DeleteMeshById(ctx utils.LCOSContext, region string, idList []int64) *lcos_error.LCOSError
}

type eFenceMeshDao struct {
}

func NewEFenceMeshDao() *eFenceMeshDao {
	return &eFenceMeshDao{}
}

type ZoneMeshInfoList []*ZoneMeshInfo

func EFenceMeshKey(region string, geoHash string) string {
	return fmt.Sprintf("%s%s", strings.ToLower(region), geoHash)
}

func EFenceZoneKey(zoneId string, dataVersion string, layerId string) string {
	// 与SPX确认过，data version一定是数字，但zone id可能为任意符号，直接拼接可能会出现重复，因此需要添加分隔符避免以下这种情况
	// eg. zone_id = A-33, data_version = 32 和 zone_id = A-3, data_version = 332，若无分隔符，拼接之后都为A-3332
	return fmt.Sprintf("%s_%s_%s", zoneId, dataVersion, layerId)
}

func (e *eFenceMeshDao) LoadEFenceMeshByZoneInfo(ctx utils.LCOSContext, region string, zoneId string, dataVersion string, layerId string, resultMap map[string]interface{}) (int, *lcos_error.LCOSError) {
	tableName := GetEFenceMeshTableName(ctx, region, true)

	// 无论查出来多少key，只有2种value
	zoneMeshFullCoverage := &ZoneMeshInfo{ZoneId: zoneId, FullCoverage: true, Version: dataVersion, LayerId: layerId}
	zoneMeshPartCoverage := &ZoneMeshInfo{ZoneId: zoneId, FullCoverage: false, Version: dataVersion, LayerId: layerId}

	db := ctx.ReadDB().Table(tableName).Where("zone_id=? AND data_version=? AND layer_id=?", zoneId, dataVersion, layerId)
	batchSize := config.GetEFenceDBDumpBatchSize(ctx)
	var batchRet []*EFenceMeshTab
	totalCount := 0

	err := db.FindInBatches(&batchRet, batchSize, func(tx scorm.SQLCommon, batch int) error {
		// 读到的数据直接放进map里
		for _, data := range batchRet {
			// 区分data的true/false，选择已创建好的2个对象之一，以geohash为key，放进resultMap
			var zoneValue *ZoneMeshInfo
			if data.FullCoverage {
				zoneValue = zoneMeshFullCoverage
			} else {
				zoneValue = zoneMeshPartCoverage
			}

			// 放之前先尝试取，如果用geohash能在map中取到value，说明这个geohash也被另一个zone覆盖，则执行append操作，否则创新一个新的list
			meshKey := EFenceMeshKey(region, data.GeoHashCode)
			valueList, ok := resultMap[meshKey]
			if !ok {
				resultMap[meshKey] = ZoneMeshInfoList{zoneValue}
			} else {
				realValueList := valueList.(ZoneMeshInfoList)
				realValueList = append(realValueList, zoneValue)
				resultMap[meshKey] = realValueList
			}
		}

		// 统计数据量
		totalCount += len(batchRet)

		// 避免短时间太多次访问，加个sleep保护
		time.Sleep(config.GetMutableConf(ctx).EFenceConfig.GetDumpSleep())

		return nil
	}).GetError()

	if err != nil {
		return 0, lcos_error.NewLCOSErrorf(lcos_error.DBReadWriteErrorCode, "load e_fence mesh data to local cache error|cause=%s|zone_id=%s", err.Error(), zoneId)
	}

	return totalCount, nil
}

func (e *eFenceMeshDao) LoadAllEFenceMesh(ctx utils.LCOSContext, region string, zoneInfoMap map[string]int, resultMap map[string]interface{}) (int, *lcos_error.LCOSError) {
	tableName := GetEFenceMeshTableName(ctx, region, true)

	db := ctx.ReadDB().Table(tableName)
	batchSize := config.GetEFenceDBDumpBatchSize(ctx)
	var batchRet []*EFenceMeshTab
	totalCount := 0

	err := db.FindInBatches(&batchRet, batchSize, func(tx scorm.SQLCommon, batch int) error {
		// 读到的数据直接放进map里
		for _, data := range batchRet {
			// 将查询到的数据在本地过滤
			meshCount, ok := zoneInfoMap[EFenceZoneKey(data.ZoneId, data.DataVersion, data.LayerId)]
			if !ok {
				continue
			}
			zoneInfoMap[EFenceZoneKey(data.ZoneId, data.DataVersion, data.LayerId)] = meshCount + 1
			// 区分data的true/false，选择已创建好的2个对象之一，以geohash为key，放进resultMap
			var zoneValue *ZoneMeshInfo
			if data.FullCoverage {
				zoneValue = &ZoneMeshInfo{ZoneId: data.ZoneId, FullCoverage: true, Version: data.DataVersion, LayerId: data.LayerId}
			} else {
				zoneValue = &ZoneMeshInfo{ZoneId: data.ZoneId, FullCoverage: false, Version: data.DataVersion, LayerId: data.LayerId}
			}

			// 放之前先尝试取，如果用geohash能在map中取到value，说明这个geohash也被另一个zone覆盖，则执行append操作，否则创建一个新的list
			meshKey := EFenceMeshKey(region, data.GeoHashCode)
			valueList, ok := resultMap[meshKey]
			if !ok {
				resultMap[meshKey] = ZoneMeshInfoList{zoneValue}
			} else {
				realValueList := valueList.(ZoneMeshInfoList)
				realValueList = append(realValueList, zoneValue)
				resultMap[meshKey] = realValueList
			}
			// 统计数据量
			totalCount++
		}

		// 避免短时间太多次访问，加个sleep保护
		time.Sleep(config.GetMutableConf(ctx).EFenceConfig.GetDumpSleep())

		return nil
	}).GetError()

	if err != nil {
		return 0, lcos_error.NewLCOSErrorf(lcos_error.DBReadWriteErrorCode, "load e_fence mesh data by full tab to local cache error|cause=%s", err.Error())
	}

	return totalCount, nil
}

func (e *eFenceMeshDao) BatchInsertEFenceMesh(ctx utils.LCOSContext, region string, meshList []*EFenceMeshTab) *lcos_error.LCOSError {
	tableName := GetEFenceMeshTableName(ctx, region, false)

	d := ctx.WriteDB().Table(tableName).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "zone_id"}, {Name: "data_version"}, {Name: "geo_hash_code"}, {Name: "layer_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"full_coverage", "ctime"}),
	}).CreateInBatches(meshList, constant.DBMAXBATCHNUM)

	if d.GetError() != nil {
		logger.LogErrorf("create %s data error:%s", tableName, d.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	return nil
}

func (e *eFenceMeshDao) GetMeshFromCache(ctx utils.LCOSContext, geoHash, region string) (ZoneMeshInfoList, bool, *lcos_error.LCOSError) {
	queryExec := localcache.NewLocalCacheQueryExecutor()
	operationConfData, err := queryExec.Find(ctx, constant.EFenceMeshNamespace, EFenceMeshKey(region, geoHash))
	if err == localcache.ErrKeyNotFound {
		return nil, false, nil
	}
	if err != nil {
		return nil, false, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	data := operationConfData.(ZoneMeshInfoList)
	return data, true, nil
}

func (e *eFenceMeshDao) GetMeshIdList(ctx utils.LCOSContext, region string, queryMap map[string]interface{}) ([]int64, *lcos_error.LCOSError) {
	var idList []int64
	tableName := GetEFenceMeshTableName(ctx, region, false)

	query := ctx.ReadDB().Table(tableName).Where(queryMap).Select("id").Find(&idList)
	if query.GetError() != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, query.GetError().Error())
	}
	return idList, nil
}

func (e *eFenceMeshDao) ListMesh(ctx utils.LCOSContext, region string, queryMap map[string]interface{}) ([]*EFenceMeshTab, *lcos_error.LCOSError) {
	var (
		tableName = GetEFenceMeshTableName(ctx, region, false)
		meshList  []*EFenceMeshTab
	)

	if err := ctx.ReadDB().Table(tableName).Where(queryMap).Find(&meshList).GetError(); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
	}
	return meshList, nil
}

func (e *eFenceMeshDao) DeleteMeshById(ctx utils.LCOSContext, region string, idList []int64) *lcos_error.LCOSError {
	tableName := GetEFenceMeshTableName(ctx, region, false)
	db := ctx.WriteDB().Table(tableName).Where("id in ?", idList).Delete(&EFenceMeshTab{})
	if db.GetError() != nil {
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}
	return nil
}
