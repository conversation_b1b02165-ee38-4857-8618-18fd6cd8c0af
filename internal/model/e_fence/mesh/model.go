package mesh

import (
	"context"
	"fmt"
	eFenceConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"strings"
)

const (
	EFenceMeshTabNamePrefix   = "e_fence_mesh_tab"
	EFenceMeshTabV2NamePrefix = "e_fence_mesh_tab_v2"
)

func GetEFenceMeshTableName(ctx context.Context, region string, cacheRefresh bool) string {
	if !eFenceConstant.IsValidRegion(region) {
		region = "SG" // 防止SQL注入，对region进行白名单过滤
	}
	v := config.GetMutableConf(ctx).EFenceConfig.RWMeshTabV2[strings.ToLower(region)]
	cond := v >= eFenceConstant.RWMeshTabV2ExceptCache
	if cacheRefresh {
		cond = v >= eFenceConstant.RWMeshTabV2Global
	}
	if cond {
		return fmt.Sprintf("%s_%s", EFenceMeshTabV2NamePrefix, strings.ToLower(region))
	}

	return fmt.Sprintf("%s_%s", EFenceMeshTabNamePrefix, strings.ToLower(region))
}

type EFenceMeshTab struct {
	ID           uint64 `gorm:"column:id;primary_key" json:"id"`
	Region       string `gorm:"column:region_code" json:"region_code"`
	ZoneId       string `gorm:"column:zone_id" json:"zone_id"`
	GeoHashCode  string `gorm:"column:geo_hash_code" json:"geo_hash_code"`
	DataVersion  string `gorm:"column:data_version" json:"data_version"`
	FullCoverage bool   `gorm:"column:full_coverage" json:"full_coverage"`

	LayerId string `gorm:"column:layer_id" json:"layer_id"`

	CTime uint32 `gorm:"autoCreateTime;column:ctime" json:"ctime"`
}

type ZoneMeshInfo struct {
	ZoneId       string // 所属的zone
	FullCoverage bool   // 是否被这个zone全覆盖
	Version      string
	LayerId      string
}

func (t *EFenceMeshTab) TableName() string {
	ctx := context.Background()
	return GetEFenceMeshTableName(ctx, t.Region, false)
}
