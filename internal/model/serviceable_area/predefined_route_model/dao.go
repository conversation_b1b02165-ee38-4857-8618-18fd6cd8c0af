package predefined_route_model

import (
	"errors"
	scorm "git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
)

type LogisticLinePredefinedRouteDao interface {
	// DB读写接口，提供给admin端使用
	BatchCreateOrUpdateLogisticLinePredefinedRoute(ctx utils.LCOSContext, dataList []*LogisticLinePredefinedRouteTab) *lcos_error.LCOSError
	UpdateLogisticLinePredefinedRouteById(ctx utils.LCOSContext, data *LogisticLinePredefinedRouteTab) *lcos_error.LCOSError
	DeleteLogisticLinePredefinedRouteById(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError
	BatchDeleteLogisticLinePredefinedRouteList(ctx utils.LCOSContext, dataList []*LogisticLinePredefinedRouteTab) *lcos_error.LCOSError
	GetLogisticLinePredefinedRouteById(ctx utils.LCOSContext, id uint64) (*LogisticLinePredefinedRouteTab, *lcos_error.LCOSError)
	ListLogisticLinePredefinedRouteByParams(ctx utils.LCOSContext, params map[string]interface{}) ([]*LogisticLinePredefinedRouteTab, *lcos_error.LCOSError)
	ListLogisticLinePredefinedRouteByParamsWithPaging(ctx utils.LCOSContext, params map[string]interface{}, pageNo, count uint32) ([]*LogisticLinePredefinedRouteTab, uint32, *lcos_error.LCOSError)

	// Local Cache查询接口，提供给在线链路使用
	GetLinePredefinedRouteListByLineAndGroupUsingCache(ctx utils.LCOSContext, lineId, groupId string) (map[string]*LogisticLinePredefinedRouteTabLite, *lcos_error.LCOSError)
	GetLinePredefinedRouteByRouteUsingCache(ctx utils.LCOSContext, lineId, groupId string, fromAreaId, toAreaId uint64) (*LogisticLinePredefinedRouteTabLite, *lcos_error.LCOSError)
}

type logisticLinePredefinedRouteDaoImpl struct {
	queryExecutor localcache.QueryExecutor
}

func NewLogisticLinePredefinedRouteDao() *logisticLinePredefinedRouteDaoImpl {
	return &logisticLinePredefinedRouteDaoImpl{
		queryExecutor: localcache.NewLocalCacheQueryExecutor(),
	}
}

func (l *logisticLinePredefinedRouteDaoImpl) BatchCreateOrUpdateLogisticLinePredefinedRoute(ctx utils.LCOSContext, dataList []*LogisticLinePredefinedRouteTab) *lcos_error.LCOSError {
	return common.BatchCreateOrUpdateData(ctx, dataList, constant.DBMAXBATCHNUM)
}

func (l *logisticLinePredefinedRouteDaoImpl) UpdateLogisticLinePredefinedRouteById(ctx utils.LCOSContext, data *LogisticLinePredefinedRouteTab) *lcos_error.LCOSError {
	return common.UpdateDataFieldsById(ctx, LogisticLinePredefinedRouteTabHook, data.Id, data)
}

func (l *logisticLinePredefinedRouteDaoImpl) DeleteLogisticLinePredefinedRouteById(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return common.DeleteDataById(ctx, LogisticLinePredefinedRouteTabHook, id)
}

func (l *logisticLinePredefinedRouteDaoImpl) BatchDeleteLogisticLinePredefinedRouteList(ctx utils.LCOSContext, dataList []*LogisticLinePredefinedRouteTab) *lcos_error.LCOSError {
	params := make([][]interface{}, 0)
	for _, data := range dataList {
		params = append(params, []interface{}{data.Region, data.LineId, data.GroupId, data.FromAreaId, data.ToAreaId})

		if len(params) >= constant.DBMAXBATCHDELNUM {
			if err := ctx.WriteDB().Where("(`region`, `line_id`, `group_id`, `from_area_id`, `to_area_id`) in ?", params).Delete(LogisticLinePredefinedRouteTabHook).GetError(); err != nil {
				return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
			}
			params = make([][]interface{}, 0)
		}
	}
	if len(params) > 0 {
		if err := ctx.WriteDB().Where("(`region`, `line_id`, `group_id`, `from_area_id`, `to_area_id`) in ?", params).Delete(LogisticLinePredefinedRouteTabHook).GetError(); err != nil {
			return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error())
		}
	}
	return nil
}

func (l *logisticLinePredefinedRouteDaoImpl) GetLogisticLinePredefinedRouteById(ctx utils.LCOSContext, id uint64) (*LogisticLinePredefinedRouteTab, *lcos_error.LCOSError) {
	data := new(LogisticLinePredefinedRouteTab)
	return data, common.GetDataById(ctx, data, id)
}

func (l *logisticLinePredefinedRouteDaoImpl) ListLogisticLinePredefinedRouteByParams(ctx utils.LCOSContext, params map[string]interface{}) ([]*LogisticLinePredefinedRouteTab, *lcos_error.LCOSError) {
	var (
		dataList []*LogisticLinePredefinedRouteTab
		subList  []*LogisticLinePredefinedRouteTab
	)
	err := common.SearchAllDataInBatchesGeneral(ctx, LogisticLinePredefinedRouteTabHook, &subList, params, constant.DBMAXBATCHNUM, func(tx scorm.SQLCommon, batch int) error {
		dataList = append(dataList, subList...)
		return nil
	})
	if err != nil {
		return nil, err
	}
	return dataList, nil
}

func (l *logisticLinePredefinedRouteDaoImpl) ListLogisticLinePredefinedRouteByParamsWithPaging(ctx utils.LCOSContext, params map[string]interface{}, pageNo, count uint32) ([]*LogisticLinePredefinedRouteTab, uint32, *lcos_error.LCOSError) {
	var dataList []*LogisticLinePredefinedRouteTab
	total, err := common.SearchAllDataByPageOrdered(ctx, LogisticLinePredefinedRouteTabHook, &dataList, pageNo, count, params, "id")
	if err != nil {
		return nil, 0, err
	}
	return dataList, total, nil
}

func (l *logisticLinePredefinedRouteDaoImpl) GetLinePredefinedRouteListByLineAndGroupUsingCache(ctx utils.LCOSContext, lineId, groupId string) (map[string]*LogisticLinePredefinedRouteTabLite, *lcos_error.LCOSError) {
	key := GenGroupKey(lineId, groupId)

	obj, err := l.queryExecutor.Find(ctx, constant.LogisticLinePredefinedRouteNamespace, key)
	if err != nil {
		if errors.Is(err, localcache.ErrKeyNotFound) {
			return nil, nil
		}
		return nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, err.Error())
	}
	if obj == nil {
		return nil, nil
	}
	routeMap, ok := obj.(map[string]*LogisticLinePredefinedRouteTabLite)
	if !ok {
		return nil, nil
	}
	return routeMap, nil
}

func (l *logisticLinePredefinedRouteDaoImpl) GetLinePredefinedRouteByRouteUsingCache(ctx utils.LCOSContext, lineId, groupId string, fromAreaId, toAreaId uint64) (*LogisticLinePredefinedRouteTabLite, *lcos_error.LCOSError) {
	key := GenRouteKey(fromAreaId, toAreaId)

	routeMap, err := l.GetLinePredefinedRouteListByLineAndGroupUsingCache(ctx, lineId, groupId)
	if err != nil {
		return nil, err
	}
	if routeMap == nil {
		return nil, nil
	}
	return routeMap[key], nil
}
