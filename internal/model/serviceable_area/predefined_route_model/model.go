package predefined_route_model

import (
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"strconv"
)

const LogisticLinePredefinedRouteTabName = "logistic_line_predefined_route_tab"

var LogisticLinePredefinedRouteTabHook = &LogisticLinePredefinedRouteTab{}

type LogisticLinePredefinedRouteTab struct {
	Id           uint64 `gorm:"column:id" json:"id"`
	Region       string `gorm:"column:region" json:"region"`
	LineId       string `gorm:"column:line_id" json:"line_id"`
	GroupId      string `gorm:"column:group_id" json:"group_id"`
	FromAreaId   uint64 `gorm:"column:from_area_id" json:"from_area_id"`
	FromAreaName string `gorm:"column:from_area_name" json:"from_area_name"`
	ToAreaId     uint64 `gorm:"column:to_area_id" json:"to_area_id"`
	ToAreaName   string `gorm:"column:to_area_name" json:"to_area_name"`
	RouteCode    string `gorm:"column:route_code" json:"route_code"`
	Ctime        uint32 `gorm:"column:ctime;autoCreateTime" json:"ctime"`
	Mtime        uint32 `gorm:"column:mtime;autoUpdateTime" json:"mtime"`
}

func (l *LogisticLinePredefinedRouteTab) TableName() string {
	return LogisticLinePredefinedRouteTabName
}

func (l *LogisticLinePredefinedRouteTab) ToLite() *LogisticLinePredefinedRouteTabLite {
	return &LogisticLinePredefinedRouteTabLite{
		LineId:     l.LineId,
		GroupId:    l.GroupId,
		FromAreaId: l.FromAreaId,
		ToAreaId:   l.ToAreaId,
		RouteCode:  l.RouteCode,
	}
}

// LogisticLinePredefinedRouteTabLite LogisticLinePredefinedRouteTab的精简结构，用于Local Cache缓存
type LogisticLinePredefinedRouteTabLite struct {
	LineId     string `json:"line_id"`
	GroupId    string `json:"group_id"`
	FromAreaId uint64 `json:"from_area_id"`
	ToAreaId   uint64 `json:"to_area_id"`
	RouteCode  string `json:"route_code"`
}

func (l *LogisticLinePredefinedRouteTabLite) GenUniqueKey() string {
	return GenUniqueKey(l.LineId, l.GroupId, l.FromAreaId, l.ToAreaId)
}

func (l *LogisticLinePredefinedRouteTabLite) GenGroupKey() string {
	return GenGroupKey(l.LineId, l.GroupId)
}

func (l *LogisticLinePredefinedRouteTabLite) GenRouteKey() string {
	return GenRouteKey(l.FromAreaId, l.ToAreaId)
}

func GenUniqueKey(lineId, groupId string, fromAreaId, toAreaId uint64) string {
	return utils.GenKey(":", lineId, groupId, strconv.FormatUint(fromAreaId, 10), strconv.FormatUint(toAreaId, 10))
}

func GenGroupKey(lintId, groupId string) string {
	return utils.GenKey(":", lintId, groupId)
}

func GenRouteKey(fromAreaId, toAreaId uint64) string {
	return utils.GenKey(":", strconv.FormatUint(fromAreaId, 10), strconv.FormatUint(toAreaId, 10))
}
