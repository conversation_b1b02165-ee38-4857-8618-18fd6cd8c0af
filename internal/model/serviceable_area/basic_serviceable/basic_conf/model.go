package basic_conf

const (
	LineBasicServiceableConfTableName  = "logistic_line_basic_serviceable_conf_tab"
	LineCollectDeliverAbilityTableName = "logistic_line_collect_deliver_ability_tab"
)

type LineBasicServiceableConfTab struct {
	ID                         uint64 `gorm:"column:id" json:"id"`
	LineId                     string `gorm:"column:line_id" json:"line_id"`
	Region                     string `gorm:"column:region" json:"region"`
	OriginServiceableType      uint8  `gorm:"column:origin_serviceable_type" json:"origin_serviceable_type"`
	DestinationServiceableType uint8  `gorm:"column:destination_serviceable_type" json:"destination_serviceable_type"`
	IsCheckServiceable         uint8  `gorm:"column:is_check_serviceable" json:"is_check_serviceable"`
	IsCheckRoute               uint8  `gorm:"column:is_check_route" json:"is_check_route"`
	RouteMode                  uint8  `gorm:"column:route_mode" json:"route_mode"`                               // 1 - whitelist; 2 - blacklist
	IsCheckPredefinedRoute     uint8  `gorm:"column:is_check_predefined_route" json:"is_check_predefined_route"` // SPLN-36590 NSS only，只有基础层白名单模式
	DefaultCollectType         uint8  `gorm:"column:default_collect_type" json:"default_collect_type"`
	DefaultDeliverType         uint8  `gorm:"column:default_deliver_type" json:"default_deliver_type"`
	DefaultPickupEnabled       uint8  `gorm:"column:default_pickup_enabled" json:"default_pickup_enabled"`
	DefaultCodPickupEnabled    uint8  `gorm:"column:default_cod_pickup_enabled" json:"default_cod_pickup_enabled"`
	DefaultDeliverEnabled      uint8  `gorm:"column:default_deliver_enabled" json:"default_deliver_enabled"`
	DefaultCodDeliverEnabled   uint8  `gorm:"column:default_cod_deliver_enabled" json:"default_cod_deliver_enabled"`
	DefaultPdpPostcode         uint8  `gorm:"column:default_pdp_postcode" json:"default_pdp_postcode"`
	CollectDeliverAbility      uint32 `gorm:"column:collect_deliver_ability" json:"collect_deliver_ability"`
	IsCheckDistance            uint8  `gorm:"column:is_check_distance" json:"is_check_distance"`
	MinimumDistanceOperator    uint8  `gorm:"column:minimum_distance_operator" json:"minimum_distance_operator"`
	MinimumDistance            uint32 `gorm:"column:minimum_distance" json:"minimum_distance"`
	MaximumDistanceOperator    uint8  `gorm:"column:maximum_distance_operator" json:"maximum_distance_operator"`
	MaximumDistance            uint32 `gorm:"column:maximum_distance" json:"maximum_distance"`
	CTime                      int64  `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	MTime                      int64  `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

type LineCollectDeliverAbilityTab struct {
	ID                 uint64 `gorm:"column:id" json:"id"`
	LineId             string `gorm:"column:line_id" json:"line_id"`
	Region             string `gorm:"column:region" json:"region"`
	CollectDeliverType uint8  `gorm:"column:collect_deliver_type" json:"collect_deliver_type"`
	MaxCapacity        uint32 `gorm:"column:max_capacity" json:"max_capacity"`
	CTime              int64  `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	MTime              int64  `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

func (rs *LineBasicServiceableConfTab) TableName() string {
	return LineBasicServiceableConfTableName
}

func (rs *LineBasicServiceableConfTab) GetCanPickup() uint8 {
	return rs.DefaultPickupEnabled
}

func (rs *LineBasicServiceableConfTab) GetCanCodPickup() uint8 {
	return rs.DefaultCodPickupEnabled
}

func (rs *LineBasicServiceableConfTab) GetCanDeliver() uint8 {
	return rs.DefaultDeliverEnabled
}

func (rs *LineBasicServiceableConfTab) GetCanCodDeliver() uint8 {
	return rs.DefaultCodDeliverEnabled
}

func (rs *LineCollectDeliverAbilityTab) TableName() string {
	return LineCollectDeliverAbilityTableName
}
