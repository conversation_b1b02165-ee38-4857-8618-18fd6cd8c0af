package parcel_library

import (
	"context"
	"fmt"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	parcelLibraryService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/golang/protobuf/proto"
)

type grpcParcelLibraryController struct {
	parcelLibraryService parcelLibraryService.LogisticParcelLibraryService
}

func NewGrpcParcelLibraryController(p parcelLibraryService.LogisticParcelLibraryService) *grpcParcelLibraryController {
	return &grpcParcelLibraryController{
		parcelLibraryService: p,
	}
}

// @core
func (c *grpcParcelLibraryController) GetParcelSizeDetailBySkuCombination(ctx context.Context, request *pb.GetParcelSizeDetailBySkuRequest) (*pb.GetParcelSizeDetailBySkuResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	response, _, err := c.parcelLibraryService.GetParcelLibraryDataBySkuInfos(lcosCtx, request.GetRegion(), request.SkuInfo, 0)
	if err != nil {
		return &pb.GetParcelSizeDetailBySkuResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}
	if len(response) < 1 {
		return &pb.GetParcelSizeDetailBySkuResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(-1, "no data in codis for this combination"),
		}, nil
	}
	return &pb.GetParcelSizeDetailBySkuResponse{
		ParcelSizeInfo: &pb.ParcelSizeInfo{
			Length:                   proto.String(fmt.Sprintf("%v", response[0].AccurateLength)),
			Width:                    proto.String(fmt.Sprintf("%v", response[0].AccurateWidth)),
			Height:                   proto.String(fmt.Sprintf("%v", response[0].AccurateHeight)),
			Weight:                   proto.String(fmt.Sprintf("%v", response[0].AccurateWeight)),
			ActualWeightAccuracy:     proto.String(fmt.Sprintf("%v", response[0].WeightAccuracy)),
			VolumetricWeightAccuracy: proto.String(fmt.Sprintf("%v", response[0].VolumetricFinAccuracy)),
		},
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

func (c *grpcParcelLibraryController) BatchGetParcelLibraryDataBySkuInfos(ctx context.Context, request *pb.BatchGetParcelLibraryDataBySkuInfosRequest) (*pb.BatchGetParcelLibraryDataBySkuInfosResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	if len(request.GetOrders()) < 1 {
		return &pb.BatchGetParcelLibraryDataBySkuInfosResponse{RespHeader: http.GrpcSuccessRespHeader(), OrderResult: nil}, nil
	}
	response, err := c.parcelLibraryService.BatchGetParcelLibraryDataBySkuInfos(lcosCtx, request)

	if err != nil {
		return &pb.BatchGetParcelLibraryDataBySkuInfosResponse{RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg), OrderResult: nil}, nil
	}
	return response, nil
}
