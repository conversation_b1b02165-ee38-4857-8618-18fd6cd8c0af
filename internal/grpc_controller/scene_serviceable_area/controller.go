package scene_serviceable_area

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/protobuf/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	Logger "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic"
	siteservice "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
)

type grpcSceneServiceableAreaController struct {
	serviceableCheckerService  service.ServiceableCheckerServiceInterface
	siteServiceableAreaService siteservice.SiteServiceableAreaInterface
	parcelLibraryService       parcel_library.LogisticParcelLibraryService
	pb.UnimplementedLcosSceneServiceableServer
}

func NewGrpcSceneServiceableAreaController(serviceableCheckerService service.ServiceableCheckerServiceInterface, siteServiceableChecker siteservice.SiteServiceableAreaInterface, parcelLibraryService parcel_library.LogisticParcelLibraryService) *grpcSceneServiceableAreaController {
	return &grpcSceneServiceableAreaController{
		serviceableCheckerService:  serviceableCheckerService,
		siteServiceableAreaService: siteServiceableChecker,
		parcelLibraryService:       parcelLibraryService,
	}
}

func generateErrorResponse(err *lcos_error.LCOSError) *pb.BatchCheckProductServiceableRsp {
	return &pb.BatchCheckProductServiceableRsp{
		RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
	}
}

func getLanePossibleAreaServiceableLines(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct) ([]*lfs_service.SingleCompose, *lcos_error.LCOSError) {
	fm, lm, lcosErr := laneInfo.GetFmAndLm()
	if lcosErr != nil {
		return nil, lcosErr
	}
	if fm != nil && lm != nil && fm.ResourceID != lm.ResourceID && (fm.IsLastMile() || lm.IsFirstMile()) {
		errMsg := fmt.Sprintf("multiple first mile or last mile found|lane_code=%s", laneInfo.LaneCode)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	fl, _, lcosErr := laneInfo.GetFlAndLm()
	if lcosErr != nil {
		return nil, lcosErr
	}
	lineInfoMap := map[string]*lfs_service.SingleCompose{}

	if fm != nil && fm.SubType != int(constant.C_FM) {
		lineInfoMap[fm.ResourceID] = fm
	}
	if lm != nil {
		lineInfoMap[lm.ResourceID] = lm
	}
	if fl != nil {
		lineInfoMap[fl.ResourceID] = fl
	}

	lines := make([]*lfs_service.SingleCompose, 0, len(lineInfoMap))

	for _, line := range lineInfoMap {
		lines = append(lines, line)
	}
	return lines, nil
}

func getLaneAreaServiceableLinesFulfillment(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct) ([]*lfs_service.SingleCompose, *lcos_error.LCOSError) {
	fm, lm, lcosErr := laneInfo.GetFmAndLm()
	if lcosErr != nil {
		return nil, lcosErr
	}
	if fm != nil && lm != nil && fm.ResourceID != lm.ResourceID && (fm.IsLastMile() || lm.IsFirstMile()) {
		errMsg := fmt.Sprintf("multiple first mile or last mile found|lane_code=%s", laneInfo.LaneCode)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	fl, _, lcosErr := laneInfo.GetFlAndLmFulfillment()
	if lcosErr != nil {
		return nil, lcosErr
	}
	lineInfoMap := map[string]*lfs_service.SingleCompose{}

	if fm != nil && fm.SubType != int(constant.C_FM) {
		lineInfoMap[fm.ResourceID] = fm
	}
	if lm != nil {
		lineInfoMap[lm.ResourceID] = lm
	}
	if fl != nil {
		lineInfoMap[fl.ResourceID] = fl
	}

	var lines []*lfs_service.SingleCompose

	for _, line := range lineInfoMap {
		lines = append(lines, line)
	}
	return lines, nil
}

func serviceableAddress2LocationInfo(addr *pb.ServiceableAddress) *pb.LocationInfo {
	if addr == nil {
		return nil
	}
	return &pb.LocationInfo{
		StateLocationId:    addr.StateLocationId,
		CityLocationId:     addr.CityLocationId,
		DistrictLocationId: addr.DistrictLocationId,
		StreetLocationId:   addr.StreetLocationId,
		Postcode:           addr.PostalCode,
		Longitude:          addr.Longitude,
		Latitude:           addr.Latitude,
	}
}

func actualPointAddress2LocationInfo(apInfo *llspb.ActualPointInfo) *pb.LocationInfo {
	if apInfo == nil {
		return nil
	}
	return &pb.LocationInfo{
		StateLocationId:    apInfo.StateLocationId,
		CityLocationId:     apInfo.CityLocationId,
		DistrictLocationId: apInfo.DistrictLocationId,
		StreetLocationId:   apInfo.StreetLocationId,
		Postcode:           apInfo.ZipCode,
		Longitude:          apInfo.Longitude,
		Latitude:           apInfo.Latitude,
	}
}

// collect type is optional, will not check if not given
func generateServiceableCollectType(line *lfs_service.SingleCompose, basis *pb.ServiceableBasis) *uint32 {
	if utils.ContainsUint32(constant.CollectionTypeB2C, uint32(line.SubType)) {
		collectType := uint32(constant.B2C)
		return &collectType
	}
	if basis.CollectType != nil {
		collectType := uint32(basis.GetCollectType())
		return &collectType
	}
	return nil
}

// deliver type is optional, will not check if not given
func generateServiceableDeliverType(line *lfs_service.SingleCompose, basis *pb.ServiceableBasis) *uint32 {
	if utils.ContainsUint32(constant.DeliverType3PL, uint32(line.SubType)) {
		deliverType := uint32(constant.TO3PL)
		return &deliverType
	} else if utils.ContainsUint32(constant.DeliverTypeTws, uint32(line.SubType)) {
		deliverType := uint32(constant.TOWMS)
		return &deliverType
	}
	if basis.DeliverType != nil {
		deliverType := uint32(basis.GetDeliverType())
		return &deliverType
	}
	return nil
}

func generateServiceableCheckSender(line *lfs_service.SingleCompose, basis *pb.ServiceableBasis) uint32 {
	if utils.ContainsUint32(constant.CheckSenderFalse, uint32(line.SubType)) {
		return uint32(constant.FALSE)
	}
	return basis.GetCheckSender()
}

func generateServiceableCheckReceiver(line *lfs_service.SingleCompose, basis *pb.ServiceableBasis) uint32 {
	if utils.ContainsUint32(constant.CheckReceiverFalse, uint32(line.SubType)) {
		return uint32(constant.FALSE)
	}
	return basis.GetCheckReceiver()
}

func serviceableBasis2LcosGetBaseInfo(line *lfs_service.SingleCompose, basis *pb.ServiceableBasis) *pb.GetServiceableInfoBase2 {
	lineID := line.ResourceID
	skipPostcode := basis.GetCheckPostalCode() ^ uint32(constant.TRUE)
	checkSender := generateServiceableCheckSender(line, basis)
	checkReceiver := generateServiceableCheckReceiver(line, basis)
	info := &pb.GetServiceableInfoBase2{
		LineId:               &lineID,
		IsCheckOperation:     basis.CheckOperation,
		IsCheckBasic:         basis.CheckBasic,
		SkipPostcode:         &skipPostcode,
		CollectType:          generateServiceableCollectType(line, basis),
		DeliverType:          generateServiceableDeliverType(line, basis),
		CheckSender:          &checkSender,
		CheckReceiver:        &checkReceiver,
		SkipZoneRoute:        basis.SkipZoneRoute,
		SkipElectricFence:    basis.SkipElectricFence,
		UseElectricFence:     basis.UseElectricFence,
		IsCheckTradeIn:       basis.IsCheckTradeIn,
		CheckPredefinedRoute: basis.CheckPredefinedRoute,
		PredefinedRouteCodes: basis.PredefinedRouteCodes,
	}
	if info.GetCheckSender() == uint32(constant.TRUE) {
		if basis.SenderCheckLevel != nil {
			senderCheckLevel := uint32(basis.GetSenderCheckLevel())
			info.SenderCheckLevel = &senderCheckLevel
		}
	}
	if info.GetCheckReceiver() == uint32(constant.TRUE) {
		if basis.ReceiverCheckLevel != nil {
			receiverCheckLevel := uint32(basis.GetReceiverCheckLevel())
			info.ReceiverCheckLevel = &receiverCheckLevel
		}
	}
	return info
}

func serviceableBasis2LcosRuleBaseInfo(lineID string, line *lfs_service.SingleModelRule, basis *pb.ServiceableBasis) *pb.GetServiceableInfoBase2 {
	skipPostcode := basis.GetCheckPostalCode() ^ uint32(constant.TRUE)
	lineCompose := &lfs_service.SingleCompose{
		ResourceID:   lineID,
		ResourceType: line.ResourceType,
		SubType:      line.SubType,
	}

	// CollectType、DeliverType 可能修改
	info := &pb.GetServiceableInfoBase2{
		LineId:               &lineID,
		IsCheckOperation:     basis.CheckOperation,
		IsCheckBasic:         basis.CheckBasic,
		SkipPostcode:         &skipPostcode,
		CollectType:          generateServiceableCollectType(lineCompose, basis),
		DeliverType:          generateServiceableDeliverType(lineCompose, basis),
		CheckSender:          utils.NewUint32(uint32(line.CheckSender)),
		CheckReceiver:        utils.NewUint32(uint32(line.CheckReceiver)),
		SkipZoneRoute:        basis.SkipZoneRoute,
		SkipElectricFence:    basis.SkipElectricFence,
		UseElectricFence:     basis.UseElectricFence,
		IsCheckTradeIn:       basis.IsCheckTradeIn,
		CheckPredefinedRoute: basis.CheckPredefinedRoute,
		PredefinedRouteCodes: basis.PredefinedRouteCodes,
	}
	if info.GetCheckSender() == uint32(constant.TRUE) && basis.SenderCheckLevel != nil {
		info.SenderCheckLevel = utils.NewUint32(uint32(basis.GetSenderCheckLevel()))
	}
	if info.GetCheckReceiver() == uint32(constant.TRUE) && basis.ReceiverCheckLevel != nil {
		info.ReceiverCheckLevel = utils.NewUint32(uint32(basis.GetReceiverCheckLevel()))
	}
	// todo 日志打印

	return info
}

func serviceableBasis2SiteSAReq(site *lfs_service.SingleCompose, basis *pb.ServiceableBasis, loc *pb.LocationInfo) *siteservice.GetSiteServiceableAreaBasicRequest {
	skipPostcode := basis.GetCheckPostalCode() ^ uint32(constant.TRUE)
	singleReq := &siteservice.GetSiteServiceableAreaBasicRequest{
		SiteID:             site.ResourceID,
		StateLocationID:    int(loc.GetStateLocationId()),
		CityLocationID:     int(loc.GetCityLocationId()),
		DistrictLocationID: int(loc.GetDistrictLocationId()),
		StreetLocationID:   int(loc.GetStreetLocationId()),
		Zipcode:            loc.GetPostcode(),
		SkipPostcode:       int(skipPostcode),
	}
	return singleReq
}

func replaceLocationInfo(ctx utils.LCOSContext, rule *lfs_service.SingleModelRule, deliverInfo, pickupInfo *pb.LocationInfo) (*pb.LocationInfo, *pb.LocationInfo) {
	originDeliverLoc := deliverInfo
	originPickupLoc := pickupInfo
	if rule.SiteSortingDeliverAddrFlag == constant.UsePickupAddress {
		deliverInfo = originPickupLoc
		logger.CtxLogInfof(ctx, "replaceLocationInfo,originDeliverInfo=%v,newDeliverInfo=%v", utils.MarshToStringWithoutError(originDeliverLoc), utils.MarshToStringWithoutError(deliverInfo))
	}
	if rule.SiteSortingPickupAddrFlag == constant.UseDeliverAddress {
		pickupInfo = originDeliverLoc
		logger.CtxLogInfof(ctx, "replaceLocationInfo,originPickupInfo=%v,newPickupInfo=%v", utils.MarshToStringWithoutError(originPickupLoc), utils.MarshToStringWithoutError(pickupInfo))
	}

	return deliverInfo, pickupInfo
}

func errLaneAreaServiceable(laneCode string, errCode int32, message string) *pb.LaneAreaServiceable {
	return &pb.LaneAreaServiceable{
		LaneCode: &laneCode,
		Serviceable: &pb.AreaServiceable{
			Code:    &errCode,
			Message: &message,
		},
	}
}

func successAreaServiceable() *pb.AreaServiceable {
	var retCode int32 = 0
	message := "Success"
	var canAbility uint32 = 1
	return &pb.AreaServiceable{
		Code:    &retCode,
		Message: &message,
		Ability: &pb.AreaServiceability{
			CanPickup:       &canAbility,
			CanCodPickup:    &canAbility,
			CanDeliver:      &canAbility,
			CanCodDeliver:   &canAbility,
			PickupInEFence:  &canAbility,
			DeliverInEFence: &canAbility,
			SupportTradeIn:  &canAbility,
		},
	}
}

// getLinesAreaServiceable 获取多条line的服务范围
func (c *grpcSceneServiceableAreaController) getLinesAreaServiceable(ctx utils.LCOSContext, lines []*lfs_service.SingleCompose, laneInfo *lfs_service.LaneCodeStruct, singleProductReq *pb.LaneAreaServiceabilityReq, header *pb.ReqHeader) *pb.LaneAreaServiceable {
	pickupInfo := serviceableAddress2LocationInfo(singleProductReq.GetPickupAddr())
	deliverInfo := serviceableAddress2LocationInfo(singleProductReq.GetDeliverAddr())
	var requests []*pb.SingleGetServiceableRequest2
	for _, line := range lines {
		requests = append(requests, &pb.SingleGetServiceableRequest2{
			BaseInfo:    serviceableBasis2LcosGetBaseInfo(line, singleProductReq.Basis),
			PickupInfo:  pickupInfo,
			DeliverInfo: deliverInfo,
		})
	}

	// 请求lcos的接口
	lcosReq := &pb.BatchGetLineServiceableInfoRequest2{
		ReqHeader:          header,
		ServiceableReqList: requests,
	}
	// 存放lane的返回结果
	ability := &pb.LaneAreaServiceable{
		LaneCode:    &laneInfo.LaneCode,
		Serviceable: successAreaServiceable(),
	}
	results, _ := c.serviceableCheckerService.BatchGetServiceable(ctx, lcosReq)
	for _, tmpLineResult := range results.GetServiceableRespList() {
		lineResult := tmpLineResult
		if lineResult.GetItemCode() != lcos_error.SuccessCode {
			failedMsg := fmt.Sprintf("serviceable area get fail. line=%s, code=%d, err_msg=%s", lineResult.GetLineId(), lineResult.GetItemCode(), lineResult.GetMessage())
			ability.Serviceable = &pb.AreaServiceable{
				Code:    lineResult.ItemCode,
				Message: &failedMsg,
			}
			break
		}

		// 只要有一条线为false，lane整体返回false
		ability.Serviceable.Ability.CanPickup = utils.NewUint32(lineResult.ServiceableInfo.GetCanPickup() & ability.Serviceable.Ability.GetCanPickup())
		ability.Serviceable.Ability.CanCodPickup = utils.NewUint32(lineResult.ServiceableInfo.GetCanCodPickup() & ability.Serviceable.Ability.GetCanCodPickup())
		ability.Serviceable.Ability.CanDeliver = utils.NewUint32(lineResult.ServiceableInfo.GetCanDeliver() & ability.Serviceable.Ability.GetCanDeliver())
		ability.Serviceable.Ability.CanCodDeliver = utils.NewUint32(lineResult.ServiceableInfo.GetCanCodDeliver() & ability.Serviceable.Ability.GetCanCodDeliver())
		ability.Serviceable.Ability.SupportTradeIn = utils.NewUint32(lineResult.ServiceableInfo.GetCanTradeIn() & ability.Serviceable.Ability.GetSupportTradeIn())
		// 单独返回电子围栏结果，仅用于问题排查
		ability.Serviceable.Ability.PickupInEFence = utils.NewUint32(lineResult.ServiceableInfo.GetPickupInEFence() & ability.Serviceable.Ability.GetPickupInEFence())
		ability.Serviceable.Ability.DeliverInEFence = utils.NewUint32(lineResult.ServiceableInfo.GetDeliverInEFence() & ability.Serviceable.Ability.GetDeliverInEFence())
	}

	// 服务范围数据上报
	defer func() {
		for index, tmpLineResult := range results.GetServiceableRespList() {
			lineResult := tmpLineResult
			// 设置线服务范围全链路日志
			serviceAble := Logger.ServiceableResponse{
				CanPickup:     lineResult.ServiceableInfo.GetCanPickup(),
				CanCodPickup:  lineResult.ServiceableInfo.GetCanCodPickup(),
				CanDeliver:    lineResult.ServiceableInfo.GetCanDeliver(),
				CanCodDeliver: lineResult.ServiceableInfo.GetCanCodDeliver(),
			}
			baseInfo := Logger.BaseInfo{
				LineId:             requests[index].GetBaseInfo().GetLineId(),
				IsCheckOperation:   requests[index].GetBaseInfo().GetIsCheckOperation(),
				IsCheckBasic:       requests[index].GetBaseInfo().GetIsCheckBasic(),
				CollectType:        requests[index].GetBaseInfo().GetCollectType(),
				DeliverType:        requests[index].GetBaseInfo().GetDeliverType(),
				CheckSender:        requests[index].GetBaseInfo().GetCheckSender(),
				SenderCheckLevel:   requests[index].GetBaseInfo().GetSenderCheckLevel(),
				CheckReceiver:      requests[index].GetBaseInfo().GetCheckReceiver(),
				ReceiverCheckLevel: requests[index].GetBaseInfo().GetReceiverCheckLevel(),
				SkipPostcode:       requests[index].GetBaseInfo().GetSkipPostcode(),
				SkipZoneRoute:      requests[index].GetBaseInfo().GetSkipZoneRoute(),
			}
			tmpPickupInfo := Logger.Location{
				StateLocationId:    requests[index].GetPickupInfo().GetStateLocationId(),
				CityLocationId:     requests[index].GetPickupInfo().GetCityLocationId(),
				DistrictLocationId: requests[index].GetPickupInfo().GetDistrictLocationId(),
				StreetLocationId:   requests[index].GetPickupInfo().GetStreetLocationId(),
				Postcode:           requests[index].GetPickupInfo().GetPostcode(),
				Longitude:          requests[index].GetPickupInfo().GetLongitude(),
				Latitude:           requests[index].GetPickupInfo().GetLatitude(),
			}
			tmpDeliverInfo := Logger.Location{
				StateLocationId:    requests[index].GetDeliverInfo().GetStateLocationId(),
				CityLocationId:     requests[index].GetDeliverInfo().GetCityLocationId(),
				DistrictLocationId: requests[index].GetDeliverInfo().GetDistrictLocationId(),
				StreetLocationId:   requests[index].GetDeliverInfo().GetStreetLocationId(),
				Postcode:           requests[index].GetDeliverInfo().GetPostcode(),
				Longitude:          requests[index].GetDeliverInfo().GetLongitude(),
				Latitude:           requests[index].GetDeliverInfo().GetLatitude(),
			}
			// 上报线服务范围全链路日志
			ctx.SetLineServiceAble(laneInfo.LaneCode, lineResult.GetLineId(), baseInfo, tmpPickupInfo, tmpDeliverInfo, serviceAble)

			// 上报线服务范围到prometheus
			lineAbility := &pb.AreaServiceability{
				CanPickup:      utils.NewUint32(lineResult.ServiceableInfo.GetCanPickup()),
				CanCodPickup:   utils.NewUint32(lineResult.ServiceableInfo.GetCanCodPickup()),
				CanDeliver:     utils.NewUint32(lineResult.ServiceableInfo.GetCanDeliver()),
				CanCodDeliver:  utils.NewUint32(lineResult.ServiceableInfo.GetCanCodDeliver()),
				SupportTradeIn: utils.NewUint32(lineResult.ServiceableInfo.GetCanTradeIn()),
			}
			_ = serviceAbleReport(ctx, singleProductReq.GetProductId(), laneInfo.LaneCode, lineResult.GetLineId(), lineResult.GetItemCode(), lineAbility, singleProductReq.GetBasis().GetPaymentMethod())
		}

		serviceAble := Logger.ServiceableResponse{
			CanPickup:     ability.Serviceable.Ability.GetCanPickup(),
			CanCodPickup:  ability.Serviceable.Ability.GetCanCodPickup(),
			CanDeliver:    ability.Serviceable.Ability.GetCanDeliver(),
			CanCodDeliver: ability.Serviceable.Ability.GetCanCodDeliver(),
		}
		// 设置laneCode服务范围全链路日志
		ctx.SetLaneServiceAble(laneInfo.LaneCode, serviceAble)
	}()

	return ability
}

func (c *grpcSceneServiceableAreaController) getLinesAreaServiceableByRule(ctx utils.LCOSContext, lineMap map[string]*lfs_service.SingleModelRule, siteMap map[string]*lfs_service.SingleModelRule, actualPoints []*pb.ActualPoint, laneInfo *lfs_service.LaneCodeStruct, singleProductReq *pb.LaneAreaServiceabilityReq, header *pb.ReqHeader, isPdpCheck bool) (*pb.LaneAreaServiceable, *lcos_error.LCOSError) {
	requests, err := c.generateLineServiceableAreaReq(ctx, lineMap, siteMap, actualPoints, laneInfo, singleProductReq, isPdpCheck)
	if err != nil {
		return nil, err
	}

	// 请求lcos的接口
	lcosReq := &pb.BatchGetLineServiceableInfoRequest2{
		ReqHeader:          header,
		ServiceableReqList: requests,
	}
	// 存放lane的返回结果,服务范围初始化全部成功
	ability := &pb.LaneAreaServiceable{
		LaneCode:    &laneInfo.LaneCode,
		Serviceable: successAreaServiceable(),
	}

	if len(requests) == 0 {
		logger.CtxLogErrorf(ctx, "request is 0")
		// 异常情况，不太可能出现不包含线信息
		return ability, nil
	}

	results, _ := c.serviceableCheckerService.BatchGetServiceable(ctx, lcosReq)
	for _, tmpLineResult := range results.GetServiceableRespList() {
		lineResult := tmpLineResult

		if lineResult.GetItemCode() != lcos_error.SuccessCode {
			failedMsg := fmt.Sprintf("serviceable area get fail. lane_code=%s, line=%s, code=%d, err_msg=%s", laneInfo.LaneCode, lineResult.GetLineId(), lineResult.GetItemCode(), lineResult.GetMessage())
			ability.Serviceable = &pb.AreaServiceable{
				Code:    lineResult.ItemCode,
				Message: &failedMsg,
			}
			break
		}

		// 只要有一条线为false，lane整体返回false
		ability.Serviceable.Ability.CanPickup = utils.NewUint32(lineResult.ServiceableInfo.GetCanPickup() & ability.Serviceable.Ability.GetCanPickup())
		ability.Serviceable.Ability.CanCodPickup = utils.NewUint32(lineResult.ServiceableInfo.GetCanCodPickup() & ability.Serviceable.Ability.GetCanCodPickup())
		ability.Serviceable.Ability.CanDeliver = utils.NewUint32(lineResult.ServiceableInfo.GetCanDeliver() & ability.Serviceable.Ability.GetCanDeliver())
		ability.Serviceable.Ability.CanCodDeliver = utils.NewUint32(lineResult.ServiceableInfo.GetCanCodDeliver() & ability.Serviceable.Ability.GetCanCodDeliver())
		ability.Serviceable.Ability.SupportTradeIn = utils.NewUint32(lineResult.ServiceableInfo.GetCanTradeIn() & ability.Serviceable.Ability.GetSupportTradeIn())
		// 单独返回电子围栏结果，仅用于问题排查
		ability.Serviceable.Ability.PickupInEFence = utils.NewUint32(lineResult.ServiceableInfo.GetPickupInEFence() & ability.Serviceable.Ability.GetPickupInEFence())
		ability.Serviceable.Ability.DeliverInEFence = utils.NewUint32(lineResult.ServiceableInfo.GetDeliverInEFence() & ability.Serviceable.Ability.GetDeliverInEFence())

	}

	// 服务范围数据上报
	defer func() {
		for index, tmpLineResult := range results.GetServiceableRespList() {
			lineResult := tmpLineResult
			// 设置线服务范围全链路日志
			serviceAble := Logger.ServiceableResponse{
				CanPickup:     lineResult.ServiceableInfo.GetCanPickup(),
				CanCodPickup:  lineResult.ServiceableInfo.GetCanCodPickup(),
				CanDeliver:    lineResult.ServiceableInfo.GetCanDeliver(),
				CanCodDeliver: lineResult.ServiceableInfo.GetCanCodDeliver(),
			}
			baseInfo := Logger.BaseInfo{
				LineId:             requests[index].GetBaseInfo().GetLineId(),
				IsCheckOperation:   requests[index].GetBaseInfo().GetIsCheckOperation(),
				IsCheckBasic:       requests[index].GetBaseInfo().GetIsCheckBasic(),
				CollectType:        requests[index].GetBaseInfo().GetCollectType(),
				DeliverType:        requests[index].GetBaseInfo().GetDeliverType(),
				CheckSender:        requests[index].GetBaseInfo().GetCheckSender(),
				SenderCheckLevel:   requests[index].GetBaseInfo().GetSenderCheckLevel(),
				CheckReceiver:      requests[index].GetBaseInfo().GetCheckReceiver(),
				ReceiverCheckLevel: requests[index].GetBaseInfo().GetReceiverCheckLevel(),
				SkipPostcode:       requests[index].GetBaseInfo().GetSkipPostcode(),
				SkipZoneRoute:      requests[index].GetBaseInfo().GetSkipZoneRoute(),
			}
			pickupInfo := Logger.Location{
				StateLocationId:    requests[index].GetPickupInfo().GetStateLocationId(),
				CityLocationId:     requests[index].GetPickupInfo().GetCityLocationId(),
				DistrictLocationId: requests[index].GetPickupInfo().GetDistrictLocationId(),
				StreetLocationId:   requests[index].GetPickupInfo().GetStreetLocationId(),
				Postcode:           requests[index].GetPickupInfo().GetPostcode(),
				Longitude:          requests[index].GetPickupInfo().GetLongitude(),
				Latitude:           requests[index].GetPickupInfo().GetLatitude(),
			}
			deliverInfo := Logger.Location{
				StateLocationId:    requests[index].GetDeliverInfo().GetStateLocationId(),
				CityLocationId:     requests[index].GetDeliverInfo().GetCityLocationId(),
				DistrictLocationId: requests[index].GetDeliverInfo().GetDistrictLocationId(),
				StreetLocationId:   requests[index].GetDeliverInfo().GetStreetLocationId(),
				Postcode:           requests[index].GetDeliverInfo().GetPostcode(),
				Longitude:          requests[index].GetDeliverInfo().GetLongitude(),
				Latitude:           requests[index].GetDeliverInfo().GetLatitude(),
			}
			// 上报线服务范围全链路日志
			ctx.SetLineServiceAble(laneInfo.LaneCode, lineResult.GetLineId(), baseInfo, pickupInfo, deliverInfo, serviceAble)

			// 上报线服务范围到prometheus
			lineAbility := &pb.AreaServiceability{
				CanPickup:      utils.NewUint32(lineResult.ServiceableInfo.GetCanPickup()),
				CanCodPickup:   utils.NewUint32(lineResult.ServiceableInfo.GetCanCodPickup()),
				CanDeliver:     utils.NewUint32(lineResult.ServiceableInfo.GetCanDeliver()),
				CanCodDeliver:  utils.NewUint32(lineResult.ServiceableInfo.GetCanCodDeliver()),
				SupportTradeIn: utils.NewUint32(lineResult.ServiceableInfo.GetCanTradeIn()),
			}
			_ = serviceAbleReport(ctx, singleProductReq.GetProductId(), laneInfo.LaneCode, lineResult.GetLineId(), lineResult.GetItemCode(), lineAbility, singleProductReq.GetBasis().GetPaymentMethod())
		}

		serviceAble := Logger.ServiceableResponse{
			CanPickup:     ability.Serviceable.Ability.GetCanPickup(),
			CanCodPickup:  ability.Serviceable.Ability.GetCanCodPickup(),
			CanDeliver:    ability.Serviceable.Ability.GetCanDeliver(),
			CanCodDeliver: ability.Serviceable.Ability.GetCanCodDeliver(),
		}

		// 设置laneCode服务范围全链路日志
		ctx.SetLaneServiceAble(laneInfo.LaneCode, serviceAble)
	}()

	return ability, nil
}

func (c *grpcSceneServiceableAreaController) generateLineServiceableAreaReq(ctx utils.LCOSContext, lineMap map[string]*lfs_service.SingleModelRule, siteMap map[string]*lfs_service.SingleModelRule, actualPoints []*pb.ActualPoint, laneInfo *lfs_service.LaneCodeStruct, singleProductReq *pb.LaneAreaServiceabilityReq, isPdpCheck bool) ([]*pb.SingleGetServiceableRequest2, *lcos_error.LCOSError) {
	var needCheck bool
	// pdp的校验逻辑不需要关注点服务范围，也就不需要做三段式替换
	if !isPdpCheck {
		for _, site := range siteMap {
			if site.IsNextResourceSACheck() || site.IsPreResourceSACheck() {
				needCheck = true
				break
			}
		}
	}

	if needCheck && len(actualPoints) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCheckFail, "need actual point info but no available actual points")
	}
	var actualPointInfoMap = make(map[string]*llspb.ActualPointInfo)
	var err *lcos_error.LCOSError
	if needCheck && len(actualPoints) > 0 {
		actualPointIDList := make([]string, 0, len(actualPoints))
		for _, ap := range actualPoints {
			actualPointIDList = append(actualPointIDList, ap.GetPointId())
		}
		// 去重
		actualPointIDList = utils.RemoveDuplicateElement(actualPointIDList)

		actualPointInfoMap, err = lls_service.GetActualPointsMapWithGreySwitch(ctx, actualPointIDList)
		if err != nil {
			return nil, err
		}
		logger.CtxLogInfof(ctx, "actual point info fetch from lls:%v", actualPointInfoMap)
	}
	requestList := make([]*pb.SingleGetServiceableRequest2, 0, len(lineMap))

	pickupInfo := serviceableAddress2LocationInfo(singleProductReq.GetPickupAddr())
	deliverInfo := serviceableAddress2LocationInfo(singleProductReq.GetDeliverAddr())
	handoverPointInfo := serviceableAddress2LocationInfo(singleProductReq.GetHandoverPointAddr())
	for lineID, line := range lineMap {
		singleReq := &pb.SingleGetServiceableRequest2{
			BaseInfo:    serviceableBasis2LcosRuleBaseInfo(lineID, line, singleProductReq.Basis),
			PickupInfo:  pickupInfo,
			DeliverInfo: deliverInfo,
		}
		// 非pdp场景才需要这一段替换逻辑
		if !isPdpCheck {
			// handover point地址替换逻辑
			if line.CheckSenderAddrType == constant.AddrTypeHandoverPoint {
				singleReq.PickupInfo = handoverPointInfo
			}
			if line.CheckReceiverAddrType == constant.AddrTypeHandoverPoint {
				singleReq.DeliverInfo = handoverPointInfo
			}

			// actual point地址替换逻辑
			var pickupApId, deliverApId string
			// 这里赋值了两个没有用的字段，用于判断是否被实际点的地址替换，而不是重新再去找对应的line。
			if line.PreResourceSACheckFlag != 0 {
				for _, actualPoint := range actualPoints {
					if actualPoint.GetPointType() == constant.SiteSortingPointTypeOut || actualPoint.GetPointType() == constant.SiteSortingPointTypeAll {
						pickupApId = actualPoint.GetPointId()
					}
				}
			}
			if line.NextResourceSACheckFlag != 0 {
				for _, actualPoint := range actualPoints {
					if actualPoint.GetPointType() == constant.SiteSortingPointTypeIn || actualPoint.GetPointType() == constant.SiteSortingPointTypeAll {
						deliverApId = actualPoint.GetPointId()
					}
				}
			}
			// 将点的地址信息替换入参的请求地址信息
			if actualPointInfo, ok := actualPointInfoMap[pickupApId]; ok && pickupApId != "" {
				newPickupInfo := actualPointAddress2LocationInfo(actualPointInfo)
				singleReq.BaseInfo.CheckSender = utils.NewUint32(1)
				singleReq.BaseInfo.SenderCheckLevel = utils.NewUint32(uint32(singleProductReq.GetBasis().GetSenderCheckLevel()))
				logger.CtxLogInfof(ctx, "generateLineServiceableAreaReq, originPickupInfo=%v,newPickupInfo=%v", Logger.JsonStringForDebugLog(ctx, singleReq.PickupInfo), Logger.JsonStringForDebugLog(ctx, newPickupInfo))
				singleReq.PickupInfo = newPickupInfo
			}
			// 将点的地址信息替换入参的请求地址信息
			if actualPointInfo, ok := actualPointInfoMap[deliverApId]; ok && deliverApId != "" {
				newDeliverInfo := actualPointAddress2LocationInfo(actualPointInfo)
				singleReq.BaseInfo.CheckReceiver = utils.NewUint32(1)
				singleReq.BaseInfo.ReceiverCheckLevel = utils.NewUint32(uint32(singleProductReq.GetBasis().GetReceiverCheckLevel()))
				logger.CtxLogInfof(ctx, "generateLineServiceableAreaReq, originDeliverInfo=%v,newDeliverInfo=%v", Logger.JsonStringForDebugLog(ctx, singleReq.DeliverInfo), Logger.JsonStringForDebugLog(ctx, newDeliverInfo))
				singleReq.DeliverInfo = newDeliverInfo
			}
		}

		//logger.CtxLogInfof(ctx, "line serviceable area req|lane_code=%s ,line_id=%s, pickup:%v, deliver:%v, collect_type:%d, deliver_type:%d", laneInfo.LaneCode, lineID, singleReq.GetPickupInfo(), singleReq.GetDeliverInfo(), singleReq.GetBaseInfo().GetCollectType(), singleReq.GetBaseInfo().GetDeliverType())
		requestList = append(requestList, singleReq)
	}

	return requestList, nil
}

func (c *grpcSceneServiceableAreaController) callLcosSitesAreaServiceable(ctx utils.LCOSContext, site *lfs_service.SingleCompose, singleReq *siteservice.GetSiteServiceableAreaBasicRequest, pointType int32) ([]*pb.ActualPoint, *lcos_error.LCOSError) {
	actualPointList, lcosErr := c.siteServiceableAreaService.GetSiteServiceableAreaList(ctx, singleReq)
	if lcosErr != nil {
		errMsg := fmt.Sprintf("get ssa error, error=%s|site_id=%s, state_location_id=%d, city_location_id=%d, district_location_id=%d, street_location_id=%d, zipcode=%s", lcosErr.Msg, singleReq.SiteID, singleReq.StateLocationID, singleReq.CityLocationID, singleReq.DistrictLocationID, singleReq.StreetLocationID, singleReq.Zipcode)
		logger.CtxLogErrorf(ctx, errMsg)
		return nil, lcosErr
	}
	siteSAResult := make([]*pb.ActualPoint, 0, len(actualPointList))
	for _, actualPoint := range actualPointList {
		siteSAResult = append(siteSAResult, &pb.ActualPoint{
			SiteId:      utils.NewString(site.ResourceID),
			PointId:     utils.NewString(actualPoint),
			SiteSubType: utils.NewInt32(int32(site.SubType)),
			PointType:   utils.NewInt32(pointType),
		})
	}

	return siteSAResult, nil
}

func (c *grpcSceneServiceableAreaController) getSitesAreaServiceable(ctx utils.LCOSContext, siteList []*lfs_service.SingleCompose, laneInfo *lfs_service.LaneCodeStruct, singleProductReq *pb.LaneAreaServiceabilityReq, header *pb.ReqHeader) ([]*pb.ActualPoint, *lcos_error.LCOSError) {
	pickupInfo := serviceableAddress2LocationInfo(singleProductReq.GetPickupAddr())
	deliverInfo := serviceableAddress2LocationInfo(singleProductReq.GetDeliverAddr())
	siteSAResult := make([]*pb.ActualPoint, 0)
	for _, site := range siteList {
		var pointType int32
		var loc *pb.LocationInfo
		rule := site.GetRule(laneInfo.ModelRules)
		newDeliverInfo, newPickupInfo := replaceLocationInfo(ctx, rule, deliverInfo, pickupInfo)

		if rule.IsSortingAsPreDeliver() {
			loc = newPickupInfo
			pointType = constant.SiteSortingPointTypeIn
			singleReq := serviceableBasis2SiteSAReq(site, singleProductReq.GetBasis(), loc)
			apList, err := c.callLcosSitesAreaServiceable(ctx, site, singleReq, pointType)
			if err != nil {
				return nil, err
			}
			siteSAResult = append(siteSAResult, apList...)
		}
		if rule.IsSortingAsNextPickup() {
			loc = newDeliverInfo
			pointType = constant.SiteSortingPointTypeOut
			singleReq := serviceableBasis2SiteSAReq(site, singleProductReq.GetBasis(), loc)
			apList, err := c.callLcosSitesAreaServiceable(ctx, site, singleReq, pointType)
			if err != nil {
				return nil, err
			}
			siteSAResult = append(siteSAResult, apList...)
		}
	}

	// 设置点服务范围全链路日志
	defer func() {
		actualPointList := make([]Logger.ActualPoint, 0, len(siteSAResult))
		for _, actualPoint := range siteSAResult {
			tmpActualPoint := Logger.ActualPoint{
				SiteId:      actualPoint.GetSiteId(),
				PointId:     actualPoint.GetPointId(),
				PointType:   actualPoint.GetPointType(),
				SiteSubType: actualPoint.GetSiteSubType(),
			}
			actualPointList = append(actualPointList, tmpActualPoint)
		}
		ctx.SetSiteServiceAble(laneInfo.LaneCode, actualPointList)
	}()

	return siteSAResult, nil
}

func (c *grpcSceneServiceableAreaController) getSitesAreaServiceableByRule(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct, siteMap map[string]*lfs_service.SingleModelRule, singleProductReq *pb.LaneAreaServiceabilityReq) ([]*pb.ActualPoint, *lcos_error.LCOSError) {
	pickupInfo := serviceableAddress2LocationInfo(singleProductReq.GetPickupAddr())
	deliverInfo := serviceableAddress2LocationInfo(singleProductReq.GetDeliverAddr())
	siteSAResult := make([]*pb.ActualPoint, 0, len(siteMap))
	for siteID, rule := range siteMap {
		var pointType int32
		var loc *pb.LocationInfo
		// 可能会替换地址信息
		newDeliverInfo, newPickupInfo := replaceLocationInfo(ctx, rule, deliverInfo, pickupInfo)
		skipPostcode := singleProductReq.GetBasis().GetCheckPostalCode() ^ uint32(constant.TRUE)
		singleReq := &siteservice.GetSiteServiceableAreaBasicRequest{
			SiteID:             siteID,
			StateLocationID:    int(loc.GetStateLocationId()),
			CityLocationID:     int(loc.GetCityLocationId()),
			DistrictLocationID: int(loc.GetDistrictLocationId()),
			StreetLocationID:   int(loc.GetStreetLocationId()),
			Zipcode:            loc.GetPostcode(),
			SkipPostcode:       int(skipPostcode),
		}
		site := &lfs_service.SingleCompose{
			ResourceID: siteID,
			SubType:    rule.SubType,
		}
		if rule.IsSortingAsPreDeliver() {
			loc = newPickupInfo
			pointType = constant.SiteSortingPointTypeIn
			singleReq.StateLocationID = int(loc.GetStateLocationId())
			singleReq.CityLocationID = int(loc.GetCityLocationId())
			singleReq.DistrictLocationID = int(loc.GetDistrictLocationId())
			singleReq.StreetLocationID = int(loc.GetStreetLocationId())
			singleReq.Zipcode = loc.GetPostcode()
			apList, err := c.callLcosSitesAreaServiceable(ctx, site, singleReq, pointType)
			if err != nil {
				return nil, err
			}
			siteSAResult = append(siteSAResult, apList...)
		}
		if rule.IsSortingAsNextPickup() {
			loc = newDeliverInfo
			pointType = constant.SiteSortingPointTypeOut
			singleReq.StateLocationID = int(loc.GetStateLocationId())
			singleReq.CityLocationID = int(loc.GetCityLocationId())
			singleReq.DistrictLocationID = int(loc.GetDistrictLocationId())
			singleReq.StreetLocationID = int(loc.GetStreetLocationId())
			singleReq.Zipcode = loc.GetPostcode()
			apList, err := c.callLcosSitesAreaServiceable(ctx, site, singleReq, pointType)
			if err != nil {
				return nil, err
			}
			siteSAResult = append(siteSAResult, apList...)
		}
	}

	// 设置点服务范围全链路日志
	defer func() {
		actualPointList := make([]Logger.ActualPoint, 0, len(siteSAResult))
		for _, actualPoint := range siteSAResult {
			tmpActualPoint := Logger.ActualPoint{
				SiteId:      actualPoint.GetSiteId(),
				PointId:     actualPoint.GetPointId(),
				PointType:   actualPoint.GetPointType(),
				SiteSubType: actualPoint.GetSiteSubType(),
			}
			actualPointList = append(actualPointList, tmpActualPoint)
		}
		ctx.SetSiteServiceAble(laneInfo.LaneCode, actualPointList)
	}()

	return siteSAResult, nil
}

func (c *grpcSceneServiceableAreaController) getCheckWithActualPointsReq(ctx utils.LCOSContext, lines []*lfs_service.SingleCompose, actualPoints []*pb.ActualPoint, laneInfo *lfs_service.LaneCodeStruct, singleProductReq *pb.LaneAreaServiceabilityReq) ([]*pb.SingleGetServiceableRequest2, *lcos_error.LCOSError) {
	// TODO define return type & val
	if len(actualPoints) <= 0 {
		return nil, nil
	}
	needCheck := false
	var needCheckSiteList []*lfs_service.SingleCompose
	for _, resource := range laneInfo.Composes {
		if resource.IsLine() {
			continue
		}
		rule := resource.GetRule(laneInfo.ModelRules)
		if rule == nil {
			continue
		}
		if rule.IsNextResourceSACheck() || rule.IsPreResourceSACheck() {
			needCheck = true
			needCheckSiteList = append(needCheckSiteList, resource)
		}
	}
	if !needCheck || len(needCheckSiteList) <= 0 {
		return nil, nil
	}
	var actualPointIDList []string
	for _, ap := range actualPoints {
		actualPointIDList = append(actualPointIDList, ap.GetPointId())
	}
	// 去重
	actualPointIDList = utils.RemoveDuplicateElement(actualPointIDList)

	actualPointInfoMap, err := lls_service.GetActualPointsMapWithGreySwitch(ctx, actualPointIDList)
	if err != nil {
		return nil, err
	}
	if len(actualPointInfoMap) <= 0 {
		return nil, nil
	}

	// build line sa req
	pickupInfo := serviceableAddress2LocationInfo(singleProductReq.GetPickupAddr())
	deliverInfo := serviceableAddress2LocationInfo(singleProductReq.GetDeliverAddr())
	var requests []*pb.SingleGetServiceableRequest2

	for _, site := range needCheckSiteList {
		preLine, nextLine := site.GetPreLineAndNextLine(laneInfo.Composes, laneInfo.ModelRules)
		var pickupApId, deliverApId string
		for _, actualPoint := range actualPoints {
			if nextLine != nil {
				if actualPoint.GetPointType() == constant.SiteSortingPointTypeOut || actualPoint.GetPointType() == constant.SiteSortingPointTypeAll {
					pickupApId = actualPoint.GetPointId()
				}
			}
			if preLine != nil {
				if actualPoint.GetPointType() == constant.SiteSortingPointTypeIn || actualPoint.GetPointType() == constant.SiteSortingPointTypeAll {
					deliverApId = actualPoint.GetPointId()
				}
			}
		}

		if actualPointInfo, ok := actualPointInfoMap[pickupApId]; ok && pickupApId != "" {
			newPickupInfo := actualPointAddress2LocationInfo(actualPointInfo)
			req := &pb.SingleGetServiceableRequest2{
				BaseInfo:    serviceableBasis2LcosGetBaseInfo(nextLine, singleProductReq.Basis),
				PickupInfo:  newPickupInfo,
				DeliverInfo: deliverInfo,
			}
			req.BaseInfo.CheckSender = utils.NewUint32(1)
			req.BaseInfo.SenderCheckLevel = utils.NewUint32(uint32(singleProductReq.GetBasis().GetSenderCheckLevel()))
			requests = append(requests, req)
			logger.CtxLogInfof(ctx, "line serviceable area(BR)|next_line_id=%s, pickup:%v, deliver:%v", nextLine.ResourceID, newPickupInfo, deliverInfo)
		}
		if actualPointInfo, ok := actualPointInfoMap[deliverApId]; ok && deliverApId != "" {
			newDeliverInfo := actualPointAddress2LocationInfo(actualPointInfo)
			req := &pb.SingleGetServiceableRequest2{
				BaseInfo:    serviceableBasis2LcosGetBaseInfo(preLine, singleProductReq.Basis),
				PickupInfo:  pickupInfo,
				DeliverInfo: newDeliverInfo,
			}
			req.BaseInfo.CheckReceiver = utils.NewUint32(1)
			req.BaseInfo.ReceiverCheckLevel = utils.NewUint32(uint32(singleProductReq.GetBasis().GetReceiverCheckLevel()))
			requests = append(requests, req)
			logger.CtxLogInfof(ctx, "line serviceable area(BR)|pre_line_id=%s, pickup:%v, deliver:%v", preLine.ResourceID, pickupInfo, newDeliverInfo)
		}
	}
	return requests, nil
}

func (c *grpcSceneServiceableAreaController) getLineServiceableArea(ctx utils.LCOSContext, lines []*lfs_service.SingleCompose, actualPoints []*pb.ActualPoint, laneInfo *lfs_service.LaneCodeStruct, singleProductReq *pb.LaneAreaServiceabilityReq, header *pb.ReqHeader) (*pb.LaneAreaServiceable, *lcos_error.LCOSError) {
	requests, lcosErr := c.getCheckWithActualPointsReq(ctx, lines, actualPoints, laneInfo, singleProductReq)
	if lcosErr != nil {
		return nil, lcosErr
	}
	// if no need to check BR line SA, return nil
	if len(requests) <= 0 {
		return nil, nil
	}

	lcosReq := &pb.BatchGetLineServiceableInfoRequest2{
		ReqHeader:          header,
		ServiceableReqList: requests,
	}
	// 存放lane的返回结果
	ability := &pb.LaneAreaServiceable{
		LaneCode:    &laneInfo.LaneCode,
		Serviceable: successAreaServiceable(),
	}
	results, _ := c.serviceableCheckerService.BatchGetServiceable(ctx, lcosReq)
	for _, lineResult := range results.GetServiceableRespList() {
		if lineResult.GetItemCode() != 0 {
			failedMsg := fmt.Sprintf("serviceable area get fail. line=%s, code=%d, err_msg=%s", lineResult.GetLineId(), lineResult.GetItemCode(), lineResult.GetMessage())
			ability.Serviceable = &pb.AreaServiceable{
				Code:    lineResult.ItemCode,
				Message: &failedMsg,
			}
			break
		}

		// 只要有一个能力为false，就整体返回false
		ability.Serviceable.Ability.CanPickup = utils.NewUint32(lineResult.ServiceableInfo.GetCanPickup() & ability.Serviceable.Ability.GetCanPickup())
		ability.Serviceable.Ability.CanCodPickup = utils.NewUint32(lineResult.ServiceableInfo.GetCanCodPickup() & ability.Serviceable.Ability.GetCanCodPickup())
		ability.Serviceable.Ability.CanDeliver = utils.NewUint32(lineResult.ServiceableInfo.GetCanDeliver() & ability.Serviceable.Ability.GetCanDeliver())
		ability.Serviceable.Ability.CanCodDeliver = utils.NewUint32(lineResult.ServiceableInfo.GetCanCodDeliver() & ability.Serviceable.Ability.GetCanCodDeliver())
		ability.Serviceable.Ability.SupportTradeIn = utils.NewUint32(lineResult.ServiceableInfo.GetCanTradeIn() & ability.Serviceable.Ability.GetSupportTradeIn())
		// 单独返回电子围栏结果，仅用于问题排查
		ability.Serviceable.Ability.PickupInEFence = utils.NewUint32(lineResult.ServiceableInfo.GetPickupInEFence() & ability.Serviceable.Ability.GetPickupInEFence())
		ability.Serviceable.Ability.DeliverInEFence = utils.NewUint32(lineResult.ServiceableInfo.GetDeliverInEFence() & ability.Serviceable.Ability.GetDeliverInEFence())
	}
	return ability, nil
}

// 获取lane的服务范围
func (c *grpcSceneServiceableAreaController) getLaneAreaServiceable(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct, req *pb.LaneAreaServiceabilityReq, header *pb.ReqHeader) (*pb.LaneAreaServiceable, *lcos_error.LCOSError) {
	// 获取相关lines
	lines, lcosErr := getLanePossibleAreaServiceableLines(ctx, laneInfo)
	if lcosErr != nil {
		return nil, lcosErr
	}
	// 获取lines相关服务范围
	return c.getLinesAreaServiceable(ctx, lines, laneInfo, req, header), nil
}

func isServiceAreaSuccess(ability *lcos_protobuf.AreaServiceability, paymentMethod lcos_protobuf.PaymentMethodEnum) int {
	if paymentMethod == lcos_protobuf.PaymentMethodEnum_COD && ability != nil && *ability.CanCodPickup == 1 && *ability.CanCodDeliver == 1 {
		return 0
	}

	if paymentMethod == lcos_protobuf.PaymentMethodEnum_STANDARD && ability != nil && *ability.CanPickup == 1 && *ability.CanDeliver == 1 {
		return 0
	}

	return 1
}

// this function match v2 api, contains both line SA & siteservice SA checker
func (c *grpcSceneServiceableAreaController) getLaneAreaServiceableV2(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct, req *pb.LaneAreaServiceabilityReq, header *pb.ReqHeader) (*pb.LaneAreaServiceable, *lcos_error.LCOSError) {
	// 获取相关lines
	lines, lcosErr := getLanePossibleAreaServiceableLines(ctx, laneInfo)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 获取lines相关服务范围
	laneAreaRes := c.getLinesAreaServiceable(ctx, lines, laneInfo, req, header)
	logger.CtxLogInfof(ctx, "line serviceable area|lane:%v, retCode:%v, retMsg:%v, ability:%v", laneAreaRes.GetLaneCode(), laneAreaRes.GetServiceable().GetCode(), laneAreaRes.GetServiceable().GetMessage(), laneAreaRes.GetServiceable().GetAbility())
	if laneAreaRes.GetServiceable().GetCode() != lcos_error.SuccessCode {
		return laneAreaRes, nil
	}

	// 获取点
	sites := laneInfo.GetSite()

	// 如果有需要校验的点，调用点服务范围校验。
	if len(sites) > 0 {
		actualSiteList, lcosErr := c.getSitesAreaServiceable(ctx, sites, laneInfo, req, header)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "site serviceable area check failed|lane_code=%v, err:%v", laneInfo.LaneCode, lcosErr)
			laneAreaRes.Serviceable.Code = utils.NewInt32(lcosErr.RetCode)
			laneAreaRes.Serviceable.Message = utils.NewString(lcosErr.Msg)
			return laneAreaRes, nil
		}
		if len(actualSiteList) <= 0 {
			errMsg := fmt.Sprintf("site serviceable area check failed|lane_code=%v, number of sites:%d", laneInfo.LaneCode, len(sites))
			logger.CtxLogErrorf(ctx, errMsg)
			laneAreaRes.Serviceable.Code = utils.NewInt32(lcos_error.SiteServiceableAreaCheckFail)
			laneAreaRes.Serviceable.Message = utils.NewString(errMsg)
			return laneAreaRes, nil
		}
		logger.CtxLogInfof(ctx, "lane_code=%s, site sa result:%v", laneInfo.LaneCode, actualSiteList)
		laneAreaRes.Serviceable.ActualPoints = actualSiteList
	}

	// BR三段式校验
	if len(laneAreaRes.Serviceable.ActualPoints) > 0 {
		brLaneAreaRes, err := c.getLineServiceableArea(ctx, lines, laneAreaRes.Serviceable.ActualPoints, laneInfo, req, header)
		if err == nil && brLaneAreaRes != nil {
			logger.CtxLogInfof(ctx, "line serviceable area (BR) |lane:%v, retCode:%v, retMsg:%v, ability:%v", brLaneAreaRes.GetLaneCode(), brLaneAreaRes.GetServiceable().GetCode(), brLaneAreaRes.GetServiceable().GetMessage(), brLaneAreaRes.GetServiceable().GetAbility())
			// 如果校验完成了，用结果覆盖之前的线服务范围校验结果
			laneAreaRes.Serviceable.Code = brLaneAreaRes.Serviceable.Code
			laneAreaRes.Serviceable.Message = brLaneAreaRes.Serviceable.Message
			if laneAreaRes.Serviceable.GetAbility() != nil && brLaneAreaRes.Serviceable.GetAbility() != nil {
				laneAreaRes.Serviceable.Ability.CanPickup = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetCanPickup() & brLaneAreaRes.Serviceable.GetAbility().GetCanPickup())
				laneAreaRes.Serviceable.Ability.CanDeliver = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetCanDeliver() & brLaneAreaRes.Serviceable.GetAbility().GetCanDeliver())
				laneAreaRes.Serviceable.Ability.CanCodPickup = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetCanCodPickup() & brLaneAreaRes.Serviceable.GetAbility().GetCanCodPickup())
				laneAreaRes.Serviceable.Ability.CanCodDeliver = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetCanCodDeliver() & brLaneAreaRes.Serviceable.GetAbility().GetCanCodDeliver())
				laneAreaRes.Serviceable.Ability.SupportTradeIn = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetSupportTradeIn() & brLaneAreaRes.Serviceable.GetAbility().GetSupportTradeIn())
				laneAreaRes.Serviceable.Ability.PickupInEFence = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetPickupInEFence() & brLaneAreaRes.Serviceable.GetAbility().GetCanCodPickup())
				laneAreaRes.Serviceable.Ability.DeliverInEFence = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetDeliverInEFence() & brLaneAreaRes.Serviceable.GetAbility().GetDeliverInEFence())
			}

		} else if err != nil {
			logger.CtxLogErrorf(ctx, fmt.Sprintf("line serviceable area (BR) failed | lane:%v, err:%v, msg:%v", laneInfo.LaneCode, err, err.Msg))
			laneAreaRes.Serviceable.Code = proto.Int32(err.RetCode)
			laneAreaRes.Serviceable.Message = proto.String(err.Msg)
		}
	}
	return laneAreaRes, nil
}

func (c *grpcSceneServiceableAreaController) getLaneAreaServiceableFulfillment(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct, req *pb.LaneAreaServiceabilityReq, header *pb.ReqHeader) (*pb.LaneAreaServiceable, *lcos_error.LCOSError) {
	// 获取相关lines
	lines, lcosErr := getLaneAreaServiceableLinesFulfillment(ctx, laneInfo)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 获取lines相关服务范围
	laneAreaRes := c.getLinesAreaServiceable(ctx, lines, laneInfo, req, header)
	logger.CtxLogInfof(ctx, "line serviceable area|lane:%v, retCode:%v, retMsg:%v, ability:%v", laneAreaRes.GetLaneCode(), laneAreaRes.GetServiceable().GetCode(), laneAreaRes.GetServiceable().GetMessage(), laneAreaRes.GetServiceable().GetAbility())
	if laneAreaRes.GetServiceable().GetCode() != lcos_error.SuccessCode {
		return laneAreaRes, nil
	}

	// 获取点
	sites := laneInfo.GetSite()

	// 如果有需要校验的点，调用点服务范围校验。
	if len(sites) > 0 {
		actualSiteList, lcosErr := c.getSitesAreaServiceable(ctx, sites, laneInfo, req, header)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "site serviceable area check failed|lane=%v, err:%v", laneInfo.LaneCode, lcosErr)
			laneAreaRes.Serviceable.Code = utils.NewInt32(lcosErr.RetCode)
			laneAreaRes.Serviceable.Message = utils.NewString(lcosErr.Msg)
			return laneAreaRes, nil
		}
		if len(actualSiteList) <= 0 {
			errMsg := fmt.Sprintf("site serviceable area check failed|lane=%v, siteList:%v", laneInfo.LaneCode, sites)
			logger.CtxLogErrorf(ctx, errMsg)
			laneAreaRes.Serviceable.Code = utils.NewInt32(lcos_error.SiteServiceableAreaCheckFail)
			laneAreaRes.Serviceable.Message = utils.NewString(errMsg)
			return laneAreaRes, nil
		}
		logger.CtxLogInfof(ctx, "lane_code=%s, site sa result:%v", laneInfo.LaneCode, actualSiteList)
		laneAreaRes.Serviceable.ActualPoints = actualSiteList
	}

	// BR三段式校验
	if len(laneAreaRes.Serviceable.ActualPoints) > 0 {
		brLaneAreaRes, err := c.getLineServiceableArea(ctx, lines, laneAreaRes.Serviceable.ActualPoints, laneInfo, req, header)
		if err == nil && brLaneAreaRes != nil {
			logger.CtxLogInfof(ctx, "line serviceable area (BR) |lane:%v, retCode:%v, retMsg:%v, ability:%v", brLaneAreaRes.GetLaneCode(), brLaneAreaRes.GetServiceable().GetCode(), brLaneAreaRes.GetServiceable().GetMessage(), brLaneAreaRes.GetServiceable().GetAbility())
			// 如果校验完成了，用结果覆盖之前的线服务范围校验结果
			laneAreaRes.Serviceable.Code = brLaneAreaRes.Serviceable.Code
			laneAreaRes.Serviceable.Message = brLaneAreaRes.Serviceable.Message
			if laneAreaRes.Serviceable.GetAbility() != nil && brLaneAreaRes.Serviceable.GetAbility() != nil {
				laneAreaRes.Serviceable.Ability.CanPickup = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetCanPickup() & brLaneAreaRes.Serviceable.GetAbility().GetCanPickup())
				laneAreaRes.Serviceable.Ability.CanDeliver = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetCanDeliver() & brLaneAreaRes.Serviceable.GetAbility().GetCanDeliver())
				laneAreaRes.Serviceable.Ability.CanCodPickup = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetCanCodPickup() & brLaneAreaRes.Serviceable.GetAbility().GetCanCodPickup())
				laneAreaRes.Serviceable.Ability.CanCodDeliver = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetCanCodDeliver() & brLaneAreaRes.Serviceable.GetAbility().GetCanCodDeliver())
				laneAreaRes.Serviceable.Ability.SupportTradeIn = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetSupportTradeIn() & brLaneAreaRes.Serviceable.GetAbility().GetSupportTradeIn())
				laneAreaRes.Serviceable.Ability.PickupInEFence = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetPickupInEFence() & brLaneAreaRes.Serviceable.GetAbility().GetPickupInEFence())
				laneAreaRes.Serviceable.Ability.DeliverInEFence = utils.NewUint32(laneAreaRes.Serviceable.GetAbility().GetDeliverInEFence() & brLaneAreaRes.Serviceable.GetAbility().GetDeliverInEFence())
			}

		} else if err != nil {
			logger.CtxLogErrorf(ctx, fmt.Sprintf("line serviceable area (BR) failed | lane:%v, err:%v, msg:%v", laneInfo.LaneCode, err, err.Msg))
			laneAreaRes.Serviceable.Code = proto.Int32(err.RetCode)
			laneAreaRes.Serviceable.Message = proto.String(err.Msg)
		}
	}
	return laneAreaRes, nil
}

func (c *grpcSceneServiceableAreaController) getResourcesWithCheckFlag(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct, singleProductReq *pb.LaneAreaServiceabilityReq) (map[string]*lfs_service.SingleModelRule, map[string]*lfs_service.SingleModelRule, *lcos_error.LCOSError) {
	effectiveRule, err := c.serviceableCheckerService.GetLaneServiceableRule(ctx, laneInfo.LaneCode)
	serviceableAreaRuleLog(ctx, laneInfo, err)
	if err != nil {
		return nil, nil, err
	}
	lineResourceMap := make(map[string]*lfs_service.SingleModelRule)
	siteResourceMap := make(map[string]*lfs_service.SingleModelRule)
	if len(effectiveRule.RuleDetail) != len(laneInfo.Composes) {
		return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "rule detail can't match lane compose")
	}
	for i := 0; i < len(laneInfo.Composes); i++ {
		if laneInfo.Composes[i].ResourceType == constant.LaneComposeLine {
			if laneInfo.Composes[i].SubType == int(constant.C_FM) {
				// C_FM类型不做任何校验，直接忽略，不需要加入待校验列表中
				continue
			}
			moduleRule := &lfs_service.SingleModelRule{
				MainType:                   laneInfo.Composes[i].MainType,
				SubType:                    int(effectiveRule.RuleDetail[i].GetResourceSubType()),
				ResourceType:               int(effectiveRule.RuleDetail[i].GetResourceType()),
				Sequence:                   int(effectiveRule.RuleDetail[i].GetSequence()),
				CheckSender:                effectiveRule.RuleDetail[i].GetCheckSender(),
				CheckReceiver:              effectiveRule.RuleDetail[i].GetCheckReceiver(),
				SortingAsPreDeliverFlag:    int(effectiveRule.RuleDetail[i].GetSortingAsPreDeliverFlag()),
				SortingAsNextPickupFlag:    int(effectiveRule.RuleDetail[i].GetSortingAsNextPickupFlag()),
				SiteSortingPickupAddrFlag:  int(effectiveRule.RuleDetail[i].GetSiteSortingPickupAddressFlag()),
				SiteSortingDeliverAddrFlag: int(effectiveRule.RuleDetail[i].GetSiteSortingDeliverAddressFlag()),
				PreResourceSACheckFlag:     int(effectiveRule.RuleDetail[i].GetPreResourceSaCheckFlag()),
				NextResourceSACheckFlag:    int(effectiveRule.RuleDetail[i].GetNextResourceSaCheckFlag()),
				SortingFlag:                int(effectiveRule.RuleDetail[i].GetSiteSortingFlag()),
				CheckSenderAddrType:        effectiveRule.RuleDetail[i].GetCheckSenderAddrType(),
				CheckReceiverAddrType:      effectiveRule.RuleDetail[i].GetCheckReceiverAddrType(),
			}
			if effectiveRule.RuleDetail[i].GetSenderUseLps() == 1 {
				moduleRule.CheckSender = int32(singleProductReq.GetBasis().GetCheckSender())
			}
			if effectiveRule.RuleDetail[i].GetReceiverUseLps() == 1 {
				moduleRule.CheckReceiver = int32(singleProductReq.GetBasis().GetCheckReceiver())
			}
			lineResourceMap[laneInfo.Composes[i].ResourceID] = moduleRule
		}
		if laneInfo.Composes[i].ResourceType == constant.LaneComposeSite {
			if effectiveRule.RuleDetail[i].GetSiteSortingFlag() == 1 {
				siteResourceMap[laneInfo.Composes[i].ResourceID] = &lfs_service.SingleModelRule{
					MainType:                   laneInfo.Composes[i].MainType,
					SubType:                    int(effectiveRule.RuleDetail[i].GetResourceSubType()),
					ResourceType:               int(effectiveRule.RuleDetail[i].GetResourceType()),
					Sequence:                   int(effectiveRule.RuleDetail[i].GetSequence()),
					SortingAsPreDeliverFlag:    int(effectiveRule.RuleDetail[i].GetSortingAsPreDeliverFlag()),
					SortingAsNextPickupFlag:    int(effectiveRule.RuleDetail[i].GetSortingAsNextPickupFlag()),
					SiteSortingPickupAddrFlag:  int(effectiveRule.RuleDetail[i].GetSiteSortingPickupAddressFlag()),
					SiteSortingDeliverAddrFlag: int(effectiveRule.RuleDetail[i].GetSiteSortingDeliverAddressFlag()),
					PreResourceSACheckFlag:     int(effectiveRule.RuleDetail[i].GetPreResourceSaCheckFlag()),
					NextResourceSACheckFlag:    int(effectiveRule.RuleDetail[i].GetNextResourceSaCheckFlag()),
					SortingFlag:                int(effectiveRule.RuleDetail[i].GetSiteSortingFlag()),
				}
			}
		}
	}
	return lineResourceMap, siteResourceMap, nil
}

func (c *grpcSceneServiceableAreaController) getLaneAreaServiceableByRule(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct, req *pb.LaneAreaServiceabilityReq, header *pb.ReqHeader, isPdpCheck bool) (*pb.LaneAreaServiceable, *lcos_error.LCOSError) {
	lineMap, siteMap, err := c.getResourcesWithCheckFlag(ctx, laneInfo, req)
	if err != nil {
		return nil, err
	}
	logger.CtxLogInfof(ctx, "lane_code[%s] pending check lines:%v, pending check sites:%v", laneInfo.LaneCode, lineMap, siteMap)

	laneAreaRes := &pb.LaneAreaServiceable{
		LaneCode:    &laneInfo.LaneCode,
		Serviceable: successAreaServiceable(),
	}

	// todo isPdpCheck 修改成 checkSite, 接口不应该是有状态的
	if len(siteMap) > 0 && !isPdpCheck {
		actualSiteList, lcosErr := c.getSitesAreaServiceableByRule(ctx, laneInfo, siteMap, req)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "site serviceable area check failed|lane_code=%v, err:%v", laneInfo.LaneCode, lcosErr)
			laneAreaRes.Serviceable.Code = utils.NewInt32(lcosErr.RetCode)
			laneAreaRes.Serviceable.Message = utils.NewString(lcosErr.Msg)
			return laneAreaRes, nil
		}
		if len(actualSiteList) <= 0 {
			errMsg := fmt.Sprintf("site serviceable area check failed|lane_code=%v, number of sites:%d", laneInfo.LaneCode, len(siteMap))
			logger.CtxLogErrorf(ctx, errMsg)
			laneAreaRes.Serviceable.Code = utils.NewInt32(lcos_error.SiteServiceableAreaCheckFail)
			laneAreaRes.Serviceable.Message = utils.NewString(errMsg)
			return laneAreaRes, nil
		}
		logger.CtxLogInfof(ctx, "lane_code=%s, site actual point list:%v", laneInfo.LaneCode, actualSiteList)
		laneAreaRes.Serviceable.ActualPoints = actualSiteList
	}
	lineRes, err := c.getLinesAreaServiceableByRule(ctx, lineMap, siteMap, laneAreaRes.Serviceable.ActualPoints, laneInfo, req, header, isPdpCheck)
	if err != nil {
		laneAreaRes.Serviceable.Code = utils.NewInt32(err.RetCode)
		laneAreaRes.Serviceable.Message = utils.NewString(err.Msg)
		laneAreaRes.Serviceable.ActualPoints = make([]*pb.ActualPoint, 0)
		return laneAreaRes, nil
	}
	laneAreaRes.Serviceable.Code = lineRes.Serviceable.Code
	if laneAreaRes.Serviceable.GetCode() != lcos_error.SuccessCode {
		laneAreaRes.Serviceable.ActualPoints = make([]*pb.ActualPoint, 0)
	}
	laneAreaRes.Serviceable.Message = lineRes.Serviceable.Message
	laneAreaRes.Serviceable.Ability = lineRes.Serviceable.Ability
	return laneAreaRes, nil
}

func (c *grpcSceneServiceableAreaController) getLaneAreaServiceableByRuleForReroute(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct, req *pb.LaneAreaServiceabilityReq, header *pb.ReqHeader) (*pb.RerouteServiceableArea, *lcos_error.LCOSError) {
	lineMap, siteMap, err := c.getResourcesWithCheckFlag(ctx, laneInfo, req)
	if err != nil {
		return nil, err
	}
	logger.CtxLogInfof(ctx, "lane[%s] pending check lines:%v, pending check sites:%v", laneInfo.LaneCode, lineMap, siteMap)
	laneAreaRes := &pb.RerouteServiceableArea{
		LaneCode: &laneInfo.LaneCode,
	}
	var lineResMap = make(map[string]*pb.SingleGetServiceableResponse2)
	var siteResMap = make(map[string][]*pb.ActualPoint)
	var lcosErr *lcos_error.LCOSError
	var actualPointList []*pb.ActualPoint

	if len(siteMap) > 0 {
		actualPointList, lcosErr = c.getSitesAreaServiceableByRule(ctx, laneInfo, siteMap, req)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "site serviceable area check failed|lane_code=%v, err:%v", laneInfo.LaneCode, lcosErr)
			laneAreaRes.Code = utils.NewInt32(lcosErr.RetCode)
			laneAreaRes.Message = utils.NewString(lcosErr.Msg)
			return laneAreaRes, nil
		}
		if len(actualPointList) <= 0 {
			errMsg := fmt.Sprintf("site serviceable area check failed|lane_code=%v, number of sites:%d", laneInfo.LaneCode, len(siteMap))
			logger.CtxLogErrorf(ctx, errMsg)
			laneAreaRes.Code = utils.NewInt32(lcos_error.SiteServiceableAreaCheckFail)
			laneAreaRes.Message = utils.NewString(errMsg)
			return laneAreaRes, nil
		}
		logger.CtxLogInfof(ctx, "lane_code=%s, site sa result:%v", laneInfo.LaneCode, actualPointList)

		for _, actualPoint := range actualPointList {
			siteResMap[actualPoint.GetSiteId()] = append(siteResMap[actualPoint.GetSiteId()], actualPoint)
		}
	}
	requests, err := c.generateLineServiceableAreaReq(ctx, lineMap, siteMap, actualPointList, laneInfo, req, false)
	if err != nil {
		return nil, err
	}
	if len(requests) == 0 {
		// 异常情况，不太可能出现不包含线信息
		return laneAreaRes, nil
	}
	lineReq := &pb.BatchGetLineServiceableInfoRequest2{
		ReqHeader:          header,
		ServiceableReqList: requests,
	}
	lineRes, lcosErr := c.serviceableCheckerService.BatchGetServiceable(ctx, lineReq)
	if lcosErr != nil {
		laneAreaRes.Code = utils.NewInt32(lcosErr.RetCode)
		laneAreaRes.Message = utils.NewString(lcosErr.Msg)
		return laneAreaRes, nil
	}

	for _, result := range lineRes.ServiceableRespList {
		lineResMap[result.GetLineId()] = result
	}
	laneAreaRes = c.genRerouteServiceable(lineResMap, siteResMap, laneAreaRes, laneInfo)
	return laneAreaRes, nil
}

func (c *grpcSceneServiceableAreaController) QueryLaneInfoFromLFS(ctx utils.LCOSContext, allLaneCodesMap map[string][]string) (map[string]*lfs_service.LaneCodeStruct, *lcos_error.LCOSError) {
	laneCodeInfo := map[string]*lfs_service.LaneCodeStruct{}

	// 调用LFS获取lane信息，按照region分类
	// 把拼装抽离，能满足多个函数不同入参
	for region, laneCodes := range allLaneCodesMap {
		lfsService := lfs_service.NewLFSService(ctx, region)
		regionLaneCodeInfoMap, lcosErr := lfsService.GetLaneCodeMapWithGreySwitch(ctx, laneCodes)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, lcosErr.Msg)
			return nil, lcosErr
		}

		// 填充lanecode信息
		for singleLaneCode, laneCodeInfoStruct := range regionLaneCodeInfoMap {
			laneCodeInfo[singleLaneCode] = laneCodeInfoStruct
		}
	}
	return laneCodeInfo, nil
}

// @core
func (c *grpcSceneServiceableAreaController) BatchCheckProductServiceable(ctx context.Context, req *pb.BatchCheckProductServiceableReq) (*pb.BatchCheckProductServiceableRsp, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	allLaneCodesMap := map[string][]string{}
	allLaneCodeList := make([]string, 0, len(req.GetProductList()))
	for _, singleProduct := range req.GetProductList() {
		allLaneCodeList = append(allLaneCodeList, singleProduct.GetLaneCodes()...)
		allLaneCodesMap[singleProduct.GetRegion()] = append(allLaneCodesMap[singleProduct.GetRegion()], singleProduct.GetLaneCodes()...)
	}
	allLaneCodeList = utils.RemoveDuplicateElement(allLaneCodeList)
	// 获取 laneCode 对应的生效规则
	ruleInfoMap, _ := c.serviceableCheckerService.GetEffectiveRuleByLaneCodes(lcosCtx, allLaneCodeList)
	// 获取 laneCode 详细信息
	laneCodeInfo, lcosErr := c.QueryLaneInfoFromLFS(lcosCtx, allLaneCodesMap)
	if lcosErr != nil {
		return generateErrorResponse(lcosErr), nil
	}

	productListServiceable := make([]*pb.ProductServiceableRsp, 0, len(req.GetProductList()))
	localRuleConfig, cbRuleConfig, doubleCall := config.GetServiceableEffectiveRuleConfig(ctx)
	// 获取lane code的服务范围信息
	for _, singleProductReq := range req.GetProductList() {
		laneServiceableList := make([]*pb.LaneAreaServiceable, 0, len(singleProductReq.GetLaneCodes()))
		//logger.CtxLogInfof(ctx, "all lane:%v, use rule lane:%v", singleProductReq.GetLaneCodes(), localRuleConfig)

		for _, laneCode := range singleProductReq.GetLaneCodes() {
			laneServiceable := &pb.LaneAreaServiceable{
				LaneCode:    utils.NewString(laneCode),
				Serviceable: nil,
			}
			// 找不到lane code
			if _, ok := laneCodeInfo[laneCode]; !ok {
				laneServiceable.Serviceable = &pb.AreaServiceable{
					Code:    utils.NewInt32(lcos_error.NotFoundLaneCodeErrorCode),
					Message: utils.NewString(fmt.Sprintf("serviceable lane not found|lane_code=%s", laneCode)),
				}
				laneServiceableList = append(laneServiceableList, laneServiceable)
				continue
			}
			if laneCodeInfo[laneCode].ErrCode != int(lcos_error.SuccessCode) { //  找到了 laneCode 但是报错
				laneServiceable.Serviceable = &pb.AreaServiceable{
					Code:    utils.NewInt32(int32(laneCodeInfo[laneCode].ErrCode)),
					Message: utils.NewString(laneCodeInfo[laneCode].Message),
				}
				laneServiceableList = append(laneServiceableList, laneServiceable)
				continue
			}

			var singleLaneResult *lcos_protobuf.LaneAreaServiceable
			var singleLaneResultErr *lcos_error.LCOSError
			// config switch
			ruleCheck := false
			if laneCodeStruct, laneCodeOk := laneCodeInfo[laneCode]; laneCodeOk {
				isLocal := laneCodeStruct.IsLocal() && localRuleConfig.IsLocalUseRule(laneCode)
				isCb := laneCodeStruct.IsCb() && cbRuleConfig.IsCbUseRule(laneCode)
				//logger.CtxLogInfof(ctx, "isLocal:%v, isCb:%v", isLocal, isCb)
				if isLocal || isCb {
					if _, ruleOk := ruleInfoMap[laneCode]; !ruleOk {
						laneServiceable.Serviceable = &pb.AreaServiceable{
							Code:    utils.NewInt32(lcos_error.NotFoundLaneEffectiveRuleErrorCode),
							Message: utils.NewString(fmt.Sprintf("serviceable lane effective rule not found|lane_code=%s", laneCode)),
						}
						laneServiceableList = append(laneServiceableList, laneServiceable)
						continue
					}
					ruleCheck = true
				}
			}
			//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRule, "total", "0", "")
			if ruleCheck {
				//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRule, "after", "0", "")
				// use effective rule check
				singleLaneResult, singleLaneResultErr = c.getLaneAreaServiceableByRule(lcosCtx, laneCodeInfo[laneCode], singleProductReq, req.GetReqHeader(), true)
				if doubleCall {
					singleLaneResultB, singleLaneResultErrB := c.getLaneAreaServiceable(lcosCtx, laneCodeInfo[laneCode], singleProductReq, req.GetReqHeader())
					actualPointsA := singleLaneResult.GetServiceable().GetActualPoints()
					if len(actualPointsA) > 0 {
						sort.Slice(actualPointsA, func(i, j int) bool {
							return actualPointsA[i].GetPointId() < actualPointsA[j].GetPointId()
						})
					}
					actualPointsB := singleLaneResultB.GetServiceable().GetActualPoints()
					if len(actualPointsB) > 0 {
						sort.Slice(actualPointsB, func(i, j int) bool {
							return actualPointsB[i].GetPointId() < actualPointsB[j].GetPointId()
						})
					}

					tmpStrA := utils.MarshToStringWithoutError(singleLaneResult)
					tmpStrB := utils.MarshToStringWithoutError(singleLaneResultB)
					if tmpStrA != tmpStrB {
						if singleLaneResult.GetServiceable().GetCode() != lcos_error.SuccessCode && (singleLaneResult.GetServiceable().GetCode() == singleLaneResultB.GetServiceable().GetCode()) {

						} else {
							//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRule, "diff", "0", "")
							logger.CtxLogInfof(ctx, "BatchCheckProductServiceable diff, singleLaneResult=%v,singleLaneResultErr=%v,singleLaneResultB=%v,singleLaneResultErrB=%v", tmpStrA, singleLaneResultErr, tmpStrB, singleLaneResultErrB)
							singleLaneResult = singleLaneResultB
							singleLaneResultErr = singleLaneResultErrB
						}
					}
				}
			} else {
				//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRule, "before", "0", "")
				singleLaneResult, singleLaneResultErr = c.getLaneAreaServiceable(lcosCtx, laneCodeInfo[laneCode], singleProductReq, req.GetReqHeader())
			}

			if singleLaneResultErr != nil {
				laneServiceable.Serviceable = &pb.AreaServiceable{
					Code:    utils.NewInt32(singleLaneResultErr.RetCode),
					Message: utils.NewString(singleLaneResultErr.Msg),
				}
			} else {
				if singleLaneResult.GetServiceable().GetCode() != lcos_error.SuccessCode {
					laneServiceable.Serviceable = &pb.AreaServiceable{
						Code:    utils.NewInt32(singleLaneResult.GetServiceable().GetCode()),
						Message: utils.NewString(singleLaneResult.GetServiceable().GetMessage()),
					}
				} else {
					laneServiceable.Serviceable = &pb.AreaServiceable{
						Code:         utils.NewInt32(lcos_error.SuccessCode),
						Message:      utils.NewString("success"),
						Ability:      singleLaneResult.GetServiceable().GetAbility(),
						ActualPoints: singleLaneResult.GetServiceable().GetActualPoints(),
					}
				}
			}
			_ = serviceAbleReport(lcosCtx, singleProductReq.GetProductId(), laneCode, "", laneServiceable.GetServiceable().GetCode(), laneServiceable.GetServiceable().GetAbility(), singleProductReq.GetBasis().GetPaymentMethod())
			// 封装product的返回信息
			laneServiceableList = append(laneServiceableList, laneServiceable)
		}

		productListServiceable = append(productListServiceable, &pb.ProductServiceableRsp{
			UniqueId:  utils.NewString(singleProductReq.GetUniqueId()),
			ProductId: utils.NewInt32(singleProductReq.GetProductId()),
			LaneList:  laneServiceableList,
		})
	}

	return &pb.BatchCheckProductServiceableRsp{
		RespHeader: &pb.RespHeader{
			RequestId: utils.NewString(req.GetReqHeader().GetRequestId()),
			Retcode:   utils.NewInt32(lcos_error.SuccessCode),
			Message:   utils.NewString("success"),
		},
		ServiceableList: productListServiceable,
	}, nil
}

// BatchCheckProductServiceableV2 for new checkout one-api, supporting cb multi-product
// @core
func (c *grpcSceneServiceableAreaController) BatchCheckProductServiceableV2(ctx context.Context, req *pb.BatchCheckProductServiceableReq) (*pb.BatchCheckProductServiceableRsp, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	allLaneCodesMap := map[string][]string{}
	allLaneCodeList := make([]string, 0)
	for _, singleProduct := range req.GetProductList() {
		allLaneCodeList = append(allLaneCodeList, singleProduct.GetLaneCodes()...)
		allLaneCodesMap[singleProduct.GetRegion()] = append(allLaneCodesMap[singleProduct.GetRegion()], singleProduct.GetLaneCodes()...)
	}
	allLaneCodeList = utils.RemoveDuplicateElement(allLaneCodeList)
	// 获取 laneCode 对应的生效规则
	ruleInfoMap, _ := c.serviceableCheckerService.GetEffectiveRuleByLaneCodes(lcosCtx, allLaneCodeList)
	// 获取 laneCode 详细信息
	laneCodeInfo, lcosErr := c.QueryLaneInfoFromLFS(lcosCtx, allLaneCodesMap)
	if lcosErr != nil {
		return generateErrorResponse(lcosErr), nil
	}

	productListServiceable := make([]*pb.ProductServiceableRsp, 0, len(req.GetProductList()))
	localRuleConfig, cbRuleConfig, doubleCall := config.GetServiceableEffectiveRuleConfig(ctx)
	// 获取lane code的服务范围信息
	for _, singleProductReq := range req.GetProductList() {
		laneServiceableList := make([]*pb.LaneAreaServiceable, 0)
		//logger.CtxLogInfof(ctx, "all lane:%v, use rule lane:%v", singleProductReq.GetLaneCodes(), localRuleConfig)

		for _, laneCode := range singleProductReq.GetLaneCodes() {
			laneServiceable := &pb.LaneAreaServiceable{
				LaneCode:    utils.NewString(laneCode),
				Serviceable: nil,
			}
			if strings.Contains(laneCode, "/") {
				laneServiceable.LaneCodeGroup = strings.Split(laneCode, "/")
			}
			// 找不到lane code
			if _, ok := laneCodeInfo[laneCode]; !ok {
				laneServiceable.Serviceable = &pb.AreaServiceable{
					Code:    utils.NewInt32(lcos_error.NotFoundLaneCodeErrorCode),
					Message: utils.NewString(fmt.Sprintf("serviceable lane not found|lane_code=%s", laneCode)),
				}
				laneServiceableList = append(laneServiceableList, laneServiceable)
				continue
			}
			if laneCodeInfo[laneCode].ErrCode != int(lcos_error.SuccessCode) { //  找到了laneCode但是报错
				laneServiceable.Serviceable = &pb.AreaServiceable{
					Code:    utils.NewInt32(int32(laneCodeInfo[laneCode].ErrCode)),
					Message: utils.NewString(laneCodeInfo[laneCode].Message),
				}
				laneServiceableList = append(laneServiceableList, laneServiceable)
				continue
			}
			var singleLaneResult *lcos_protobuf.LaneAreaServiceable
			var singleLaneResultErr *lcos_error.LCOSError
			// config switch
			ruleCheck := false
			if laneCodeStruct, laneCodeOk := laneCodeInfo[laneCode]; laneCodeOk {
				isLocal := laneCodeStruct.IsLocal() && localRuleConfig.IsLocalUseRule(laneCode)
				isCb := laneCodeStruct.IsCb() && cbRuleConfig.IsCbUseRule(laneCode)
				//logger.CtxLogInfof(ctx, "isLocal:%v, isCb:%v", isLocal, isCb)
				if isLocal || isCb {
					if _, ruleOk := ruleInfoMap[laneCode]; !ruleOk {
						laneServiceable.Serviceable = &pb.AreaServiceable{
							Code:    utils.NewInt32(lcos_error.NotFoundLaneEffectiveRuleErrorCode),
							Message: utils.NewString(fmt.Sprintf("serviceable lane effective rule not found|lane_code=%s", laneCode)),
						}
						laneServiceableList = append(laneServiceableList, laneServiceable)
						continue
					}
					ruleCheck = true
				}
			}
			//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRuleV2, "total", "0", "")
			if ruleCheck {
				//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRuleV2, "after", "0", "")
				// use effective rule check
				singleLaneResult, singleLaneResultErr = c.getLaneAreaServiceableByRule(lcosCtx, laneCodeInfo[laneCode], singleProductReq, req.GetReqHeader(), false)
				if doubleCall {
					singleLaneResultB, singleLaneResultErrB := c.getLaneAreaServiceableV2(lcosCtx, laneCodeInfo[laneCode], singleProductReq, req.GetReqHeader())
					actualPointsA := singleLaneResult.GetServiceable().GetActualPoints()
					if len(actualPointsA) > 0 {
						sort.Slice(actualPointsA, func(i, j int) bool {
							return actualPointsA[i].GetPointId() < actualPointsA[j].GetPointId()
						})
					}
					actualPointsB := singleLaneResultB.GetServiceable().GetActualPoints()
					if len(actualPointsB) > 0 {
						sort.Slice(actualPointsB, func(i, j int) bool {
							return actualPointsB[i].GetPointId() < actualPointsB[j].GetPointId()
						})
					}
					tmpStrA := utils.MarshToStringWithoutError(singleLaneResult)
					tmpStrB := utils.MarshToStringWithoutError(singleLaneResultB)
					if tmpStrA != tmpStrB {
						if singleLaneResult.GetServiceable().GetCode() != lcos_error.SuccessCode && (singleLaneResult.GetServiceable().GetCode() == singleLaneResultB.GetServiceable().GetCode()) {

						} else {
							//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRuleV2, "diff", "0", "")
							logger.CtxLogInfof(ctx, "BatchCheckProductServiceableV2 diff, singleLaneResult=%v,singleLaneResultErr=%v,singleLaneResultB=%v,singleLaneResultErrB=%v", tmpStrA, singleLaneResultErr, tmpStrB, singleLaneResultErrB)
							singleLaneResult = singleLaneResultB
							singleLaneResultErr = singleLaneResultErrB
						}
					}
				}
			} else {
				//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRuleV2, "before", "0", "")
				singleLaneResult, singleLaneResultErr = c.getLaneAreaServiceableV2(lcosCtx, laneCodeInfo[laneCode], singleProductReq, req.GetReqHeader())
			}
			if singleLaneResultErr != nil {
				laneServiceable.Serviceable = &pb.AreaServiceable{
					Code:    utils.NewInt32(singleLaneResultErr.RetCode),
					Message: utils.NewString(singleLaneResultErr.Msg),
				}
			} else {
				if singleLaneResult.GetServiceable().GetCode() != lcos_error.SuccessCode {
					laneServiceable.Serviceable = &pb.AreaServiceable{
						Code:    utils.NewInt32(singleLaneResult.GetServiceable().GetCode()),
						Message: utils.NewString(singleLaneResult.GetServiceable().GetMessage()),
					}
				} else {
					laneServiceable.Serviceable = &pb.AreaServiceable{
						Code:         utils.NewInt32(lcos_error.SuccessCode),
						Message:      utils.NewString("success"),
						Ability:      singleLaneResult.GetServiceable().GetAbility(),
						ActualPoints: singleLaneResult.GetServiceable().GetActualPoints(),
					}
				}
			}
			_ = serviceAbleReport(lcosCtx, singleProductReq.GetProductId(), laneCode, "", laneServiceable.GetServiceable().GetCode(), laneServiceable.GetServiceable().GetAbility(), singleProductReq.GetBasis().GetPaymentMethod())
			// 封装product的返回信息
			laneServiceableList = append(laneServiceableList, laneServiceable)
		}

		productListServiceable = append(productListServiceable, &pb.ProductServiceableRsp{
			UniqueId:  utils.NewString(singleProductReq.GetUniqueId()),
			ProductId: utils.NewInt32(singleProductReq.GetProductId()),
			LaneList:  laneServiceableList,
		})
	}

	return &pb.BatchCheckProductServiceableRsp{
		RespHeader: &pb.RespHeader{
			RequestId: utils.NewString(req.GetReqHeader().GetRequestId()),
			Retcode:   utils.NewInt32(lcos_error.SuccessCode),
			Message:   utils.NewString("success"),
		},
		ServiceableList: productListServiceable,
	}, nil
}

// @core
func (c *grpcSceneServiceableAreaController) BatchCheckProductServiceableFulfillment(ctx context.Context, req *pb.BatchCheckProductServiceableReq) (*pb.BatchCheckProductServiceableRsp, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	allLaneCodesMap := map[string][]string{}
	allLaneCodeList := make([]string, 0)
	for _, singleProduct := range req.GetProductList() {
		allLaneCodeList = append(allLaneCodeList, singleProduct.GetLaneCodes()...)
		allLaneCodesMap[singleProduct.GetRegion()] = append(allLaneCodesMap[singleProduct.GetRegion()], singleProduct.GetLaneCodes()...)
	}
	allLaneCodeList = utils.RemoveDuplicateElement(allLaneCodeList)
	// 获取 laneCode 对应的生效规则
	ruleInfoMap, _ := c.serviceableCheckerService.GetEffectiveRuleByLaneCodes(lcosCtx, allLaneCodeList)
	// 获取 laneCode 详细信息
	laneCodeInfo, lcosErr := c.QueryLaneInfoFromLFS(lcosCtx, allLaneCodesMap)
	if lcosErr != nil {
		return generateErrorResponse(lcosErr), nil
	}

	var productListServiceable []*pb.ProductServiceableRsp
	localRuleConfig, cbRuleConfig, doubleCall := config.GetServiceableEffectiveRuleConfig(ctx)
	// 获取lane code的服务范围信息
	for _, singleProductReq := range req.GetProductList() {
		var laneListServiceable []*pb.LaneAreaServiceable
		logger.CtxLogInfof(ctx, "all lane:%v, use rule lane:%v", singleProductReq.GetLaneCodes(), localRuleConfig)

		for _, laneCode := range singleProductReq.GetLaneCodes() {
			laneServiceable := &pb.LaneAreaServiceable{
				LaneCode:    utils.NewString(laneCode),
				Serviceable: nil,
			}
			if strings.Contains(laneCode, "/") {
				laneServiceable.LaneCodeGroup = strings.Split(laneCode, "/")
			}
			// 找不到lane code
			if _, ok := laneCodeInfo[laneCode]; !ok {
				laneServiceable.Serviceable = &pb.AreaServiceable{
					Code:    utils.NewInt32(lcos_error.NotFoundLaneCodeErrorCode),
					Message: utils.NewString(fmt.Sprintf("serviceable lane not found|lane_code=%s", laneCode)),
				}
				laneListServiceable = append(laneListServiceable, laneServiceable)
				continue
			}
			if laneCodeInfo[laneCode].ErrCode != int(lcos_error.SuccessCode) { //  找到了laneCode但是报错
				laneServiceable.Serviceable = &pb.AreaServiceable{
					Code:    utils.NewInt32(int32(laneCodeInfo[laneCode].ErrCode)),
					Message: utils.NewString(laneCodeInfo[laneCode].Message),
				}
				laneListServiceable = append(laneListServiceable, laneServiceable)
				continue
			}
			var singleLaneResult *lcos_protobuf.LaneAreaServiceable
			var singleLaneResultErr *lcos_error.LCOSError
			// config switch
			ruleCheck := false
			if laneCodeStruct, laneCodeOk := laneCodeInfo[laneCode]; laneCodeOk {
				isLocal := laneCodeStruct.IsLocal() && localRuleConfig.IsLocalUseRule(laneCode)
				isCb := laneCodeStruct.IsCb() && cbRuleConfig.IsCbUseRule(laneCode)
				logger.CtxLogInfof(ctx, "isLocal:%v, isCb:%v", isLocal, isCb)
				if isLocal || isCb {
					if _, ruleOk := ruleInfoMap[laneCode]; !ruleOk {
						laneServiceable.Serviceable = &pb.AreaServiceable{
							Code:    utils.NewInt32(lcos_error.NotFoundLaneEffectiveRuleErrorCode),
							Message: utils.NewString(fmt.Sprintf("serviceable lane effective rule not found|lane_code=%s", laneCode)),
						}
						laneListServiceable = append(laneListServiceable, laneServiceable)
						continue
					}
					ruleCheck = true
				}
			}
			//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableFulfillmentRule, "total", "0", "")
			if ruleCheck {
				//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableFulfillmentRule, "after", "0", "")
				// use effective rule check
				singleLaneResult, singleLaneResultErr = c.getLaneAreaServiceableByRule(lcosCtx, laneCodeInfo[laneCode], singleProductReq, req.GetReqHeader(), false)
				if doubleCall {
					singleLaneResultB, singleLaneResultErrB := c.getLaneAreaServiceableFulfillment(lcosCtx, laneCodeInfo[laneCode], singleProductReq, req.GetReqHeader())
					actualPointsA := singleLaneResult.GetServiceable().GetActualPoints()
					if len(actualPointsA) > 0 {
						sort.Slice(actualPointsA, func(i, j int) bool {
							return actualPointsA[i].GetPointId() < actualPointsA[j].GetPointId()
						})
					}
					actualPointsB := singleLaneResultB.GetServiceable().GetActualPoints()
					if len(actualPointsB) > 0 {
						sort.Slice(actualPointsB, func(i, j int) bool {
							return actualPointsB[i].GetPointId() < actualPointsB[j].GetPointId()
						})
					}
					tmpStrA := utils.MarshToStringWithoutError(singleLaneResult)
					tmpStrB := utils.MarshToStringWithoutError(singleLaneResultB)
					if tmpStrA != tmpStrB {
						if singleLaneResult.GetServiceable().GetCode() != lcos_error.SuccessCode && (singleLaneResult.GetServiceable().GetCode() == singleLaneResultB.GetServiceable().GetCode()) {

						} else {
							//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableFulfillmentRule, "diff", "0", "")
							logger.CtxLogInfof(ctx, "BatchCheckProductServiceableFulfillment diff, singleLaneResult=%v,singleLaneResultErr=%v,singleLaneResultB=%v,singleLaneResultErrB=%v", tmpStrA, singleLaneResultErr, tmpStrB, singleLaneResultErrB)
							singleLaneResult = singleLaneResultB
							singleLaneResultErr = singleLaneResultErrB
						}
					}
				}
			} else {
				//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableFulfillmentRule, "before", "0", "")
				singleLaneResult, singleLaneResultErr = c.getLaneAreaServiceableFulfillment(lcosCtx, laneCodeInfo[laneCode], singleProductReq, req.GetReqHeader())
			}
			if singleLaneResultErr != nil {
				laneServiceable.Serviceable = &pb.AreaServiceable{
					Code:    utils.NewInt32(singleLaneResultErr.RetCode),
					Message: utils.NewString(singleLaneResultErr.Msg),
				}
			} else {
				if singleLaneResult.GetServiceable().GetCode() != lcos_error.SuccessCode {
					laneServiceable.Serviceable = &pb.AreaServiceable{
						Code:    utils.NewInt32(singleLaneResult.GetServiceable().GetCode()),
						Message: utils.NewString(singleLaneResult.GetServiceable().GetMessage()),
					}
				} else {
					laneServiceable.Serviceable = &pb.AreaServiceable{
						Code:         utils.NewInt32(lcos_error.SuccessCode),
						Message:      utils.NewString("success"),
						Ability:      singleLaneResult.GetServiceable().GetAbility(),
						ActualPoints: singleLaneResult.GetServiceable().GetActualPoints(),
					}
				}
			}
			_ = serviceAbleReport(lcosCtx, singleProductReq.GetProductId(), laneCode, "", laneServiceable.GetServiceable().GetCode(), laneServiceable.GetServiceable().GetAbility(), singleProductReq.GetBasis().GetPaymentMethod())
			// 封装product的返回信息
			laneListServiceable = append(laneListServiceable, laneServiceable)
		}

		productListServiceable = append(productListServiceable, &pb.ProductServiceableRsp{
			UniqueId:  utils.NewString(singleProductReq.GetUniqueId()),
			ProductId: utils.NewInt32(singleProductReq.GetProductId()),
			LaneList:  laneListServiceable,
		})
	}

	return &pb.BatchCheckProductServiceableRsp{
		RespHeader: &pb.RespHeader{
			RequestId: utils.NewString(req.GetReqHeader().GetRequestId()),
			Retcode:   utils.NewInt32(lcos_error.SuccessCode),
			Message:   utils.NewString("success"),
		},
		ServiceableList: productListServiceable,
	}, nil
}

func (c *grpcSceneServiceableAreaController) BatchCheckProductServiceableForItemScene(ctx context.Context, itemReq *pb.BatchCheckProductServiceableForItemSceneReq) (*pb.BatchCheckProductServiceableForItemSceneResp, error) {
	ctx = context.WithValue(ctx, constant.ItemCardSceneFlag, true) // 写入item card场景flag

	lcosCtx := utils.NewCommonCtx(ctx)

	// 1. 打平服务范围请求参数，同时获取lane的点线信息以及生效规则
	req := &pb.BatchCheckProductServiceableReq{
		ReqHeader: itemReq.ReqHeader,
	}
	allLaneCodesMap := map[string][]string{}
	allLaneCodeList := make([]string, 0, len(req.GetProductList()))
	for _, itemServiceableReq := range itemReq.GetServiceableList() {
		for _, singleItemProduct := range itemServiceableReq.GetProductList() {
			req.ProductList = append(req.ProductList, &pb.LaneAreaServiceabilityReq{
				LaneCodes:   singleItemProduct.LaneCodes,
				UniqueId:    singleItemProduct.UniqueId,
				ProductId:   singleItemProduct.ProductId,
				Region:      itemServiceableReq.Region,
				Basis:       singleItemProduct.Basis,
				PickupAddr:  itemServiceableReq.PickupAddr,
				DeliverAddr: itemServiceableReq.DeliverAddr,
			})

			allLaneCodeList = append(allLaneCodeList, singleItemProduct.GetLaneCodes()...)
			allLaneCodesMap[itemServiceableReq.GetRegion()] = append(allLaneCodesMap[itemServiceableReq.GetRegion()], singleItemProduct.GetLaneCodes()...)
		}
	}
	allLaneCodeList = utils.RemoveDuplicateElement(allLaneCodeList)
	// 获取生效规则
	ruleInfoMap, _ := c.serviceableCheckerService.GetEffectiveRuleByLaneCodes(lcosCtx, allLaneCodeList)
	// 获取点线信息
	laneInfoMap, lcosErr := c.QueryLaneInfoFromLFS(lcosCtx, allLaneCodesMap)
	if lcosErr != nil {
		return &pb.BatchCheckProductServiceableForItemSceneResp{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
		}, nil
	}

	// 2. 批量异步校验product服务范围
	productListServiceable := make([]*pb.ItemSceneProductServiceableResp, len(req.GetProductList())) // 预先将返回结果列表len和cap都设置为请求长度
	localRuleConfig, cbRuleConfig, doubleCall := config.GetServiceableEffectiveRuleConfig(ctx)       // 获取配置化校验灰度规则
	queue := make(chan struct{}, config.GetConcurrencyQueueCapacity(ctx, "BatchCheckProductServiceableForItemScene"))
	defer close(queue)
	wg := &sync.WaitGroup{}
	for i, productReq := range req.GetProductList() {
		wg.Add(1)
		queue <- struct{}{}
		go func(idx int, singleProductReq *pb.LaneAreaServiceabilityReq) {
			// 批量串行校验lane维度服务范围
			laneServiceableList := make([]*pb.LaneAreaServiceable, 0, len(singleProductReq.GetLaneCodes()))
			for _, laneCode := range singleProductReq.GetLaneCodes() {
				laneServiceable := &pb.LaneAreaServiceable{
					LaneCode:    utils.NewString(laneCode),
					Serviceable: nil,
				}

				// 1. lane前置校验：lane是否存在，查询点线信息是否报错
				laneInfo, ok := laneInfoMap[laneCode]
				if !ok || laneInfo == nil {
					// 请求LFS获取获取lane点线信息，返回lane not found报错
					laneServiceable.Serviceable = &pb.AreaServiceable{
						Code:    utils.NewInt32(lcos_error.NotFoundLaneCodeErrorCode),
						Message: utils.NewString(fmt.Sprintf("serviceable lane not found|lane_code=%s", laneCode)),
					}
					laneServiceableList = append(laneServiceableList, laneServiceable)
					continue
				}
				if laneInfo.ErrCode != int(lcos_error.SuccessCode) {
					// 请求LFS获取lane点线信息返回报错，将LFS错误信息返回
					laneServiceable.Serviceable = &pb.AreaServiceable{
						Code:    utils.NewInt32(int32(laneInfo.ErrCode)),
						Message: utils.NewString(laneInfo.Message),
					}
					laneServiceableList = append(laneServiceableList, laneServiceable)
					continue
				}

				// 2. lane维度服务范围校验
				var singleLaneResult *lcos_protobuf.LaneAreaServiceable
				var singleLaneResultErr *lcos_error.LCOSError
				// 2.1 校验灰度规则，判断是否走服务范围配置化校验流程
				ruleCheck := false
				isLocalUseRule := laneInfo.IsLocal() && localRuleConfig.IsLocalUseRule(laneCode)
				isCbUseRule := laneInfo.IsCb() && cbRuleConfig.IsCbUseRule(laneCode)
				if isLocalUseRule || isCbUseRule {
					if _, ruleOk := ruleInfoMap[laneCode]; !ruleOk {
						// 灰度命中需要走配置化流程，但生效规则不存在，返回effective rule not found报错
						laneServiceable.Serviceable = &pb.AreaServiceable{
							Code:    utils.NewInt32(lcos_error.NotFoundLaneEffectiveRuleErrorCode),
							Message: utils.NewString(fmt.Sprintf("serviceable lane effective rule not found|lane_code=%s", laneCode)),
						}
						laneServiceableList = append(laneServiceableList, laneServiceable)
						continue
					}
					ruleCheck = true
				}
				// 2.2 校验lane维度服务范围
				if ruleCheck {
					// 基于生效规则校验lane服务范围
					singleLaneResult, singleLaneResultErr = c.getLaneAreaServiceableByRule(lcosCtx, laneInfo, singleProductReq, req.GetReqHeader(), true)
					if doubleCall {
						// 如果开启了double call比对，则额外用原流程校验一次服务范围
						singleLaneResultB, singleLaneResultErrB := c.getLaneAreaServiceable(lcosCtx, laneInfo, singleProductReq, req.GetReqHeader())
						// actual points排序，仅用于序列化后比对服务范围校验结果
						actualPointsA := singleLaneResult.GetServiceable().GetActualPoints()
						if len(actualPointsA) > 0 {
							sort.Slice(actualPointsA, func(i, j int) bool {
								return actualPointsA[i].GetPointId() < actualPointsA[j].GetPointId()
							})
						}
						actualPointsB := singleLaneResultB.GetServiceable().GetActualPoints()
						if len(actualPointsB) > 0 {
							sort.Slice(actualPointsB, func(i, j int) bool {
								return actualPointsB[i].GetPointId() < actualPointsB[j].GetPointId()
							})
						}

						tmpStrA := utils.MarshToStringWithoutError(singleLaneResult)
						tmpStrB := utils.MarshToStringWithoutError(singleLaneResultB)
						if tmpStrA != tmpStrB {
							if singleLaneResult.GetServiceable().GetCode() != lcos_error.SuccessCode && (singleLaneResult.GetServiceable().GetCode() == singleLaneResultB.GetServiceable().GetCode()) {
								// 由于报错信息可能不同，因此如果序列化比对不一致，但新旧流程返回错误码都不为0且相同，则可以判断为无差异
							} else {
								// 新旧流程返回结果差异，输出日志并返回旧流程结果
								logger.CtxLogInfof(ctx, "BatchCheckProductServiceable diff, singleLaneResult=%v,singleLaneResultErr=%v,singleLaneResultB=%v,singleLaneResultErrB=%v", tmpStrA, singleLaneResultErr, tmpStrB, singleLaneResultErrB)
								singleLaneResult = singleLaneResultB
								singleLaneResultErr = singleLaneResultErrB
							}
						}
					}
				} else {
					// 服务范围校验旧流程，从LFS获取校验规则
					singleLaneResult, singleLaneResultErr = c.getLaneAreaServiceable(lcosCtx, laneInfo, singleProductReq, req.GetReqHeader())
				}
				// 2.3 组装lane服务范围校验结果
				if singleLaneResultErr != nil {
					laneServiceable.Serviceable = &pb.AreaServiceable{
						Code:    utils.NewInt32(singleLaneResultErr.RetCode),
						Message: utils.NewString(singleLaneResultErr.Msg),
					}
				} else {
					if singleLaneResult.GetServiceable().GetCode() != lcos_error.SuccessCode {
						laneServiceable.Serviceable = &pb.AreaServiceable{
							Code:    utils.NewInt32(singleLaneResult.GetServiceable().GetCode()),
							Message: utils.NewString(singleLaneResult.GetServiceable().GetMessage()),
						}
					} else {
						laneServiceable.Serviceable = &pb.AreaServiceable{
							Code:         utils.NewInt32(lcos_error.SuccessCode),
							Message:      utils.NewString("success"),
							Ability:      singleLaneResult.GetServiceable().GetAbility(),
							ActualPoints: singleLaneResult.GetServiceable().GetActualPoints(),
						}
					}
				}
				laneServiceableList = append(laneServiceableList, laneServiceable)
			}
			productListServiceable[idx] = &pb.ItemSceneProductServiceableResp{
				UniqueId:  utils.NewString(singleProductReq.GetUniqueId()),
				ProductId: utils.NewInt32(singleProductReq.GetProductId()),
				LaneList:  laneServiceableList,
				ItemId:    utils.NewUint64(singleProductReq.GetItemId()),
			}

			<-queue
			wg.Done()
		}(i, productReq)
	}
	wg.Wait()

	// 3. SPLN-36443 为了减少LPS切换接口的工作量。在原有的PDP接口集成parcel library查询逻辑
	parcelLibraryInfoMap := make(map[string]*pb.ParcelLibraryInfo)
	if len(itemReq.GetParcelLibraryQueries()) > 0 {
		dataMap, _ := c.parcelLibraryService.BatchGetLatestParcelLibraryDataBySkuInfos(lcosCtx, itemReq.GetParcelLibraryQueries())
		for uniqueId, data := range dataMap {
			if data == nil {
				continue
			}

			parcelLibraryInfoMap[uniqueId] = &pb.ParcelLibraryInfo{
				UniqueId:              proto.String(uniqueId),
				AccurateLength:        proto.Float64(data.GetAccurateLength()),
				AccurateWidth:         proto.Float64(data.GetAccurateWidth()),
				AccurateHeight:        proto.Float64(data.GetAccurateHeight()),
				AccurateWeight:        proto.Float64(data.GetAccurateWeight()),
				WeightFinAccuracy:     proto.Float64(data.WeightFinAccuracy),
				VolumetricFinAccuracy: proto.Float64(data.VolumetricFinAccuracy),
				IsBulkyParcel:         proto.Int32(int32(data.IsBulkyParcel)),
			}
		}
	}

	return &pb.BatchCheckProductServiceableForItemSceneResp{
		RespHeader: &pb.RespHeader{
			RequestId: utils.NewString(req.GetReqHeader().GetRequestId()),
			Retcode:   utils.NewInt32(lcos_error.SuccessCode),
			Message:   utils.NewString("success"),
		},
		ServiceableList:      productListServiceable,
		ParcelLibraryInfoMap: parcelLibraryInfoMap,
	}, nil
}

func (c *grpcSceneServiceableAreaController) BatchCheckShopServiceable(ctx context.Context, req *pb.BatchCheckShopServiceableReq) (*pb.BatchCheckShopServiceableResp, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	uniqueIdMap := make(map[string]struct{}, len(req.GetCheckList()))
	resultList := make([]*pb.CheckShopServiceableResp, 0, len(req.GetCheckList()))
	for _, singleRequest := range req.GetCheckList() {
		uniqueId := singleRequest.GetUniqueId()
		if uniqueId == "" {
			return &pb.BatchCheckShopServiceableResp{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is empty"),
			}, nil
		}
		if _, ok := uniqueIdMap[uniqueId]; ok {
			return &pb.BatchCheckShopServiceableResp{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is duplicate"),
			}, nil
		}
		uniqueIdMap[uniqueId] = struct{}{}

		singleCheckResult, _ := c.serviceableCheckerService.CheckShopServiceable(lcosCtx, singleRequest)
		resultList = append(resultList, singleCheckResult)
	}
	return &pb.BatchCheckShopServiceableResp{
		RespHeader:      http.GrpcSuccessRespHeader(),
		ServiceableList: resultList,
	}, nil
}

func (c *grpcSceneServiceableAreaController) SearchProductServiceableZone(ctx context.Context, req *pb.SearchProductServiceableZoneReq) (*pb.SearchProductServiceableZoneResp, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	uniqueIdMap := make(map[string]struct{}, len(req.GetAddressList()))
	resultList := make([]*pb.SearchProductServiceableZoneResult, 0, len(req.GetAddressList()))
	for _, address := range req.GetAddressList() {
		uniqueId := address.GetUniqueId()
		if uniqueId == "" {
			return &pb.SearchProductServiceableZoneResp{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is empty"),
			}, nil
		}
		if _, ok := uniqueIdMap[uniqueId]; ok {
			return &pb.SearchProductServiceableZoneResp{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is duplicate"),
			}, nil
		}
		uniqueIdMap[uniqueId] = struct{}{}

		result, _ := c.serviceableCheckerService.SearchProductServiceableZone(lcosCtx, address)
		resultList = append(resultList, result)
	}
	return &pb.SearchProductServiceableZoneResp{
		RespHeader: &pb.RespHeader{
			RequestId: utils.NewString(req.GetReqHeader().GetRequestId()),
			Retcode:   utils.NewInt32(lcos_error.SuccessCode),
			Message:   utils.NewString("success"),
		},
		ZoneList: resultList,
	}, nil
}

// @core
func (c *grpcSceneServiceableAreaController) BatchCheckLaneServiceableReroute(ctx context.Context, req *pb.RerouteLaneServiceableReq) (*pb.RerouteLaneServiceableRsp, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	allLaneCodesMap := map[string][]string{}
	allLaneCodeList := make([]string, 0)
	for _, singleProduct := range req.GetLaneServiceableList() {
		allLaneCodeList = append(allLaneCodeList, singleProduct.GetLaneCodes()...)
		allLaneCodesMap[singleProduct.GetRegion()] = append(allLaneCodesMap[singleProduct.GetRegion()], singleProduct.GetLaneCodes()...)
	}
	allLaneCodeList = utils.RemoveDuplicateElement(allLaneCodeList)
	// 获取 laneCode 对应的生效规则
	ruleInfoMap, _ := c.serviceableCheckerService.GetEffectiveRuleByLaneCodes(lcosCtx, allLaneCodeList)
	// 获取 laneCode 详细信息
	laneCodeInfo, lcosErr := c.QueryLaneInfoFromLFS(lcosCtx, allLaneCodesMap)
	if lcosErr != nil {
		return &pb.RerouteLaneServiceableRsp{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
		}, nil
	}

	var serviceableList []*pb.RerouteLaneServiceable
	localRuleConfig, cbRuleConfig, _ := config.GetServiceableEffectiveRuleConfig(ctx)
	// 获取lane code的服务范围信息
	for _, singleReq := range req.GetLaneServiceableList() {
		var laneListServiceable []*pb.RerouteServiceableArea
		logger.CtxLogInfof(ctx, "all lane:%v, use rule lane:%v", singleReq.GetLaneCodes(), localRuleConfig)

		for _, laneCode := range singleReq.GetLaneCodes() {
			laneServiceable := &pb.RerouteServiceableArea{
				Code:        proto.Int32(lcos_error.SuccessCode),
				Message:     proto.String("success"),
				LaneCode:    utils.NewString(laneCode),
				Serviceable: nil,
			}
			if strings.Contains(laneCode, "/") {
				laneServiceable.LaneCodeGroup = strings.Split(laneCode, "/")
			}
			// 找不到lane code
			if _, ok := laneCodeInfo[laneCode]; !ok {
				laneServiceable = &pb.RerouteServiceableArea{
					LaneCode: utils.NewString(laneCode),
					Code:     utils.NewInt32(lcos_error.NotFoundLaneCodeErrorCode),
					Message:  utils.NewString(fmt.Sprintf("serviceable lane not found|lane_code=%s", laneCode)),
				}
				laneListServiceable = append(laneListServiceable, laneServiceable)
				continue
			}
			if laneCodeInfo[laneCode].ErrCode != int(lcos_error.SuccessCode) { //  找到了lanecode但是报错
				laneServiceable = &pb.RerouteServiceableArea{
					LaneCode: utils.NewString(laneCode),
					Code:     utils.NewInt32(int32(laneCodeInfo[laneCode].ErrCode)),
					Message:  utils.NewString(laneCodeInfo[laneCode].Message),
				}
				laneListServiceable = append(laneListServiceable, laneServiceable)
				continue
			}

			laneInfo := laneCodeInfo[laneCode]
			pickupInfo := serviceableAddress2LocationInfo(singleReq.GetPickupAddr())
			deliverInfo := serviceableAddress2LocationInfo(singleReq.GetDeliverAddr())
			basis := &pb.ServiceableBasis{
				CheckOperation:     singleReq.Basis.CheckOperation,
				CheckBasic:         singleReq.Basis.CheckBasic,
				CollectType:        singleReq.Basis.CollectType,
				DeliverType:        singleReq.Basis.DeliverType,
				CheckPostalCode:    proto.Uint32(singleReq.Basis.GetSkipPostalCode() ^ uint32(constant.TRUE)),
				PaymentMethod:      singleReq.Basis.PaymentMethod,
				SenderCheckLevel:   singleReq.Basis.SenderCheckLevel,
				ReceiverCheckLevel: singleReq.Basis.ReceiverCheckLevel,
				CheckSender:        singleReq.Basis.CheckSender,
				CheckReceiver:      singleReq.Basis.CheckReceiver,
				SkipZoneRoute:      singleReq.Basis.SkipZoneRoute,
			}
			// 如果使用规则校验，按规则筛选需要校验的site/line，并进行校验
			convertReq := c.convertToLaneServiceabilityReq(singleReq, basis)

			//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRerouteRule, "total", "0", "")
			// config switch
			ruleCheck := false
			if laneCodeStruct, ok := laneCodeInfo[laneCode]; ok {
				isLocal := laneCodeStruct.IsLocal() && localRuleConfig.IsLocalUseRule(laneCode)
				isCb := laneCodeStruct.IsCb() && cbRuleConfig.IsCbUseRule(laneCode)
				logger.CtxLogInfof(ctx, "isLocal:%v, isCb:%v", isLocal, isCb)
				if isLocal || isCb {
					if _, ruleOk := ruleInfoMap[laneCode]; !ruleOk {
						laneServiceable = &pb.RerouteServiceableArea{
							LaneCode: utils.NewString(laneCode),
							Code:     utils.NewInt32(lcos_error.NotFoundLaneEffectiveRuleErrorCode),
							Message:  utils.NewString(fmt.Sprintf("serviceable lane effective rule not found|lane_code=%s", laneCode)),
						}
						laneListServiceable = append(laneListServiceable, laneServiceable)
						continue
					}
					ruleCheck = true
				}
			}
			if ruleCheck {
				//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRerouteRule, "after", "0", "")
				serviceableRes, serviceableResErr := c.getLaneAreaServiceableByRuleForReroute(lcosCtx, laneInfo, convertReq, req.ReqHeader)
				if serviceableResErr != nil {
					laneServiceable = &pb.RerouteServiceableArea{
						LaneCode: utils.NewString(laneCode),
						Code:     utils.NewInt32(serviceableResErr.RetCode),
						Message:  utils.NewString(serviceableResErr.Msg),
					}
					laneListServiceable = append(laneListServiceable, laneServiceable)
				} else {
					laneListServiceable = append(laneListServiceable, serviceableRes)
				}
				continue
			}
			//_ = monitor.AwesomeReportEvent(ctx, constant.CatServiceableRerouteRule, "before", "0", "")
			// 获取相关lines
			lines, lineErr := getLaneAreaServiceableLinesFulfillment(lcosCtx, laneInfo)
			if lineErr != nil {
				laneServiceable = &pb.RerouteServiceableArea{
					LaneCode: utils.NewString(laneCode),
					Code:     utils.NewInt32(lineErr.RetCode),
					Message:  utils.NewString(lineErr.Msg),
				}
				laneListServiceable = append(laneListServiceable, laneServiceable)
				continue
			}

			var requests []*pb.SingleGetServiceableRequest2
			for _, line := range lines {
				requests = append(requests, &pb.SingleGetServiceableRequest2{
					BaseInfo:    serviceableBasis2LcosGetBaseInfo(line, basis),
					PickupInfo:  pickupInfo,
					DeliverInfo: deliverInfo,
				})
			}

			// 请求lcos的接口
			lineReq := &pb.BatchGetLineServiceableInfoRequest2{
				ReqHeader:          req.ReqHeader,
				ServiceableReqList: requests,
			}
			lineRes, lcosErr := c.serviceableCheckerService.BatchGetServiceable(lcosCtx, lineReq)
			if lcosErr != nil {
				laneServiceable = &pb.RerouteServiceableArea{
					LaneCode: utils.NewString(laneCode),
					Code:     utils.NewInt32(int32(lcosErr.RetCode)),
					Message:  utils.NewString(lcosErr.Msg),
				}
				laneListServiceable = append(laneListServiceable, laneServiceable)
				continue
			}
			var lineResMap = make(map[string]*pb.SingleGetServiceableResponse2)
			for _, result := range lineRes.ServiceableRespList {
				lineResMap[result.GetLineId()] = result
			}
			sites := laneInfo.GetSite()

			actualPointList, lcosErr := c.getSitesAreaServiceable(lcosCtx, sites, laneInfo, convertReq, req.ReqHeader)
			if lcosErr != nil {
				laneServiceable = &pb.RerouteServiceableArea{
					LaneCode: utils.NewString(laneCode),
					Code:     utils.NewInt32(int32(lcosErr.RetCode)),
					Message:  utils.NewString(lcosErr.Msg),
				}
				laneListServiceable = append(laneListServiceable, laneServiceable)
				continue
			}
			var siteResMap = make(map[string][]*pb.ActualPoint)
			for _, actualPoint := range actualPointList {
				siteResMap[actualPoint.GetSiteId()] = append(siteResMap[actualPoint.GetSiteId()], actualPoint)
			}
			if len(actualPointList) > 0 {
				requests, lcosErr := c.getCheckWithActualPointsReq(lcosCtx, lines, actualPointList, laneInfo, convertReq)
				if lcosErr != nil {
					laneServiceable = &pb.RerouteServiceableArea{
						LaneCode: utils.NewString(laneCode),
						Code:     utils.NewInt32(int32(lcosErr.RetCode)),
						Message:  utils.NewString(lcosErr.Msg),
					}
					laneListServiceable = append(laneListServiceable, laneServiceable)
					continue
				}
				// if no need to check BR line SA, return nil
				if len(requests) > 0 {
					lineReq.ServiceableReqList = requests
					brLineRes, lcosErr := c.serviceableCheckerService.BatchGetServiceable(lcosCtx, lineReq)
					if lcosErr != nil {
						laneServiceable = &pb.RerouteServiceableArea{
							LaneCode: utils.NewString(laneCode),
							Code:     utils.NewInt32(int32(lcosErr.RetCode)),
							Message:  utils.NewString(lcosErr.Msg),
						}
						laneListServiceable = append(laneListServiceable, laneServiceable)
						continue
					}
					for _, result := range brLineRes.ServiceableRespList {
						if res, ok := lineResMap[result.GetLineId()]; ok {
							lineResMap[result.GetLineId()].ItemCode = result.ItemCode
							lineResMap[result.GetLineId()].Message = result.Message
							lineResMap[result.GetLineId()].ServiceableInfo.CanPickup = proto.Uint32(res.ServiceableInfo.GetCanPickup() & result.ServiceableInfo.GetCanPickup())
							lineResMap[result.GetLineId()].ServiceableInfo.CanCodDeliver = proto.Uint32(res.ServiceableInfo.GetCanCodDeliver() & result.ServiceableInfo.GetCanCodDeliver())
							lineResMap[result.GetLineId()].ServiceableInfo.CanCodPickup = proto.Uint32(res.ServiceableInfo.GetCanCodPickup() & result.ServiceableInfo.GetCanCodPickup())
							lineResMap[result.GetLineId()].ServiceableInfo.CanDeliver = proto.Uint32(res.ServiceableInfo.GetCanDeliver() & result.ServiceableInfo.GetCanDeliver())
							lineResMap[result.GetLineId()].ServiceableInfo.PickupInEFence = proto.Uint32(res.ServiceableInfo.GetPickupInEFence() & result.ServiceableInfo.GetPickupInEFence())
							lineResMap[result.GetLineId()].ServiceableInfo.DeliverInEFence = proto.Uint32(res.ServiceableInfo.GetDeliverInEFence() & result.ServiceableInfo.GetDeliverInEFence())
						}
					}
				}
			}
			laneServiceable = c.genRerouteServiceable(lineResMap, siteResMap, laneServiceable, laneInfo)
			laneListServiceable = append(laneListServiceable, laneServiceable)
		}
		serviceableList = append(serviceableList, &pb.RerouteLaneServiceable{
			UniqueId:        utils.NewString(singleReq.GetUniqueId()),
			ProductId:       utils.NewInt32(singleReq.GetProductId()),
			ServiceableList: laneListServiceable,
		})

	}

	return &pb.RerouteLaneServiceableRsp{
		RespHeader: &pb.RespHeader{
			RequestId: utils.NewString(req.GetReqHeader().GetRequestId()),
			Retcode:   utils.NewInt32(lcos_error.SuccessCode),
			Message:   utils.NewString("success"),
		},
		ServiceableRsp: serviceableList,
	}, nil
}

func (c *grpcSceneServiceableAreaController) BatchGetProductServiceableRouteCode(ctx context.Context, req *pb.BatchGetProductServiceableRouteCodeReq) (*pb.BatchGetProductServiceableRouteCodeResp, error) {
	lcosCtx := utils.NewCommonCtx(ctx)

	respList, err := c.serviceableCheckerService.BatchGetProductServiceableRouteCode(lcosCtx, req.GetReqList())
	if err != nil {
		return &pb.BatchGetProductServiceableRouteCodeResp{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}
	return &pb.BatchGetProductServiceableRouteCodeResp{
		RespHeader: http.GrpcSuccessRespHeader(),
		RespList:   respList,
	}, nil
}

func (c *grpcSceneServiceableAreaController) convertToLaneServiceabilityReq(originReq *pb.LaneServiceableReq, newBasis *pb.ServiceableBasis) *pb.LaneAreaServiceabilityReq {
	return &pb.LaneAreaServiceabilityReq{
		LaneCodes:   originReq.LaneCodes,
		UniqueId:    originReq.UniqueId,
		ProductId:   originReq.ProductId,
		Region:      originReq.Region,
		Basis:       newBasis,
		PickupAddr:  originReq.PickupAddr,
		DeliverAddr: originReq.DeliverAddr,
	}
}

func (c *grpcSceneServiceableAreaController) genRerouteServiceable(lineResMap map[string]*pb.SingleGetServiceableResponse2, siteResMap map[string][]*pb.ActualPoint, laneServiceable *pb.RerouteServiceableArea, laneInfo *lfs_service.LaneCodeStruct) *pb.RerouteServiceableArea {
	for lineId, res := range lineResMap {
		laneServiceable.Serviceable = append(laneServiceable.Serviceable, &pb.Serviceable{
			ResourceId:      proto.String(lineId),
			Sequence:        proto.Uint32(laneInfo.GetSequenceNo(lineId)),
			Code:            res.ItemCode,
			Message:         res.Message,
			CanPickup:       res.ServiceableInfo.CanPickup,
			CanDeliver:      res.ServiceableInfo.CanDeliver,
			CanCodPickup:    res.ServiceableInfo.CanCodPickup,
			CanCodDeliver:   res.ServiceableInfo.CanCodDeliver,
			PickupInEFence:  res.ServiceableInfo.PickupInEFence,
			DeliverInEFence: res.ServiceableInfo.DeliverInEFence,
		})
	}
	for siteId, siteRes := range siteResMap {
		laneServiceable.Serviceable = append(laneServiceable.Serviceable, &pb.Serviceable{
			Code:         proto.Int32(lcos_error.SuccessCode),
			Message:      proto.String("success"),
			ResourceId:   proto.String(siteId),
			Sequence:     proto.Uint32(laneInfo.GetSequenceNo(siteId)),
			ActualPoints: siteRes,
		})
	}
	laneServiceable.Code = proto.Int32(lcos_error.SuccessCode)
	laneServiceable.Message = proto.String("success")
	return laneServiceable
}

func serviceAbleReport(ctx utils.LCOSContext, productId int32, laneCode string, line string, errCode int32, ability *lcos_protobuf.AreaServiceability, paymentMethod lcos_protobuf.PaymentMethodEnum) error {
	// 上报服务范围开关
	if !config.GetServiceAbleReport(ctx) {
		return nil
	}

	reportMap := map[string]string{
		"url":             ctx.GetUrl(),
		"can_pickup":      strconv.Itoa(int(ability.GetCanPickup())),
		"can_cod_pickup":  strconv.Itoa(int(ability.GetCanCodPickup())),
		"can_deliver":     strconv.Itoa(int(ability.GetCanDeliver())),
		"can_cod_deliver": strconv.Itoa(int(ability.GetCanCodDeliver())),
		"can_trade_in":    strconv.Itoa(int(ability.GetSupportTradeIn())),
		"payment_method":  strconv.Itoa(int(paymentMethod)),
		"rsp_status":      strconv.Itoa(isServiceAreaSuccess(ability, paymentMethod)),
		"err_code":        strconv.Itoa(int(errCode)),
		"lane":            laneCode,
		"line":            line,
		"product":         strconv.Itoa(int(productId)),
	}

	return metrics.CounterIncr(constant.MetricLaneStatus, reportMap)
}

func serviceableAreaRuleLog(ctx context.Context, laneInfo *lfs_service.LaneCodeStruct, err *lcos_error.LCOSError) {
	laneCode := ""
	if laneInfo != nil {
		laneCode = laneInfo.LaneCode
	}
	if err != nil {
		Logger.OpsLogServiceableAreaRuleData(ctx,
			Logger.ServiceableAreaRuleParams{LaneCode: laneCode},
			Logger.ServiceableAreaRuleResult{Error: err.Msg},
		)
		return
	}
	Logger.OpsLogServiceableAreaRuleData(ctx,
		Logger.ServiceableAreaRuleParams{LaneCode: laneCode},
		Logger.ServiceableAreaRuleResult{},
	)
}
