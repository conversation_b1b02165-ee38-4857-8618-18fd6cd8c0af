package cdt_calculation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/day_group_and_time_bucket_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"github.com/bytedance/sonic"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"
	auto_update_rule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lpop_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	jsoniter "github.com/json-iterator/go"
)

type cdtCalculationController struct {
	pb.UnimplementedLcosCdtCalculationServiceServer

	cdtCalculationService auto_update_rule.CdtCalculationServiceInterface
}

var _ pb.LcosCdtCalculationServiceServer = (*cdtCalculationController)(nil)

func NewCdtCalculationController(cdtCalculationService auto_update_rule.CdtCalculationServiceInterface) *cdtCalculationController {
	return &cdtCalculationController{
		cdtCalculationService: cdtCalculationService,
	}
}

func (c *cdtCalculationController) GetEddInfo(ctx context.Context, request *pb.QueryCdtInfoForTrackingRequest) (*pb.GetEddInfoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	productInfos := transferPbProductToProduct(request.GetProductInfo())
	eddMin, eddMax, eddProcess, lcosErr := c.cdtCalculationService.GetEDDInfo(lcosCtx, productInfos, request.GetForderId(), int64(request.GetCbLmInboundDate()))
	if lcosErr != nil {
		return &pb.GetEddInfoResponse{RespHeader: http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg)}, nil
	}
	eddProcessPb := eddProcess.ToPb()
	return &pb.GetEddInfoResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		EddMin:     utils.NewInt64(eddMin),
		Edd:        utils.NewInt64(eddMax),
		EddProcess: eddProcessPb,
	}, nil
}

// @core
func (c *cdtCalculationController) BatchGetCdtInfo(ctx context.Context, request *pb.QueryCdtInfoRequest) (*pb.QueryCdtInfoRequestResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	var productInfos []*cdt_calculation.CdtProductInfo
	queryMap := make(map[string]QueryMap, 0)
	for _, productInfo := range request.GetProductInfo() {
		productInfos = append(productInfos, transferPbProductToProduct(productInfo))
		queryMap[productInfo.GetQueryId()] = CreateQueryIdInfoMap(productInfo.GetQueryId(), productInfo.GetUpdateEvent(), productInfo.StartDay, productInfo.StartHour, productInfo.EndDay, productInfo.EndHour, productInfo.GetGroupTag(), productInfo.GetRegion())
	}
	cdtReplies, lcosErr := c.cdtCalculationService.BatchGetCdtInfoByProductsWithGreySwitch(lcosCtx, uint8(request.GetObjectType()), productInfos, constant.CdtQueryDataSourceDB)
	if lcosErr != nil {
		return &pb.QueryCdtInfoRequestResponse{
			RespHeader:   http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
			CdtReplyList: nil,
		}, nil
	}
	var returnCdtReplies []*pb.CdtReply
	for _, reply := range cdtReplies {
		queryInfo := queryMap[reply.QueryId]
		needDayGroupAndTimeBucket := queryInfo.NeedDayGroupAndTimeBucket()
		var fchannelCdtAndVolumes []*pb.FChannelCdtAndVolumeInfo
		for _, info := range reply.FchannelCdtAndVolumes {
			var volumeInfosPb []*pb.AllLevelVolumeInfoWithLocationLevel
			for _, volume := range info.VolumeInfos {
				originLocationLevel := pb.CdtLocationLevelEnum(volume.OriginLocationLevel)
				destLocationLevel := pb.CdtLocationLevelEnum(volume.DestLocationLevel)
				volumepb := &pb.AllLevelVolumeInfoWithLocationLevel{
					Volume:              utils.NewUint64(volume.Volume),
					OriginLocationLevel: &originLocationLevel,
					DestLocationLevel:   &destLocationLevel,
				}
				volumeInfosPb = append(volumeInfosPb, volumepb)
			}

			var cdtInfosPb []*pb.AllLevelCdtInfoWithLocationLevel
			for _, cdtInfo := range info.CdtInfos {
				originLocationLevel := pb.CdtLocationLevelEnum(cdtInfo.OriginLocationLevel)
				destLocationLevel := pb.CdtLocationLevelEnum(cdtInfo.DestLocationLevel)
				var dayAndTimeList []*pb.DayAndTime
				if needDayGroupAndTimeBucket {
					var dayGroups *common_utils.CdtExtraData
					err := sonic.UnmarshalString(cdtInfo.CdtExtraData, &dayGroups)
					if err != nil && cdtInfo.CdtExtraData != "" {
						logger.CtxLogErrorf(ctx, "unmarshal CdtExtraData failed|query_id=%s, CdtExtraData=%s", reply.QueryId, cdtInfo.CdtExtraData)
					}

					var deltaMin float64
					var deltaMax float64
					if cdtInfo.CdtProcess != nil && cdtInfo.CdtProcess.ManualManipulationProcess != nil {
						deltaMin = cdtInfo.CdtProcess.ManualManipulationProcess.CdtMinDelta
						deltaMax = cdtInfo.CdtProcess.ManualManipulationProcess.CdtMaxDelta
					}
					dayAndTimeList = transferDayGroupToDayAndTime(ctx, dayGroups, queryInfo.Region, deltaMin, deltaMax)
					dayAndTimeList = getRealDayAndTimeList(queryInfo.GetStartDay(), queryInfo.GetEndDay(), queryInfo.GetStartHour(), queryInfo.GetEndHour(), dayAndTimeList)
				}

				cdtInfoPb := &pb.AllLevelCdtInfoWithLocationLevel{
					CdtMin:              utils.NewFloat64(cdtInfo.CdtMin),
					CdtMax:              utils.NewFloat64(cdtInfo.CdtMax),
					OriginLocationLevel: &originLocationLevel,
					DestLocationLevel:   &destLocationLevel,
					DayAndTimeList:      dayAndTimeList,
				}
				cdtInfosPb = append(cdtInfosPb, cdtInfoPb)
			}

			fchannelCdtAndVolume := &pb.FChannelCdtAndVolumeInfo{
				Retcode:              utils.NewInt32(info.Retcode),
				Message:              utils.NewString(info.Message),
				FulfillmentChannelId: utils.NewString(info.FChannelID),
				CdtInfos:             cdtInfosPb,
				VolumeInfos:          volumeInfosPb,
			}
			fchannelCdtAndVolumes = append(fchannelCdtAndVolumes, fchannelCdtAndVolume)
		}

		var dayAndTimeList []*pb.DayAndTime
		if needDayGroupAndTimeBucket {
			var dayGroups *common_utils.CdtExtraData
			err := jsoniter.Unmarshal([]byte(reply.CdtExtraData), &dayGroups)
			if err != nil && reply.CdtExtraData != "" {
				logger.CtxLogErrorf(ctx, "unmarshal CdtExtraData failed|product_id=%s, CdtExtraData=%s", reply.QueryId, reply.CdtExtraData)
			}
			var deltaMin float64
			var deltaMax float64
			if reply.CdtProcess != nil && reply.CdtProcess.ManualManipulationProcess != nil {
				deltaMin = reply.CdtProcess.ManualManipulationProcess.CdtMinDelta
				deltaMax = reply.CdtProcess.ManualManipulationProcess.CdtMaxDelta
			}
			dayAndTimeList = transferDayGroupToDayAndTime(ctx, dayGroups, queryInfo.Region, deltaMin, deltaMax)
			dayAndTimeList = getRealDayAndTimeList(queryInfo.GetStartDay(), queryInfo.GetEndDay(),
				queryInfo.GetStartHour(), queryInfo.GetEndHour(), dayAndTimeList)
		}

		tmpCdtReply := &pb.CdtReply{
			QueryId:              utils.NewString(reply.QueryId),
			CdtMin:               utils.NewFloat64(reply.CdtMin),
			CdtMax:               utils.NewFloat64(reply.CdtMax),
			CbLmMax:              utils.NewFloat64(reply.CBLMCdtMax),
			OverrideType:         utils.NewString(reply.OverrideType),
			ItemId:               utils.NewUint64(reply.ItemId),
			FchannelCdtAndVolume: fchannelCdtAndVolumes,
			DayAndTime:           dayAndTimeList,
			RequestUpdateEvent:   utils.NewUint32(queryInfo.UpdateEvent),
			StartDay:             queryInfo.StartDay,
			StartHour:            queryInfo.StartHour,
			EndDay:               queryInfo.EndDay,
			EndHour:              queryInfo.EndHour,
			GroupTag:             utils.NewUint32(queryInfo.GroupTag),
		}
		if reply.Error != nil {
			tmpCdtReply.Error = &pb.Error{
				Retcode: utils.NewInt32(reply.Error.RetCode),
				Message: utils.NewString(reply.Error.Msg),
			}
		}
		returnCdtReplies = append(returnCdtReplies, tmpCdtReply)
	}
	return &pb.QueryCdtInfoRequestResponse{
		RespHeader:   http.GrpcSuccessRespHeader(),
		CdtReplyList: returnCdtReplies,
	}, nil
}

func (c *cdtCalculationController) GetCdtInfoForTracking(ctx context.Context, request *pb.QueryCdtInfoForTrackingRequest) (*pb.QueryCdtInfoForTrackingResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)

	productInfo := transferPbProductToProduct(request.GetProductInfo())
	eddCalculation, _, lcosErr := c.cdtCalculationService.GetCdtInfoForTracking(lcosCtx, productInfo, int64(request.GetCbLmInboundDate()))
	if lcosErr != nil {
		logger.CtxLogErrorf(lcosCtx, lcosErr.Msg)
		return &pb.QueryCdtInfoForTrackingResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
		}, nil
	}

	cdtResponse := &pb.QueryCdtInfoForTrackingResponse{
		RespHeader:     http.GrpcSuccessRespHeader(),
		CbLmHolidayExt: utils.NewFloat64(eddCalculation.EddMaxHolidayExt),
		CbLmCbMax:      utils.NewFloat64(eddCalculation.LeadTimeMax),
	}
	return cdtResponse, nil
}

func (c *cdtCalculationController) GetEventCodeList(ctx context.Context, request *pb.QueryEventCodeRequest) (*pb.QueryEventCodeResponse, error) {
	var lmInboundStatus []string
	lcosCtx := utils.NewCommonCtx(ctx)
	lpopService := lpop_service.NewLpopService()
	slsEventList, lfsEventList, lcosErr := lpopService.GetSlaLMInboundStatusWithLruCache(lcosCtx)
	if lcosErr != nil {
		return &pb.QueryEventCodeResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.ServerErrorCode, lcosErr.Msg),
		}, nil
	}
	if uint8(request.GetIsSiteLine()) == constant.TRUE {
		lmInboundStatus = lfsEventList
	} else {
		lmInboundStatus = slsEventList
	}
	return &pb.QueryEventCodeResponse{
		RespHeader:     http.GrpcSuccessRespHeader(),
		ActionCodeList: lmInboundStatus,
	}, nil
}

func (c *cdtCalculationController) GetExtendedEDD(ctx context.Context, request *pb.GetExtendedEDDRequest) (*pb.GetExtendedEDDResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	resultMap := make(map[string]*pb.SingleEDDResponse)
	for _, singleRequest := range request.GetEddExtensionList() {
		realRequest := &cdt_calculation.SingleExtendedEDDInfo{
			UniqueID:        singleRequest.GetUniqueId(),
			SlsTN:           singleRequest.GetSlsTn(),
			ProductID:       singleRequest.GetProductId(),
			StateLocationID: int(singleRequest.GetStateLocationId()),
			Region:          singleRequest.GetRegion(),
			Adjustment:      int(singleRequest.GetAdjustment()),
			Edd:             singleRequest.GetEdd(),
			IsLM:            uint8(singleRequest.GetIsLm()),
			IsSiteLine:      uint8(singleRequest.GetIsSitLine()),

			EddMinAdjustment: int(singleRequest.GetEddMinAdjustment()),
			EddMin:           singleRequest.GetEddMin(),
		}

		lineInfoList := make([]*cdt_calculation.LineInfo, 0, len(singleRequest.GetLineIdList()))
		for _, tmp := range singleRequest.GetLineIdList() {
			lineInfoList = append(lineInfoList, &cdt_calculation.LineInfo{
				LineID:  tmp.GetLineId(),
				SubType: tmp.GetLineSubType(),
			})
		}
		realRequest.LineIDList = lineInfoList

		response, lcosErr := c.cdtCalculationService.GetExtendedEDDInfo(lcosCtx, realRequest)
		if lcosErr != nil {
			logger.CtxLogErrorf(lcosCtx, "cannot calculate extended for edd:[%d], product:[%s]", singleRequest.GetEdd(), singleRequest.GetProductId())
		} else {
			resultMap[singleRequest.GetUniqueId()] = response.TransferStructToPb()
		}
	}
	return &pb.GetExtendedEDDResponse{
		RespHeader:             http.GrpcSuccessRespHeader(),
		ExtendedEddResponseMap: resultMap,
	}, nil
}

// GetEddCalculationInfo SPLN-29072新增，返回用于计算EDD和DDL的详细信息，包括cdt、nwd以及edd range配置等
func (c *cdtCalculationController) GetEddCalculationInfo(ctx context.Context, request *pb.GetEddCalculationInfoRequest) (*pb.GetEddCalculationInfoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	productInfo := transferPbProductToProduct(request.GetProductInfo())

	eddInfo, ddlInfo, eddProcess, err := c.cdtCalculationService.GetEddCalculationInfo(lcosCtx, productInfo, request.GetForderId(), int64(request.GetEventTime()))
	if err != nil {
		return &pb.GetEddCalculationInfoResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	return &pb.GetEddCalculationInfoResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		EddInfo: &pb.EDDCalculationInfo{
			EddMinAvailable: utils.NewBool(eddInfo.CalculateEddMin),
			EddRangeLimit:   utils.NewInt64(eddInfo.EddRangeLimit),
			CdtMin:          utils.NewFloat64(eddInfo.LeadTimeMin),
			CdtMax:          utils.NewFloat64(eddInfo.LeadTimeMax),
			Holiday:         eddInfo.Holiday,
			Weekend:         eddInfo.Weekend,
		},
		DdlInfo: &pb.DDLCalculationInfo{
			ForwardCdt:           utils.NewFloat64(ddlInfo.ForwardCdt),
			BackwardCdt:          utils.NewFloat64(ddlInfo.BackwardCdt),
			NextEventForwardCdt:  utils.NewFloat64(ddlInfo.NextEventForwardCdt),
			NextEventBackwardCdt: utils.NewFloat64(ddlInfo.NextEventBackwardCdt),
		},
		EddProcess: eddProcess.ToPb(),
	}, nil
}

// SPLN-33284 通过algo计算edd预测值
func (c *cdtCalculationController) GetEddInfoByAlgo(ctx context.Context, request *pb.GetEddInfoByAlgoRequest) (*pb.GetEddInfoByAlgoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	productInfo := transferPbProductToProduct(request.GetProductInfo())

	eddMin, eddMax, algoEDDInfo, eddProcess, lcosErr := c.cdtCalculationService.GetEDDInfoByAlgo(lcosCtx, productInfo, request.GetSlsTn(), int64(request.GetEventTime()), request.GetEventTrackingCode())
	if lcosErr != nil {
		return &pb.GetEddInfoByAlgoResponse{RespHeader: http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg)}, nil
	}

	return &pb.GetEddInfoByAlgoResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		EddMin:     utils.NewInt64(eddMin),
		EddMax:     utils.NewInt64(eddMax),
		EddProcess: eddProcess.ToPb(),
		AlgoResult: &pb.AlgoResult{
			EddMin:    &algoEDDInfo.EDDMin,
			EddMax:    &algoEDDInfo.EDDMax,
			EventType: &algoEDDInfo.EventType,
			EventTime: &algoEDDInfo.EventTime,
		},
	}, nil
}

func (c *cdtCalculationController) BatchQueryEDDInfo(ctx context.Context, request *pb.QueryEDDInfoRequest) (*pb.QueryEDDInfoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)

	eddInfoMap := map[string]*pb.EDDInfo{}

	for _, singleRequest := range request.GetQueryInfoList() {
		slsTN := singleRequest.GetSlsTn()
		region := singleRequest.GetRegion()
		eddResult, lcosErr := c.cdtCalculationService.QueryEDDInfo(lcosCtx, &cdt_calculation.QueryEDDInfoRequest{
			SlsTN:  slsTN,
			Region: region,
		})
		var returnedEDDInfo *pb.EDDInfo
		if lcosErr != nil {
			returnedEDDInfo = &pb.EDDInfo{
				Retcode:      utils.NewInt32(lcosErr.RetCode),
				ErrorMessage: utils.NewString(lcosErr.Msg),
			}
		} else {
			if eddResult.Edd <= 0 {
				returnedEDDInfo = &pb.EDDInfo{
					Retcode:      utils.NewInt32(lcos_error.EDDNotValid),
					ErrorMessage: utils.NewString("edd is less than or equal to 0"),
				}
			} else {
				returnedEDDInfo = &pb.EDDInfo{
					Retcode:   utils.NewInt32(lcos_error.SuccessCode),
					EddMax:    utils.NewInt64(eddResult.Edd),
					EddMin:    utils.NewInt64(eddResult.EddMin),
					Ctime:     utils.NewInt64(eddResult.Ctime),
					ProductId: utils.NewString(eddResult.ProductID),
					SlsTn:     utils.NewString(eddResult.SlsTN),
				}
			}
		}
		returnedEDDInfo.QueryId = utils.NewString(singleRequest.GetQueryId())
		eddInfoMap[singleRequest.GetQueryId()] = returnedEDDInfo
	}
	return &pb.QueryEDDInfoResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		EddInfoMap: eddInfoMap,
	}, nil
}

func (c *cdtCalculationController) BatchGetCdtInfoForItemScene(ctx context.Context, request *pb.QueryCdtInfoRequest) (*pb.QueryCdtInfoRequestResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	var productInfos []*cdt_calculation.CdtProductInfo
	queryMap := make(map[string]QueryMap, 0)
	for _, productInfo := range request.GetProductInfo() {
		productInfos = append(productInfos, transferPbProductToProduct(productInfo))
		queryMap[productInfo.GetQueryId()] = CreateQueryIdInfoMap(productInfo.GetQueryId(), productInfo.GetUpdateEvent(), productInfo.StartDay, productInfo.StartHour, productInfo.EndDay, productInfo.EndHour, productInfo.GetGroupTag(), productInfo.GetRegion())
	}
	cdtReplies, lcosErr := c.cdtCalculationService.BatchGetCdtInfoByProductsWithConcurrency(lcosCtx, uint8(request.GetObjectType()), productInfos)
	if lcosErr != nil {
		return &pb.QueryCdtInfoRequestResponse{
			RespHeader:   http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
			CdtReplyList: nil,
		}, nil
	}
	var returnCdtReplies []*pb.CdtReply
	for _, reply := range cdtReplies {
		queryInfo := queryMap[reply.QueryId]
		needDayGroupAndTimeBucket := queryInfo.NeedDayGroupAndTimeBucket()
		degradeDayGroupAndTimeBucket := !config.GetNeedUseGroupDayAndTimeBucketConf(ctx, queryInfo.Region, day_group_and_time_bucket_constant.EdtUse)
		needDayGroupAndTimeBucket = !degradeDayGroupAndTimeBucket && needDayGroupAndTimeBucket
		var (
			dayGroupAndTimeBucketCount int
			dayGroupAndTimeBucket      map[uint32]map[uint32]int
		)
		if needDayGroupAndTimeBucket {
			dayGroupAndTimeBucketCount, dayGroupAndTimeBucket = GetNeededTimeWindows(queryInfo.GetStartDay(), queryInfo.GetEndDay(), queryInfo.GetStartHour(), queryInfo.GetEndHour())
		}

		var fchannelCdtAndVolumes []*pb.FChannelCdtAndVolumeInfo
		for _, info := range reply.FchannelCdtAndVolumes {
			var volumeInfosPb []*pb.AllLevelVolumeInfoWithLocationLevel
			for _, volume := range info.VolumeInfos {
				originLocationLevel := pb.CdtLocationLevelEnum(volume.OriginLocationLevel)
				destLocationLevel := pb.CdtLocationLevelEnum(volume.DestLocationLevel)
				volumepb := &pb.AllLevelVolumeInfoWithLocationLevel{
					Volume:              utils.NewUint64(volume.Volume),
					OriginLocationLevel: &originLocationLevel,
					DestLocationLevel:   &destLocationLevel,
				}
				volumeInfosPb = append(volumeInfosPb, volumepb)
			}

			var cdtInfosPb []*pb.AllLevelCdtInfoWithLocationLevel
			for _, cdtInfo := range info.CdtInfos {
				originLocationLevel := pb.CdtLocationLevelEnum(cdtInfo.OriginLocationLevel)
				destLocationLevel := pb.CdtLocationLevelEnum(cdtInfo.DestLocationLevel)
				var dayAndTimeList []*pb.DayAndTime
				if needDayGroupAndTimeBucket {
					var dayGroups *common_utils.CdtExtraData
					err := jsoniter.Unmarshal([]byte(cdtInfo.CdtExtraData), &dayGroups)
					if err != nil && cdtInfo.CdtExtraData != "" {
						logger.CtxLogErrorf(ctx, "unmarshal CdtExtraData failed|query_id=%s, CdtExtraData=%s", reply.QueryId, cdtInfo.CdtExtraData)
					}
					var deltaMin float64
					var deltaMax float64
					if cdtInfo.CdtProcess != nil && cdtInfo.CdtProcess.ManualManipulationProcess != nil {
						deltaMin = cdtInfo.CdtProcess.ManualManipulationProcess.CdtMinDelta
						deltaMax = cdtInfo.CdtProcess.ManualManipulationProcess.CdtMaxDelta
					}
					dayAndTimeList = transferDayGroupToDayAndTimeV2(ctx, dayGroups, queryInfo.Region, deltaMin, deltaMax, dayGroupAndTimeBucketCount, dayGroupAndTimeBucket)
					//dayAndTimeList = getRealDayAndTimeList(queryInfo.GetStartDay(), queryInfo.GetEndDay(), queryInfo.GetStartHour(), queryInfo.GetEndHour(), dayAndTimeList)
				}

				cdtInfoPb := &pb.AllLevelCdtInfoWithLocationLevel{
					CdtMin:              utils.NewFloat64(cdtInfo.CdtMin),
					CdtMax:              utils.NewFloat64(cdtInfo.CdtMax),
					OriginLocationLevel: &originLocationLevel,
					DestLocationLevel:   &destLocationLevel,
					DayAndTimeList:      dayAndTimeList,
				}
				cdtInfosPb = append(cdtInfosPb, cdtInfoPb)
			}

			fchannelCdtAndVolume := &pb.FChannelCdtAndVolumeInfo{
				Retcode:              utils.NewInt32(info.Retcode),
				Message:              utils.NewString(info.Message),
				FulfillmentChannelId: utils.NewString(info.FChannelID),
				CdtInfos:             cdtInfosPb,
				VolumeInfos:          volumeInfosPb,
			}
			fchannelCdtAndVolumes = append(fchannelCdtAndVolumes, fchannelCdtAndVolume)
		}
		var dayAndTimeList []*pb.DayAndTime
		if needDayGroupAndTimeBucket {
			var dayGroups *common_utils.CdtExtraData
			err := jsoniter.Unmarshal([]byte(reply.CdtExtraData), &dayGroups)
			if err != nil && reply.CdtExtraData != "" {
				logger.CtxLogErrorf(ctx, "unmarshal CdtExtraData failed|product_id=%s, CdtExtraData=%s", reply.QueryId, reply.CdtExtraData)
			}
			var deltaMin float64
			var deltaMax float64
			if reply.CdtProcess != nil && reply.CdtProcess.ManualManipulationProcess != nil {
				deltaMin = reply.CdtProcess.ManualManipulationProcess.CdtMinDelta
				deltaMax = reply.CdtProcess.ManualManipulationProcess.CdtMaxDelta
			}
			dayAndTimeList = transferDayGroupToDayAndTimeV2(ctx, dayGroups, queryInfo.Region, deltaMin, deltaMax,
				dayGroupAndTimeBucketCount, dayGroupAndTimeBucket)
			//dayAndTimeList = getRealDayAndTimeList(queryInfo.GetStartDay(), queryInfo.GetEndDay(), queryInfo.GetStartHour(), queryInfo.GetEndHour(), dayAndTimeList)
		}

		tmpCdtReply := &pb.CdtReply{
			QueryId:              utils.NewString(reply.QueryId),
			CdtMin:               utils.NewFloat64(reply.CdtMin),
			CdtMax:               utils.NewFloat64(reply.CdtMax),
			CbLmMax:              utils.NewFloat64(reply.CBLMCdtMax),
			OverrideType:         utils.NewString(reply.OverrideType),
			ItemId:               utils.NewUint64(reply.ItemId),
			FchannelCdtAndVolume: fchannelCdtAndVolumes,
			DayAndTime:           dayAndTimeList,
			RequestUpdateEvent:   utils.NewUint32(queryInfo.UpdateEvent),
			StartDay:             queryInfo.StartDay,
			StartHour:            queryInfo.StartHour,
			EndDay:               queryInfo.EndDay,
			EndHour:              queryInfo.EndHour,
			GroupTag:             utils.NewUint32(queryInfo.GroupTag),
		}
		if reply.Error != nil {
			tmpCdtReply.Error = &pb.Error{
				Retcode: utils.NewInt32(reply.Error.RetCode),
				Message: utils.NewString(reply.Error.Msg),
			}
		}
		returnCdtReplies = append(returnCdtReplies, tmpCdtReply)
	}
	return &pb.QueryCdtInfoRequestResponse{
		RespHeader:   http.GrpcSuccessRespHeader(),
		CdtReplyList: returnCdtReplies,
	}, nil
}

func (c *cdtCalculationController) ListMChannelRuleByRegion(ctx context.Context, request *pb.ListMChannelRuleByRegionRequest) (*pb.MChannelRuleByRegionResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	mChannelRules, lcosErr := c.cdtCalculationService.ListMChannelRuleByRegion(lcosCtx, strings.ToUpper(request.GetRegion()))
	if lcosErr != nil {
		return &pb.MChannelRuleByRegionResponse{
			RespHeader:       http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
			MchannelRuleList: nil,
		}, nil
	}
	return &pb.MChannelRuleByRegionResponse{
		RespHeader:       http.GrpcSuccessRespHeader(),
		MchannelRuleList: mChannelRules,
	}, nil
}

func (c *cdtCalculationController) ListMChannelGreyConfigByRegion(ctx context.Context, request *pb.ListMChannelGreyConfigByRegionRequest) (*pb.MChannelGreyConfigByRegionResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	mChannelGreyConfigList, lcosErr := c.cdtCalculationService.ListMChannelGreyConfigByRegion(lcosCtx, strings.ToUpper(request.GetRegion()))
	if lcosErr != nil {
		return &pb.MChannelGreyConfigByRegionResponse{
			RespHeader:             http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
			MchannelGreyConfigList: nil,
		}, nil
	}
	return &pb.MChannelGreyConfigByRegionResponse{
		RespHeader:             http.GrpcSuccessRespHeader(),
		MchannelGreyConfigList: mChannelGreyConfigList,
	}, nil
}

func (c *cdtCalculationController) ListABTestRuleByRegion(ctx context.Context, request *pb.ListABTestRuleByRegionRequest) (*pb.ListABTestRuleByRegionResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	abTestRuleList, lcosErr := c.cdtCalculationService.ListABTestRuleByRegion(lcosCtx, strings.ToUpper(request.GetRegion()))
	if lcosErr != nil {
		return &pb.ListABTestRuleByRegionResponse{
			RespHeader:     http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
			AbTestRuleList: nil,
		}, nil
	}

	return &pb.ListABTestRuleByRegionResponse{
		RespHeader:     http.GrpcSuccessRespHeader(),
		AbTestRuleList: abTestRuleList,
	}, nil
}

func (c *cdtCalculationController) GetEDTManualManipulationForAlgoModel(ctx context.Context, request *pb.QueryManualManipulationRequest) (*pb.QueryManualManipulationResponse, error) {
	var (
		inputs   = make([]*cdt_calculation.CdtProductInfo, 0, len(request.GetProductInfo()))
		response = &pb.QueryManualManipulationResponse{
			RespHeader:    http.GrpcSuccessRespHeader(),
			DeltaReplyMap: make(map[string]*pb.DeltaReply),
		}
	)

	lcosCtx := utils.NewCommonCtx(ctx)

	for _, productInfo := range request.GetProductInfo() {
		inputs = append(inputs, transferPbSimpleProductToProduct(productInfo))
	}

	replyMap := c.cdtCalculationService.BatchGetEDTManualManipulationForAlgoModel(lcosCtx, inputs)

	for key, reply := range replyMap {
		response.DeltaReplyMap[key] = &pb.DeltaReply{
			QueryId:    utils.NewString(key),
			Retcode:    utils.NewInt32(reply.Retcode),
			Message:    utils.NewString(reply.Message),
			DeltaMin:   utils.NewFloat64(reply.DeltaMin),
			DeltaMax:   utils.NewFloat64(reply.DeltaMax),
			EffectTime: utils.NewUint32(reply.EffectiveTime),
			ExpireTime: utils.NewUint32(reply.ExpirationTime),
		}
	}
	return response, nil
}

func (c *cdtCalculationController) GetFallbackEddInfo(ctx context.Context, request *pb.GetFallbackEddInfoRequest) (*pb.GetFallbackEddInfoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	eddMin, eddMax, err := c.cdtCalculationService.GetFallbackEddInfo(lcosCtx, request.GetRegion(), request.GetForderId(), request.GetDaysToDelivery())
	if err != nil {
		return &pb.GetFallbackEddInfoResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}
	return &pb.GetFallbackEddInfoResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		EddMin:     utils.NewUint32(uint32(eddMin)),
		EddMax:     utils.NewUint32(uint32(eddMax)),
	}, nil
}
