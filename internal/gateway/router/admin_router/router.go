package admin_router

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/admin_api"
	_ "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/doc/api"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/handler"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/handler/common_handler"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/handler/lcos_handler"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/branch_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/algo_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_ab_test_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_overview"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_delay_queue_api"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_forecast_result"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_update_rule_conf"
	edd_update_rule_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/data_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/delivery_method"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/edt_config_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/geo_distance_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/parcel_library_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/holidays"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/open_logistic_pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/open_logistic_timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/pickup_window"
	holidays2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/recurring_holidays"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pis_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/sa_task/task_configuration"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/sa_task/task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/scheduled_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/basic_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/card_delivery_address_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/predefined_route_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/zone_serviceable_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area_rule"
	basic_serviceable2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/spx_serviceable_area_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/station"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/sync_item_card_cdt_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/time_experiment_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spx_service"
	"git.garena.com/shopee/bg-logistics/logistics/sls-location-plugin/geo_distance"
)

type AdminResource struct {
}

func InitAdminResource() *AdminResource {
	adminResource := &AdminResource{}
	return adminResource
}

func (r *AdminResource) URLPatterns() []restful.Route {
	server := admin_api.InitAdminAPIService()

	group := restful.NewRouterGroup("/lcos/admin/")
	handler.RegisterValidation()
	handler.RegisterTranslation()

	groupApi := group.Group("api/")
	// bank id使用
	{
		groupApi.POST("card_delivery_address/get_download_file_path", lcos_handler.Handle(server.CardDeliveryAddressController.GetCardDeliveryAddressDownloadFilePath, card_delivery_address_protocol.GetCardDeliveryAddressDownloadFilePathRequest{}))
	}

	group.POST("test/", Test)
	group.GET("enum_type/list/", GetClientEnumTypeList)
	{
		group.POST("line_package_limit/create/", lcos_handler.Handle(server.LinePackageLimitController.BatchCreatePackageLimits, &line_package_limit.CreateOrUpdateLinePackageLimitRequest{}))
		group.POST("line_package_limit/create_with_draft/", lcos_handler.Handle(server.LinePackageLimitController.BatchCreatePackageLimitsWithDraft, &line_package_limit.CreateOrUpdateLinePackageLimitRequest{}))
		group.POST("line_package_limit/update/", lcos_handler.Handle(server.LinePackageLimitController.BatchUpdatePackageLimits, &line_package_limit.CreateOrUpdateLinePackageLimitRequest{}))
		// group.GET("line_package_limit/list/", lcos_handler.Handle(server.LinePackageLimitController.GetPackageLimitList, &line_package_limit.GetPackageLimitListRequest{}))
		group.GET("line_package_limit/detail/", lcos_handler.Handle(server.LinePackageLimitController.GetPackageLimitByLineId, &common_protocol.LineIdRequest{}))
		group.POST("line_package_limit/delete/", lcos_handler.Handle(server.LinePackageLimitController.DeletePackageLimitByLineId, &common_protocol.LineIdRequest{}))
		group.POST("line_package_limit/create_or_update/", lcos_handler.Handle(server.LinePackageLimitController.CreateOrUpdatePackageLimits, &line_package_limit.CreateOrUpdateLinePackageLimitRequest{}))

		group.POST("line_package_limit/upload", lcos_handler.Handle(server.LinePackageLimitController.UploadPackageLimits, &common_protocol.LinePackageLimitUploadRequest{}))
	}

	{
		group.POST("product_package_limit/create/", lcos_handler.Handle(server.ProductPackageLimitController.BatchCreatePackageLimits, &product_package_limit.CreateOrUpdateProductPackageLimitRequest{}))
		group.POST("product_package_limit/update/", lcos_handler.Handle(server.ProductPackageLimitController.BatchUpdatePackageLimits, &product_package_limit.CreateOrUpdateProductPackageLimitRequest{}))
		// group.GET("product_package_limit/list/", admin_api.Handle(server.linePackageLimitService.GetPackageLimitList, &protocol.GetPackageLimitListRequest{}))
		group.GET("product_package_limit/detail/", lcos_handler.Handle(server.ProductPackageLimitController.GetPackageLimitByProductId, &common_protocol.ProductIdRequest{}))
		group.POST("product_package_limit/create_or_update/", lcos_handler.Handle(server.ProductPackageLimitController.CreateOrUpdatePackageLimits, &product_package_limit.CreateOrUpdateProductPackageLimitRequest{}))
	}

	// collect deliver group
	{
		group.GET("collect_deliver_group/list/", lcos_handler.Handle(server.CollectDeliverGroupController.GetAllCollectDeliverGroup, nil))
	}

	// basic基础层
	{
		ctrl := server.LineBasicServiceableConfController
		group.POST("basic_serviceable/create_conf/", lcos_handler.Handle(ctrl.CreateBasicServiceableConf, &basic_serviceable.CreateOrUpdateBasicServiceableConfRequest{}))
		group.POST("basic_serviceable/update_conf/", lcos_handler.Handle(ctrl.UpdateBasicServiceableConf, &basic_serviceable.CreateOrUpdateBasicServiceableConfRequest{}))
		group.GET("basic_serviceable/get_conf/", lcos_handler.Handle(ctrl.GetBasicServiceableConf, &common_protocol.LineIdRequest{}))
		group.GET("basic_serviceable/get_conf_list/", lcos_handler.Handle(ctrl.GetBasicServiceableConfList, &basic_serviceable.BasicServiceableConfListRequest{}))
		group.POST("basic_serviceable/delete_conf/", lcos_handler.Handle(ctrl.DeleteBasicServiceableConf, &common_protocol.LineIdRequest{}))
		group.POST("basic_serviceable/delete_all/", lcos_handler.Handle(ctrl.DeleteAllServiceableConf, &common_protocol.LineIdRequest{}))
		group.POST("basic_serviceable/simple_put_conf/", lcos_handler.Handle(ctrl.SimplePutBasicServiceableConf, &basic_serviceable.SimplePutBasicServiceableConfRequest{}))
		group.GET("basic_serviceable/batch_get_conf/", lcos_handler.Handle(ctrl.BatchGetBasicServiceableConf, &common_protocol.LineIdsRequest{}))
		group.GET("basic_serviceable/check_collect_deliver_group/", lcos_handler.Handle(ctrl.CheckCollectDeliverGroupWithLine, &basic_serviceable.CheckCollectDeliverGroupRequest{}))
		group.GET("basic_serviceable/lines_using_location_checked/export/", common_handler.Handle(ctrl.ExportLinesUsingLocationChecked, &basic_serviceable.ExportLinesUsingLocationChecked{}))
	}

	// 基础location层
	{
		group.POST("basic_serviceable/create_location", lcos_handler.Handle(server.LineBasicServiceableLocationController.CreateBasicServiceableLocation, &basic_serviceable.CreateBasicLocationRequest{}))
		group.POST("basic_serviceable/batch_create_location", lcos_handler.Handle(server.LineBasicServiceableLocationController.BatchCreateBasicServiceableLocation, &basic_serviceable.BatchCreateBasicLocationRequest{}))
		group.POST("basic_serviceable/update_location", lcos_handler.Handle(server.LineBasicServiceableLocationController.UpdateBasicServiceableLocation, &basic_serviceable.UpdateBasicLocationRequest{}))
		group.GET("basic_serviceable/get_location_list", lcos_handler.Handle(server.LineBasicServiceableLocationController.GetBasicServiceableLocationList, &basic_serviceable.GetBasicLocationListRequest{}))
		group.POST("basic_serviceable/delete_location", lcos_handler.Handle(server.LineBasicServiceableLocationController.DeleteBasicServiceableLocation, &basic_serviceable.DeleteBasicLocationRequest{}))
		group.GET("basic_serviceable/download_location", common_handler.Handle(server.LineBasicServiceableLocationController.DownloadBasicServiceableLocation, &basic_serviceable.GetAllBasicLocationRequest{}))
		group.POST("basic_serviceable/upload_location", lcos_handler.Handle(server.LineBasicServiceableLocationController.UploadBasicServiceableLocation, &common_protocol.UploadFileRequest{}))
		group.GET("basic_serviceable/search_address", lcos_handler.Handle(server.LineBasicServiceableLocationController.SearchAllFourLevelAddress, &common_protocol.SearchAllFourLevelAddressRequest{}))
	}

	// 基础postcode层
	{
		group.POST("basic_serviceable/create_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.CreateBasicServiceablePostcode, &basic_serviceable.CreateBasicPostcodeRequest{}))
		group.POST("basic_serviceable/update_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.UpdateBasicServiceablePostcode, &basic_serviceable.UpdateBasicPostcodeRequest{}))
		group.GET("basic_serviceable/get_postcode_list/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.GetBasicServiceablePostcodeList, &basic_serviceable.GetBasicPostcodeListRequest{}))
		group.POST("basic_serviceable/delete_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.DeleteBasicServiceablePostcode, &basic_serviceable.DeleteBasicPostcodeRequest{}))
		group.GET("basic_serviceable/download_postcode/", common_handler.Handle(server.LineBasicServiceablePostcodeController.DownloadBasicServiceablePostcode, &basic_serviceable.GetAllBasicPostcodeRequest{}))
		group.POST("basic_serviceable/upload_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.UploadBasicServiceablePostcode, &common_protocol.UploadFileRequest{}))
	}

	// 运营层location
	{
		group.POST("operation_serviceable/create_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.CreateOperationServiceableLocation, &operation_serviceable.CreateOperationLocationRequest{}))
		group.POST("operation_serviceable/batch_create_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.BatchCreateOperationServiceableLocation, &operation_serviceable.BatchCreateOperationLocationRequest{}))
		group.POST("operation_serviceable/update_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.UpdateOperationServiceableLocation, &operation_serviceable.UpdateOperationLocationRequest{}))
		group.GET("operation_serviceable/get_location_list/", lcos_handler.Handle(server.LineOperationServiceableLocationController.GetOperationServiceableLocationList, &operation_serviceable.GetOperationLocationListRequest{}))
		group.POST("operation_serviceable/delete_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.DeleteOperationServiceableLocation, &common_protocol.IDRequest{}))
		group.GET("operation_serviceable/download_location/", common_handler.Handle(server.LineOperationServiceableLocationController.DownloadOperationServiceableLocation, &operation_serviceable.GetAllOperationLocationRequest{}))
		group.POST("operation_serviceable/upload_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.UploadOperationServiceableLocation, &common_protocol.UploadFileRequest{}))
		group.POST("operation_serviceable/sync_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.SyncSearchLocationToBasicserviceable, &operation_serviceable.GetAllOperationLocationRequest{}))
		group.GET("operation_serviceable/search_address", lcos_handler.Handle(server.LineBasicServiceableLocationController.SearchAllFourLevelAddress, &common_protocol.SearchAllFourLevelAddressRequest{}))
	}
	// 运营层postcode
	{
		group.POST("operation_serviceable/create_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.CreateOperationServiceablePostcode, &operation_serviceable.CreateOperationPostcodeRequest{}))
		group.POST("operation_serviceable/update_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.UpdateOperationServiceablePostcode, &operation_serviceable.UpdateOperationPostcodeRequest{}))
		group.GET("operation_serviceable/get_postcode_list/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.GetOperationServiceablePostcodeList, &operation_serviceable.GetOperationPostcodeListRequest{}))
		group.POST("operation_serviceable/delete_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.DeleteOperationServiceablePostcode, &common_protocol.IDRequest{}))
		group.GET("operation_serviceable/download_postcode/", common_handler.Handle(server.LineOperationServiceablePostcodeController.DownloadOperationServiceablePostcode, &operation_serviceable.GetAllOperationPostcodeRequest{}))
		group.POST("operation_serviceable/upload_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.UploadOperationServiceablePostcode, &common_protocol.UploadFileRequest{}))
		group.POST("operation_serviceable/sync_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.SyncSearchPostcodeToBasicserviceable, &operation_serviceable.GetAllOperationPostcodeRequest{}))
	}

	// Deprecated
	{
		group.POST("basic_serviceable/create_origin_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.CreateBasicServiceableOriginLocation, &basic_serviceable.CreateBasicOriginLocationRequest{}))
		group.POST("basic_serviceable/batch_create_origin_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.BatchCreateBasicServiceableOriginLocation, &basic_serviceable.BatchCreateBasicOriginLocationRequest{}))
		group.POST("basic_serviceable/create_destination_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.CreateBasicServiceableDestLocation, &basic_serviceable.CreateBasicDestLocationRequest{}))
		group.POST("basic_serviceable/batch_create_destination_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.BatchCreateBasicServiceableDestLocation, &basic_serviceable.BatchCreateBasicDestLocationRequest{}))
		group.POST("basic_serviceable/update_origin_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.UpdateBasicServiceableOriginLocation, &basic_serviceable.UpdateBasicOriginLocationRequest{}))
		group.POST("basic_serviceable/update_destination_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.UpdateBasicServiceableDestLocation, &basic_serviceable.UpdateBasicDestLocationRequest{}))
		group.GET("basic_serviceable/get_origin_location_list/", lcos_handler.Handle(server.LineBasicServiceableLocationController.GetBasicServiceableOriginLocationList, &basic_serviceable.GetBasicOriginLocationListRequest{}))
		group.GET("basic_serviceable/get_destination_location_list/", lcos_handler.Handle(server.LineBasicServiceableLocationController.GetBasicServiceableDestLocationList, &basic_serviceable.GetBasicDestLocationListRequest{}))
		group.POST("basic_serviceable/delete_origin_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.DeleteBasicServiceableOriginLocation, &basic_serviceable.DeleteBasicLocationRequest{}))
		group.POST("basic_serviceable/delete_destination_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.DeleteBasicServiceableDestLocation, &basic_serviceable.DeleteBasicLocationRequest{}))
		group.GET("basic_serviceable/download_origin_location/", common_handler.Handle(server.LineBasicServiceableLocationController.DownloadBasicServiceableOriginLocation, &basic_serviceable.GetAllBasicOriginLocationRequest{}))
		group.GET("basic_serviceable/download_destination_location/", common_handler.Handle(server.LineBasicServiceableLocationController.DownloadBasicServiceableDestLocation, &basic_serviceable.GetAllBasicDestLocationRequest{}))
		group.POST("basic_serviceable/upload_origin_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.UploadBasicServiceableOriginLocation, &common_protocol.UploadFileRequest{}))
		group.POST("basic_serviceable/upload_destination_location/", lcos_handler.Handle(server.LineBasicServiceableLocationController.UploadBasicServiceableDestLocation, &common_protocol.UploadFileRequest{}))
	}

	// Deprecated
	{
		group.POST("basic_serviceable/create_origin_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.CreateBasicServiceableOriginPostcode, &basic_serviceable.CreateBasicOriginPostcodeRequest{}))
		group.POST("basic_serviceable/create_destination_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.CreateBasicServiceableDestPostcode, &basic_serviceable.CreateBasicDestPostcodeRequest{}))
		group.POST("basic_serviceable/update_origin_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.UpdateBasicServiceableOriginPostcode, &basic_serviceable.UpdateBasicOriginPostcodeRequest{}))
		group.POST("basic_serviceable/update_destination_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.UpdateBasicServiceableDestPostcode, &basic_serviceable.UpdateBasicDestPostcodeRequest{}))
		group.GET("basic_serviceable/get_origin_postcode_list/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.GetBasicServiceableOriginPostcodeList, &basic_serviceable.GetBasicOriginPostcodeListRequest{}))
		group.GET("basic_serviceable/get_destination_postcode_list/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.GetBasicServiceableDestPostcodeList, &basic_serviceable.GetBasicDestPostcodeListRequest{}))
		group.POST("basic_serviceable/delete_origin_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.DeleteBasicServiceableOriginPostcode, &basic_serviceable.DeleteBasicPostcodeRequest{}))
		group.POST("basic_serviceable/delete_destination_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.DeleteBasicServiceableDestPostcode, &basic_serviceable.DeleteBasicPostcodeRequest{}))
		group.GET("basic_serviceable/download_origin_postcode/", common_handler.Handle(server.LineBasicServiceablePostcodeController.DownloadBasicServiceableOriginPostcode, &basic_serviceable.GetAllBasicOriginPostcodeRequest{}))
		group.GET("basic_serviceable/download_destination_postcode/", common_handler.Handle(server.LineBasicServiceablePostcodeController.DownloadBasicServiceableDestPostcode, &basic_serviceable.GetAllBasicDestPostcodeRequest{}))
		group.POST("basic_serviceable/upload_origin_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.UploadBasicServiceableOriginPostcode, &common_protocol.UploadFileRequest{}))
		group.POST("basic_serviceable/upload_destination_postcode/", lcos_handler.Handle(server.LineBasicServiceablePostcodeController.UploadBasicServiceableDestPostcode, &common_protocol.UploadFileRequest{}))
	}

	// Deprecated operation
	{
		group.POST("operation_serviceable/create_conf/", lcos_handler.Handle(server.LineOperationServiceableConfController.CreateOperationServiceableConf, &operation_serviceable.CreateOrUpdateOperationServiceableConfRequest{}))
		group.POST("operation_serviceable/update_conf/", lcos_handler.Handle(server.LineOperationServiceableConfController.UpdateOperationServiceableConf, &operation_serviceable.CreateOrUpdateOperationServiceableConfRequest{}))
		group.GET("operation_serviceable/get_conf/", lcos_handler.Handle(server.LineOperationServiceableConfController.GetOperationServiceableConf, &common_protocol.LineIdRequest{}))
		group.GET("operation_serviceable/get_conf_list/", lcos_handler.Handle(server.LineOperationServiceableConfController.GetOperationServiceableConfList, &operation_serviceable.OperationServiceableConfListRequest{}))
		group.POST("operation_serviceable/delete_conf/", lcos_handler.Handle(server.LineOperationServiceableConfController.DeleteOperationServiceableConf, &common_protocol.LineIdRequest{}))
	}

	// Deprecated
	{
		group.POST("operation_serviceable/create_origin_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.CreateOperationServiceableOriginLocation, &operation_serviceable.CreateOperationOriginLocationRequest{}))
		group.POST("operation_serviceable/batch_create_origin_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.BatchCreateOperationServiceableOriginLocation, &operation_serviceable.BatchCreateOperationOriginLocationRequest{}))
		group.POST("operation_serviceable/create_destination_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.CreateOperationServiceableDestLocation, &operation_serviceable.CreateOperationDestLocationRequest{}))
		group.POST("operation_serviceable/batch_create_destination_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.BatchCreateOperationServiceableDestLocation, &operation_serviceable.BatchCreateOperationDestLocationRequest{}))
		group.POST("operation_serviceable/update_origin_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.UpdateOperationServiceableOriginLocation, &operation_serviceable.UpdateOperationOriginLocationRequest{}))
		group.POST("operation_serviceable/update_destination_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.UpdateOperationServiceableDestLocation, &operation_serviceable.UpdateOperationDestLocationRequest{}))
		group.GET("operation_serviceable/get_origin_location_list/", lcos_handler.Handle(server.LineOperationServiceableLocationController.GetOperationServiceableOriginLocationList, &operation_serviceable.GetOperationOriginLocationListRequest{}))
		group.GET("operation_serviceable/get_destination_location_list/", lcos_handler.Handle(server.LineOperationServiceableLocationController.GetOperationServiceableDestLocationList, &operation_serviceable.GetOperationDestLocationListRequest{}))
		group.POST("operation_serviceable/delete_origin_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.DeleteOperationServiceableOriginLocation, &common_protocol.IDRequest{}))
		group.POST("operation_serviceable/delete_destination_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.DeleteOperationServiceableDestLocation, &common_protocol.IDRequest{}))
		group.GET("operation_serviceable/download_origin_location/", common_handler.Handle(server.LineOperationServiceableLocationController.DownloadOperationServiceableOriginLocation, &operation_serviceable.GetAllOperationOriginLocationRequest{}))
		group.GET("operation_serviceable/download_destination_location/", common_handler.Handle(server.LineOperationServiceableLocationController.DownloadOperationServiceableDestLocation, &operation_serviceable.GetAllOperationDestLocationRequest{}))
		group.POST("operation_serviceable/upload_origin_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.UploadOperationServiceableOriginLocation, &common_protocol.UploadFileRequest{}))
		group.POST("operation_serviceable/upload_destination_location/", lcos_handler.Handle(server.LineOperationServiceableLocationController.UploadOperationServiceableDestLocation, &common_protocol.UploadFileRequest{}))
	}

	// Deprecated
	{
		group.POST("operation_serviceable/create_origin_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.CreateOperationServiceableOriginPostcode, &operation_serviceable.CreateOperationOriginPostcodeRequest{}))
		group.POST("operation_serviceable/create_destination_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.CreateOperationServiceableDestPostcode, &operation_serviceable.CreateOperationDestPostcodeRequest{}))
		group.POST("operation_serviceable/update_origin_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.UpdateOperationServiceableOriginPostcode, &operation_serviceable.UpdateOperationOriginPostcodeRequest{}))
		group.POST("operation_serviceable/update_destination_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.UpdateOperationServiceableDestPostcode, &operation_serviceable.UpdateOperationDestPostcodeRequest{}))
		group.GET("operation_serviceable/get_origin_postcode_list/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.GetOperationServiceableOriginPostcodeList, &operation_serviceable.GetOperationOriginPostcodeListRequest{}))
		group.GET("operation_serviceable/get_destination_postcode_list/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.GetOperationServiceableDestPostcodeList, &operation_serviceable.GetOperationDestPostcodeListRequest{}))
		group.POST("operation_serviceable/delete_origin_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.DeleteOperationServiceableOriginPostcode, &common_protocol.IDRequest{}))
		group.POST("operation_serviceable/delete_destination_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.DeleteOperationServiceableDestPostcode, &common_protocol.IDRequest{}))
		group.GET("operation_serviceable/download_origin_postcode/", common_handler.Handle(server.LineOperationServiceablePostcodeController.DownloadOperationServiceableOriginPostcode, &operation_serviceable.GetAllOperationOriginPostcodeRequest{}))
		group.GET("operation_serviceable/download_destination_postcode/", common_handler.Handle(server.LineOperationServiceablePostcodeController.DownloadOperationServiceableDestPostcode, &operation_serviceable.GetAllOperationDestPostcodeRequest{}))
		group.POST("operation_serviceable/upload_origin_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.UploadOperationServiceableOriginPostcode, &common_protocol.UploadFileRequest{}))
		group.POST("operation_serviceable/upload_destination_postcode/", lcos_handler.Handle(server.LineOperationServiceablePostcodeController.UploadOperationServiceableDestPostcode, &common_protocol.UploadFileRequest{}))
	}

	// zone & route
	{
		// area
		group.POST("area_route_serviceable/area/create/", lcos_handler.Handle(server.LineServiceableAreaController.Create, area_route_serviceable.CreateAreaRequest{}))
		group.POST("area_route_serviceable/area/delete/", lcos_handler.Handle(server.LineServiceableAreaController.Delete, common_protocol.IDRequest{}))
		group.GET("area_route_serviceable/area/get/", lcos_handler.Handle(server.LineServiceableAreaController.GetByID, common_protocol.IDRequest{}))
		group.GET("area_route_serviceable/area/list/", lcos_handler.Handle(server.LineServiceableAreaController.List, area_route_serviceable.SearchAreaRequest{}))
		group.GET("area_route_serviceable/area/getAll/", lcos_handler.Handle(server.LineServiceableAreaController.GetAll, area_route_serviceable.GetAllAreaRequest{}))
		group.GET("area_route_serviceable/area_location_ref/list/", lcos_handler.Handle(server.LineServiceableAreaController.ListAreaLocationRef, area_route_serviceable.SearchAreaLocationRefRequest{}))
		group.POST("area_route_serviceable/area_location_ref/delete/", lcos_handler.Handle(server.LineServiceableAreaController.DeleteAreaLocationRef, area_route_serviceable.DeleteAreaLocationRef{}))
		group.GET("area_route_serviceable/area_location_ref/download/", common_handler.Handle(server.LineServiceableAreaController.DownloadAreaLocationRef, area_route_serviceable.GetAllAreaLocationRefRequest{}))
		group.POST("area_route_serviceable/area_location_ref/upload/", lcos_handler.Handle(server.LineServiceableAreaController.UploadAreaLocationRef, common_protocol.UploadFileRequest{}))

		// route
		group.POST("area_route_serviceable/route/create/", lcos_handler.Handle(server.LineServiceableRouteController.CreateLineRoute, area_route_serviceable.CreateLineRouteRequest{}))
		group.POST("area_route_serviceable/route/delete/", lcos_handler.Handle(server.LineServiceableRouteController.DeleteLineRoute, area_route_serviceable.NewIDRequest{}))
		group.POST("area_route_serviceable/route/update/", lcos_handler.Handle(server.LineServiceableRouteController.UpdateLineRoute, area_route_serviceable.UpdateLineRouteRequest{}))
		// route/get 废弃了
		group.GET("area_route_serviceable/route/get/", lcos_handler.Handle(server.LineServiceableRouteController.GetLineRouteByID, area_route_serviceable.IDLineRouteRequest{}))
		group.GET("area_route_serviceable/route/list/", lcos_handler.Handle(server.LineServiceableRouteController.ListLineRoute, area_route_serviceable.SearchLineRouteRequest{}))
		group.POST("area_route_serviceable/route/upload/", lcos_handler.Handle(server.LineServiceableRouteController.UploadLineRoute, area_route_serviceable.UploadRouteRequest{}))
		group.GET("area_route_serviceable/route/download/", common_handler.Handle(server.LineServiceableRouteController.DownloadLineRoute, area_route_serviceable.SearchAllLineRouteRequest{}))

		// ops route
		group.POST("area_route_serviceable/operation_route/create/", lcos_handler.Handle(server.LineOperationRouteController.CreateOperationRoute, operation_serviceable.CreateOperationRouteRequest{}))
		group.POST("area_route_serviceable/operation_route/delete/", lcos_handler.Handle(server.LineOperationRouteController.DeleteOperationRoute, common_protocol.IDRequest{}))
		group.POST("area_route_serviceable/operation_route/update/", lcos_handler.Handle(server.LineOperationRouteController.UpdateOperationRoute, operation_serviceable.UpdateLineOperationRouteRequest{}))
		group.GET("area_route_serviceable/operation_route/get/", lcos_handler.Handle(server.LineOperationRouteController.GetLineOperationRouteByID, common_protocol.IDRequest{}))
		group.GET("area_route_serviceable/operation_route/list/", lcos_handler.Handle(server.LineOperationRouteController.ListLineOperationRoute, operation_serviceable.SearchLineOperationRouteRequest{}))
		group.POST("area_route_serviceable/operation_route/upload/", lcos_handler.Handle(server.LineOperationRouteController.UploadLineOperationRoute, operation_serviceable.UploadOperationRouteRequest{}))
		group.GET("area_route_serviceable/operation_route/download/", common_handler.Handle(server.LineOperationRouteController.DownloadLineOperationRoute, operation_serviceable.SearchAllLineOperationRouteRequest{}))
		group.POST("area_route_serviceable/operation_route/sync/", lcos_handler.Handle(server.LineOperationRouteController.SyncSearchRouteToBasicServiceable, operation_serviceable.SearchAllLineOperationRouteRequest{}))

	}
	// pickup window
	{
		group.POST("pickup_window/get_nonworking_days/", lcos_handler.Handle(server.PickupWindowController.GetNonworkingDays, pickup_window.GetNonworkingDaysRequest{}))
	}
	// non working days
	{
		group.POST("non_working_days/holidays/import/", lcos_handler.Handle(server.HolidaysController.Import, holidays.ImportHolidayRequest{}))
		group.GET("non_working_days/holidays/export/", common_handler.Handle(server.HolidaysController.Export, holidays.ListHolidayRequest{}))
		group.POST("non_working_days/holidays/delete/", lcos_handler.Handle(server.HolidaysController.Delete, holidays.DeleteHolidayRequest{}))
		group.POST("non_working_days/holidays/create/", lcos_handler.Handle(server.HolidaysController.Create, holidays.CreateHolidayRequest{}))
		group.GET("non_working_days/holidays/list/", lcos_handler.Handle(server.HolidaysController.List, holidays.ListHolidayRequest{}))
		group.POST("non_working_days/weekends/update/", lcos_handler.Handle(server.RecurringHolidaysController.Update, holidays2.UpdateRecurringHolidayRequest{}))
	}
	// pick up configuration
	{
		group.POST("pickup_configuration/pickup_conf/create/", lcos_handler.Handle(server.PickupConfigController.Create, pickup_config.CreatePickupConfigRequest{}))
		group.POST("pickup_configuration/pickup_conf/update/", lcos_handler.Handle(server.PickupConfigController.Update, pickup_config.UpdatePickupConfigRequest{}))
		group.POST("pickup_configuration/pickup_conf/delete/", lcos_handler.Handle(server.PickupConfigController.Delete, pickup_config.DeletePickupConfigRequest{}))
		group.POST("pickup_configuration/pickup_conf/disable/", lcos_handler.Handle(server.PickupConfigController.Disable, pickup_config.DisablePickupConfigRequest{}))
		group.GET("pickup_configuration/pickup_conf/list/", lcos_handler.Handle(server.PickupConfigController.List, pickup_config.ListPickupConfigRequest{}))
		// group.GET("pickup_configuration/pickup_conf/search/", lcos_handler.Handle(server.PickupConfigController.Search, pickup_config.SearchPickupConfigRequest{}))
		group.POST("pickup_configuration/timeslot/create_or_update/", lcos_handler.Handle(server.PickupTimeslotController.CreateOrUpdate, timeslot.CreateOrUpdateTimeslotRequest{}))
		group.POST("pickup_configuration/timeslot/delete/", lcos_handler.Handle(server.PickupTimeslotController.Delete, timeslot.DeleteTimeslotRequest{}))
		group.GET("pickup_configuration/timeslot/list/", lcos_handler.Handle(server.PickupTimeslotController.List, timeslot.ListTimeslotRequest{}))
	}
	// pick up group
	{
		group.POST("pickup_configuration/pickup_group/create/", lcos_handler.Handle(server.PickupGroupController.Create, pickup_group.CreatePickupGroupRequest{}))
		group.POST("pickup_configuration/pickup_group/update/", lcos_handler.Handle(server.PickupGroupController.Update, pickup_group.UpdatePickupGroupRequest{}))
		group.POST("pickup_configuration/pickup_group/Update_with_draft/", lcos_handler.Handle(server.PickupGroupController.UpdateWithDraft, pickup_group.UpdatePickupGroupRequest{}))
		group.POST("pickup_configuration/pickup_group/delete/", lcos_handler.Handle(server.PickupGroupController.Delete, pickup_group.DeletePickupGroupRequest{}))
		group.GET("pickup_configuration/pickup_group/get/", lcos_handler.Handle(server.PickupGroupController.GetByGroupId, pickup_group.GetPickupGroupRequest{}))
		group.GET("pickup_configuration/pickup_group/list/", lcos_handler.Handle(server.PickupGroupController.List, pickup_group.ListPickupGroupRequest{}))
		group.GET("pickup_configuration/pickup_group/all/", lcos_handler.Handle(server.PickupGroupController.All, pickup_group.ListAllPickupGroupRequest{}))
		group.GET("pickup_configuration/pickup_group/all_pickup_lines/", lcos_handler.Handle(server.PickupGroupController.AllPickupLines, pickup_group.ListAllPickupLinesRequest{}))
		group.GET("pickup_configuration/pickup_group/search/", lcos_handler.Handle(server.PickupGroupController.SearchPickupGroup, pickup_group.SearchPickupGroupRequest{}))
	}
	// open logistic pick up configuration
	{
		group.POST("open_logistic_pickup_configuration/pickup_conf/create/", lcos_handler.Handle(server.OpenLogisticPickupConfigController.Create, open_logistic_pickup_config.CreateOpenLogisticPickupConfigRequest{}))
		group.POST("open_logistic_pickup_configuration/pickup_conf/update/", lcos_handler.Handle(server.OpenLogisticPickupConfigController.Update, open_logistic_pickup_config.UpdateOpenLogisticPickupConfigRequest{}))
		group.POST("open_logistic_pickup_configuration/pickup_conf/delete/", lcos_handler.Handle(server.OpenLogisticPickupConfigController.Delete, open_logistic_pickup_config.DeleteOpenLogisticPickupConfigRequest{}))
		group.POST("open_logistic_pickup_configuration/pickup_conf/delete_by_merchant_type/", lcos_handler.Handle(server.OpenLogisticPickupConfigController.DeleteByMerchanType, open_logistic_pickup_config.DeleteOpenLogisticPickupConfigByMerchantTypeRequest{}))
		group.GET("open_logistic_pickup_configuration/pickup_conf/list/", lcos_handler.Handle(server.OpenLogisticPickupConfigController.List, open_logistic_pickup_config.ListOpenLogisticPickupConfigRequest{}))
		group.POST("open_logistic_pickup_configuration/pickup_conf/list_by_merchant_type/", lcos_handler.Handle(server.OpenLogisticPickupConfigController.ListByMerchanTypes, open_logistic_pickup_config.GetOpenLogisticPickupConfigByMerchantTypeRequest{}))
		group.POST("open_logistic_pickup_configuration/timeslot/create_or_update/", lcos_handler.Handle(server.OpenLogisticPickupTimeslotController.CreateOrUpdate, open_logistic_timeslot.CreateOrUpdateOpenLogisticTimeslotRequest{}))
		group.POST("open_logistic_pickup_configuration/timeslot/delete/", lcos_handler.Handle(server.OpenLogisticPickupTimeslotController.Delete, open_logistic_timeslot.DeleteOpenLogisticTimeslotRequest{}))
		group.GET("open_logistic_pickup_configuration/timeslot/list/", lcos_handler.Handle(server.OpenLogisticPickupTimeslotController.List, open_logistic_timeslot.ListOpenLogisticTimeslotRequest{}))
	}

	// cep_range

	{
		// 运营层
		group.POST("operation_cep_range/create", lcos_handler.Handle(server.LineOperationServiceableCepRangeController.CreateOperationServiceableCepRange, &operation_serviceable.OperationCepRangeCreateRequest{}))
		group.POST("operation_cep_range/delete", lcos_handler.Handle(server.LineOperationServiceableCepRangeController.DeleteOperationServiceableCepRange, &common_protocol.IDRequest{}))
		group.POST("operation_cep_range/update", lcos_handler.Handle(server.LineOperationServiceableCepRangeController.UpdateOperationServiceableCepRange, &operation_serviceable.OperationCepRangeUpdateRequest{}))
		group.GET("operation_cep_range/list", lcos_handler.Handle(server.LineOperationServiceableCepRangeController.ListOperationServiceableCepRange, &operation_serviceable.OperationCepRangeListRequest{}))
		group.POST("operation_cep_range/upload", lcos_handler.Handle(server.LineOperationServiceableCepRangeController.UploadOperationServiceableCepRange, &common_protocol.UploadFileRequest{}))
		group.GET("operation_cep_range/download", common_handler.Handle(server.LineOperationServiceableCepRangeController.DownloadOperationServiceableCepRange, &operation_serviceable.OperationCepRangeDownloadRequest{}))

		// 基础层
		group.POST("basic_cep_range/create", lcos_handler.Handle(server.LineBasicServiceableCepRangeController.CreateBasicServiceableCepRange, &basic_serviceable.BasicCepRangeCreateRequest{}))
		group.POST("basic_cep_range/delete", lcos_handler.Handle(server.LineBasicServiceableCepRangeController.DeleteBasicServiceableCepRange, &common_protocol.IDRequest{}))
		group.POST("basic_cep_range/update", lcos_handler.Handle(server.LineBasicServiceableCepRangeController.UpdateBasicServiceableCepRange, &basic_serviceable.BasicCepRangeUpdateRequest{}))
		group.GET("basic_cep_range/list", lcos_handler.Handle(server.LineBasicServiceableCepRangeController.ListBasicServiceableCepRange, &basic_serviceable.BasicCepRangeListRequest{}))
		group.POST("basic_cep_range/upload", lcos_handler.Handle(server.LineBasicServiceableCepRangeController.UploadBasicServiceableCepRange, &common_protocol.UploadFileRequest{}))
		group.GET("basic_cep_range/download", common_handler.Handle(server.LineBasicServiceableCepRangeController.DownloadBasicServiceableCepRange, &basic_serviceable.BasicCepRangeDownloadRequest{}))

	}

	{
		// group.POST("/admin/branch/list_branch_group", lcos_handler.Handle(server.BranchController.ListBranchGroup, &branch_protocol.ListBranchGroupRequest{}))
		service := server.BranchController
		g := group.Group("branch/")
		g.GET("list_branch_group/", lcos_handler.Handle(service.ListBranchGroup, &branch_protocol.ListBranchGroupRequest{}))
		g.GET("branch_group_detail/", lcos_handler.Handle(service.BranchGroupDetail, &branch_protocol.GetBranchGroupRequest{}))
		g.POST("update_branch_group/", lcos_handler.Handle(service.UpdateBranchGroup, &branch_protocol.UpdateBranchGroupRequest{}))
		g.POST("create_branch_group/", lcos_handler.Handle(service.CreateBranchGroup, &branch_protocol.CreateBranchGroupRequest{}))
		g.POST("sync_branch_info/", lcos_handler.Handle(service.SyncBranchInfo, &branch_protocol.SyncBranchInfoRequest{}))
		g.GET("list_branch/", lcos_handler.Handle(service.ListBranch, &branch_protocol.ListBranchRequest{}))
		g.GET("branch_detail/", lcos_handler.Handle(service.BranchDetail, &branch_protocol.GetBranchRequest{}))
		g.POST("delete_branch_group/", lcos_handler.Handle(service.DeleteBranchGroup, &branch_protocol.DeleteBranchGroupRequest{}))
		g.GET("search_branch_group/", lcos_handler.Handle(service.SearchBranchGroup, &branch_protocol.SearchBranchGroupRequest{}))
		g.GET("all_branch_supply_type/", lcos_handler.Handle(service.AllBranchSupply, &branch_protocol.GetAllBranchSupplyRequest{}))
		g.POST("get_sync_branch_supply_type/", lcos_handler.Handle(service.GetSyncBranchSupplyType, &branch_protocol.GetSyncBranchSupplyTypeRequest{}))
		g.POST("sync_branch_file_data/", lcos_handler.Handle(service.SyncBranchFileData, &branch_protocol.SyncBranchFileDataRequest{}))
		g.POST("sync_branch_file_data_for_pis/", lcos_handler.Handle(service.SyncBranchFileDataForPIS, &pis_protocol.PISCommonRequest{}))
		g.POST("sync_branch_data/", lcos_handler.Handle(service.SyncBranchData, &pis_protocol.SyncBranchDataRequest{}))

		// SPLN-21665 新增branch导入导出
		g.POST("upload/", lcos_handler.Handle(service.UploadBranch, &branch_protocol.UploadBranchRequest{}))
		g.GET("export/", lcos_handler.Handle(service.ExportBranch, &branch_protocol.ExportBranchRequest{}))
		g.GET("list_branch_task_record/", lcos_handler.Handle(service.ListBranchTaskRecord, &branch_protocol.ListBranchTaskRecordsRequest{}))
	}

	// for cdt common
	{
		group.GET("cdt_management/tpl_id_line_id_ref/list_3pl_info/", lcos_handler.Handle(server.TplIDLineIDRefController.List3plInfoList, tpl_id_line_id_ref.List3plInfo{}))
		group.GET("cdt_management/get_time_period/", lcos_handler.Handle(server.CdtAutoUpdateRuleController.GetTimePeriod, auto_update_rule.GetTimePeriod{}))
		group.GET("cdt_management/list_product_lanecode", lcos_handler.Handle(server.CdtManualManipulationRuleController.ListProductLaneCode, manual_manipulation_rule.ListProductLaneCodeReq{}))
	}

	// cdt auto update rule
	{
		group.POST("cdt_management/auto_update_rule/create/", lcos_handler.Handle(server.CdtAutoUpdateRuleController.CreateCdtAutoUpdateRule, auto_update_rule.CreateAutoRuleRequest{}))
		group.GET("cdt_management/auto_update_rule/list/", lcos_handler.Handle(server.CdtAutoUpdateRuleController.ListCdtAutoUpdateRuleByParams, auto_update_rule.ListAutoRuleRequest{}))
		group.GET("cdt_management/auto_update_rule/detail/", lcos_handler.Handle(server.CdtAutoUpdateRuleController.GetCdtAutoUpdateRuleByID, auto_update_rule.GetAutoRuleRequest{}))
		group.POST("cdt_management/auto_update_rule/delete/", lcos_handler.Handle(server.CdtAutoUpdateRuleController.DeleteCdtAutoUpdateRuleByID, auto_update_rule.DeleteAutoRuleRequest{}))
		group.POST("cdt_management/auto_update_rule/toggle_status/", lcos_handler.Handle(server.CdtAutoUpdateRuleController.ToggleCdtAutoUpdateRuleStatus, auto_update_rule.ToggleAutoUpdateRuleStatusRequest{}))
		group.GET("cdt_management/auto_update_rule/export_cep_range/", common_handler.Handle(server.CdtAutoUpdateRuleController.ExportCdtAutoUpdateRuleCepRangeByID, auto_update_rule.GetAutoRuleRequest{}))
		group.GET("cdt_management/auto_update_rule/export_postcode/", common_handler.Handle(server.CdtAutoUpdateRuleController.ExportCdtAutoUpdatePostcodeByID, auto_update_rule.GetAutoRuleRequest{}))
		group.GET("cdt_management/auto_update_rule/export/", common_handler.Handle(server.CdtAutoUpdateRuleController.ExportCdtAutoUpdateID, auto_update_rule.GetAutoRuleRequest{}))
		group.POST("cdt_management/auto_update_rule/update_product_ctime/", lcos_handler.Handle(server.CdtAutoUpdateRuleController.UpdateProductCtime, auto_update_rule.UpdateProductCtimeRequest{}))
		group.POST("cdt_management/auto_update_rule/notify/", lcos_handler.Handle(server.CdtAutoUpdateRuleController.ParseAndImportCdtAutoUpdateData, auto_update_rule.AutoUpdateDataNotify{}))
		// for qa test
		group.GET("cdt_management/auto_update_rule/list_lane_data", lcos_handler.Handle(server.CdtAutoUpdateRuleController.ListLaneAutoUpdateData, auto_update_rule.ListLaneAutoUpdateDataRequest{}))
	}

	// cdt manual manipulation rule
	{
		group.POST("cdt_management/manual_manipulation_rule/create/", lcos_handler.Handle(server.CdtManualManipulationRuleController.CreateManualManipulationRule, manual_manipulation_rule.CreateManualManipulationRequest{}))
		group.GET("cdt_management/manual_manipulation_rule/detail/", lcos_handler.Handle(server.CdtManualManipulationRuleController.GetManualManipulationRule, manual_manipulation_rule.GetManualManipulationRequest{}))
		group.GET("cdt_management/manual_manipulation_rule/list/", lcos_handler.Handle(server.CdtManualManipulationRuleController.ListCdtManualManipulationByParams, manual_manipulation_rule.ListManualManipulationRequest{}))
		group.POST("cdt_management/manual_manipulation_rule/toggle_status/", lcos_handler.Handle(server.CdtManualManipulationRuleController.ToggleManualManipulationStatus, manual_manipulation_rule.ToggleManualManipulationRuleStatusRequest{}))
		group.GET("cdt_management/manual_manipulation_rule/export_cep_range/", common_handler.Handle(server.CdtManualManipulationRuleController.ExportCepRangeFileByUUID, manual_manipulation_rule.GetManualManipulationRequest{}))
		group.GET("cdt_management/manual_manipulation_rule/export_postcode/", common_handler.Handle(server.CdtManualManipulationRuleController.ExportPostcodeFileByUUID, manual_manipulation_rule.GetManualManipulationRequest{}))
		group.POST("cdt_management/manual_manipulation_rule/delete/", lcos_handler.Handle(server.CdtManualManipulationRuleController.DeleteManualManipulationRuleById, manual_manipulation_rule.GetManualManipulationRequest{}))
		// for qa test
		group.GET("cdt_management/manual_manipulation_rule/list_lane_data", lcos_handler.Handle(server.CdtManualManipulationRuleController.ListLaneManualManipulationRules, manual_manipulation_rule.ListLaneManualManipulationRuleRequest{}))
	}

	// cdt manual update rule
	{
		group.POST("cdt_management/manual_update_rule/import/", lcos_handler.Handle(server.CdtManualUpdateRuleController.ImportCdtManualUpdateData, manual_update_rule.ImportCdtDataRequest{}))
		group.GET("cdt_management/manual_update_rule/sync_location_redis/", lcos_handler.Handle(server.CdtManualUpdateRuleController.SyncCdtManualLocationDataToRedis, manual_update_rule.SyncCdtManualLocationDataRequest{}))
		group.GET("cdt_management/manual_update_rule/export/", common_handler.Handle(server.CdtManualUpdateRuleController.ExportCdtManualUpdateData, manual_update_rule.ExportCdtDataRequest{}))
		group.GET("cdt_management/manual_update_rule/list_all_products/", lcos_handler.Handle(server.CdtManualUpdateRuleController.ListAllCdtProducts, manual_update_rule.ListAllProductList{}))
		group.GET("cdt_management/manual_update_rule/list/", lcos_handler.Handle(server.CdtManualUpdateRuleController.ListAllCdtRecordsPaging, manual_update_rule.ListManualUpdateRecordsPaging{}))
		group.POST("cdt_management/manual_update_rule/check_exist_by_product/", lcos_handler.Handle(server.CdtManualUpdateRuleController.CheckManualCdteExistsByProduct, manual_update_rule.MoudleCheckOfCdtByProductRequest{}))
		group.POST("cdt_management/manual_update_rule/delete_cdt_by_product/", lcos_handler.Handle(server.CdtManualUpdateRuleController.DeleteCdtByProduct, manual_update_rule.MoudleCheckOfCdtByProductRequest{}))
		// for qa test
		group.GET("cdt_management/manual_update_rule/list_lane_data", lcos_handler.Handle(server.CdtManualUpdateRuleController.ListLaneManualUpdateData, manual_update_rule.ListLaneManualUpdateDataRequest{}))
	}
	// cdt rule overview
	{
		group.POST("cdt_management/cdt_overview/list/", lcos_handler.Handle(server.CdtRuleOverviewController.ListRecordByParams, cdt_overview.ListRecordRequest{}))
	}

	// site serviceable area
	{
		// basic conf
		group.GET("site_serviceable_area/basic_conf/list/", lcos_handler.Handle(server.SiteServiceableAreaBasicConfController.ListSiteServiceableAreaBasicConf, basic_serviceable2.ListSiteServiceableAreaBasicConf{}))
		group.POST("site_serviceable_area/basic_conf/create/", lcos_handler.Handle(server.SiteServiceableAreaBasicConfController.CreateSiteServiceableAreaBasicConf, basic_serviceable2.CreateSiteServiceableAreaBasicConf{}))
		group.POST("site_serviceable_area/basic_conf/update/", lcos_handler.Handle(server.SiteServiceableAreaBasicConfController.UpdateSiteServiceableAreaBasicConf, basic_serviceable2.UpdateSiteServiceableAreaBasicConf{}))
		group.POST("site_serviceable_area/basic_conf/delete/", lcos_handler.Handle(server.SiteServiceableAreaBasicConfController.DeleteSiteServiceableAreaBasicConf, basic_serviceable2.DeleteSiteServiceableAreaBasicConf{}))

		// location
		group.GET("site_serviceable_area/location/list/", lcos_handler.Handle(server.SiteServiceableAreaLocationController.ListSiteServiceableAreaLocation, basic_serviceable2.ListSiteServiceableAreaLocation{}))
		group.POST("site_serviceable_area/location/create/", lcos_handler.Handle(server.SiteServiceableAreaLocationController.CreateSiteServiceableAreaLocation, basic_serviceable2.CreateSiteServiceableAreaLocation{}))
		group.POST("site_serviceable_area/location/delete/", lcos_handler.Handle(server.SiteServiceableAreaLocationController.DeleteSiteServiceableAreaLocation, basic_serviceable2.DeleteSiteServiceableAreaLocation{}))
		group.POST("site_serviceable_area/location/import/", lcos_handler.Handle(server.SiteServiceableAreaLocationController.UploadSiteServiceableAreaLocation, basic_serviceable2.UploadSiteServiceableAreaLocationRequest{}))
		group.GET("site_serviceable_area/location/export/", common_handler.Handle(server.SiteServiceableAreaLocationController.ExportSiteServiceableAreaLocation, basic_serviceable2.ExportSiteServiceableAreaLocation{}))

		// postcode
		group.GET("site_serviceable_area/postcode/list/", lcos_handler.Handle(server.SiteServiceableAreaPostcodeController.ListSiteServiceableAreaPostcode, basic_serviceable2.ListSiteServiceableAreaPostcode{}))
		group.POST("site_serviceable_area/postcode/create/", lcos_handler.Handle(server.SiteServiceableAreaPostcodeController.CreateSiteServiceableAreaPostcode, basic_serviceable2.CreateSiteServiceableAreaPostcode{}))
		group.POST("site_serviceable_area/postcode/delete/", lcos_handler.Handle(server.SiteServiceableAreaPostcodeController.DeleteSiteServiceableAreaPostcode, basic_serviceable2.DeleteSiteServiceableAreaPostcode{}))
		group.POST("site_serviceable_area/postcode/import/", lcos_handler.Handle(server.SiteServiceableAreaPostcodeController.UploadSiteServiceableAreaPostcode, basic_serviceable2.UploadSiteServiceableAreaPostcodeRequest{}))
		group.GET("site_serviceable_area/postcode/export/", common_handler.Handle(server.SiteServiceableAreaPostcodeController.ExportSiteServiceableAreaPostcode, basic_serviceable2.ExportSiteServiceableAreaPostcode{}))

		// cep_range
		group.GET("site_serviceable_area/cep_range/list/", lcos_handler.Handle(server.SiteServiceableAreaCepRangeController.ListSiteServiceableAreaCepRange, basic_serviceable2.ListSiteServiceableAreaCepRange{}))
		group.POST("site_serviceable_area/cep_range/create/", lcos_handler.Handle(server.SiteServiceableAreaCepRangeController.CreateSiteServiceableAreaCepRange, basic_serviceable2.CreateSiteServiceableAreaCepRange{}))
		group.POST("site_serviceable_area/cep_range/delete/", lcos_handler.Handle(server.SiteServiceableAreaCepRangeController.DeleteSiteServiceableAreaCepRange, basic_serviceable2.DeleteSiteServiceableAreaCepRange{}))
		group.POST("site_serviceable_area/cep_range/import/", lcos_handler.Handle(server.SiteServiceableAreaCepRangeController.UploadSiteServiceableAreaCepRange, basic_serviceable2.UploadSiteServiceableAreaCepRangeRequest{}))
		group.GET("site_serviceable_area/cep_range/export/", common_handler.Handle(server.SiteServiceableAreaCepRangeController.ExportSiteServiceableAreaCepRange, basic_serviceable2.ExportSiteServiceableAreaCepRange{}))

	}

	// sa task
	{
		// task configuration
		group.POST("basic_serviceable_task/create_task_conf", lcos_handler.Handle(server.SATaskConfigurationController.CreateSATaskConf, task_configuration.CreateTaskConfigurationRequest{}))
		group.POST("basic_serviceable_task/update_task_conf", lcos_handler.Handle(server.SATaskConfigurationController.UpdateSATaskConf, task_configuration.UpdateTaskConfigurationRequest{}))
		group.GET("basic_serviceable_task/list_task_conf", lcos_handler.Handle(server.SATaskConfigurationController.ListSATaskConf, task_configuration.ListTaskConfigurationRequest{}))
		group.GET("basic_serviceable_task/view_task_conf", lcos_handler.Handle(server.SATaskConfigurationController.GetSATaskConfDetail, task_configuration.GetTaskConfigurationDetailRequest{}))
		group.POST("basic_serviceable_task/toggle_status", lcos_handler.Handle(server.SATaskConfigurationController.ToggleSATaskStatus, task_configuration.GetTaskConfigurationDetailRequest{}))
		// find total size X
		group.GET("basic_serviceable_task/get_max_deletion_count", lcos_handler.Handle(server.SATaskConfigurationController.GetMaxDeletionCount, task_configuration.GetTaskConfigurationDetailRequest{}))

		// task record
		group.GET("basic_serviceable_task/list_task_record", lcos_handler.Handle(server.SATaskRecordController.ListSATaskRecord, task_record.ListTaskRecordRequest{}))
		group.POST("basic_serviceable_task/confirm_update", lcos_handler.Handle(server.SATaskRecordController.ConfirmUpdate, task_record.GetTaskRecordRequest{}))

		// for select query
		group.GET("basic_serviceable_task/all_task_names", lcos_handler.Handle(server.SATaskRecordController.GetAllTaskNames, nil))
		group.GET("basic_serviceable_task/all_task_operators", lcos_handler.Handle(server.SATaskRecordController.GetAllTaskOperator, nil))

	}

	// data version
	{
		group.GET("data_version_log/list", lcos_handler.Handle(server.DataVersionController.ListDataVersionLog, data_version.ListDataVersionLogRequest{}))
		group.POST("data_version/incr", lcos_handler.Handle(server.DataVersionController.IncrDataVersion, data_version.IncrDataVersionLogRequest{}))
	}

	{
		// initialized rule
		group.GET("initialized_rule/list", lcos_handler.Handle(server.ServiceableInitializedRuleController.GetServiceableInitializedRules, serviceable_area_rule.ListInitializedRuleRequest{}))
		group.GET("initialized_rule/detail", lcos_handler.Handle(server.ServiceableInitializedRuleController.DetailServiceableInitializedRules, common_protocol.IDRequest{}))
		group.POST("initialized_rule/create", lcos_handler.Handle(server.ServiceableInitializedRuleController.CreateServiceableInitializedRule, serviceable_area_rule.CreateInitializedRuleRequest{}))
		group.POST("initialized_rule/update", lcos_handler.Handle(server.ServiceableInitializedRuleController.UpdateServiceableInitializedRule, serviceable_area_rule.UpdateInitializedRuleRequest{}))
		group.POST("initialized_rule/delete", lcos_handler.Handle(server.ServiceableInitializedRuleController.DeleteServiceableInitializedRule, common_protocol.IDRequest{}))
		group.POST("initialized_rule/disable", lcos_handler.Handle(server.ServiceableInitializedRuleController.DisableServiceableInitializedRule, common_protocol.IDRequest{}))
		group.POST("initialized_rule/check_exist", lcos_handler.Handle(server.ServiceableInitializedRuleController.CheckServiceableInitializedRule, serviceable_area_rule.CheckInitializedRuleExistRequest{}))
		group.GET("initialized_rule/list_rule_id", lcos_handler.Handle(server.ServiceableInitializedRuleController.GetServiceableInitializedRuleIds, serviceable_area_rule.ListInitializedRuleRequest{}))

		// effective rule
		group.GET("effective_rule/list", lcos_handler.Handle(server.ServiceableEffectiveRuleController.GetEffectiveRule, serviceable_area_rule.ListEffectiveRuleRequest{}))
		group.GET("effective_rule/detail", lcos_handler.Handle(server.ServiceableEffectiveRuleController.DetailEffectiveRule, common_protocol.IDRequest{}))
		group.POST("effective_rule/update", lcos_handler.Handle(server.ServiceableEffectiveRuleController.UpdateEffectiveRule, serviceable_area_rule.UpdateEffectiveRuleRequest{}))
		group.POST("effective_rule/batch_update", lcos_handler.Handle(server.ServiceableEffectiveRuleController.BatchUpdateEffectiveRule, serviceable_area_rule.BatchUpdateEffectiveRuleRequest{}))
		group.POST("effective_rule/rule_info", lcos_handler.Handle(server.ServiceableEffectiveRuleController.GetBatchUpdateRuleInfo, serviceable_area_rule.GetRuleInfoRequest{}))
		group.POST("effective_rule/validate", lcos_handler.Handle(server.ServiceableEffectiveRuleController.CheckEffectiveRule, serviceable_area_rule.CheckEffectiveRuleRequest{}))
		group.GET("effective_rule/list_lanecode", lcos_handler.Handle(server.ServiceableEffectiveRuleController.GetLaneCodes, serviceable_area_rule.ListLaneCodeRequest{}))
		group.GET("effective_rule/list_product", lcos_handler.Handle(server.ServiceableEffectiveRuleController.GetProductIds, serviceable_area_rule.ListEffectiveRuleRequest{}))
		group.POST("effective_rule/export", lcos_handler.Handle(server.ServiceableEffectiveRuleController.ExportEffectiveRule, serviceable_area_rule.ExportEffectiveRuleRequest{}))

		group.GET("serviceable_rule/list_fulfillment_model", lcos_handler.Handle(server.ServiceableEffectiveRuleController.GetFulfillmentModels, serviceable_area_rule.ListFulfillmentModelRequest{}))
	}

	// edd update rule conf
	{
		group.POST("edd_update_rule_config/create", lcos_handler.Handle(server.EDDUpdateRuleConfController.CreateEDDUpdateRuleConf, edd_update_rule_conf.CreateEDDUpdateRuleConfRequest{}))
		group.POST("edd_update_rule_config/update", lcos_handler.Handle(server.EDDUpdateRuleConfController.UpdateEDDUpdateRuleConf, edd_update_rule_conf.UpdateEDDUpdateRuleConfRequest{}))
		group.GET("edd_update_rule_config/list", lcos_handler.Handle(server.EDDUpdateRuleConfController.ListEDDUpdateRuleConfByParams, edd_update_rule_conf.ListEDDUpdateRuleConfRequest{}))
		group.GET("edd_update_rule_config/detail", lcos_handler.Handle(server.EDDUpdateRuleConfController.GetEDDUpdateRuleConfByID, edd_update_rule_conf.GetEDDUpdateRuleConfRequest{}))
		group.POST("edd_update_rule_config/delete", lcos_handler.Handle(server.EDDUpdateRuleConfController.DeleteEDDUpdateRuleConf, edd_update_rule_conf.DeleteEDDUpdateRuleConfRequest{}))
	}
	// edd update task conf
	{
		group.POST("edd_update_task_config/create", lcos_handler.Handle(server.EDDUpdateTaskConfController.CreateEDDUpdateTaskConf, edd_update_rule_conf2.CreateEDDUpdateTaskConfRequest{}))
		group.POST("edd_update_task_config/preview", lcos_handler.Handle(server.EDDUpdateTaskConfController.PreviewOrderInfo, edd_update_rule_conf2.PreviewOrderInfo{}))
		group.GET("edd_update_task_config/list", lcos_handler.Handle(server.EDDUpdateTaskConfController.ListEDDUpdateTaskConfByParams, edd_update_rule_conf2.ListEDDUpdateTaskConfRequest{}))
		group.GET("edd_update_task_config/detail", lcos_handler.Handle(server.EDDUpdateTaskConfController.GetEDDUpdateTaskConfByID, edd_update_rule_conf2.DetailEDDUpdateTaskConfIDRequest{}))
		group.POST("edd_update_task_config/delete", lcos_handler.Handle(server.EDDUpdateTaskConfController.DeleteUpdateTaskConfByID, edd_update_rule_conf2.DetailEDDUpdateTaskConfIDRequest{}))
		group.POST("edd_update_task_config/trigger_push", lcos_handler.Handle(server.EDDUpdateTaskConfController.TriggerEDDUpdateTask, edd_update_rule_conf2.DetailEDDUpdateTaskConfIDRequest{}))

		// SPLN-27978
		group.GET("edd_update_task_config/preview_progress", lcos_handler.Handle(server.EDDUpdateTaskConfController.GetPreviewTaskRecord, edd_update_rule_conf2.GetPreviewTaskRequest{}))
		group.POST("edd_update_task_config/cancel", lcos_handler.Handle(server.EDDUpdateTaskConfController.CancelPreviewTaskRecord, edd_update_rule_conf2.GetPreviewTaskRequest{}))
	}
	// edd history list
	{
		group.GET("edd_history/list", lcos_handler.Handle(server.EDDHistoryController.ListEDDHistories, edd_history.ListEDDHistoryRequest{}))
	}
	// edd auto update
	{
		group.POST("edd_auto_update_rule/check_can_create", lcos_handler.Handle(server.EDDAutoUpdateController.CheckCanCreateEDDAutoUpdateRule, edd_auto_update_rule.CreateEDDAutoUpdateRuleRequest{}))
		group.POST("edd_auto_update_rule/create", lcos_handler.Handle(server.EDDAutoUpdateController.CreateEDDAutoUpdateRule, edd_auto_update_rule.CreateEDDAutoUpdateRuleRequest{}))
		group.GET("edd_auto_update_rule/list", lcos_handler.Handle(server.EDDAutoUpdateController.ListEDDAutoUpdateRulesByParams, edd_auto_update_rule.ListEDDAutoUpdateRuleRequest{}))
		// deprecated SPLN-29072 will not allow update edd auto update rule in the future
		group.POST("edd_auto_update_rule/update", lcos_handler.Handle(server.EDDAutoUpdateController.UpdateEDDAutoUpdateRule, edd_auto_update_rule.UpdateEDDAutoUpdateRuleRequest{}))
		group.POST("edd_auto_update_rule/delete", lcos_handler.Handle(server.EDDAutoUpdateController.DeleteEDDAutoUpdateRuleConf, edd_auto_update_rule.GetEDDAutoUpdateRuleRequest{}))
		group.POST("edd_auto_update_rule/toggle_status", lcos_handler.Handle(server.EDDAutoUpdateController.ToggleEDDAutoUpdateRuleConf, edd_auto_update_rule.ToggleEDDAutoUpdateStatusRequest{}))
		group.GET("edd_auto_update_rule/detail", lcos_handler.Handle(server.EDDAutoUpdateController.GetEDDAutoUpdateRuleByID, edd_auto_update_rule.GetEDDAutoUpdateRuleRequest{}))
	}
	// edd all in one model
	{
		eddModelService := server.EDDModelConfController
		group.GET("edd_model_config/list", lcos_handler.Handle(eddModelService.ListEDDModelConf, algo_model_conf.ListAlgoModelConfRequest{}))
		group.GET("edd_model_config/detail", lcos_handler.Handle(eddModelService.GetDetailEDDModelConf, algo_model_conf.GetDetailAlgoModelConfRequest{}))
		group.POST("edd_model_config/enable", lcos_handler.Handle(eddModelService.ToggleEDDModelConf, algo_model_conf.ToggleAlgoModelConfRequest{}))
		group.POST("edd_model_config/simulation/list", lcos_handler.Handle(eddModelService.ListEDDSimulation, algo_model_conf.ListAlgoSimulationRequest{}))
		group.POST("edd_model_config/simulation/route_group/list", lcos_handler.Handle(eddModelService.ListEDDRouteSimulation, algo_model_conf.ListAlgoSimulationRequest{}))
		group.POST("edd_model_config/simulation/deploy", lcos_handler.Handle(eddModelService.DeployEDDSimulation, algo_model_conf.DeployAlgoSimulationRequest{}))
		group.POST("edd_model_config/simulation/revoke", lcos_handler.Handle(eddModelService.RevokeEDDSimulation, algo_model_conf.RevokeAlgoSimulationRequest{}))
		group.POST("edd_model_config/simulation/route_group/deploy", lcos_handler.Handle(eddModelService.DeployEDDRouteSimulation, algo_model_conf.DeployAlgoSimulationRequest{}))
		group.POST("edd_model_config/simulation/route_group/revoke", lcos_handler.Handle(eddModelService.RevokeEDDRouteSimulation, algo_model_conf.RevokeAlgoSimulationRequest{}))

		group.GET("edd_model_config/metadata/query/", lcos_handler.Handle(eddModelService.QueryAllMetaData, nil))
		group.POST("edd_model_config/metadata/deploy_all/", lcos_handler.Handle(eddModelService.DeployAllMetaData, algo_model_conf.DeployAllMetaDataRequest{}))
		group.POST("edd_model_config/metadata/list_all_recommend/", lcos_handler.Handle(eddModelService.ListRecommendMeta, algo_model_conf.ListRecommendMetaDataRequest{}))
		group.POST("edd_model_config/metadata/replace_recommend/", lcos_handler.Handle(eddModelService.ReplaceRecommendMeta, algo_model_conf.ReplaceRecommendMetaDataRequest{}))
		group.POST("edd_model_config/metadata/route_group/replace_recommend/", lcos_handler.Handle(eddModelService.ReplaceRouteRecommendMeta, algo_model_conf.ReplaceRouteRecommendMetaDataRequest{}))
		group.POST("edd_model_config/metadata/route_group/clear_recommend/", lcos_handler.Handle(eddModelService.ClearRouteRecommendMeta, algo_model_conf.ClearRecommendMetaDataRequest{}))
		group.POST("edd_model_config/metadata/simulation/list/", lcos_handler.Handle(eddModelService.ListMetaDataSimulation, algo_model_conf.ListAlgoSimulationRequest{}))
		group.POST("edd_model_config/metadata/simulation/route_group/list/", lcos_handler.Handle(eddModelService.ListRouteMetaDataSimulation, algo_model_conf.ListAlgoSimulationRequest{}))
		group.GET("edd_model_config/metadata/simulation/detail/", lcos_handler.Handle(eddModelService.GetMetaDataSimulationDetail, algo_model_conf.GetDetailAlgoModelConfRequest{}))
	}

	// edt all in one model
	{
		edtModelService := server.EDTModelConfController
		group.GET("edt_model_config/list", lcos_handler.Handle(edtModelService.ListEDTModelConf, algo_model_conf.ListAlgoModelConfRequest{}))
		group.GET("edt_model_config/detail", lcos_handler.Handle(edtModelService.GetDetailEDTModelConf, algo_model_conf.GetDetailAlgoModelConfRequest{}))
		group.POST("edt_model_config/enable", lcos_handler.Handle(edtModelService.ToggleEDTModelConf, algo_model_conf.ToggleAlgoModelConfRequest{}))
		group.POST("edt_model_config/simulation/list", lcos_handler.Handle(edtModelService.ListEDTSimulation, algo_model_conf.ListAlgoSimulationRequest{}))
		group.POST("edt_model_config/simulation/route_group/list", lcos_handler.Handle(edtModelService.ListEDTRouteSimulation, algo_model_conf.ListAlgoSimulationRequest{}))
		group.POST("edt_model_config/simulation/deploy", lcos_handler.Handle(edtModelService.DeployEDTSimulation, algo_model_conf.DeployAlgoSimulationRequest{}))
		group.POST("edt_model_config/simulation/revoke", lcos_handler.Handle(edtModelService.RevokeEDTSimulation, algo_model_conf.RevokeAlgoSimulationRequest{}))
		group.POST("edt_model_config/simulation/route_group/deploy", lcos_handler.Handle(edtModelService.DeployEDTRouteSimulation, algo_model_conf.DeployAlgoSimulationRequest{}))
		group.POST("edt_model_config/simulation/route_group/revoke", lcos_handler.Handle(edtModelService.RevokeEDTRouteSimulation, algo_model_conf.RevokeAlgoSimulationRequest{}))

		group.GET("edt_model_config/metadata/query/", lcos_handler.Handle(edtModelService.QueryAllMetaData, nil))
		group.POST("edt_model_config/metadata/deploy_all/", lcos_handler.Handle(edtModelService.DeployAllMetaData, algo_model_conf.DeployAllMetaDataRequest{}))
		group.POST("edt_model_config/metadata/list_all_recommend/", lcos_handler.Handle(edtModelService.ListRecommendMeta, algo_model_conf.ListRecommendMetaDataRequest{}))
		group.POST("edt_model_config/metadata/replace_recommend/", lcos_handler.Handle(edtModelService.ReplaceRecommendMeta, algo_model_conf.ReplaceRecommendMetaDataRequest{}))
		group.POST("edt_model_config/metadata/route_group/replace_recommend/", lcos_handler.Handle(edtModelService.ReplaceRouteRecommendMeta, algo_model_conf.ReplaceRouteRecommendMetaDataRequest{}))
		group.POST("edt_model_config/metadata/route_group/clear_recommend/", lcos_handler.Handle(edtModelService.ClearRouteRecommendMeta, algo_model_conf.ClearRecommendMetaDataRequest{}))
		group.POST("edt_model_config/metadata/simulation/list/", lcos_handler.Handle(edtModelService.ListMetaDataSimulation, algo_model_conf.ListAlgoSimulationRequest{}))
		group.POST("edt_model_config/metadata/simulation/route_group/list/", lcos_handler.Handle(edtModelService.ListRouteMetaDataSimulation, algo_model_conf.ListAlgoSimulationRequest{}))
		group.GET("edt_model_config/metadata/simulation/detail/", lcos_handler.Handle(edtModelService.GetMetaDataSimulationDetail, algo_model_conf.GetDetailAlgoModelConfRequest{}))
	}

	{
		// scheduled
		group.POST("scheduled/update", lcos_handler.Handle(server.AdminScheduledController.UpdateScheduledJob, scheduled_protocol.UpdateScheduledJobRequest{}))
		group.GET("scheduled/list", lcos_handler.Handle(server.AdminScheduledController.ListScheduledJob, scheduled_protocol.ListScheduledJobRequest{}))
	}
	{
		// SPLN-28855
		// forecast task
		group.POST("edd_forecast/create", lcos_handler.Handle(server.EDDForecastTaskController.CreateEDDForecastTask, edd_forecast_task.CreateEDDForecastTaskRequest{}))
		group.GET("edd_forecast/list", lcos_handler.Handle(server.EDDForecastTaskController.ListEDDForecastTasks, edd_forecast_task.ListEDDForecastTaskRequest{}))
		group.GET("edd_forecast/detail", lcos_handler.Handle(server.EDDForecastTaskController.DetailEDDForecastTask, edd_forecast_task.DetailEDDForecastTaskRequest{}))
		group.POST("edd_forecast/deploy", lcos_handler.Handle(server.EDDForecastTaskController.DeployEDDForecastRules, edd_forecast_task.DeployEDDForecastTaskRequest{}))

		// forecast task result
		group.POST("edd_forecast_result/import", lcos_handler.Handle(server.EDDForecastTaskResultController.ImportEDDForecastTaskResult, edd_forecast_result.ImportEDDForecastTaskResultRequest{}))
		group.GET("edd_forecast_result/detail", lcos_handler.Handle(server.EDDForecastTaskResultController.DetailEDDForecastTaskResult, edd_forecast_result.DetailEDDForecastTaskResultRequest{}))
		group.GET("edd_forecast_result/export", common_handler.Handle(server.EDDForecastTaskResultController.ExportEDDForecastTaskResult, edd_forecast_result.ExportEDDForecastTaskResultRequest{}))
		group.GET("edd_forecast_result/get_deploy_info", lcos_handler.Handle(server.EDDForecastTaskResultController.GetDeployInfo, edd_forecast_result.GetDeployInfoRequest{}))

		// for qa test only
		group.POST("data_sdk/calculate", lcos_handler.Handle(server.EDDForecastTaskResultController.CalculateEDDBySDK, edd_forecast_result.CalculateEDDBySDKRequest{}))
	}
	{
		group.GET("edd_delay_queue/get_queue_name", lcos_handler.Handle(server.EDDDelayQueueDebugController.GetQueueName, edd_delay_queue_api.GetQueueNameRequest{}))
		group.POST("edd_delay_queue/push_edd_waybill", lcos_handler.Handle(server.EDDDelayQueueDebugController.PushEddWayBill, edd_delay_queue_api.EddWayBillRequest{}))
		group.POST("edd_delay_queue/remove_edd_waybill", lcos_handler.Handle(server.EDDDelayQueueDebugController.RemoveEddWayBill, edd_delay_queue_api.EddWayBillRequest{}))
		group.GET("edd_delay_queue/list_edd_waybill", lcos_handler.Handle(server.EDDDelayQueueDebugController.ListEddWayBill, edd_delay_queue_api.ListEddWayBillRequest{}))
	}

	// delivery instruction
	// spln-30192 spln-30606
	{
		group.POST("delivery_instruction/create", lcos_handler.Handle(server.DeliveryInstructionConfController.CreateDeliveryInstructionConf, delivery_method.EditDeliveryInstructionRequest{}))
		group.POST("delivery_instruction/edit", lcos_handler.Handle(server.DeliveryInstructionConfController.EditDeliveryInstructionConf, delivery_method.EditDeliveryInstructionRequest{}))
		group.GET("delivery_instruction/list", lcos_handler.Handle(server.DeliveryInstructionConfController.ListDeliveryInstructionConf, delivery_method.ListDeliveryInstructionRequest{}))
		group.GET("delivery_instruction/detail", lcos_handler.Handle(server.DeliveryInstructionConfController.DetailDeliveryInstructionConf, delivery_method.RuleIdDeliveryInstructionRequest{}))
		group.POST("delivery_instruction/delete", lcos_handler.Handle(server.DeliveryInstructionConfController.DeleteDeliveryInstructionConf, delivery_method.RuleIdDeliveryInstructionRequest{}))
		group.GET("delivery_instruction/option_list", lcos_handler.Handle(server.DeliveryInstructionConfController.OptionListDeliveryInstruction, nil))

		// whitelist location
		group.GET("delivery_instruction/whitelist_config/location/list/", lcos_handler.Handle(server.DeliveryInstructionWhitelistLocationController.ListDeliveryInstructionWhitelistLocation, whitelist.ListDeliveryInstructionWhitelistLocation{}))
		group.POST("delivery_instruction/whitelist_config/location/delete/", lcos_handler.Handle(server.DeliveryInstructionWhitelistLocationController.DeleteDeliveryInstructionWhitelistLocation, whitelist.DeleteDeliveryInstructionWhitelistLocation{}))
		group.POST("delivery_instruction/whitelist_config/location/import/", lcos_handler.Handle(server.DeliveryInstructionWhitelistLocationController.UploadDeliveryInstructionWhitelistLocation, whitelist.UploadDeliveryInstructionWhitelistLocationRequest{}))
		group.GET("delivery_instruction/whitelist_config/location/export/", common_handler.Handle(server.DeliveryInstructionWhitelistLocationController.ExportDeliveryInstructionWhitelistLocation, whitelist.ExportDeliveryInstructionWhitelistLocation{}))

		// whitelist cep_range
		group.GET("delivery_instruction/whitelist_config/cep_range/list/", lcos_handler.Handle(server.DeliveryInstructionWhitelistCepRangeController.ListDeliveryInstructionWhitelistCepRange, whitelist.ListDeliveryInstructionWhitelistCepRange{}))
		group.POST("delivery_instruction/whitelist_config/cep_range/delete/", lcos_handler.Handle(server.DeliveryInstructionWhitelistCepRangeController.DeleteDeliveryInstructionWhitelistCepRange, whitelist.DeleteDeliveryInstructionWhitelistCepRange{}))
		group.POST("delivery_instruction/whitelist_config/cep_range/import/", lcos_handler.Handle(server.DeliveryInstructionWhitelistCepRangeController.UploadDeliveryInstructionWhitelistCepRange, whitelist.UploadDeliveryInstructionWhitelistCepRangeRequest{}))
		group.GET("delivery_instruction/whitelist_config/cep_range/export/", common_handler.Handle(server.DeliveryInstructionWhitelistCepRangeController.ExportDeliveryInstructionWhitelistCepRange, whitelist.ExportDeliveryInstructionWhitelistCepRange{}))
	}

	{
		// SPLN-28857 SLS-SPX服务范围同步
		spxServiceableAreaGroup := group.Group("spx_serviceable_area/")

		// common
		spxServiceableAreaGroup.GET("list_order_account", lcos_handler.Handle(server.SpxServiceableAreaController.ListAllOrderAccount, nil))
		spxServiceableAreaGroup.GET("get_location_type", lcos_handler.Handle(server.SpxServiceableAreaController.GetLocationType, nil))

		// order account mapping management
		spxServiceableAreaGroup.POST("order_account_mapping/create", lcos_handler.Handle(server.OrderAccountMappingController.CreateOrderAccountMapping, spx_serviceable_area_protocol.CreateOrderAccountMappingRequest{}))
		spxServiceableAreaGroup.POST("order_account_mapping/update", lcos_handler.Handle(server.OrderAccountMappingController.UpdateOrderAccountMapping, spx_serviceable_area_protocol.UpdateOrderAccountMappingReqeust{}))
		spxServiceableAreaGroup.POST("order_account_mapping/delete", lcos_handler.Handle(server.OrderAccountMappingController.DeleteOrderAccountMapping, spx_serviceable_area_protocol.DeleteOrderAccountMappingRequest{}))
		spxServiceableAreaGroup.GET("order_account_mapping/list", lcos_handler.Handle(server.OrderAccountMappingController.ListOrderAccountMapping, spx_serviceable_area_protocol.ListOrderAccountMappingRequest{}))

		// spx version notify
		spxServiceableAreaGroup.POST("version_notify/create", lcos_handler.Handle(server.SpxServiceableAreaController.CreateSpxServiceableAreaVersion, spx_service.ServiceableAreaScheduledPlanning{}))
		spxServiceableAreaGroup.POST("version_notify/cancel", lcos_handler.Handle(server.SpxServiceableAreaController.CancelSpxServiceableAreaVersion, spx_service.ServiceableAreaScheduledPlanning{}))
		spxServiceableAreaGroup.POST("version_notify/enable", lcos_handler.Handle(server.SpxServiceableAreaController.EnableSpxServiceableAreaVersion, spx_service.ServiceableAreaScheduledPlanning{}))

		// spx version config data management, will be invoked by dev side
		spxServiceableAreaGroup.POST("version_data/pull", lcos_handler.Handle(server.SpxServiceableAreaController.PullSpxServiceableAreaData, spx_service.ServiceableAreaScheduledPlanning{}))
		spxServiceableAreaGroup.POST("version_data/clear", lcos_handler.Handle(server.SpxServiceableAreaController.ClearExpiredSpxServiceableArea, nil))
		spxServiceableAreaGroup.POST("version_data/update", lcos_handler.Handle(server.SpxServiceableAreaController.UpdateSpxServiceableArea, spx_serviceable_area_protocol.UpdateSpxServiceableAreaRequest{}))

		// spx serviceable area compare
		spxServiceableAreaGroup.GET("compare_task/list", lcos_handler.Handle(server.SpxServiceableAreaController.ListSpxServiceableAreaCompareTask, spx_serviceable_area_protocol.ListSpxServiceableAreaCompareTaskRequest{}))
		spxServiceableAreaGroup.POST("compare_task/trigger", lcos_handler.Handle(server.SpxServiceableAreaController.TriggerSpxServiceableAreaCompare, spx_serviceable_area_protocol.SpxServiceableAreaCompareRequest{}))
		spxServiceableAreaGroup.POST("compare_task/cancel", lcos_handler.Handle(server.SpxServiceableAreaController.CancelSpxServiceableAreaCompare, spx_serviceable_area_protocol.SpxServiceableAreaCompareRequest{}))
	}

	{
		cdtAbTestGroup := group.Group("cdt_ab_test/")
		cdtAbTestGroup.POST("create", lcos_handler.Handle(server.CdtAbTestController.CreateCdtAbTestRule, cdt_ab_test_protocol.CreateCdtAbTestRuleRequest{}))
		cdtAbTestGroup.POST("update", lcos_handler.Handle(server.CdtAbTestController.UpdateCdtAbTestRule, cdt_ab_test_protocol.UpdateCdtAbTestRuleRequest{}))
		cdtAbTestGroup.POST("delete", lcos_handler.Handle(server.CdtAbTestController.DeleteCdtAbTestRule, cdt_ab_test_protocol.DeleteCdtAbTestRuleRequest{}))
		cdtAbTestGroup.GET("list", lcos_handler.Handle(server.CdtAbTestController.ListCdtAbTestRule, cdt_ab_test_protocol.ListCdtAbTestRuleRequest{}))
		cdtAbTestGroup.POST("enable", lcos_handler.Handle(server.CdtAbTestController.EnableCdtAbTestRule, cdt_ab_test_protocol.EnableCdtAbTestRuleRequest{}))
		cdtAbTestGroup.POST("deploy", lcos_handler.Handle(server.CdtAbTestController.DeployCdtAbTestRule, cdt_ab_test_protocol.DeployCdtAbTestRuleRequest{}))
		cdtAbTestGroup.GET("get_ab_test_available_rule", lcos_handler.Handle(server.CdtAbTestController.GetAbTestAvailableRule, cdt_ab_test_protocol.GetAbTestAvailableRuleRequest{}))
	}

	// spln-31544 cdt aggregate m-channel auto update
	{
		group.POST("aggregate_masked_channel_cdt/create", lcos_handler.Handle(server.AggregateMaskedChannelCdtController.CreateAggregateMaskedChannelCdt, aggregate_masked_channel_cdt.CreateAggregateMaskedChannelRequest{}))
		group.GET("aggregate_masked_channel_cdt/list", lcos_handler.Handle(server.AggregateMaskedChannelCdtController.ListAggregateMaskedChannelCdt, aggregate_masked_channel_cdt.ListAggregateMaskedChannelRequest{}))
		group.GET("aggregate_masked_channel_cdt/detail", lcos_handler.Handle(server.AggregateMaskedChannelCdtController.DetailAggregateMaskedChannelCdt, aggregate_masked_channel_cdt.RuleIdAggregateMaskedChannelRequest{}))
		group.POST("aggregate_masked_channel_cdt/disable", lcos_handler.Handle(server.AggregateMaskedChannelCdtController.DisableAggregateMaskedChannelCdt, aggregate_masked_channel_cdt.RuleIdAggregateMaskedChannelRequest{}))

		group.POST("automated_volume_generation/create", lcos_handler.Handle(server.AutomatedVolumeRuleController.CreateAutomatedVolumeRule, automated_volume.CreateAutomatedVolumeRuleRequest{}))
		group.GET("automated_volume_generation/list", lcos_handler.Handle(server.AutomatedVolumeRuleController.ListAutomatedVolumeRule, automated_volume.ListAutomatedVolumeRuleRequest{}))
		group.GET("automated_volume_generation/detail", lcos_handler.Handle(server.AutomatedVolumeRuleController.DetailAutomatedVolumeRule, automated_volume.DetailAutomatedVolumeRuleRequest{}))
		// data 推送单量统计结果接口
		group.POST("automated_volume_generation/import", lcos_handler.Handle(server.AutomatedVolumeRuleController.ParseAndImportAutomatedVolumeRule, automated_volume.ImportAutomatedVolumeRuleRequest{}))
	}

	{
		// sync item card cdt to codis
		group.POST("item_card_cdt/sync_auto_update_data", lcos_handler.Handle(server.SyncItemCardCdtController.SyncAutoUpdateData, sync_item_card_cdt_protocol.SyncAutoUpdateCdtRequest{}))
		group.POST("item_card_cdt/sync_manual_update_data", lcos_handler.Handle(server.SyncItemCardCdtController.SyncManualUpdateData, sync_item_card_cdt_protocol.SyncManualUpdateCdtRequest{}))
	}
	{
		// sync site sa to mpl codis
		group.POST("site_serviceable_area/sync_location_data", lcos_handler.Handle(server.SiteServiceableAreaLocationController.SyncLocationData, basic_serviceable2.SyncLocationDataRequest{}))
	}

	// station
	{
		service := server.StationController
		g := group.Group("station/")
		g.GET("list", lcos_handler.Handle(service.ListStation, &station.ListStationRequest{}))
		g.GET("detail", lcos_handler.Handle(service.GetStation, &station.GetStationRequest{}))
		g.GET("export", lcos_handler.Handle(service.ExportStation, &station.ExportStationRequest{}))
		g.POST("batch_sync", lcos_handler.Handle(service.BatchSyncStation, &pis_protocol.PISCommonRequest{}))
		// manual refresh station geo in redis, used to handle the update_station_geo exception alert
		g.POST("refresh_redis", lcos_handler.Handle(service.RefreshStationInfoInRedis, &station.RefreshStationInfoInRedisReq{}))
		g.POST("get_cache_val", lcos_handler.Handle(service.GetCacheVal, &station.QueryCacheValReq{}))
		g.POST("update_station_volume", lcos_handler.Handle(service.UpdateStationVolume, &station.StationVolumeReq{}))

		// get distance partition
		g.POST("get_distance_partition", lcos_handler.Handle(service.GetDistancePartition, &station.GetDistancePartitionReq{}))

		g.POST("batch_get_distance_from_cache", lcos_handler.Handle(service.BatchGetDistanceFromCache, &station.BatchGetDistanceReq{}))
		g.POST("batch_get_distance_by_addr_id", lcos_handler.Handle(service.BatchGetDistanceCacheByAddrId, &station.BatchGetDistanceReq{}))
		g.POST("sync_distance_data", lcos_handler.Handle(service.SyncDistanceData, &station.SyncDistanceDataReq{})) // 同步买家-站点行驶距离数据接口
		g.POST("handle_station_buyer_relation", lcos_handler.Handle(service.HandleStationBuyerRelation, &station.SyncDistanceDataReq{}))
		g.POST("calculate_buyer_station_distance", lcos_handler.Handle(service.CalculateBuyerStationDistance, &station.CalculateStationBuyerDistance{}))
		g.POST("address_revision", lcos_handler.Handle(service.AddressRevision, &pis_protocol.PISCommonRequest{}))
		g.POST("address_revision_data_insert", lcos_handler.Handle(service.AddressRevisionDataInsert, &schema.AddressRevisionDataInsertMsg{}))
		g.POST("address_revision_data_handle", lcos_handler.Handle(service.AddressRevisionHandle, &station.HandleAddressRevisionJobReq{}))
	}

	{
		// SPLN-33121 BR direct delivery
		group.POST("zone_serviceable_area/product_cep_group/import", lcos_handler.Handle(server.ProductServiceableZoneController.ImportProductServiceableCepGroup, zone_serviceable_protocol.ImportProductServiceableCepGroupRequest{}))
		group.GET("zone_serviceable_area/product_cep_group/export", common_handler.Handle(server.ProductServiceableZoneController.ExportProductServiceableCepGroup, zone_serviceable_protocol.ExportProductServiceableCepGroupRequest{}))
		group.POST("zone_serviceable_area/product_serviceable_zone/import", lcos_handler.Handle(server.ProductServiceableZoneController.ImportProductServiceableZone, zone_serviceable_protocol.ImportProductServiceableZoneRequest{}))
		group.GET("zone_serviceable_area/product_serviceable_zone/list_zone", lcos_handler.Handle(server.ProductServiceableZoneController.ListProductServiceableZone, zone_serviceable_protocol.ListProductServiceableZoneRequest{}))
		group.GET("zone_serviceable_area/product_serviceable_zone/list_zone_name", lcos_handler.Handle(server.ProductServiceableZoneController.ListProductServiceableZoneWithCepGroup, zone_serviceable_protocol.ListProductServiceableZoneRequest{}))
		group.GET("zone_serviceable_area/product_serviceable_zone/list_zone_as_geo_json", lcos_handler.Handle(server.ProductServiceableZoneController.ListProductServiceableZoneAsGeoJson, zone_serviceable_protocol.ListProductServiceableZoneRequest{}))

		group.POST("zone_serviceable_area/shop_serviceable_zone/update", lcos_handler.Handle(server.ShopServiceableZoneController.UpdateShopServiceableZone, zone_serviceable_protocol.UpdateShopServiceableZoneRequest{}))
		group.GET("zone_serviceable_area/shop_serviceable_zone/list", lcos_handler.Handle(server.ShopServiceableZoneController.ListShopServiceableZone, zone_serviceable_protocol.ListShopServiceableZoneRequest{}))

		group.GET("zone_serviceable_area/list_seller_zone", lcos_handler.Handle(server.ShopServiceableZoneController.QuerySellerServiceableZoneInfo, zone_serviceable_protocol.QuerySellerServiceableZoneInfoRequest{}))
	}

	{
		// geo test
		group.GET("electronic_fence/test/getgeohash", lcos_handler.Handle(server.EFenceController.TestGetGeoHash, e_fence.GetGeoHashReq{}))
		group.POST("electronic_fence/test/transferpolygontomesh", lcos_handler.Handle(server.EFenceController.TestTransferPolygonToMesh, e_fence.TransferPolygonToMeshReq{}))
		group.POST("electronic_fence/test/checkpolygoncovergeohash", lcos_handler.Handle(server.EFenceController.TestCheckPolygonCoverGeohash, e_fence.CheckPolygonCoverGeohashReq{}))
		group.POST("electronic_fence/test/iscoordinateInsidepolygon", lcos_handler.Handle(server.EFenceController.TestIsCoordinateInsidePolygon, e_fence.IsCoordinateInsidePolygonReq{}))
		group.POST("electronic_fence/test/polygon2geojson", lcos_handler.Handle(server.EFenceController.TestParsePolygon2GeoJson, e_fence.ParsePolygon2GeoJsonReq{}))
		// debug接口
		group.POST("electronic_fence/debug/regenerate_mesh", lcos_handler.Handle(server.EFenceController.DebugRegenerateMesh, e_fence.RegenerateMeshReq{}))

		// location id whitelist
		group.GET("electronic_fence/whitelist/list", lcos_handler.Handle(server.EFenceController.ListLocationWhiteList, e_fence.ListLocationWhitelistReq{}))
		group.GET("electronic_fence/whitelist/export", common_handler.Handle(server.EFenceController.ExportLocationWhiteList, e_fence.ExportWhitelistReq{}))
		group.POST("electronic_fence/whitelist/import", lcos_handler.Handle(server.EFenceController.UploadLocationWhiteList, e_fence.UploadLocationWhitelistReq{}))
		group.POST("electronic_fence/whitelist/delete", lcos_handler.Handle(server.EFenceController.DeleteLocationWhiteList, e_fence.DeleteLocationWhitelistReq{}))

		// line toggle
		group.POST("electronic_fence/toggle/delete", lcos_handler.Handle(server.EFenceController.DeleteLineToggle, e_fence.DeleteLineToggleReq{}))
		group.POST("electronic_fence/toggle/update", lcos_handler.Handle(server.EFenceController.UpdateLineToggle, e_fence.UpdateLineToggleReq{}))
		group.GET("electronic_fence/toggle/list", lcos_handler.Handle(server.EFenceController.GetLineToggleList, e_fence.GetLineToggleListReq{}))
		group.GET("electronic_fence/toggle/get_all_layer", lcos_handler.Handle(server.EFenceController.GetLineToggleLayer, e_fence.GetLineToggleLayerReq{}))

		group.GET("electronic_fence/zone/list", lcos_handler.Handle(server.EFenceController.ListAllZonesByPage, e_fence.ListZoneRequest{}))
		group.GET("electronic_fence/zone/export", common_handler.Handle(server.EFenceController.ExportAllZones, e_fence.ExportZoneRequest{}))
	}

	{
		group.POST("card_delivery_address/notify_all", lcos_handler.Handle(server.CardDeliveryAddressController.NotifyCardDeliveryAddressAllData, nil))
		group.POST("card_delivery_address/notify_file_data", lcos_handler.Handle(server.CardDeliveryAddressController.NotifyCardDeliveryAddressFileData, card_delivery_address_protocol.NotifyCardDeliveryAddressFileDataRequest{}))
		group.POST("card_delivery_address/notify_version", lcos_handler.Handle(server.CardDeliveryAddressController.NotifyCardDeliveryAddressChangeVersion, card_delivery_address_protocol.NotifyCardDeliveryAddressChangeVersionRequest{}))
		group.GET("card_delivery_address/download", lcos_handler.Handle(server.CardDeliveryAddressController.DownloadCardDeliveryAddressChangeVersionData, card_delivery_address_protocol.NotifyCardDeliveryAddressChangeVersionRequest{}))

	}

	// SPLN-33950 EDT Rule
	{
		group.POST("edt_rule/create", lcos_handler.Handle(server.EdtRuleController.CreateEdtRule, edt_rule.CreateEdtRuleRequest{}))
		group.GET("edt_rule/list", lcos_handler.Handle(server.EdtRuleController.ListEdtRule, edt_rule.ListEdtRuleRequest{}))
		group.POST("edt_rule/detail", lcos_handler.Handle(server.EdtRuleController.GetEdtRule, edt_rule.GetRuleDetailRequest{}))
		group.POST("edt_rule/disable", lcos_handler.Handle(server.EdtRuleController.DisableEdtRule, edt_rule.GetRuleDetailRequest{}))

		group.POST("route_edt/upload", lcos_handler.Handle(server.EdtRuleController.UploadRouteFixedEdt, edt_rule.UploadRouteFixedEdtReq{}))
		group.POST("route_edt/list", lcos_handler.Handle(server.EdtRuleController.ListRouteFixedEdt, edt_rule.ListRouteFixedEdtReq{}))
		group.POST("route_edt/export", common_handler.Handle(server.EdtRuleController.ExportRouteFixedEdt, edt_rule.ListRouteFixedEdtReq{}))

		// SPLN-34923
		group.POST("route_edt_for_cep/upload", lcos_handler.Handle(server.EdtRuleController.UploadCepRouteFixedEdt, edt_rule.UploadCepRouteFixedEdtReq{}))
		group.POST("route_edt_for_cep/list", lcos_handler.Handle(server.EdtRuleController.ListCepRouteFixedEdt, edt_rule.ListCepRouteFixedEdtReq{}))
		group.POST("route_edt_for_cep/export", common_handler.Handle(server.EdtRuleController.ExportCepRouteFixedEdt, edt_rule.ListCepRouteFixedEdtReq{}))
	}

	// SPLN-35059 EDT Config
	{
		group.POST("product_edt/create", lcos_handler.Handle(server.EdtConfigController.CreateProductEdtConfig, edt_config_protocol.CreateProductEdtConfigRequest{}))
		group.POST("product_edt/update", lcos_handler.Handle(server.EdtConfigController.UpdateProductEdtConfig, edt_config_protocol.UpdateProductEdtConfigRequest{}))
		group.POST("product_edt/delete", lcos_handler.Handle(server.EdtConfigController.DeleteProductEdtConfig, edt_config_protocol.DeleteProductEdtConfigRequest{}))

		// SPLN-34923
		group.POST("product_edt/list", lcos_handler.Handle(server.EdtConfigController.ListProductEdtConfig, edt_config_protocol.ListProductEdtConfigRequest{}))
		group.POST("product_edt/detail", lcos_handler.Handle(server.EdtConfigController.GetProductEdtConfig, edt_config_protocol.GetProductEdtConfigRequest{}))
	}

	// SPLN-35915 Time A/BTest experiment
	{
		service := server.TimeExperimentController
		g := group.Group("time_experiment/")
		// time experiment route group
		g.POST("route_group/create", lcos_handler.Handle(service.CreateABTestRouteGroup, &time_experiment_protocol.RouteGroupCreateRequest{}))
		g.POST("route_group/list", lcos_handler.Handle(service.ListABTestRouteGroup, &time_experiment_protocol.RouteGroupListRequest{}))
		g.POST("route_group/view", lcos_handler.Handle(service.ViewABTestRouteGroup, &time_experiment_protocol.RouteGroupViewRequest{}))
		g.POST("route_group/edit", lcos_handler.Handle(service.EditABTestRouteGroup, &time_experiment_protocol.RouteGroupEditRequest{}))
		g.POST("route_group/delete", lcos_handler.Handle(service.DeleteABTestRouteGroup, &time_experiment_protocol.RouteGroupDeleteRequest{}))
		// time experiment edt strategy
		g.POST("edt_strategy/create", lcos_handler.Handle(service.CreateEdtStrategyRule, &time_experiment_protocol.EdtStrategyCreateRequest{}))
		g.POST("edt_strategy/list", lcos_handler.Handle(service.ListEdtStrategyRule, &time_experiment_protocol.EdtStrategyListRequest{}))
		g.POST("edt_strategy/view", lcos_handler.Handle(service.ViewEdtStrategyRule, &time_experiment_protocol.EdtStrategyViewRequest{}))
		g.POST("edt_strategy/edit", lcos_handler.Handle(service.EditEdtStrategyRule, &time_experiment_protocol.EdtStrategyEditRequest{}))
		g.POST("edt_strategy/delete", lcos_handler.Handle(service.DeleteEdtStrategyRule, &time_experiment_protocol.EdtStrategyDeleteRequest{}))
	}

	// SPLN-35376 parcel library
	{
		group.POST("parcel_library/version_notify", lcos_handler.Handle(server.ParcelLibraryController.NotifyLogisticParcelLibraryVersion, parcel_library_protocol.NotifyParcelLibraryVersionRequest{}))
		group.POST("parcel_library/gen_combination_id", lcos_handler.Handle(server.ParcelLibraryController.GetSkusCombinationId, parcel_library_protocol.GetSkusCombinationIdRequest{}))
		group.POST("parcel_library/create_or_update", lcos_handler.Handle(server.ParcelLibraryController.BatchCreateOrUpdateParcelLibraryData, parcel_library_protocol.BatchCreateOrUpdateParcelLibraryDataRequest{}))
		group.POST("parcel_library/query_data", lcos_handler.Handle(server.ParcelLibraryController.BatchGetParcelLibraryData, parcel_library_protocol.BatchGetParcelLibraryDataRequest{}))
	}

	{
		group.POST("geo_distance/create_or_update", lcos_handler.Handle(server.GeoDistanceController.CreateOrUpdateProductGeoDistanceConf, geo_distance_protocol.CreateOrUpdateProductGeoDistanceConfRequest{}))
		group.POST("geo_distance/delete", lcos_handler.Handle(server.GeoDistanceController.DeleteProductGeoDistanceConf, geo_distance_protocol.DeleteProductGeoDistanceConfRequest{}))
		group.GET("geo_distance/get_conf", lcos_handler.Handle(server.GeoDistanceController.GetProductGeoDistanceConf, geo_distance_protocol.GetProductGeoDistanceConfRequest{}))

		group.POST("geo_distance/calculate", lcos_handler.Handle(server.GeoDistanceController.GetGeoDistance, geo_distance.GetGeoDistanceRequest{}))
		group.POST("geo_distance/batch_calculate", lcos_handler.Handle(server.GeoDistanceController.BatchGetGeoDistance, geo_distance.BatchGetGeoDistanceRequest{}))
	}

	// SPLN-36590 NSS Easy Pack Predefined Route Check
	{
		group.POST("serviceable_area/predefined_route/create", lcos_handler.Handle(server.PredefinedRouteController.CreateLogisticPredefinedRoute, predefined_route_protocol.CreateLinePredefinedRouteReq{}))
		group.POST("serviceable_area/predefined_route/update", lcos_handler.Handle(server.PredefinedRouteController.UpdateLogisticPredefinedRoute, predefined_route_protocol.UpdateLinePredefinedRouteReq{}))
		group.POST("serviceable_area/predefined_route/delete", lcos_handler.Handle(server.PredefinedRouteController.DeleteLogisticPredefinedRoute, predefined_route_protocol.DeleteLinePredefinedRouteReq{}))
		group.GET("serviceable_area/predefined_route/list", lcos_handler.Handle(server.PredefinedRouteController.ListLogisticPredefinedRoute, predefined_route_protocol.ListLinePredefinedRouteReq{}))
		group.POST("serviceable_area/predefined_route/upload", lcos_handler.Handle(server.PredefinedRouteController.UploadLogisticPredefinedRoute, common_protocol.UploadFileRequest{}))
		group.GET("serviceable_area/predefined_route/export", common_handler.Handle(server.PredefinedRouteController.ExportLogisticPredefinedRoute, predefined_route_protocol.ExportLinePredefinedRouteReq{}))
		group.GET("serviceable_area/predefined_route/list_all_route_code", lcos_handler.Handle(server.PredefinedRouteController.ListLogisticPredefinedRouteCode, nil))
	}

	return group.GetRouters()
}
