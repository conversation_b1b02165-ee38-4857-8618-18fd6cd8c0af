package basic_serviceable

type (
	CreateOrUpdateBasicServiceableConfRequest struct {
		LineId                      string             `form:"line_id" json:"line_id" validate:"required,gt=3"`
		Region                      string             `form:"region" json:"region" validate:"required"`
		OriginServiceableType       uint8              `form:"origin_serviceable_type" json:"origin_serviceable_type" validate:"required,min=1,max=3"`
		DestServiceableType         uint8              `form:"destination_serviceable_type" json:"destination_serviceable_type" validate:"required,min=1,max=3"`
		IsCheckBasicServiceable     *uint8             `form:"is_check_basic_serviceable" json:"is_check_basic_serviceable" validate:"required,min=0,max=1"`
		IsCheckOperationServiceable *uint8             `form:"is_check_operation_serviceable" json:"is_check_operation_serviceable" validate:"required,min=0,max=1"`
		OperationMaintenanceMode    uint8              `form:"operation_maintenance_mode" json:"operation_maintenance_mode" validate:"min=0,max=1"`
		IsCheckRoute                *uint8             `form:"is_check_route" json:"is_check_route" validate:"required,min=0,max=1"`
		RouteMode                   *uint8             `form:"route_mode" json:"route_mode"`
		DefaultPickupEnabled        *uint8             `form:"default_pickup_enabled" json:"default_pickup_enabled" validate:"required,min=0,max=1"`
		DefaultCodPickupEnabled     *uint8             `form:"default_cod_pickup_enabled" json:"default_cod_pickup_enabled" validate:"required,min=0,max=1"`
		DefaultDeliverEnabled       *uint8             `form:"default_deliver_enabled" json:"default_deliver_enabled" validate:"required,min=0,max=1"`
		DefaultCodDeliverEnabled    *uint8             `form:"default_cod_deliver_enabled" json:"default_cod_deliver_enabled" validate:"required,min=0,max=1"`
		DefaultPdpPostcode          *uint8             `form:"default_pdp_postcode" json:"default_pdp_postcode" validate:"required,min=0,max=1"`
		CollectDeliverAbility       uint32             `form:"collect_deliver_ability" json:"collect_deliver_ability" validate:"required"`
		IsCheckDistance             *uint8             `form:"is_check_distance" json:"is_check_distance" validate:"required,min=0,max=1"`
		MinDistanceOperator         *uint8             `form:"minimum_distance_operator" json:"minimum_distance_operator"`
		MinDistance                 *uint32            `form:"minimum_distance" json:"minimum_distance"`
		MaxDistanceOperator         *uint8             `form:"maximum_distance_operator" json:"maximum_distance_operator"`
		MaxDistance                 *uint32            `form:"maximum_distance" json:"maximum_distance"`
		BusinessAbility             []*BusinessAbility `form:"business_ability" json:"business_ability" validate:"dive"`
		ScenarioConf                []*ScenarioConf    `form:"scenario_conf" json:"scenario_conf" validate:"dive"`
		GroupIdList                 []string           `form:"group_id_list" json:"group_id_list" validate:"required"`
		IsCheckPredefinedRoute      uint8              `form:"is_check_predefined_route" json:"is_check_predefined_route"`
	}

	BusinessAbility struct {
		CollectDeliverType uint8   `form:"collect_deliver_type" json:"collect_deliver_type" validate:"required,collectDeliverType"`
		MaxCapacity        *uint32 `form:"max_capacity" json:"max_capacity"`
	}

	ScenarioConf struct {
		Scenario     uint8 `form:"scenario" json:"scenario" validate:"required,min=1,max=4"`
		CheckLevel   uint8 `form:"check_level" json:"check_level,min=1,max=4"`
		CheckAddress uint8 `form:"check_address" json:"check_address,min=0,max=3"`
	}

	SimplePutBasicServiceableConfRequest struct {
		LineId                string             `form:"line_id" json:"line_id" validate:"required,gt=3"`
		Region                string             `form:"region" json:"region" validate:"required"`
		CollectDeliverAbility uint32             `form:"collect_deliver_ability" json:"collect_deliver_ability" validate:"required"`
		IsCheckDistance       *uint8             `form:"is_check_distance" json:"is_check_distance" validate:"required,min=0,max=1"`
		MinDistanceOperator   *uint8             `form:"minimum_distance_operator" json:"minimum_distance_operator"`
		MinDistance           *uint32            `form:"minimum_distance" json:"minimum_distance"`
		MaxDistanceOperator   *uint8             `form:"maximum_distance_operator" json:"maximum_distance_operator"`
		MaxDistance           *uint32            `form:"maximum_distance" json:"maximum_distance"`
		BusinessAbility       []*BusinessAbility `form:"business_ability" json:"business_ability" validate:"dive"`
	}

	CheckCollectDeliverGroupRequest struct {
		LineId  string `json:"line_id" form:"line_id" validate:"required"`
		GroupId string `json:"group_id" form:"group_id" validate:"required"`
	}
)

type BasicServiceableConfListRequest struct {
	LineId                  *string `struct2map:"line_id,omitempty" form:"line_id" json:"line_id,gt=3"`
	Region                  string  `struct2map:"region" form:"region" json:"region" validate:"required"`
	IsCheckBasicServiceable *uint8  `struct2map:"is_check_serviceable,omitempty" form:"is_check_basic_serviceable" json:"is_check_basic_serviceable,min=0,max=1"`
	PageNo                  *uint32 `form:"pageno" json:"pageno"`
	Count                   *uint32 `form:"count" json:"count"`
}
