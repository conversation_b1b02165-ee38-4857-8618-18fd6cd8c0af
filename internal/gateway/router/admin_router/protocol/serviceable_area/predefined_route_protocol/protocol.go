package predefined_route_protocol

type (
	CreateLinePredefinedRouteReq struct {
		LineId     string `json:"line_id" form:"line_id"  validate:"required"`
		GroupId    string `json:"group_id" form:"group_id" validate:"required"`
		FromAreaId uint64 `json:"from_area_id" form:"from_area_id" validate:"required"`
		ToAreaId   uint64 `json:"to_area_id" form:"to_area_id" validate:"required"`
		RouteCode  string `json:"route_code" form:"route_code" validate:"required"`
	}

	UpdateLinePredefinedRouteReq struct {
		Id         uint64 `json:"id" form:"id"  validate:"required"`
		FromAreaId uint64 `json:"from_area_id" form:"from_area_id" validate:"required"`
		ToAreaId   uint64 `json:"to_area_id" form:"to_area_id" validate:"required"`
		RouteCode  string `json:"route_code" form:"route_code" validate:"required"`
	}

	DeleteLinePredefinedRouteReq struct {
		Id uint64 `json:"id" form:"id" validate:"required"`
	}

	ListLinePredefinedRouteReq struct {
		LineId     *string `json:"line_id" form:"line_id" struct2map:"line_id,omitempty" validate:"required"`
		GroupId    *string `json:"group_id" form:"group_id" struct2map:"group_id,omitempty" validate:"required"`
		FromAreaId *uint64 `json:"from_area_id" form:"from_area_id" struct2map:"from_area_id,omitempty"`
		ToAreaId   *uint64 `json:"to_area_id" form:"to_area_id" struct2map:"to_area_id,omitempty"`
		RouteCode  string  `json:"route_code" form:"route_code" struct2map:"-"`
		PageNo     uint32  `json:"page_no" form:"page_no" validate:"required" struct2map:"-"`
		PageSize   uint32  `json:"page_size" form:"page_size" validate:"required" struct2map:"-"`
	}

	ExportLinePredefinedRouteReq struct {
		LineId     *string `json:"line_id" form:"line_id"  struct2map:"line_id,omitempty" validate:"required"`
		GroupId    *string `json:"group_id" form:"group_id" struct2map:"group_id,omitempty" validate:"required"`
		FromAreaId *uint64 `json:"from_area_id" form:"from_area_id" struct2map:"from_area_id,omitempty"`
		ToAreaId   *uint64 `json:"to_area_id" form:"to_area_id" struct2map:"to_area_id,omitempty"`
		RouteCode  string  `json:"route_code" form:"route_code" struct2map:"-"`
	}
)

func (l *ListLinePredefinedRouteReq) GetPageNo() uint32 {
	return l.PageNo
}

func (l *ListLinePredefinedRouteReq) GetPageSize() uint32 {
	return l.PageSize
}
