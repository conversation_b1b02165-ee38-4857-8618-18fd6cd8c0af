package e_fence

import (
	"git.garena.com/shopee/bg-logistics/logistics/geopolygon"
)

type ListZoneRequest struct {
	ZoneId   string `form:"zone_id" json:"zone_id" validate:"-"`
	ZoneName string `form:"zone_name" json:"zone_name" validate:"-"`
	PageNo   uint32 `form:"page_no" json:"page_no" validate:"-"`
	Count    uint32 `form:"count" json:"count" validate:"-"`

	LayerId string `form:"layer_id" json:"layer_id" validate:"-"`
}

type ZoneItem struct {
	ZoneId       string `form:"zone_id" json:"zone_id"`
	ZoneName     string `form:"zone_name" json:"zone_name"`
	StationId    string `form:"station_id" json:"station_id"`
	StationName  string `form:"station_name" json:"station_name"`
	StationType  string `form:"station_type" json:"station_type"`
	LastSyncTime uint32 `form:"last_sync_time" json:"last_sync_time"`
	Operator     string `form:"operator" json:"operator"`

	LayerId   string `form:"layer_id" json:"layer_id"`
	LayerName string `form:"layer_name" json:"layer_name"`

	Id string `form:"id" json:"id" validate:"-"`
}

type GetGeoHashReq struct {
	Lng  float64 `form:"lng" json:"lng"`
	Lat  float64 `form:"lat" json:"lat"`
	Size int     `form:"size" json:"size"`
}

type GetGeoHashResp struct {
	GeoHash string `json:"geo_hash"`
}

type TransferPolygonToMeshReq struct {
	PolyDetail *geopolygon.PolyDetail `json:"poly_detail"`
	Min        int                    `json:"min"`
	Max        int                    `json:"max"`
	PipMode    int                    `json:"pip_mode"`
}

type TransferPolygonToMeshResp struct {
	List []*geopolygon.PolygonMeshResult `json:"list"`
}

type CheckPolygonCoverGeohashReq struct {
	PolyDetail *geopolygon.PolyDetail `json:"poly_detail"`
	GeoHash    string                 `json:"geo_hash"`
}

type CheckPolygonCoverGeohashResp struct {
	Result int `json:"result"`
}

type IsCoordinateInsidePolygonReq struct {
	PolyDetail *geopolygon.PolyDetail `json:"poly_detail"`
	Lng        float64                `form:"lng" json:"lng"`
	Lat        float64                `form:"lat" json:"lat"`
}

type IsCoordinateInsidePolygonResp struct {
	Result bool `json:"result"`
}

type ParsePolygon2GeoJsonReq struct {
	ZoneId         string   `form:"zone_id" json:"zone_id"`
	Version        string   `form:"version" json:"version"`
	LayerId        string   `form:"layer_id" json:"layer_id"`
	IncludeMesh    bool     `form:"include_mesh" json:"include_mesh"`
	IncludeGeoHash []string `form:"include_geo_hash" json:"include_geo_hash"`
}

type RegenerateMeshReq struct {
	ZoneId       string `form:"zone_id" json:"zone_id"`
	Version      string `form:"version" json:"version"`
	LayerId      string `form:"layer_id" json:"layer_id"`
	PipMode      int    `form:"pip_mode" json:"pip_mode"`
	RefreshCache bool   `form:"refresh_cache" json:"refresh_cache"`
}

type ExportZoneRequest struct {
}

type ListLocationWhitelistReq struct {
	LocationId *int64  `form:"location_id" json:"location_id"`
	State      *string `form:"state" json:"state"`
	City       *string `form:"city" json:"city"`
	District   *string `form:"district" json:"district"`
	Street     *string `form:"street" json:"street"`
	PageNo     int     `form:"page_no" json:"page_no" validate:"required"`
	Count      int     `form:"count" json:"count" validate:"required"`

	LayerId *string `form:"layer_id" json:"layer_id"`
}

type LocationInfo struct {
	LocationId     int64  `json:"location_id"`
	State          string `json:"state"`
	City           string `json:"city"`
	District       string `json:"district"`
	Street         string `json:"street"`
	LastUpdateTime int64  `json:"last_update_time"`
	Operator       string `json:"operator"`

	LayerId   string `form:"layer_id" json:"layer_id"`
	LayerName string `form:"layer_name" json:"layer_name"`
}

type ListLocationWhitelistResp struct {
	List  []*LocationInfo `json:"list"`
	Total int64           `json:"total"`
}

type DeleteLocationWhitelistReq struct {
	LocationId int64  `json:"location_id" validate:"required"`
	LayerId    string `json:"layer_id"`
}

type UploadLocationWhitelistReq struct {
	FileUrl string `json:"file_url" validate:"required"`
}

type ExportWhitelistReq struct {
}

type GetLineToggleListReq struct {
	LineId string `form:"line_id" json:"line_id"`
	PageNo int    `form:"page_no" json:"page_no" validate:"required"`
	Count  int    `form:"count" json:"count" validate:"required"`

	LayerId string `form:"layer_id" json:"layer_id"`
}

type GetLineToggleListResp struct {
	List  []*LineToggleInfo `json:"list"`
	Total int64             `json:"total"`
}

type LineToggleInfo struct {
	LineId           string `json:"line_id"`
	LineName         string `json:"line_name"`
	SupportLmHubZone int32  `json:"support_lm_hub_zone"`
	CheckWith        int32  `json:"check_with"`
	Operator         string `json:"operator"`
	CreateTime       int64  `json:"create_time"`
	ModifyTime       int64  `json:"modify_time"`

	LayerId   string `json:"layer_id"`
	LayerName string `json:"layer_name"`
}

type UpdateLineToggleReq struct {
	LineId           string `json:"line_id"`
	LineName         string `json:"line_name"`
	SupportLmHubZone int32  `json:"support_lm_hub_zone" validate:"gte=0,lte=1"`
	CheckWith        int32  `json:"check_with" validate:"gte=1,lte=3"`
	LayerId          string `json:"layer_id"`
}

type DeleteLineToggleReq struct {
	LineId string `json:"line_id" validate:"required"`
}

type GetLineToggleLayerReq struct {
}

type GetLineToggleLayerResp struct {
	LayerId   string `json:"layer_id"`
	LayerName string `json:"layer_name"`
}
