package http

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
)

type PagingQuery interface {
	GetPageNo() uint32
	GetPageSize() uint32
}

func ValidatePagingQuery(ctx utils.LCOSContext, req PagingQuery) *lcos_error.LCOSError {
	if req == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "request is nil")
	}
	if req.GetPageNo() <= 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "request page_no should be larger than 0")
	}
	maxPageSize := config.GetQueryMaxPageSize(ctx)
	if req.GetPageSize() <= 0 || req.GetPageSize() > maxPageSize {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "request page_size should be larger than 0 and less than %d", maxPageSize)
	}
	return nil
}
