package cache

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	"github.com/go-faker/faker/v4/pkg/slice"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/geopolygon"
	delivery_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/delivery_instruction"
	eFenceConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/pickup_window_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/env"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf_detail"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area"
	siteBasicConfSiteServiceableAreaBasicConf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	site_serviceable_area_cep_range "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	site_serviceable_area_location "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	site_serviceable_area_postcode "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/station_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tw_store"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	auto_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	model11 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	model15 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/holiday"
	model17 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_pickup_config"
	model18 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_timeslot"
	model19 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
	model20 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	model16 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/recurring_holiday"
	model14 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/postal_code_to_geo"
	model5 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	operation_route_model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	model4 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	model2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	model8 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	model9 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	model13 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	model12 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	model3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	model10 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	model6 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	model7 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_holiday"
	tpl_id_line_id_ref2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
)

type DumpManager struct {
}

// 结果为line_id->limit list的map
func (db *DumpManager) DumpLinePackageLimitTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var packageLimitList []*model.LinePackageLimitTab
	// LCOSError := common.GetAllData(ctx, &model.LinePackageLimitTab{}, &packageLimitList)
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model.LinePackageLimitTab{}, &packageLimitList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	packageLimitMap := make(map[string]interface{})
	for _, packageLimit := range packageLimitList {
		if v, ok := packageLimitMap[packageLimit.LineId]; ok {
			packageLimitMap[packageLimit.LineId] = append(v.([]*model.LinePackageLimitTab), packageLimit)
		} else {
			packageLimitMap[packageLimit.LineId] = []*model.LinePackageLimitTab{packageLimit}
		}
	}
	return packageLimitMap, nil
}

// 结果为product_id->limit list的map
func (db *DumpManager) DumpProductPackageLimitTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var packageLimitList []*model11.ProductPackageLimitTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model11.ProductPackageLimitTab{}, &packageLimitList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	packageLimitMap := make(map[string]interface{})
	for _, packageLimit := range packageLimitList {
		if v, ok := packageLimitMap[packageLimit.ProductId]; ok {
			packageLimitMap[packageLimit.ProductId] = append(v.([]*model11.ProductPackageLimitTab), packageLimit)
		} else {
			packageLimitMap[packageLimit.ProductId] = []*model11.ProductPackageLimitTab{packageLimit}
		}
	}
	return packageLimitMap, nil
}

// 结果为line_id -> site_serviceable_area_basic_conf 的map
func (db *DumpManager) DumpBasicServiceableConfTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineBasicServiceableConfList []*model2.LineBasicServiceableConfTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model2.LineBasicServiceableConfTab{}, &lineBasicServiceableConfList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineBasicConfMap := make(map[string]interface{})
	for _, lineBasicServiceableConf := range lineBasicServiceableConfList {
		lineBasicConfMap[lineBasicServiceableConf.LineId] = lineBasicServiceableConf
	}
	return lineBasicConfMap, nil
}

// 结果为line_id -> groupId list 的map
func (db *DumpManager) DumpBasicServiceableGroupRefTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineBasicServiceGroupRefList []*model13.LineBasicServiceableGroupRefTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model13.LineBasicServiceableGroupRefTab{}, &lineBasicServiceGroupRefList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineBasicConfRefMap := make(map[string]interface{})
	for _, lineBasicServiceableGroupRef := range lineBasicServiceGroupRefList {
		if v, ok := lineBasicConfRefMap[lineBasicServiceableGroupRef.LineId]; ok {
			lineBasicConfRefMap[lineBasicServiceableGroupRef.LineId] = append(v.([]string), lineBasicServiceableGroupRef.CollectDeliverGroupId)
		} else {
			lineBasicConfRefMap[lineBasicServiceableGroupRef.LineId] = []string{lineBasicServiceableGroupRef.CollectDeliverGroupId}
		}
	}
	return lineBasicConfRefMap, nil
}

// 结果为line_id->scenario list的map
func (db *DumpManager) DumpScenarioConfTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineScenarioConfList []*model3.LineCommonServiceableScenarioConfTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model3.LineCommonServiceableScenarioConfTab{}, &lineScenarioConfList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineScenarioConfMap := make(map[string]interface{})
	for _, lineScenarioConf := range lineScenarioConfList {
		if v, ok := lineScenarioConfMap[lineScenarioConf.LineId]; ok {
			lineScenarioConfMap[lineScenarioConf.LineId] = append(v.([]*model3.LineCommonServiceableScenarioConfTab), lineScenarioConf)
		} else {
			lineScenarioConfMap[lineScenarioConf.LineId] = []*model3.LineCommonServiceableScenarioConfTab{lineScenarioConf}
		}
	}
	return lineScenarioConfMap, nil
}

// 结果为line_id->ability list的map
func (db *DumpManager) DumpAbilityConfTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineAbilityConfList []*model2.LineCollectDeliverAbilityTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model2.LineCollectDeliverAbilityTab{}, &lineAbilityConfList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineAbilityConfMap := make(map[string]interface{})
	for _, lineAbilityConf := range lineAbilityConfList {
		if v, ok := lineAbilityConfMap[lineAbilityConf.LineId]; ok {
			lineAbilityConfMap[lineAbilityConf.LineId] = append(v.([]*model2.LineCollectDeliverAbilityTab), lineAbilityConf)
		} else {
			lineAbilityConfMap[lineAbilityConf.LineId] = []*model2.LineCollectDeliverAbilityTab{lineAbilityConf}
		}
	}
	return lineAbilityConfMap, nil
}

// 结果为 binaryString(pickMode+deliverMode) -> collectDeliverGroupId list 的map
func (db *DumpManager) DumpSupportCollectDeliverGroupTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var collectDeliverGroupList []*model12.LineCollectDeliverGroupConfTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model12.LineCollectDeliverGroupConfTab{}, &collectDeliverGroupList, batchSize)
	resultConfMap := make(map[string]interface{}) // key: binaryString(pickMode+deliverMode), value: collectDeliverGroup列表
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	for _, collectDeliverGroup := range collectDeliverGroupList {
		binaryCodeList := localcache.GenSupportPickupDeliverBinaryCode(collectDeliverGroup.PickupMode, collectDeliverGroup.DeliverMode)
		if len(binaryCodeList) > 0 {
			for _, keyCode := range binaryCodeList {
				if v, ok := resultConfMap[keyCode]; ok {
					resultConfMap[keyCode] = append(v.([]string), collectDeliverGroup.GroupId)
				} else {
					resultConfMap[keyCode] = []string{collectDeliverGroup.GroupId}
				}
			}
		}
	}
	return resultConfMap, nil
}

// 结果为 groupId -> collectDeliverGroup 的map
func (db *DumpManager) DumpCollectDeliverGroupTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var collectDeliverGroupList []*model12.LineCollectDeliverGroupConfTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model12.LineCollectDeliverGroupConfTab{}, &collectDeliverGroupList, batchSize)
	resultConfMap := make(map[string]interface{}) // key: groupId, value: collectDeliverGroup
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	for _, collectDeliverGroup := range collectDeliverGroupList {
		resultConfMap[collectDeliverGroup.GroupId] = collectDeliverGroup
	}
	return resultConfMap, nil
}

// 结果为line_id+collec_deliver_group_id+from_area_id+to_area_id(唯一索引)->route的map
func (db *DumpManager) DumpRouteTable2(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	batchSize := config.GetDBDumpBatchSize(ctx)
	lineRouteList := []*model4.LineServiceableAreaRouteTabLite{}
	lineRouteMap := make(map[string]interface{})
	selectCol := []string{"id", "concat_ws(':',line_id,collect_deliver_group_id,from_area_id) as key_word, to_area_id_list"}
	handler := func(result interface{}) error {
		data, ok := result.(*[]*model4.LineServiceableAreaRouteTabLite)
		if !ok {
			return errors.New("unsupported type, should be '*[]*model4.LineServiceableAreaRouteTabLite'")
		}
		for _, lineRoute := range *data {
			var (
				m       map[uint64]struct{}
				valueOk bool
			)
			if v, lineOk := lineRouteMap[lineRoute.KeyWord]; lineOk {
				m, valueOk = v.(map[uint64]struct{})
				if !valueOk {
					return errors.New("unsupported type, should be 'map[uint64]struct{}'")
				}
			} else {
				m = make(map[uint64]struct{})
			}
			// 设置 line_id + collect_deliver_group_id+ from_area_id + to_area_id 维度是否存在
			for _, toAreaId := range lineRoute.ToAreaIDList {
				m[toAreaId] = struct{}{}
			}
			// 设置 line_id + collect_deliver_group_id+ from_area_id 维度是否存在
			m[constant.ToAreaPlaceholder] = struct{}{}
			lineRouteMap[lineRoute.KeyWord] = m
		}
		if config.GetMutableConf(ctx).RouteDumpSleep > 0 {
			time.Sleep(time.Duration(config.GetMutableConf(ctx).RouteDumpSleep) * time.Millisecond)
		}
		return nil
	}

	LCOSError := common.HandAllDataBatch(ctx, &model4.LineServiceableAreaRouteTabLite{}, &lineRouteList, batchSize, selectCol, handler)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	return lineRouteMap, nil
}

// 结果为line_id+collec_deliver_group_id+from_area_id+to_area_id(唯一索引)->route的map
func (db *DumpManager) DumpRouteBlacklistTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	batchSize := config.GetDBDumpBatchSize(ctx)
	lineRouteList := make([]*model4.LineServiceableBlackListRouteTabLite, batchSize)
	lineRouteMap := make(map[string]interface{})
	selectCol := []string{"id", "line_id", "collect_deliver_group_id", "from_area_id", "to_area_id"}
	handler := func(result interface{}) error {
		data, ok := result.(*[]*model4.LineServiceableBlackListRouteTabLite)
		if !ok {
			return errors.New("unsupported type, should be '*[]*model4.LineServiceableBlackListRouteTabLite'")
		}
		for _, lineRoute := range *data {
			// 设置route line_id + group_id + from_area_id + to_area_id 维度是否存在
			keyword := model4.GenerateRouteCacheKey2(lineRoute.LineID, lineRoute.CollectDeliverGroupId, lineRoute.FromAreaID, lineRoute.ToAreaID)
			lineRouteMap[keyword] = struct{}{}

			// 设置route line_id + group_id 维度是否存在
			keyword = model4.GenerateRouteCacheKey2(lineRoute.LineID, lineRoute.CollectDeliverGroupId, constant.FromAreaPlaceholder, constant.ToAreaPlaceholder)
			lineRouteMap[keyword] = struct{}{}

			// 设置route line_id + group_id + from_area_id 维度是否存在
			keyword = model4.GenerateRouteCacheKey2(lineRoute.LineID, lineRoute.CollectDeliverGroupId, lineRoute.FromAreaID, constant.ToAreaPlaceholder)
			lineRouteMap[keyword] = struct{}{}
		}
		if config.GetMutableConf(ctx).RouteDumpSleep > 0 {
			time.Sleep(time.Duration(config.GetMutableConf(ctx).RouteDumpSleep) * time.Millisecond)
		}
		return nil
	}

	LCOSError := common.HandAllDataBatch(ctx, &model4.LineServiceableBlackListRouteTab{}, &lineRouteList, batchSize, selectCol, handler)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	return lineRouteMap, nil
}

// DumpOperationRouteTable 结果为line_id+collec_deliver_group_id+from_area_id+to_area_id(唯一索引)->route的map
func (db *DumpManager) DumpOperationRouteTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	batchSize := config.GetDBDumpBatchSize(ctx)
	lineRouteList := make([]*operation_route_model.LineOperationRouteLiteTab, batchSize)
	lineRouteMap := make(map[string]interface{})
	selectCol := []string{"id", "line_id", "collect_deliver_group_id", "disable_from_area_id", "disable_to_area_id"}
	handler := func(result interface{}) error {
		data, ok := result.(*[]*operation_route_model.LineOperationRouteLiteTab)
		if !ok {
			return errors.New("unsupported type, should be '*[]*model4.LineOperationRouteLiteTab'")
		}
		for _, lineRoute := range *data {
			// 设置运营层 line_id + group_id + disable_from_area_id + disable_to_area_id 维度是否存在
			keyword := operation_route_model.GenerateRouteCacheKey(lineRoute.LineID, lineRoute.CollectDeliverGroupId, lineRoute.DisableFromAreaID, lineRoute.DisableToAreaID)
			lineRouteMap[keyword] = struct{}{}
			// 设置运营层 line_id + group_id 维度整体是否存在
			keyword = operation_route_model.GenerateRouteCacheKey(lineRoute.LineID, lineRoute.CollectDeliverGroupId, constant.FromAreaPlaceholder, constant.ToAreaPlaceholder)
			lineRouteMap[keyword] = struct{}{}
			// 设置运营层 line_id + group_id + disable_from_area_id 维度是否存在
			keyword = operation_route_model.GenerateRouteCacheKey(lineRoute.LineID, lineRoute.CollectDeliverGroupId, lineRoute.DisableFromAreaID, constant.ToAreaPlaceholder)
			lineRouteMap[keyword] = struct{}{}
		}

		if config.GetMutableConf(ctx).RouteDumpSleep > 0 {
			time.Sleep(time.Duration(config.GetMutableConf(ctx).RouteDumpSleep) * time.Millisecond)
		}
		return nil
	}

	LCOSError := common.HandAllDataBatch(ctx, &operation_route_model.LineOperationRouteTab{}, &lineRouteList, batchSize, selectCol, handler)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	return lineRouteMap, nil
}

// DumpAreaLocationTable location_id->area list的map
func (db *DumpManager) DumpAreaLocationTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineRouteList []*model5.LogisticLineServiceableAreaLocationRefTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model5.LogisticLineServiceableAreaLocationRefTab{}, &lineRouteList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineAreaLocationRefMap := make(map[string]interface{})
	for _, lineAreaLocationRef := range lineRouteList {
		if v, ok := lineAreaLocationRefMap[strconv.Itoa(int(lineAreaLocationRef.LocationID))]; ok {
			lineAreaLocationRefMap[strconv.Itoa(int(lineAreaLocationRef.LocationID))] = append(v.([]*model5.LogisticLineServiceableAreaLocationRefTab), lineAreaLocationRef)
		} else {
			lineAreaLocationRefMap[strconv.Itoa(int(lineAreaLocationRef.LocationID))] = []*model5.LogisticLineServiceableAreaLocationRefTab{lineAreaLocationRef}
		}
	}
	return lineAreaLocationRefMap, nil
}

// line_id->oepration conf的map
func (db *DumpManager) DumpOperationConfTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineOperationConfList []*model10.LogisticLineOperationServiceableConfTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model10.LogisticLineOperationServiceableConfTab{}, &lineOperationConfList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineOperationConfMap := make(map[string]interface{})
	for _, lineOperationConf := range lineOperationConfList {
		lineOperationConfMap[lineOperationConf.LineID] = lineOperationConf
	}
	return lineOperationConfMap, nil
}

// pickup_group_id->pickupGroupList的map
func (db *DumpManager) DumpPickupGroupTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var pickupGroupList []*model20.PickupGroupTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model20.PickupGroupTab{}, &pickupGroupList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	pickupGroupIDInfoMap := make(map[string]interface{})
	for _, pickupGroup := range pickupGroupList {
		if _, ok := pickupGroupIDInfoMap[pickupGroup.PickupGroupID]; !ok {
			pickupGroupIDInfoMap[pickupGroup.PickupGroupID] = make([]*model20.PickupGroupTab, 0, 1)
		}
		pickupGroupIDInfoMap[pickupGroup.PickupGroupID] = append(pickupGroupIDInfoMap[pickupGroup.PickupGroupID].([]*model20.PickupGroupTab), pickupGroup)
	}
	return pickupGroupIDInfoMap, nil
}

// line_id->pickupGroupLineIDRefTab的map
func (db *DumpManager) DumpPickupGroupLineIDRefTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var pickupGroupLineIDRefList []*model20.PickupGroupLineIDRefTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model20.PickupGroupLineIDRefTab{}, &pickupGroupLineIDRefList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	pickupGroupIDLineIDInfoMap := make(map[string]interface{})
	for _, item := range pickupGroupLineIDRefList {
		if _, ok := pickupGroupIDLineIDInfoMap[item.LineID]; !ok {
			pickupGroupIDLineIDInfoMap[item.LineID] = item
		}
	}
	return pickupGroupIDLineIDInfoMap, nil
}

// pickup_group_id-pickupConfList的map
func (db *DumpManager) DumpPickupConfTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var pickupConfList []*model19.PickupConfTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model19.PickupConfTab{}, &pickupConfList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	pickupConfInfoMap := make(map[string]interface{})
	var enabledPickupConfList []*model19.PickupConfTab
	var enabledPickupConfIdList []uint64
	for _, pickupConf := range pickupConfList {
		// 缓存中只存放status为1的pickup conf
		if pickupConf.EnableStatus == constant.ENABLED {
			if _, ok := pickupConfInfoMap[pickupConf.PickupGroupID]; !ok {
				pickupConfInfoMap[pickupConf.PickupGroupID] = make([]*model19.PickupConfTab, 0, 1)
			}
			//pickupConfInfoMap[pickupConf.PickupGroupID] = append(pickupConfInfoMap[pickupConf.PickupGroupID].([]*model19.PickupConfTab), pickupConf)

			enabledPickupConfList = append(enabledPickupConfList, pickupConf)
			enabledPickupConfIdList = append(enabledPickupConfIdList, pickupConf.ID)
		}
	}

	// 查询pickup window配置关联的shop group
	var shopGroupRefList []*model19.PickupConfShopGroupRefTab
	if err := common.SearchAllDataGeneral(ctx, &model19.PickupConfShopGroupRefTab{}, &shopGroupRefList, map[string]interface{}{"pickup_conf_id in": enabledPickupConfIdList}, false); err != nil {
		return nil, errors.New(err.Msg)
	}
	shopGroupRefMap := make(map[uint64][]string, len(enabledPickupConfIdList))
	for _, shopGroupRef := range shopGroupRefList {
		shopGroupRefMap[shopGroupRef.PickupConfId] = append(shopGroupRefMap[shopGroupRef.PickupConfId], shopGroupRef.ShopGroupId)
	}
	for _, pickupConf := range enabledPickupConfList {
		shopGroupList, ok := shopGroupRefMap[pickupConf.ID]
		if !ok {
			shopGroupList = []string{pickup_window_constant.DefaultShopGroup}
		}
		pickupConf.ShopGroupList = shopGroupList

		// 按region+pickup_group+shop_group维度生成key
		for _, shopGroup := range pickupConf.ShopGroupList {
			key := model19.GenerateCacheKey(pickupConf.DestinationRegion, pickupConf.PickupGroupID, shopGroup)

			if _, ok = pickupConfInfoMap[key]; !ok {
				pickupConfInfoMap[key] = make([]*model19.PickupConfTab, 0, 1)
			}
			pickupConfInfoMap[key] = append(pickupConfInfoMap[key].([]*model19.PickupConfTab), pickupConf)
		}
	}

	return pickupConfInfoMap, nil
}

// pickup_group_id->openLogisticPickupConf的map
func (db *DumpManager) DumpOpenLogisticPickupConfTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var pickupConfList []*model17.OpenLogisticPickupConfTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model17.OpenLogisticPickupConfTab{}, &pickupConfList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	pickupConfInfoMap := make(map[string]interface{})
	for _, pickupConf := range pickupConfList {
		// 缓存中只存放status为1的pickup conf
		if pickupConf.EnableStatus == constant.ENABLED {
			if _, ok := pickupConfInfoMap[pickupConf.PickupGroupID]; !ok {
				pickupConfInfoMap[pickupConf.PickupGroupID] = make([]*model17.OpenLogisticPickupConfTab, 0, 1)
			}
			pickupConfInfoMap[pickupConf.PickupGroupID] = append(pickupConfInfoMap[pickupConf.PickupGroupID].([]*model17.OpenLogisticPickupConfTab), pickupConf)
		}
	}
	return pickupConfInfoMap, nil
}

// pickup_group_id->timeslotList的map
func (db *DumpManager) DumpTimeslotTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var pickupTimeslots []*model14.PickupTimeslotTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model14.PickupTimeslotTab{}, &pickupTimeslots, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	pickupTimeslotsInfoMap := make(map[string]interface{})
	for _, timeslot := range pickupTimeslots {
		if _, ok := pickupTimeslotsInfoMap[timeslot.PickupGroupID]; !ok {
			pickupTimeslotsInfoMap[timeslot.PickupGroupID] = make([]*model14.PickupTimeslotTab, 0, 1)
		}
		pickupTimeslotsInfoMap[timeslot.PickupGroupID] = append(pickupTimeslotsInfoMap[timeslot.PickupGroupID].([]*model14.PickupTimeslotTab), timeslot)
	}
	return pickupTimeslotsInfoMap, nil
}

// pickup_group_id->timeslotList的map
func (db *DumpManager) DumpOpenLogisticPickupTimeslotTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var pickupTimeslots []*model18.OpenLogisticPickupTimeslotTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model18.OpenLogisticPickupTimeslotTab{}, &pickupTimeslots, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	pickupTimeslotsInfoMap := make(map[string]interface{})
	for _, timeslot := range pickupTimeslots {
		if _, ok := pickupTimeslotsInfoMap[timeslot.PickupGroupID]; !ok {
			pickupTimeslotsInfoMap[timeslot.PickupGroupID] = make([]*model18.OpenLogisticPickupTimeslotTab, 0, 1)
		}
		pickupTimeslotsInfoMap[timeslot.PickupGroupID] = append(pickupTimeslotsInfoMap[timeslot.PickupGroupID].([]*model18.OpenLogisticPickupTimeslotTab), timeslot)
	}
	return pickupTimeslotsInfoMap, nil
}

// pickup_group_id->destinationRegion->holidays的map
func (db *DumpManager) DumpHolidayTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var pickupHolidays []*model15.LogisticHolidayTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model15.LogisticHolidayTab{}, &pickupHolidays, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	pickupHolidaysInfoMap := make(map[string]interface{})
	for _, holiday := range pickupHolidays {
		// key := fmt.Sprintf("%v:%v:%v", holiday.PickupGroupID, holiday.DestinationRegion, holiday.StateId)
		key := model15.GenHolidayCacheKey(holiday.PickupGroupID, holiday.DestinationRegion, holiday.StateId)
		if _, ok := pickupHolidaysInfoMap[key]; !ok {
			pickupHolidaysInfoMap[key] = []*model15.LogisticHolidayTab{}
		}
		pickupHolidaysInfoMap[key] = append(pickupHolidaysInfoMap[key].([]*model15.LogisticHolidayTab), holiday)
	}
	return pickupHolidaysInfoMap, nil
}

// pickup_group_id:state_id:zipcode->weekend的map
func (db *DumpManager) DumpWeekendsTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var pickupWeekends []*model16.RecurringHolidayTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model16.RecurringHolidayTab{}, &pickupWeekends, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	pickupWeekendsInfoMap := make(map[string]interface{})
	for _, weekend := range pickupWeekends {
		// key := fmt.Sprintf("%v:%v:%v:%v", weekend.PickupGroupID, weekend.DestinationRegion, weekend.StateId, weekend.ZipCode)
		key := model16.GenRecurringHolidayCacheKey(weekend.PickupGroupID, weekend.DestinationRegion, weekend.StateId, weekend.ZipCode)
		if _, ok := pickupWeekendsInfoMap[key]; !ok {
			pickupWeekendsInfoMap[key] = []*model16.RecurringHolidayTab{}
		}
		pickupWeekendsInfoMap[key] = append(pickupWeekendsInfoMap[key].([]*model16.RecurringHolidayTab), weekend)
	}
	return pickupWeekendsInfoMap, nil
}

// location_id->location list的map
// line_id->location list的map
func (db *DumpManager) DumpOperationLocationTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineOperationLocationList []*model6.LineOperationServiceableLocationTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model6.LineOperationServiceableLocationTab{}, &lineOperationLocationList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineOperationLocationMap := make(map[string]interface{})
	for _, lineOperationLocation := range lineOperationLocationList {
		if v, ok := lineOperationLocationMap[lineOperationLocation.LineID]; ok {
			lineOperationLocationMap[lineOperationLocation.LineID] = append(v.([]*model6.LineOperationServiceableLocationTab), lineOperationLocation)
		} else {
			lineOperationLocationMap[lineOperationLocation.LineID] = []*model6.LineOperationServiceableLocationTab{lineOperationLocation}
		}

		uniqueCacheKey := lineOperationLocation.GenCacheKey()
		lineOperationLocationMap[uniqueCacheKey] = lineOperationLocation
	}
	return lineOperationLocationMap, nil
}

// line_id->location list的map
func (db *DumpManager) DumpOperationOriginLocationTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineOperationOriginLocationList []*model6.LogisticLineOperationServiceableOriginLocationTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model6.LogisticLineOperationServiceableOriginLocationTab{}, &lineOperationOriginLocationList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineOperationOriginLocationMap := make(map[string]interface{})
	for _, lineOperationOriginLocation := range lineOperationOriginLocationList {
		if v, ok := lineOperationOriginLocationMap[lineOperationOriginLocation.LineID]; ok {
			lineOperationOriginLocationMap[lineOperationOriginLocation.LineID] = append(v.([]*model6.LogisticLineOperationServiceableOriginLocationTab), lineOperationOriginLocation)
		} else {
			lineOperationOriginLocationMap[lineOperationOriginLocation.LineID] = []*model6.LogisticLineOperationServiceableOriginLocationTab{lineOperationOriginLocation}
		}
	}
	return lineOperationOriginLocationMap, nil
}

// line_id->location list的map
func (db *DumpManager) DumpOperationDestLocationTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineOperationDestLocationList []*model6.LogisticLineOperationServiceableDestLocationTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model6.LogisticLineOperationServiceableDestLocationTab{}, &lineOperationDestLocationList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineOperationDestLocationMap := make(map[string]interface{})
	for _, lineOperationDestLocation := range lineOperationDestLocationList {
		if v, ok := lineOperationDestLocationMap[lineOperationDestLocation.LineID]; ok {
			lineOperationDestLocationMap[lineOperationDestLocation.LineID] = append(v.([]*model6.LogisticLineOperationServiceableDestLocationTab), lineOperationDestLocation)
		} else {
			lineOperationDestLocationMap[lineOperationDestLocation.LineID] = []*model6.LogisticLineOperationServiceableDestLocationTab{lineOperationDestLocation}
		}
	}
	return lineOperationDestLocationMap, nil
}

// line_id->postcode list的map
func (db *DumpManager) DumpOperationPostcodeTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineOperationPostcodeList []*model7.LineOperationServiceablePostcodeTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model7.LineOperationServiceablePostcodeTab{}, &lineOperationPostcodeList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineOperationPostcodeMap := make(map[string]interface{})
	for _, lineOperationPostcode := range lineOperationPostcodeList {
		if v, ok := lineOperationPostcodeMap[lineOperationPostcode.LineID]; ok {
			lineOperationPostcodeMap[lineOperationPostcode.LineID] = append(v.([]*model7.LineOperationServiceablePostcodeTab), lineOperationPostcode)
		} else {
			lineOperationPostcodeMap[lineOperationPostcode.LineID] = []*model7.LineOperationServiceablePostcodeTab{lineOperationPostcode}
		}
	}
	return lineOperationPostcodeMap, nil
}

// line_id->postcode list的map
func (db *DumpManager) DumpOperationOriginPostcodeTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineOperationOriginPostcodeList []*model7.LogisticLineOperationServiceableOriginPostcodeTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model7.LogisticLineOperationServiceableOriginPostcodeTab{}, &lineOperationOriginPostcodeList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineOperationOriginPostcodeMap := make(map[string]interface{})
	for _, lineOperationOriginPostcode := range lineOperationOriginPostcodeList {
		if v, ok := lineOperationOriginPostcodeMap[lineOperationOriginPostcode.LineID]; ok {
			lineOperationOriginPostcodeMap[lineOperationOriginPostcode.LineID] = append(v.([]*model7.LogisticLineOperationServiceableOriginPostcodeTab), lineOperationOriginPostcode)
		} else {
			lineOperationOriginPostcodeMap[lineOperationOriginPostcode.LineID] = []*model7.LogisticLineOperationServiceableOriginPostcodeTab{lineOperationOriginPostcode}
		}
	}
	return lineOperationOriginPostcodeMap, nil
}

// line_id->postcode list的map
func (db *DumpManager) DumpOperationDestPostcodeTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineOperationDestPostcodeList []*model7.LogisticLineOperationServiceableDestPostcodeTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model7.LogisticLineOperationServiceableDestPostcodeTab{}, &lineOperationDestPostcodeList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineOperationDestPostcodeMap := make(map[string]interface{})
	for _, lineOperationDestPostcode := range lineOperationDestPostcodeList {
		if v, ok := lineOperationDestPostcodeMap[lineOperationDestPostcode.LineID]; ok {
			lineOperationDestPostcodeMap[lineOperationDestPostcode.LineID] = append(v.([]*model7.LogisticLineOperationServiceableDestPostcodeTab), lineOperationDestPostcode)
		} else {
			lineOperationDestPostcodeMap[lineOperationDestPostcode.LineID] = []*model7.LogisticLineOperationServiceableDestPostcodeTab{lineOperationDestPostcode}
		}
	}
	return lineOperationDestPostcodeMap, nil
}

// line_id + collectDeliverGroupId + location_id  -> location 的map
func (db *DumpManager) DumpBasicLocationTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineBasicLocationList []*model8.LineBasicServiceableLocationTabLite
	tableName := model8.GetBasicLocationGroupTableNameByIndex(dumpParam["index"].(int))
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatchWithTName(ctx, tableName, &lineBasicLocationList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineBasicLocationMap := make(map[string]interface{})
	for _, lineBasicLocation := range lineBasicLocationList {
		// key := fmt.Sprintf("%s-%s-%d", lineBasicLocation.LineId, lineBasicLocation.CollectDeliverGroupId, lineBasicLocation.LocationId)
		key := model8.GenLineBasicLocationCacheKey(lineBasicLocation.LineId, lineBasicLocation.CollectDeliverGroupId, lineBasicLocation.LocationId)
		lineBasicLocationMap[key] = lineBasicLocation
	}
	return lineBasicLocationMap, nil
}

// line_id + collectDeliverGroupId + postcode ->postcode list的map
func (db *DumpManager) DumpBasicPostcodeTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var lineBasicPostcodeList []*model9.LineBasicServiceablePostcodeTab
	tableName := model9.GetBasicPostcodeTableNameByIndex(dumpParam["index"].(int))
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatchWithTName(ctx, tableName, &lineBasicPostcodeList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	lineBasicPostcodeMap := make(map[string]interface{})
	for _, lineBasicPostcode := range lineBasicPostcodeList {
		key := model9.GenLineBasicServiceablePostcodeCacheKey(lineBasicPostcode.LineId, lineBasicPostcode.CollectDeliverGroupId, lineBasicPostcode.Postcode)
		lineBasicPostcodeMap[key] = lineBasicPostcode
	}
	return lineBasicPostcodeMap, nil
}

// 3pl id -> []*ref 的map
func (db *DumpManager) DumpTPLIDLineIDRefTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var tplIDLineIDList []*tpl_id_line_id_ref2.TPLLineIDRefTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &tpl_id_line_id_ref2.TPLLineIDRefTab{}, &tplIDLineIDList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	tplIDLineIDRefMap := make(map[string]interface{})
	// 针对3pl id和line id分别存储，方便查询。目前只有这两个查询条件

	// 3pl id的存储
	for _, ref := range tplIDLineIDList {
		// key := fmt.Sprintf("%s:[%d]", "3pl", ref.ThreePLID)
		key := tpl_id_line_id_ref2.GenTPLLineIDRef3PLCacheKey(ref.ThreePLID)
		if v, ok := tplIDLineIDRefMap[key]; ok {
			tplIDLineIDRefMap[key] = append(v.([]*tpl_id_line_id_ref2.TPLLineIDRefTab), ref)
		} else {
			tplIDLineIDRefMap[key] = []*tpl_id_line_id_ref2.TPLLineIDRefTab{ref}
		}
	}

	// line id的存储
	for _, ref := range tplIDLineIDList {
		// key := fmt.Sprintf("%s:[%s]", "line_id", ref.LineID)
		key := tpl_id_line_id_ref2.GenTPLLineIDRefLineCacheKey(ref.LineID)
		if v, ok := tplIDLineIDRefMap[key]; ok {
			tplIDLineIDRefMap[key] = append(v.([]*tpl_id_line_id_ref2.TPLLineIDRefTab), ref)
		} else {
			tplIDLineIDRefMap[key] = []*tpl_id_line_id_ref2.TPLLineIDRefTab{ref}
		}
	}
	return tplIDLineIDRefMap, nil
}

// channelID -> originLocationID:cepLeft:cepRight" -> []*manual_update_rule.CdtManualUpdatePostcodeDataTab
func (db *DumpManager) DumpCDTManualUpdatePostcodeData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	manualUpdatePostcodeDatasMap := make(map[string]interface{})
	var manualUpdatePostcodeDatas []*manual_update_rule.CdtManualUpdatePostcodeDataTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	lcosErr := common.GetAllDataBatch(ctx, &manual_update_rule.CdtManualUpdatePostcodeDataTab{}, &manualUpdatePostcodeDatas, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	for _, ref := range manualUpdatePostcodeDatas {
		key := fmt.Sprintf("%v:%v:%v", ref.OriginLocationId, ref.DestinationPostcodeInitial, ref.DestinationPostcodeFinal)
		if manualUpdatePostcodeDatasMap[ref.ProductId] == nil {
			manualUpdatePostcodeDatasMap[ref.ProductId] = map[string][]*manual_update_rule.CdtManualUpdatePostcodeDataTab{}
		}
		manualUpdatePostcodeDatasMap[ref.ProductId].(map[string][]*manual_update_rule.CdtManualUpdatePostcodeDataTab)[key] = append(manualUpdatePostcodeDatasMap[ref.ProductId].(map[string][]*manual_update_rule.CdtManualUpdatePostcodeDataTab)[key], ref)
	}
	return manualUpdatePostcodeDatasMap, nil
}

// id -> *manual_update_rule.CdtManualUpdateRecordTab
func (db *DumpManager) DumpCDTManualUpdateRecordData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var manualUpdateRecordDatas []*manual_update_rule.CdtManualUpdateRecordTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &manual_update_rule.CdtManualUpdateRecordTab{}, &manualUpdateRecordDatas, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	manualUpdateRecordDatasMap := make(map[string]interface{})

	for _, ref := range manualUpdateRecordDatas {
		manualUpdateRecordDatasMap[strconv.Itoa(int(ref.Id))] = ref
	}
	return manualUpdateRecordDatasMap, nil
}

// channel_id -> "originLocationID:destinationLocationID" -> []*manual_manipulation_rule.CDTManualManipulationLocationRuleTab
func (db *DumpManager) DumpCDTManualManipulationLocationRuleData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())

	var manualManipulationLocationRuleDatas []*manual_manipulation_rule.CDTManualManipulationLocationRuleTab
	manualManipulationLocationRulesMap := make(map[string]interface{})
	batchSize := config.GetDBDumpBatchSize(ctx)

	lcosErr := common.GetAllDataBatch(ctx, &manual_manipulation_rule.CDTManualManipulationLocationRuleTab{}, &manualManipulationLocationRuleDatas, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	for _, ref := range manualManipulationLocationRuleDatas {
		key := utils.GenLocationRouteKey(ref.OriginLocationID, ref.DestinationLocationID)
		if manualManipulationLocationRulesMap[ref.ProductID] == nil {
			manualManipulationLocationRulesMap[ref.ProductID] = map[uint64][]*manual_manipulation_rule.CDTManualManipulationLocationRuleTab{}
		}
		manualManipulationLocationRulesMap[ref.ProductID].(map[uint64][]*manual_manipulation_rule.CDTManualManipulationLocationRuleTab)[key] = append(manualManipulationLocationRulesMap[ref.ProductID].(map[uint64][]*manual_manipulation_rule.CDTManualManipulationLocationRuleTab)[key], ref)
	}
	return manualManipulationLocationRulesMap, nil
}

// channel_id -> "originLocationID:destinationLocationPostcode" -> []*manual_manipulation_rule.CDTManualManipulationZipcodeRuleTab
func (db *DumpManager) DumpCDTManualManipulationZipcodeRuleData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var manualManipulationModels []*manual_manipulation_rule.CDTManualManipulationZipcodeRuleTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &manual_manipulation_rule.CDTManualManipulationZipcodeRuleTab{}, &manualManipulationModels, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	manualManipulationMap := make(map[string]interface{})
	for _, ref := range manualManipulationModels {
		key := fmt.Sprintf("%v:%v", ref.OriginLocationID, ref.DestinationPostcode)
		if manualManipulationMap[ref.ProductID] == nil {
			manualManipulationMap[ref.ProductID] = map[string][]*manual_manipulation_rule.CDTManualManipulationZipcodeRuleTab{}
		}
		manualManipulationMap[ref.ProductID].(map[string][]*manual_manipulation_rule.CDTManualManipulationZipcodeRuleTab)[key] = append(manualManipulationMap[ref.ProductID].(map[string][]*manual_manipulation_rule.CDTManualManipulationZipcodeRuleTab)[key], ref)
	}
	return manualManipulationMap, nil
}

// channel_id -> []*manual_manipulation_rule.CDTManualManipulationPostcodeRuleTab
func (db *DumpManager) DumpCDTManualManipulationPostcodeRuleData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	batchSize := config.GetDBDumpBatchSize(ctx)

	// find disabled record, not load data into db
	var records []*manual_manipulation_record.CDTManualManipulationRuleRecordTab
	disabledRecordMap := make(map[uint64]bool)
	lcosErr := common.GetAllDataBatch(ctx, &manual_manipulation_record.CDTManualManipulationRuleRecordTab{}, &records, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	now := utils.GetTimestamp(ctx)
	for _, record := range records {
		if record.IsDisabled(now) {
			disabledRecordMap[record.ID] = true
		}
	}

	manualManipulationPostcodeRulesMap := make(map[string]interface{})
	var models []*manual_manipulation_rule.CDTManualManipulationPostcodeRuleTab
	lcosErr = common.GetAllDataBatch(ctx, &manual_manipulation_rule.CDTManualManipulationPostcodeRuleTab{}, &models, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	for _, ref := range models {
		// skip disabled record
		if _, ok := disabledRecordMap[ref.RecordID]; ok {
			continue
		}
		if manualManipulationPostcodeRulesMap[ref.ProductID] == nil {
			manualManipulationPostcodeRulesMap[ref.ProductID] = []*manual_manipulation_rule.CDTManualManipulationPostcodeRuleTab{}
		}
		manualManipulationPostcodeRulesMap[ref.ProductID] = append(manualManipulationPostcodeRulesMap[ref.ProductID].([]*manual_manipulation_rule.CDTManualManipulationPostcodeRuleTab), ref)
	}
	return manualManipulationPostcodeRulesMap, nil
}

// id -> *CDTManualManipulationRuleRecordTab
func (db *DumpManager) DumpCDTManualManipulationRuleRecordData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var manualManipulationRecordDatas []*manual_manipulation_record.CDTManualManipulationRuleRecordTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &manual_manipulation_record.CDTManualManipulationRuleRecordTab{}, &manualManipulationRecordDatas, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	manualUpdateRecordDatasMap := make(map[string]interface{})

	for _, ref := range manualManipulationRecordDatas {
		manualUpdateRecordDatasMap[strconv.Itoa(int(ref.ID))] = ref
		// key格式：{cdt_type}:{product_id}，此key只加载非disabled数据
		// SPLN-34535 新增需求，仿真模拟需要获取历史的修正规则数据，因此需要加载disable的数据。
		productKey := ref.GenerateProductKey()
		if manualUpdateRecordDatasMap[productKey] == nil {
			manualUpdateRecordDatasMap[productKey] = []*manual_manipulation_record.CDTManualManipulationRuleRecordTab{}
		}
		manualUpdateRecordDatasMap[productKey] = append(manualUpdateRecordDatasMap[productKey].([]*manual_manipulation_record.CDTManualManipulationRuleRecordTab), ref)
	}
	return manualUpdateRecordDatasMap, nil
}

// id -> *CDTManualManipulationRuleRecordTab
func (db *DumpManager) DumpAutoUpdateRuleData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var autoUpdateRules []*auto_rule2.CDTAutoUpdateRuleTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &auto_rule2.CDTAutoUpdateRuleTab{}, &autoUpdateRules, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	autoUpdateRecordDatasMap := make(map[string]interface{})

	for _, ref := range autoUpdateRules {
		// fill cep range list
		if len(ref.DestinationCepRange) > 0 {
			ref.DestinationCepRangeList = auto_rule2.ParseFromCepRangeString(ref.DestinationCepRange)
		}
		if autoUpdateRecordDatasMap[ref.ProductID] == nil {
			autoUpdateRecordDatasMap[ref.ProductID] = []*auto_rule2.CDTAutoUpdateRuleTab{}
		}
		autoUpdateRecordDatasMap[ref.ProductID] = append(autoUpdateRecordDatasMap[ref.ProductID].([]*auto_rule2.CDTAutoUpdateRuleTab), ref)
	}
	return autoUpdateRecordDatasMap, nil
}

// auto_rule_id -> *CDTAutoUpdateCalculateDataVersionTab
func (db *DumpManager) DumpAutoUpdateCalculateDataVersion(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var autoUpdateRuleVersions []*auto_update_data.CDTAutoUpdateCalculateDataVersionTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &auto_update_data.CDTAutoUpdateCalculateDataVersionTab{}, &autoUpdateRuleVersions, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	autoUpdateVersionMap := make(map[string]interface{})

	for _, ref := range autoUpdateRuleVersions {
		if autoUpdateVersionMap[strconv.Itoa(int(ref.AutoUpdateRuleID))] == nil {
			autoUpdateVersionMap[strconv.Itoa(int(ref.AutoUpdateRuleID))] = []*auto_update_data.CDTAutoUpdateCalculateDataVersionTab{}
		}
		autoUpdateVersionMap[strconv.Itoa(int(ref.AutoUpdateRuleID))] = append(autoUpdateVersionMap[strconv.Itoa(int(ref.AutoUpdateRuleID))].([]*auto_update_data.CDTAutoUpdateCalculateDataVersionTab), ref)
	}
	return autoUpdateVersionMap, nil
}

// key: lineId + groupId  value: list LineBasicServiceableCepRangeTab
func (db *DumpManager) DumpLineBasicCepRangeTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var cepRangeModels []*cep_range.LineBasicServiceableCepRangeTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &cep_range.LineBasicServiceableCepRangeTab{}, &cepRangeModels, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	cepRangeMap := make(map[string]interface{})
	for _, cepRangeModel := range cepRangeModels {
		// key := fmt.Sprintf("%s-%s", cepRangeModel.LineID, cepRangeModel.CollectDeliverGroupID)
		key := cep_range.GenLineBasicServiceableCepRangeCacheKey(cepRangeModel.LineID, cepRangeModel.CollectDeliverGroupID)
		if v, ok := cepRangeMap[key]; ok {
			cepRangeMap[key] = append(v.([]*cep_range.LineBasicServiceableCepRangeTab), cepRangeModel)
		} else {
			cepRangeMap[key] = []*cep_range.LineBasicServiceableCepRangeTab{cepRangeModel}
		}
	}

	// sort before return
	for key := range cepRangeMap {
		sort.SliceStable(cepRangeMap[key], func(i, j int) bool {
			cepRangeList := cepRangeMap[key].([]*cep_range.LineBasicServiceableCepRangeTab)
			return cepRangeList[i].Initial < cepRangeList[j].Initial
		})
	}

	return cepRangeMap, nil
}

// key: lineId + groupId  value: list LineBasicServiceableCepRangeTab
func (db *DumpManager) DumpLineOperationCepRangeTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var cepRangeModels []*cep_range.LineOperationServiceableCepRangeTab
	cepRangeMap := make(map[string]interface{})
	batchSize := config.GetDBDumpBatchSize(ctx)

	lcosErr := common.GetAllDataBatch(ctx, &cep_range.LineOperationServiceableCepRangeTab{}, &cepRangeModels, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	for _, cepRangeModel := range cepRangeModels {
		// key := fmt.Sprintf("%s-%s", cepRangeModel.LineID, cepRangeModel.CollectDeliverGroupID)
		key := cep_range.GenLineOperationServiceableCepRangeCacheKey(cepRangeModel.LineID, cepRangeModel.CollectDeliverGroupID)
		if v, ok := cepRangeMap[key]; ok {
			cepRangeMap[key] = append(v.([]*cep_range.LineOperationServiceableCepRangeTab), cepRangeModel)
		} else {
			cepRangeMap[key] = []*cep_range.LineOperationServiceableCepRangeTab{cepRangeModel}
		}
	}
	return cepRangeMap, nil
}

// key: channel_id:[%v],state_id:[%v],region:[%v]  value: []*Holiday
func (db *DumpManager) DumpSlsHoliday(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	holidayMap := make(map[string]interface{})
	batchSize := config.GetDBDumpBatchSize(ctx)
	// 加载全部国家的holiday
	regionList := config.GetMutableConf(ctx).SlsHolidayCacheDumpConfig.GetDumpRegion()
	for _, region := range regionList {
		locationOffset, ok := constant.THIRD_PARTY_LOCATION_ID_MAPPING[region]
		if !ok {
			continue
		}

		var holidays []*sls_holiday.HolidayTab
		lcosErr := common.GetAllDataBatchWithDB(ctx.ReadSlsDB(region), &sls_holiday.HolidayTab{}, &holidays, batchSize)
		if lcosErr != nil {
			return nil, errors.New(lcosErr.Msg)
		}
		for _, holiday := range holidays {
			// 只存储status为enable，且service type为lm的holiday
			if holiday.Status == 1 {
				// 对于sls的state location需要加上偏移量
				stateLocationID := holiday.StateID
				if stateLocationID > 0 && stateLocationID < locationOffset {
					stateLocationID = stateLocationID + locationOffset
				}
				// key := fmt.Sprintf("channel_id:[%v],state_id:[%v],region:[%v]", holiday.ChannelID, stateLocationID, region)
				key := sls_holiday.GenHolidayCacheKey(strconv.Itoa(holiday.ChannelID), stateLocationID, region)
				if v, ok := holidayMap[key]; ok {
					holidayMap[key] = append(v.([]*sls_holiday.HolidayTab), holiday)
				} else {
					holidayMap[key] = []*sls_holiday.HolidayTab{holiday}
				}
			}
		}
	}
	return holidayMap, nil
}

// key: channel_id:[%v],state_id:[%v],region:[%v]  value: []*RecurringHoliday
func (db *DumpManager) DumpSlsRecurringHoliday(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	recurringHolidayMap := make(map[string]interface{})
	batchSize := config.GetDBDumpBatchSize(ctx)
	// 加载全部国家的holiday
	regionList := config.GetMutableConf(ctx).SlsHolidayCacheDumpConfig.GetDumpRegion()
	for _, region := range regionList {
		locationOffset, ok := constant.THIRD_PARTY_LOCATION_ID_MAPPING[region]
		if !ok {
			continue
		}

		var recurringHolidays []*sls_holiday.RecurringHolidayTab
		lcosErr := common.GetAllDataBatchWithDB(ctx.ReadSlsDB(region), &sls_holiday.RecurringHolidayTab{}, &recurringHolidays, batchSize)
		if lcosErr != nil {
			return nil, errors.New(lcosErr.Msg)
		}
		for _, recurringHoliday := range recurringHolidays {
			// 只存储status为enable，且service type为lm的holiday
			if recurringHoliday.Status == 1 {
				// 对于sls的state location需要加上偏移量
				stateLocationID := recurringHoliday.StateID
				if stateLocationID > 0 && stateLocationID < locationOffset {
					stateLocationID = stateLocationID + locationOffset
				}
				// key := fmt.Sprintf("channel_id:[%v],state_id:[%v],region:[%v]", recurringHoliday.ChannelID, stateLocationID, region)
				key := sls_holiday.GenRecurringHolidayTabCacheKey(strconv.Itoa(recurringHoliday.ChannelID), stateLocationID, region)
				if v, ok := recurringHolidayMap[key]; ok {
					recurringHolidayMap[key] = append(v.([]*sls_holiday.RecurringHolidayTab), recurringHoliday)
				} else {
					recurringHolidayMap[key] = []*sls_holiday.RecurringHolidayTab{recurringHoliday}
				}
			}
		}
	}
	return recurringHolidayMap, nil
}

// key: product_id:[%v],state_id:[%v],region:[%v]  value: []*ProductHoliday
func (db *DumpManager) DumpLPSHoliday(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	holidayMap := make(map[string]interface{})
	batchSize := config.GetDBDumpBatchSize(ctx)
	// 加载全部国家的holiday
	regionList := config.GetMutableConf(ctx).LpsHolidayCacheDumpConfig.GetDumpRegion()
	for _, region := range regionList {
		var holidays []*lps_holiday.ProductHolidayTab
		lcosErr := common.GetAllDataBatchWithDB(ctx.ReadLpsDB(region), &lps_holiday.ProductHolidayTab{}, &holidays, batchSize)
		if lcosErr != nil {
			return nil, errors.New(lcosErr.Msg)
		}
		for _, holiday := range holidays {
			// 只存储status为enable，且service type为lm的holiday
			if holiday.ProductStatus == 1 {
				// key := fmt.Sprintf("product_id:[%v],state_id:[%v],region:[%v]", holiday.ProductID, holiday.StateID, region)
				key := lps_holiday.GenProductHolidayCacheKey(strconv.Itoa(holiday.ProductID), holiday.StateID, region)
				if v, ok := holidayMap[key]; ok {
					holidayMap[key] = append(v.([]*lps_holiday.ProductHolidayTab), holiday)
				} else {
					holidayMap[key] = []*lps_holiday.ProductHolidayTab{holiday}
				}
			}
		}

		// SPLN-35905
		var installationHolidays []*lps_holiday.InstallationProductHolidayTab
		lcosErr = common.GetAllDataBatchWithDB(ctx.ReadLpsDB(region), &lps_holiday.InstallationProductHolidayTab{}, &installationHolidays, batchSize)
		if lcosErr != nil {
			return nil, errors.New(lcosErr.Msg)
		}
		for _, installationHoliday := range installationHolidays {
			// 只存储status为enable，且service type为lm的holiday
			if installationHoliday.ProductStatus == 1 {
				// key := fmt.Sprintf("product_id:[%v],state_id:[%v],region:[%v]", holiday.ProductID, holiday.StateID, region)
				key := lps_holiday.GenProductHolidayCacheKey(strconv.Itoa(installationHoliday.ProductID), installationHoliday.StateID, region)
				if v, ok := holidayMap[key]; ok {
					holidayMap[key] = append(v.([]*lps_holiday.ProductHolidayTab), convertVasHolidayToHoliday(installationHoliday))
				} else {
					holidayMap[key] = []*lps_holiday.ProductHolidayTab{convertVasHolidayToHoliday(installationHoliday)}
				}
			}
		}

	}
	return holidayMap, nil
}

func convertVasHolidayToHoliday(vasHoliday *lps_holiday.InstallationProductHolidayTab) *lps_holiday.ProductHolidayTab {
	return &lps_holiday.ProductHolidayTab{
		ProductID:     vasHoliday.ProductID,
		ServiceType:   vasHoliday.ServiceType,
		StateID:       vasHoliday.StateID,
		ProductDate:   vasHoliday.ProductDate,
		ProductStatus: vasHoliday.ProductStatus,
		Ctime:         vasHoliday.Ctime,
		Mtime:         vasHoliday.Mtime,
		Operator:      vasHoliday.Operator,
	}
}

// key: product_id:[%v],state_id:[%v],region:[%v]  value: []*ProductRecurringHoliday
func (db *DumpManager) DumpLPSRecurringHoliday(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	recurringHolidayMap := make(map[string]interface{})
	batchSize := config.GetDBDumpBatchSize(ctx)
	// 加载全部国家的holiday
	regionList := config.GetMutableConf(ctx).LpsHolidayCacheDumpConfig.GetDumpRegion()
	for _, region := range regionList {
		var recurringHolidays []*lps_holiday.ProductRecurringHolidayTab
		lcosErr := common.GetAllDataBatchWithDB(ctx.ReadLpsDB(region), &lps_holiday.ProductRecurringHolidayTab{}, &recurringHolidays, batchSize)
		if lcosErr != nil {
			return nil, errors.New(lcosErr.Msg)
		}
		for _, recurringHoliday := range recurringHolidays {
			// 只存储status为enable，且service type为lm的holiday
			if recurringHoliday.ProductStatus == 1 {
				// key := fmt.Sprintf("product_id:[%v],state_id:[%v],region:[%v]", recurringHoliday.ProductID, recurringHoliday.StateID, region)
				key := lps_holiday.GenProductRecurringHolidayKey(strconv.Itoa(recurringHoliday.ProductID), recurringHoliday.StateID, region)
				if v, ok := recurringHolidayMap[key]; ok {
					recurringHolidayMap[key] = append(v.([]*lps_holiday.ProductRecurringHolidayTab), recurringHoliday)
				} else {
					recurringHolidayMap[key] = []*lps_holiday.ProductRecurringHolidayTab{recurringHoliday}
				}
			}
		}

		// SPLN-35905 installation product
		var installationRecurringHolidays []*lps_holiday.InstallationProductRecurringHolidayTab
		lcosErr = common.GetAllDataBatchWithDB(ctx.ReadLpsDB(region), &lps_holiday.InstallationProductRecurringHolidayTab{}, &installationRecurringHolidays, batchSize)
		if lcosErr != nil {
			return nil, errors.New(lcosErr.Msg)
		}
		for _, installationRecurringHoliday := range installationRecurringHolidays {
			// 只存储status为enable，且service type为lm的holiday
			if installationRecurringHoliday.ProductStatus == 1 {
				// key := fmt.Sprintf("product_id:[%v],state_id:[%v],region:[%v]", recurringHoliday.ProductID, recurringHoliday.StateID, region)
				key := lps_holiday.GenProductRecurringHolidayKey(strconv.Itoa(installationRecurringHoliday.ProductID), installationRecurringHoliday.StateID, region)
				if v, ok := recurringHolidayMap[key]; ok {
					recurringHolidayMap[key] = append(v.([]*lps_holiday.ProductRecurringHolidayTab), convertVasWeekendToWeekend(installationRecurringHoliday))
				} else {
					recurringHolidayMap[key] = []*lps_holiday.ProductRecurringHolidayTab{convertVasWeekendToWeekend(installationRecurringHoliday)}
				}
			}
		}
	}
	return recurringHolidayMap, nil
}

func convertVasWeekendToWeekend(vasWeekend *lps_holiday.InstallationProductRecurringHolidayTab) *lps_holiday.ProductRecurringHolidayTab {
	return &lps_holiday.ProductRecurringHolidayTab{
		ProductID:     vasWeekend.ProductID,
		ServiceType:   vasWeekend.ServiceType,
		StateID:       vasWeekend.StateID,
		ProductDay:    vasWeekend.ProductDay,
		ProductStatus: vasWeekend.ProductStatus,
		Ctime:         vasWeekend.Ctime,
		Mtime:         vasWeekend.Mtime,
		Operator:      vasWeekend.Operator,
	}
}

// 两种索引方式
// BranchID->*LogisticBranchInfoTab
// SupplyType->map[LocationID][]*LogisticBranchInfoTab
func (dp *DumpManager) DumpLogisticBranchInfo(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	if !startup.IsBranchProject() && config.GetMutableConf(context.Background()).DisableBranchLocalCache {
		return make(map[string]interface{}, 0), nil
	}

	ctx := utils.NewCommonCtx(context.Background())
	var allData []*branch_info.LogisticBranchInfoTab
	batchSize := config.GetDBDumpBranchBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &branch_info.LogisticBranchInfoTab{}, &allData, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	result := map[string]interface{}{}
	for _, v := range allData {
		result[branch_info.GenBranchInfoCacheKeyForBranchId(v.BranchID)] = v
		result[branch_info.GenBranchInfoCacheKeyForBranchRef(v.SupplyType, v.BranchRef)] = v
		supplyTypeKey := branch_info.GenBranchInfoCacheKeyForSupplyType(v.SupplyType)
		if data, ok := result[supplyTypeKey]; ok {
			mapSupplyData, succ := data.(map[uint64][]*branch_info.LogisticBranchInfoTab)
			if !succ {
				// not suppose to happen
				errmsg := "unexpected error, change data type fail"
				logger.LogErrorf(errmsg)
				return nil, errors.New(errmsg)
			}
			if dataList, exist := mapSupplyData[v.LocationID]; exist {
				mapSupplyData[v.LocationID] = append(dataList, v)
			} else {
				mapSupplyData[v.LocationID] = []*branch_info.LogisticBranchInfoTab{v}
			}
		} else {
			mapSupplyData := make(map[uint64][]*branch_info.LogisticBranchInfoTab)
			mapSupplyData[v.LocationID] = []*branch_info.LogisticBranchInfoTab{v}
			result[supplyTypeKey] = mapSupplyData
		}
	}

	branch_info.BuildIndex(ctx, allData)
	branch_info.LocationCacheManager.RefreshData(allData)
	branch_info.BranchPrometheusReport(allData)
	logger.LogDebugf("DumpLogisticBranchInfo success, data len:%d", len(allData))
	return result, nil
}

func (dp *DumpManager) DumpLogisticBranchGroup(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	if !startup.IsBranchProject() && config.GetMutableConf(context.Background()).DisableBranchLocalCache {
		return make(map[string]interface{}, 0), nil
	}

	ctx := utils.NewCommonCtx(context.Background())
	var allData []*branch_group.LogisticBranchGroupTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &branch_group.LogisticBranchGroupTab{}, &allData, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	result := map[string]interface{}{}
	for _, v := range allData {
		result[strconv.FormatUint(uint64(v.BranchGroupID), 10)] = v
	}
	return result, nil
}

func (dp *DumpManager) DumpPostalCodeGeo(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	if !startup.IsBranchProject() && config.GetMutableConf(context.Background()).DisableBranchLocalCache {
		return make(map[string]interface{}, 0), nil
	}

	ctx := utils.NewCommonCtx(context.Background())
	result := map[string]interface{}{}
	var minID uint64
	var maxID uint64
	md := &postal_code_to_geo.PostalCodeToGeo{}
	if err := ctx.ReadSlsDB("SG").Table(md.TableName()).Select("max(id)").Scan(&maxID).GetError(); err != nil {
		return nil, err
	}
	if err := ctx.ReadSlsDB("SG").Table(md.TableName()).Select("min(id)").Scan(&minID).GetError(); err != nil {
		return nil, err
	}
	if minID <= 0 {
		return result, nil
	}
	currID := minID - 1

	for currID < maxID {
		var allData []*postal_code_to_geo.PostalCodeToGeo
		// 只有sg有这个东西
		tmpMaxID := currID + 3000
		if err := ctx.ReadSlsDB("SG").Where("id > ?", currID).Where("id <= ?", tmpMaxID).Find(&allData).GetError(); err != nil {
			return nil, err
		}
		for _, v := range allData {
			result[v.Zipcode] = v
		}

		if len(allData) < 3000 {
			if tmpMaxID >= maxID {
				break
			}
			if err := ctx.ReadSlsDB("SG").Table(md.TableName()).Select("min(id)").Where("id >= ?", tmpMaxID).Scan(&currID).GetError(); err != nil {
				return nil, err
			}
		} else {
			currID = tmpMaxID
		}
	}

	return result, nil
}

// 加载TW wight size信息
func (dp *DumpManager) DumpLogisticTWSizeInfo(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var allData []*model11.LogisticSizeTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model11.LogisticSizeTab{}, &allData, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	result := map[string]interface{}{}
	for _, v := range allData {
		sizeInfo := result[v.ProductId]
		var sizeInfoMap map[string]interface{}
		if sizeInfo == nil {
			sizeInfoMap = map[string]interface{}{}
		} else {
			sizeInfoMap = sizeInfo.(map[string]interface{})
		}
		sizeInfoMap[v.SizeId] = v
		result[v.ProductId] = sizeInfoMap
	}
	return result, nil
}

// id->store    new_storeid+store_type->store
func (dp *DumpManager) DumpTWStore(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	if !startup.IsBranchProject() && config.GetMutableConf(context.Background()).DisableBranchLocalCache {
		return make(map[string]interface{}, 0), nil
	}

	ctx := utils.NewCommonCtx(context.Background())
	storeMap := make(map[string]interface{})
	var stores []*tw_store.LogisticThirdPartyConvenienceStoreTab
	// 加载全部的store
	batchSize := config.GetDBDumpBatchSize(ctx)
	lcosErr := common.GetAllDataBatch(ctx, &tw_store.LogisticThirdPartyConvenienceStoreTab{}, &stores, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	for _, store := range stores {
		// idKey := fmt.Sprintf("%v", store.ID)
		idKey := strconv.FormatUint(store.ID, 10)
		storeMap[idKey] = store
		if store.EnableStatus == constant.ENABLED && store.NewStoreID != "" {
			// dump store into cache by new_store_id ant type when enable_status is enabled and new_store_id is not empty
			newStoreIdAndTypeKey := tw_store.GenKeyForTWStoreMapCacheUsingNewStoreIdAndType(store.NewStoreID, store.StoreType)
			storeMap[newStoreIdAndTypeKey] = store
		}
	}
	return storeMap, nil
}

// oldCSVkey->store    new_csv_id+store_type->store    store_id+store_type->store
func (dp *DumpManager) DumpTWStoreMap(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	if !startup.IsBranchProject() && config.GetMutableConf(context.Background()).DisableBranchLocalCache {
		return make(map[string]interface{}, 0), nil
	}

	ctx := utils.NewCommonCtx(context.Background())
	storeMap := make(map[string]interface{})
	var stores []*tw_store.LogisticOldStoreIdxMapTab
	// 加载全部的store
	batchSize := config.GetDBDumpBatchSize(ctx)
	lcosErr := common.GetAllDataBatch(ctx, &tw_store.LogisticOldStoreIdxMapTab{}, &stores, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	for _, store := range stores {
		// addressIDKey := fmt.Sprintf("%v:%v", constant.StoreAddressIDPrefix, store.OldCVSID)
		// storeIDAndTypeKey := fmt.Sprintf("%v:%v:%v", constant.StoreIDStoreTypePrefix, store.StoreID, store.OldStoreType)
		addressIDKey := tw_store.GenKeyForOldStoreIdxMapCacheUsingAddress(store.OldCVSID)
		storeIDAndTypeKey := tw_store.GenKeyForOldStoreIdxMapCacheUsingIdAndType(store.StoreID, store.OldStoreType)
		newCvsIdAndTypeKey := tw_store.GenKeyForOldStoreIdxMapCacheUsingNewCvsIdAndType(store.NewCVSID, store.OldStoreType)
		storeMap[addressIDKey] = store
		storeMap[storeIDAndTypeKey] = store
		// new_cvs_id 和 type无法唯一确定一条记录，因此使用slice存储
		tmp, ok := storeMap[newCvsIdAndTypeKey]
		storeList, typeOk := tmp.([]*tw_store.LogisticOldStoreIdxMapTab)
		if !ok || !typeOk {
			storeList = []*tw_store.LogisticOldStoreIdxMapTab{}
		}
		storeList = append(storeList, store)
		storeMap[newCvsIdAndTypeKey] = storeList
	}
	return storeMap, nil
}

// 将ssa basic conf存为site_id->basic conf的map
func (dp *DumpManager) DumpLogisticSiteServiceableAreaBasicConf(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	basicConfMap := map[string]interface{}{}
	var allDatas []*siteBasicConfSiteServiceableAreaBasicConf.LogisticSiteBasicServiceableConfTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	lcosErr := common.GetAllDataBatch(ctx, &siteBasicConfSiteServiceableAreaBasicConf.LogisticSiteBasicServiceableConfTab{}, &allDatas, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	for _, data := range allDatas {
		basicConfMap[data.SiteId] = data
	}
	return basicConfMap, nil
}

// 将ssa basic location存为site_id+location_id->[]*location的map
func (dp *DumpManager) DumpLogisticSiteServiceableAreaLocation(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	batchSize := config.GetDBDumpBatchSize(ctx)
	var allDatas []*site_serviceable_area_location.LogisticSiteBasicServiceableLocationTab
	tableName := site_serviceable_area.GetSiteBasicServiceableAreaTableNameByIndex(site_serviceable_area_location.LogisticSiteBasicServiceableLocationTableName, dumpParam["index"].(int))
	lcosErr := common.GetAllDataBatchWithTName(ctx, tableName, &allDatas, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	basicConfMap := map[string]interface{}{}
	allActualPointIDMap := map[string]bool{} // 用于去重某条虚拟点下的所有实际点
	for _, data := range allDatas {
		key := fmt.Sprintf("%s:%d", data.SiteId, data.LocationId)
		if _, ok := basicConfMap[key]; !ok {
			basicConfMap[key] = []*site_serviceable_area_location.LogisticSiteBasicServiceableLocationTab{}
		}
		basicConfMap[key] = append(basicConfMap[key].([]*site_serviceable_area_location.LogisticSiteBasicServiceableLocationTab), data)

		// 添加实际点
		tmp := utils.GenKey(":", data.SiteId, data.ActualPointId)
		AllActualPointKey := utils.GenKey(":", data.SiteId, strconv.Itoa(constant.AllActualPointIDListLocation))
		if _, ok1 := allActualPointIDMap[tmp]; !ok1 {
			if _, ok2 := basicConfMap[AllActualPointKey]; !ok2 {
				basicConfMap[AllActualPointKey] = []*site_serviceable_area_location.LogisticSiteBasicServiceableLocationTab{}
			}
			basicConfMap[AllActualPointKey] = append(basicConfMap[AllActualPointKey].([]*site_serviceable_area_location.LogisticSiteBasicServiceableLocationTab), data)
			allActualPointIDMap[tmp] = true
		}
	}
	return basicConfMap, nil
}

// 将ssa basic conf存为site_id+postcode->[]*postcode的map
func (dp *DumpManager) DumpLogisticSiteServiceableAreaPostcode(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	tableName := site_serviceable_area.GetSiteBasicServiceableAreaTableNameByIndex(site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTableName, dumpParam["index"].(int))
	basicConfMap := map[string]interface{}{}
	allActualPointIDMap := map[string]bool{} // 用于去重某条虚拟点下的所有实际点
	batchSize := config.GetDBDumpBatchSize(ctx)
	var allDatas []*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab
	lcosErr := common.GetAllDataBatchWithTName(ctx, tableName, &allDatas, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	for _, data := range allDatas {
		key := utils.GenKey(":", data.SiteId, data.Postcode)
		if _, ok := basicConfMap[key]; !ok {
			basicConfMap[key] = []*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{}
		}
		basicConfMap[key] = append(basicConfMap[key].([]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab), data)

		// 添加实际点
		tmp := utils.GenKey(":", data.SiteId, data.ActualPointId)
		AllActualPointKey := utils.GenKey(":", data.SiteId, constant.AllActualPointIDList)
		if _, ok1 := allActualPointIDMap[tmp]; !ok1 {
			if _, ok2 := basicConfMap[AllActualPointKey]; !ok2 {
				basicConfMap[AllActualPointKey] = []*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{}
			}
			basicConfMap[AllActualPointKey] = append(basicConfMap[AllActualPointKey].([]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab), data)
			allActualPointIDMap[tmp] = true
		}
	}
	return basicConfMap, nil
}

// 将ssa basic conf存为site_id->[]*cep range的map，其中的列表经过排序，方便通过二分查找找到符合条件的信息
func (dp *DumpManager) DumpLogisticSiteServiceableAreaCepRange(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	tableName := site_serviceable_area.GetSiteBasicServiceableAreaTableNameByIndex(site_serviceable_area_cep_range.LogisticSiteBasicServiceableCepRangeTableName, dumpParam["index"].(int))
	basicConfMap := map[string]interface{}{}
	allActualPointIDMap := map[string]bool{} // 用于去重某条虚拟点下的所有实际点
	var allDatas []*site_serviceable_area_cep_range.LogisticSiteBasicServiceableCepRangeTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	lcosErr := common.GetAllDataBatchWithTName(ctx, tableName, &allDatas, batchSize)
	if lcosErr != nil {
		return nil, errors.New(lcosErr.Msg)
	}
	for _, data := range allDatas {
		if _, ok := basicConfMap[data.SiteId]; !ok {
			basicConfMap[data.SiteId] = []*site_serviceable_area_cep_range.LogisticSiteBasicServiceableCepRangeTab{}
		}
		basicConfMap[data.SiteId] = append(basicConfMap[data.SiteId].([]*site_serviceable_area_cep_range.LogisticSiteBasicServiceableCepRangeTab), data)

		// 添加实际点
		tmp := utils.GenKey(":", data.SiteId, data.ActualPointId)
		AllActualPointKey := utils.GenKey(":", data.SiteId, constant.AllActualPointIDList)
		if _, ok1 := allActualPointIDMap[tmp]; !ok1 {
			if _, ok2 := basicConfMap[AllActualPointKey]; !ok2 {
				basicConfMap[AllActualPointKey] = []*site_serviceable_area_cep_range.LogisticSiteBasicServiceableCepRangeTab{}
			}
			basicConfMap[AllActualPointKey] = append(basicConfMap[AllActualPointKey].([]*site_serviceable_area_cep_range.LogisticSiteBasicServiceableCepRangeTab), data)
			allActualPointIDMap[tmp] = true
		}
	}

	// 排序，方便查找
	for key, cepRangeListInterface := range basicConfMap {
		cepRangeList := cepRangeListInterface.([]*site_serviceable_area_cep_range.LogisticSiteBasicServiceableCepRangeTab)
		sort.SliceStable(cepRangeList, func(i, j int) bool {
			return cepRangeList[i].CepInitial < cepRangeList[j].CepInitial
		})
		basicConfMap[key] = cepRangeList
	}
	return basicConfMap, nil
}

// pickup_group_id-special settings list的map
func (db *DumpManager) DumpPickupSpecialSettingsTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var pickupSpecialSettingsList []*model19.PickupConfSpecialSettingsTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &model19.PickupConfSpecialSettingsTab{}, &pickupSpecialSettingsList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	pickupSpecialSettingsMap := make(map[string]interface{})
	for _, pickupSpecialSetting := range pickupSpecialSettingsList {
		// 缓存中只存放status为1的pickup conf
		pickupConfIDKey := strconv.FormatUint(pickupSpecialSetting.PickupConfID, 10)
		if _, ok := pickupSpecialSettingsMap[pickupConfIDKey]; !ok {
			pickupSpecialSettingsMap[pickupConfIDKey] = []*model19.PickupConfSpecialSettingsTab{}
		}
		pickupSpecialSettingsMap[pickupConfIDKey] = append(pickupSpecialSettingsMap[pickupConfIDKey].([]*model19.PickupConfSpecialSettingsTab), pickupSpecialSetting)
	}
	return pickupSpecialSettingsMap, nil
}

// effective rule dump, laneCode -> effective rule map
func (db *DumpManager) DumpServiceableEffectiveRuleTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var effectiveRuleList []*effective_rule.ServiceableEffectiveRuleTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &effective_rule.ServiceableEffectiveRuleTab{}, &effectiveRuleList, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	effectiveRuleInfoMap := make(map[string]interface{})
	for _, effectiveRule := range effectiveRuleList {
		effectiveRuleInfoMap[effectiveRule.LaneCode] = effectiveRule
	}
	return effectiveRuleInfoMap, nil
}

func (db *DumpManager) DumpDeliveryInstructionBasicConfAndDetailTables(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var basicConfList []*delivery_conf.DeliveryInstructionBasicConfTab
	batchSize := config.GetDBDumpBatchSize(ctx)

	params := make(map[string]interface{})
	params["enable_status"] = delivery_constant.Enable
	basicConfError := common.SearchAllDataWithBatch(ctx, &delivery_conf.DeliveryInstructionBasicConfTab{}, &basicConfList, params, batchSize)
	if basicConfError != nil {
		return nil, errors.New(basicConfError.Msg)
	}

	var basicConfDetailList []*delivery_conf_detail.DeliveryInstructionBasicConfDetailTab
	detailConfErrorErr := common.GetAllDataBatch(ctx, &delivery_conf_detail.DeliveryInstructionBasicConfDetailTab{}, &basicConfDetailList, batchSize)
	if detailConfErrorErr != nil {
		logger.CtxLogErrorf(ctx, "get basic conf detail data from DB fail, err:%v", detailConfErrorErr)
		return nil, errors.New("fail to get data from database")
	}

	//Checkout
	//region + scenario ->  []*delivery_conf.ProductIdOptionConf
	//productId + scenario ->  []*delivery_conf.DeliveryCategory

	//AfterShipment
	//lineId + scenario ->  []*delivery_conf.DeliveryCategory
	optionListMap := make(map[string]interface{})

	//key id -> value basicConfMap
	basicConfMap := make(map[uint64]*delivery_conf.DeliveryInstructionBasicConfTab)
	for _, basicConf := range basicConfList {
		basicConfMap[basicConf.Id] = basicConf
	}

	for _, detailConf := range basicConfDetailList {
		basicConf, ok := basicConfMap[detailConf.RuleId]
		if !ok {
			logger.CtxLogErrorf(ctx, "basic detail conf didn't get basic conf, detail rule_id:[%d]", detailConf.RuleId)
			continue
		}
		if basicConf.IsCheckoutPage() {
			db.dumpCheckoutPage(basicConf, detailConf, optionListMap)
		}
		if basicConf.IsAfterShipment() {
			db.dumpAfterShipment(basicConf, detailConf, optionListMap)
		}
	}
	return optionListMap, nil
}

func (db *DumpManager) dumpCheckoutPage(basicConf *delivery_conf.DeliveryInstructionBasicConfTab, detailConf *delivery_conf_detail.DeliveryInstructionBasicConfDetailTab, optionListMap map[string]interface{}) {
	productIDs := strings.Split(detailConf.ResourceID, ",") //product_ids

	for _, productId := range productIDs {
		productIdCacheKey := delivery_conf.GenDeliveryInstructionCacheKey(delivery_constant.Product, productId, uint32(basicConf.Scenario))
		regionCacheKey := delivery_conf.GenDeliveryInstructionCacheKey(delivery_constant.Region, basicConf.Region, uint32(basicConf.Scenario))
		option := delivery_conf.Option{
			OptionNameEnum: detailConf.AppliedOption,
		}

		deliveryCategory := &delivery_conf.DeliveryCategory{
			CategoryNameEnum: basicConf.Category,
			Options:          []delivery_conf.Option{option},
		}

		productIdOptionConf := &delivery_conf.ProductIdOptionConf{
			ProductId: productId,
			Categorys: []*delivery_conf.DeliveryCategory{deliveryCategory},
		}

		if productIdCacheValue, ok := optionListMap[productIdCacheKey]; !ok {
			//没有这个 productId 新建
			optionListMap[productIdCacheKey] = []*delivery_conf.DeliveryCategory{deliveryCategory}
			//region key 单独不可拆出来 如果一个product下有两个option,还需遍历region value 是否有该product
			if _, ok := optionListMap[regionCacheKey]; !ok {
				//没有这个 region 新建
				optionListMap[regionCacheKey] = []*delivery_conf.ProductIdOptionConf{productIdOptionConf}
			} else {
				//region下 添加这个pid
				optionListMap[regionCacheKey] = append(optionListMap[regionCacheKey].([]*delivery_conf.ProductIdOptionConf), productIdOptionConf)
			}

		} else {
			hasThisCateory := false
			//理论上checkout 每一条productId只会配置一个category 只会配置一个option
			for _, category := range productIdCacheValue.([]*delivery_conf.DeliveryCategory) {
				if category.CategoryNameEnum == basicConf.Category {
					//有这个category 新加option  checkout一般不会进入这里
					category.Options = append(category.Options, option)
					hasThisCateory = true
					break
				}
			}
			if !hasThisCateory {
				//无此category 一般也不会进入
				optionListMap[productIdCacheKey] = append(optionListMap[productIdCacheKey].([]*delivery_conf.DeliveryCategory), deliveryCategory)
			}
		}
	}
}

func (db *DumpManager) dumpAfterShipment(basicConf *delivery_conf.DeliveryInstructionBasicConfTab, detailConf *delivery_conf_detail.DeliveryInstructionBasicConfDetailTab, optionListMap map[string]interface{}) {
	lineIDs := strings.Split(detailConf.ResourceID, ",") //line_ids

	availableSloStatus := strings.Split(detailConf.AvailableSloStatus, ",")
	availableSloStatusMap := make(map[string]bool)
	for _, sloStatus := range availableSloStatus {
		availableSloStatusMap[sloStatus] = true
	}

	availableFCodes := strings.Split(detailConf.AvailableFCodes, ",")
	availableFCodesMap := make(map[string]bool)
	for _, availableFCode := range availableFCodes {
		availableFCodesMap[availableFCode] = true
	}

	for _, lineId := range lineIDs {
		lineIdCacheKey := delivery_conf.GenDeliveryInstructionCacheKey(delivery_constant.Line, lineId, uint32(basicConf.Scenario))
		option := delivery_conf.Option{
			OptionNameEnum: detailConf.AppliedOption,
			OTPFlag:        detailConf.OTPFlag,
			CODFlag:        detailConf.CODFlag,
			ExceedEddDays:  detailConf.ExceedEddDays,
			BeforeEddDays:  detailConf.BeforeEddDays,
		}

		hasThisCateory := false
		deliveryCategory := &delivery_conf.DeliveryCategory{
			SloStatusArray:   availableSloStatusMap,
			WhitelistToggle:  basicConf.WhitelistToggle,
			CategoryNameEnum: basicConf.Category,
			Options:          []delivery_conf.Option{option},
			FCodeArray:       availableFCodesMap,
		}

		if cacheValue, ok := optionListMap[lineIdCacheKey]; !ok {
			//没有这个 cacheKey 新建
			optionListMap[lineIdCacheKey] = []*delivery_conf.DeliveryCategory{deliveryCategory}
		} else {
			for _, category := range cacheValue.([]*delivery_conf.DeliveryCategory) {
				if category.CategoryNameEnum == basicConf.Category {
					//有这个category 新加option
					category.Options = append(category.Options, option)
					hasThisCateory = true
					break
				}
			}
			if !hasThisCateory {
				//无此category
				optionListMap[lineIdCacheKey] = append(optionListMap[lineIdCacheKey].([]*delivery_conf.DeliveryCategory), deliveryCategory)
			}
		}
	}
}

// key: lineId + category + locationId  value: list DeliveryInstructionWhitelistLocationTab
func (db *DumpManager) DumpDeliveryInstructionWhitelistLocationTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var locationModels []*delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab{}, &locationModels, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	locationMap := make(map[string]interface{})
	for _, locationModel := range locationModels {
		key := delivery_instruction_whitelist_location.GenLineDeliveryInstructionLocationCacheKey(locationModel.LineId, locationModel.Category, locationModel.LocationId)
		if _, ok := locationMap[key]; !ok {
			locationMap[key] = locationModel
		} else {
			logger.CtxLogErrorf(ctx, "get two locationId whitelist conf, LineId:[%s], Category:[%d], LocationId:[%d]", locationModel.LineId, locationModel.Category, locationModel.LocationId)
		}
	}

	return locationMap, nil
}

// key: lineId + category  value: list DeliveryInstructionWhitelistCepRangeTab
func (db *DumpManager) DumpDeliveryInstructionWhitelistCepRangeTable(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var cepRangeModels []*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab{}, &cepRangeModels, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	cepRangeMap := make(map[string]interface{})
	for _, cepRangeModel := range cepRangeModels {
		key := delivery_instruction_whitelist_cep_range.GenLineDeliveryInstructionCepRangeCacheKey(cepRangeModel.LineId, cepRangeModel.Category)
		if v, ok := cepRangeMap[key]; ok {
			cepRangeMap[key] = append(v.([]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab), cepRangeModel)
		} else {
			cepRangeMap[key] = []*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab{cepRangeModel}
		}
	}

	//sort before return
	for key := range cepRangeMap {
		sort.SliceStable(cepRangeMap[key], func(i, j int) bool {
			cepRangeList := cepRangeMap[key].([]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab)
			return cepRangeList[i].Initial < cepRangeList[j].Initial
		})
	}

	return cepRangeMap, nil
}

func (db *DumpManager) DumpCdtManualManipulationRouteLocationData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())

	dumpConf := config.GetMutableConf(ctx).CdtDeltaCacheDumpConfig
	// 基于服务名控制是否需要加载全量配置数据
	// time-searchgrpc跟time-grpc读取同一套apollo配置，通过配置服务名“time-grpc,time-fulfillmentgrpc,time-searchgrpc”保持加载全量数据，后续用于仿真流程
	// 仿真流程切换到time-searchgrpc后，配置修改为“time-searchgrpc”，time-grpc和time-fulfillmentgrpc仅加载当前生效数据
	if !dumpConf.NeedToDumpAll(env.GetServiceName()) {
		// 按市场仅加载当前生效的修正规则数据
		return db.dumpActiveCdtManualManipulationRouteLocationData(ctx, dumpConf.GetDumpRegion())
	}

	var dataList []*manual_manipulation_rule.CDTManualManipulationRouteLocationRuleTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	if err := common.GetAllDataBatch(ctx, &manual_manipulation_rule.CDTManualManipulationRouteLocationRuleTab{}, &dataList, batchSize); err != nil {
		return nil, errors.New(err.Msg)
	}

	dataMap := make(map[string]interface{}, len(dataList))
	for _, data := range dataList {
		key := data.GenCacheKey()

		obj, ok := dataMap[key]
		if !ok {
			obj = make([]*manual_manipulation_rule.CDTManualManipulationRouteLocationRuleTab, 0)
		}
		subList := obj.([]*manual_manipulation_rule.CDTManualManipulationRouteLocationRuleTab)
		subList = append(subList, data)

		dataMap[key] = subList
	}
	return dataMap, nil
}

func (db *DumpManager) dumpActiveCdtManualManipulationRouteLocationData(ctx utils.LCOSContext, dumpRegion []string) (map[string]interface{}, error) {
	batchSize := config.GetDBDumpBatchSize(ctx)

	// 1. 获取所有可能需要加载的修正规则record列表
	var recordList []*manual_manipulation_record.CDTManualManipulationRuleRecordTab
	if err := common.SearchAllDataGeneral(ctx, &manual_manipulation_record.CDTManualManipulationRuleRecordTab{}, &recordList, map[string]interface{}{"region in": dumpRegion}, false); err != nil {
		return nil, errors.New(err.Msg)
	}

	// 2. 依次加载每个record下的route数据
	dataMap := make(map[string]interface{})
	nowTime := utils.GetTimestamp(ctx)
	for _, record := range recordList {
		if record.IsDisabled(nowTime) {
			// 仅加载upcoming和active状态的修正数据
			continue
		}

		// 分批获取record下的所有route修正数据
		var dataList []*manual_manipulation_rule.CDTManualManipulationRouteLocationRuleTab
		if err := common.SearchAllDataWithBatch(ctx, &manual_manipulation_rule.CDTManualManipulationRouteLocationRuleTab{}, &dataList, map[string]interface{}{"record_id": record.ID}, batchSize); err != nil {
			return nil, errors.New(err.Msg)
		}
		for _, data := range dataList {
			key := data.GenCacheKey()

			obj, ok := dataMap[key]
			if !ok {
				obj = make([]*manual_manipulation_rule.CDTManualManipulationRouteLocationRuleTab, 0)
			}
			subList := obj.([]*manual_manipulation_rule.CDTManualManipulationRouteLocationRuleTab)
			subList = append(subList, data)

			dataMap[key] = subList
		}
	}
	return dataMap, nil
}

func (db *DumpManager) DumpCdtManualManipulationRoutePostcodeData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())

	var dataList []*manual_manipulation_rule.CDTManualManipulationRouteZipcodeRuleTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	if err := common.GetAllDataBatch(ctx, &manual_manipulation_rule.CDTManualManipulationRouteZipcodeRuleTab{}, &dataList, batchSize); err != nil {
		return nil, errors.New(err.Msg)
	}

	dataMap := make(map[string]interface{}, len(dataList))
	for _, data := range dataList {
		key := data.GenCacheKey()

		obj, ok := dataMap[key]
		if !ok {
			obj = make([]*manual_manipulation_rule.CDTManualManipulationRouteZipcodeRuleTab, 0)
		}
		subList := obj.([]*manual_manipulation_rule.CDTManualManipulationRouteZipcodeRuleTab)
		subList = append(subList, data)

		dataMap[key] = subList
	}
	return dataMap, nil
}

// product_id + origin_location_id -> deltas
func (db *DumpManager) DumpCdtManualManipulationRouteCepRangeData(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())

	var dataList []*manual_manipulation_rule.CDTManualManipulationRoutePostcodeRuleTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	if err := common.GetAllDataBatch(ctx, &manual_manipulation_rule.CDTManualManipulationRoutePostcodeRuleTab{}, &dataList, batchSize); err != nil {
		return nil, errors.New(err.Msg)
	}

	dataMap := make(map[string]interface{}, len(dataList))
	for _, data := range dataList {
		key := data.GenCacheKey()
		obj, ok := dataMap[key]
		if !ok {
			obj = make([]*manual_manipulation_rule.CDTManualManipulationRoutePostcodeRuleTab, 0)
		}
		subList := obj.([]*manual_manipulation_rule.CDTManualManipulationRoutePostcodeRuleTab)
		subList = append(subList, data)
		dataMap[key] = subList
	}
	return dataMap, nil
}

func (db *DumpManager) DumpCdtAbTestRule(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var dataList []*cdt_ab_test.CdtAbTestRule
	if err := common.SearchAllDataGeneral(ctx, &cdt_ab_test.CdtAbTestRule{}, &dataList, map[string]interface{}{"rule_status": edd_constant.AbTestRuleStatusActive}, false); err != nil {
		return nil, errors.New(err.Msg)
	}
	dataMap := make(map[string]interface{}, len(dataList))
	for _, rule := range dataList {
		key := rule.GetCacheKey()
		if dupRule, ok := dataMap[key]; ok {
			// 正常情况下一个渠道只应该有一个正在生效的ab test规则，如果存在多个，则上报异常
			_ = monitor.AwesomeReportEvent(ctx, "DumpCacheReport", rule.TableName(), "duplicate_error", fmt.Sprintf("use rule:%+v\nduplicated rule:%+v", rule, dupRule))
		}
		dataMap[key] = rule
	}

	return dataMap, nil
}

// id -> *AggregateMaskedChannelCdtTab
func (db *DumpManager) DumpAggregateMaskedChannelRule(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var aggregateMaskedChannelRules []*aggregate_masked_channel_cdt.AggregateMaskedChannelCdtTab
	params := make(map[string]interface{})
	params["rule_status in"] = []int8{edd_constant.UpComing, edd_constant.Active}
	LCOSError := common.SearchAllDataGeneral(ctx, &aggregate_masked_channel_cdt.AggregateMaskedChannelCdtTab{}, &aggregateMaskedChannelRules, params, true)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	aggregateMaskedChannelRulesMap := make(map[string]interface{})
	for _, rule := range aggregateMaskedChannelRules {
		if aggregateMaskedChannelRulesMap[rule.Region] == nil {
			aggregateMaskedChannelRulesMap[rule.Region] = []*aggregate_masked_channel_cdt.AggregateMaskedChannelCdtTab{}
		}
		aggregateMaskedChannelRulesMap[rule.Region] = append(aggregateMaskedChannelRulesMap[rule.Region].([]*aggregate_masked_channel_cdt.AggregateMaskedChannelCdtTab), rule)
	}
	return aggregateMaskedChannelRulesMap, nil
}

// product_id -> []*AutomatedVolumeGenerationRuleTab
func (db *DumpManager) DumpAutomatedVolumeGenerationRule(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var automatedVolumeRules []*automated_volume.AutomatedVolumeGenerationRuleTab
	params := make(map[string]interface{})
	params["rule_status"] = edd_constant.Active
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.SearchAllDataWithBatch(ctx, &automated_volume.AutomatedVolumeGenerationRuleTab{}, &automatedVolumeRules, params, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	automatedVolumeRuleMap := make(map[string]interface{})

	for _, rule := range automatedVolumeRules {
		if automatedVolumeRuleMap[rule.ProductID] == nil {
			automatedVolumeRuleMap[rule.ProductID] = []*automated_volume.AutomatedVolumeGenerationRuleTab{}
		}
		automatedVolumeRuleMap[rule.ProductID] = append(automatedVolumeRuleMap[rule.ProductID].([]*automated_volume.AutomatedVolumeGenerationRuleTab), rule)
	}
	return automatedVolumeRuleMap, nil
}

// rule_id -> []*AutomatedVolumeGenerationDataVersionTab
func (db *DumpManager) DumpAutomatedVolumeGenerationDataVersion(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var automatedVolumeVersions []*automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, &automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab{}, &automatedVolumeVersions, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}
	automatedVolumeVersionMapTemp := make(map[string]interface{})

	//一个id对应多个version记录
	for _, ref := range automatedVolumeVersions {
		// 提前过滤
		if ref.IsSuccess != constant.TRUE {
			continue
		}
		if automatedVolumeVersionMapTemp[strconv.Itoa(int(ref.AutomatedVolumeRuleID))] == nil {
			automatedVolumeVersionMapTemp[strconv.Itoa(int(ref.AutomatedVolumeRuleID))] = []*automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab{}
		}
		automatedVolumeVersionMapTemp[strconv.Itoa(int(ref.AutomatedVolumeRuleID))] = append(automatedVolumeVersionMapTemp[strconv.Itoa(int(ref.AutomatedVolumeRuleID))].([]*automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab), ref)
	}
	automatedVolumeVersionMap := make(map[string]interface{}, len(automatedVolumeVersionMapTemp))
	for k := range automatedVolumeVersionMapTemp {
		// 提前排序
		tabs, ok := automatedVolumeVersionMapTemp[k].([]*automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab)
		if !ok {
			continue
		}
		sort.Slice(tabs, func(i, j int) bool {
			return tabs[i].ID > tabs[j].ID
		})
		automatedVolumeVersionMap[k] = tabs
	}
	return automatedVolumeVersionMap, nil
}

// DumpLogisticStation
// StationId -> *LogisticStationInfoTab
func (dp *DumpManager) DumpLogisticStation(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	// if !startup.IsBranchProject() && config.GetMutableConf(context.Background()).DisableBranchLocalCache { // 因为station部分逻辑在lcos-task执行，本地缓存启动暂时不跟随branch配置禁用
	// 	return make(map[string]interface{}, 0), nil
	// }
	// 只加载部署branch station业务的市场
	cid := env.GetCID()
	if _, ok := constant.BRANCH_DEPLOY_REGIONS_MAP[cid]; !ok {
		logger.LogErrorf("dump branch local cache|not branch service deploy region")
		return make(map[string]interface{}, 0), nil
	}
	logger.LogErrorf("dump branch local cache|begin load branch local cache")
	ctx := utils.NewCommonCtx(context.Background())
	var dataList []*station_conf.LogisticStationInfoTab
	batchSize := config.GetDBDumpBranchBatchSize(ctx)
	result := map[string]interface{}{}
	for _, region := range constant.BRANCH_REGIONS { // 目前branch-grpc只部署sg，支持全市场服务，需要在启动时加载全市场数据缓存
		// get branch db with region
		db, lErr := ctx.ReadBranchDB(region)
		if lErr != nil {
			return nil, fmt.Errorf(lErr.Msg)
		}
		// get data
		LCOSError := common.GetAllDataBatchWithDB(db, &station_conf.LogisticStationInfoTab{}, &dataList, batchSize)
		if LCOSError != nil {
			return nil, errors.New(LCOSError.Msg)
		}
		// put into the cache map
		for _, v := range dataList {
			var cacheVal = &station_conf.StationCacheInfo{
				StationId:          v.StationId,
				LmDeliveryMode:     v.LmDeliveryMode,
				Support4PlLineList: v.Support4PlLineList,
				Region:             v.Region,
				Longitude:          v.Longitude,
				Latitude:           v.Latitude,
				DistanceThreshold:  v.DistanceThreshold,
				CheckoutStatus:     v.CheckoutStatus,
				CheckoutLimit:      v.CheckoutLimit,
			}
			result[station_conf.GenStationInfoCacheKey(v.StationId)] = cacheVal
		}
	}

	return result, nil
}

// DumpLogisticAllStationId
// "AllStationId" -> []uint64{110, 111, 112}
func (dp *DumpManager) DumpLogisticAllStationId(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	// if !startup.IsBranchProject() && config.GetMutableConf(context.Background()).DisableBranchLocalCache { // 因为station部分逻辑在lcos-task执行，本地缓存启动暂时不跟随branch配置禁用
	// 	return make(map[string]interface{}, 0), nil
	// }
	// 只加载部署branch station业务的市场
	cid := env.GetCID()
	if _, ok := constant.BRANCH_DEPLOY_REGIONS_MAP[cid]; !ok {
		return make(map[string]interface{}, 0), nil
	}
	ctx := utils.NewCommonCtx(context.Background())
	var dataList []*station_conf.LogisticStationInfoTab
	batchSize := config.GetDBDumpBranchBatchSize(ctx)
	result := map[string]interface{}{}
	allStationId := make([]uint64, 0)
	for _, region := range constant.BRANCH_REGIONS { // 目前branch-grpc只部署sg，支持全市场服务，需要在启动时加载全市场数据缓存
		// get branch db with region
		db, lErr := ctx.ReadBranchDB(region)
		if lErr != nil {
			return nil, fmt.Errorf(lErr.Msg)
		}
		// get data
		LCOSError := common.GetAllDataBatchWithDB(db, &station_conf.LogisticStationInfoTab{}, &dataList, batchSize)
		if LCOSError != nil {
			return nil, errors.New(LCOSError.Msg)
		}
		// put into the cache map
		for _, v := range dataList {
			allStationId = append(allStationId, v.StationId)
		}
	}
	result[station_conf.GenAllStationIdCacheKey()] = allStationId

	return result, nil
}

// product_id -> zone_name -> zone_polygon
func (db *DumpManager) DumpProductServiceableZone(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())

	var dataList []*product_serviceable_zone.LogisticProductServiceableZoneTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	if err := common.GetAllDataBatch(ctx, &product_serviceable_zone.LogisticProductServiceableZoneTab{}, &dataList, batchSize); err != nil {
		return nil, errors.New(err.Msg)
	}

	dataMap := make(map[string]interface{}, len(dataList))
	for _, data := range dataList {
		productKey := strconv.Itoa(data.ProductId)
		obj, ok := dataMap[productKey]
		if !ok {
			obj = make(map[string]*product_serviceable_zone.LogisticProductServiceableZoneTab)
		}
		productMap := obj.(map[string]*product_serviceable_zone.LogisticProductServiceableZoneTab)
		productMap[data.ZoneName] = data
		dataMap[productKey] = productMap
	}
	return dataMap, nil
}

// product_id -> zone_name -> cep_group ([]cep_range)
func (db *DumpManager) DumpProductServiceableCepGroup(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())

	var zoneList []*product_serviceable_zone.LogisticProductServiceableZoneTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	if err := common.GetAllDataBatch(ctx, &product_serviceable_zone.LogisticProductServiceableZoneTab{}, &zoneList, batchSize); err != nil {
		return nil, errors.New(err.Msg)
	}
	cityMap := make(map[string]string, len(zoneList))
	for _, zone := range zoneList {
		cityMap[utils.GenKey(":", zone.ZoneName, strconv.Itoa(zone.ProductId))] = zone.CityName
	}
	var dataList []*product_serviceable_zone.LogisticProductServiceableCepGroupTab
	if err := common.GetAllDataBatch(ctx, &product_serviceable_zone.LogisticProductServiceableCepGroupTab{}, &dataList, batchSize); err != nil {
		return nil, errors.New(err.Msg)
	}
	dataMap := make(map[string]interface{}, len(dataList))
	for _, data := range dataList {
		data.CityName = cityMap[utils.GenKey(":", data.ZoneName, strconv.Itoa(data.ProductId))] // 填充city name
		productKey := strconv.Itoa(data.ProductId)

		obj, ok := dataMap[productKey]
		if !ok {
			obj = make(map[string][]*product_serviceable_zone.LogisticProductServiceableCepGroupTab)
		}
		productMap := obj.(map[string][]*product_serviceable_zone.LogisticProductServiceableCepGroupTab)
		productMap[data.ZoneName] = append(productMap[data.ZoneName], data)

		dataMap[productKey] = productMap
	}
	return dataMap, nil
}

func (db *DumpManager) DumpShopServiceableZone(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())

	var dataList []*shop_serviceable_zone.LogisticShopServiceableZoneTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	if err := common.GetAllDataBatch(ctx, &shop_serviceable_zone.LogisticShopServiceableZoneTab{}, &dataList, batchSize); err != nil {
		return nil, errors.New(err.Msg)
	}

	dataMap := make(map[string]interface{}, len(dataList))
	for _, data := range dataList {
		key := data.GenCacheKey()

		obj, ok := dataMap[key]
		if !ok {
			obj = make([]string, 0)
		}
		enabledZoneList := obj.([]string)
		enabledZoneList = append(enabledZoneList, data.ZoneName)

		dataMap[key] = enabledZoneList
	}
	return dataMap, nil
}

func (db *DumpManager) DumpEFenceMesh(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	monCtx := monitor.AwesomeReportTransactionStart(ctx)

	var m1 runtime.MemStats
	// 获取内存统计信息
	runtime.ReadMemStats(&m1)

	eFencePolygonDao := polygon.NewEFencePolygonDao()

	// 最终返回的是一个 map[string]ZoneMeshInfoList ，key是region+geohash，使用mesh.EFenceMeshKey() 来构造
	eFenceMeshMap := make(map[string]interface{})

	// 从配置读取可用的市场
	regionList := config.GetMutableConf(ctx).EFenceConfig.GetSupportRegionList()
	queryMap := make(map[string]interface{})
	queryMap["handle_status"] = eFenceConstant.ZoneHandleStatusInUsage

	// 逐个市场加载到map中
	var totalDataCount int
	for _, region := range regionList {
		// 从zone表读取在生效的zone_id和version
		zoneInfoList, lcosErr := eFencePolygonDao.ListAllEFencePolygon(ctx, region, queryMap)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "query zone info failed|region[%s]|err[%s]", region, lcosErr.Msg)
			monitor.AwesomeReportTransactionEnd(monCtx, constant.CatModuleEFenceCache, constant.EventNameEFenceCacheMeshDump, constant.StatusError, lcosErr.Msg) // 上报成功率

			return nil, errors.New(lcosErr.Msg) // 如果任何数据有问题，整个cache都不刷新
		}

		var regionDataCount int // 用于日志和上报各个region和zone的数据量
		var err error
		dumpFullTabRegionList := config.GetMutableConf(ctx).EFenceConfig.GetMeshDumpFullTabRegionList()
		if slice.Contains(dumpFullTabRegionList, strings.ToUpper(region)) {
			// 逐批将全表数据加载
			regionDataCount, err = dumpMeshByFullTab(ctx, zoneInfoList, region, eFenceMeshMap)
		} else {
			// 每次加载一个zone_id+version的数据，写入regionDataMap
			regionDataCount, err = dumpMeshByZoneInfo(ctx, zoneInfoList, region, eFenceMeshMap)
		}
		if err != nil {
			monitor.AwesomeReportTransactionEnd(monCtx, constant.CatModuleEFenceCache, constant.EventNameEFenceCacheMeshDump, constant.StatusError, err.Error()) // 上报成功率
			return nil, errors.New(lcosErr.Msg)                                                                                                                  // 如果任何数据有问题，整个cache都不刷新
		}

		// 上报region维度数据量
		logger.CtxLogInfof(ctx, "e_fence data count region|region[%s]|count[%d]", region, regionDataCount)
		totalDataCount += regionDataCount
	}

	// 上报整个电子围栏数据量
	logger.CtxLogInfof(ctx, "e_fence data count total|count[%d]", totalDataCount)
	reportSuccMap := map[string]string{}
	_ = metrics.SummaryObserve(constant.MetricsEFenceCacheMeshSuccessCount, float64(totalDataCount), reportSuccMap)
	monitor.AwesomeReportTransactionEnd(monCtx, constant.CatModuleEFenceCache, constant.EventNameEFenceCacheMeshDump, constant.StatusSuccess, "") // 上报成功率

	var m2 runtime.MemStats
	// 获取内存统计信息
	runtime.ReadMemStats(&m2)
	logger.CtxLogInfof(ctx, "memory: total incr[%.2f GB]|heap incr[%.2f GB]", utils.CalcMemoryDelta(m1.TotalAlloc, m2.TotalAlloc), utils.CalcMemoryDelta(m1.HeapAlloc, m2.HeapAlloc))

	return eFenceMeshMap, nil
}

func dumpMeshByFullTab(ctx *utils.CommonContext, zoneInfoList []*polygon.EFencePolygonTab, region string, meshMap map[string]interface{}) (int, error) {
	eFenceMeshDao := mesh.NewEFenceMeshDao()
	zoneInfoMap := make(map[string]int)
	for _, zone := range zoneInfoList {
		// map中的key表示有效zone，value为该zone的mesh数量
		zoneInfoMap[mesh.EFenceZoneKey(zone.ZoneId, zone.DataVersion, zone.LayerId)] = 0
	}
	logger.CtxLogInfof(ctx, "In use zone count[%d]|region[%s]", len(zoneInfoMap), region)
	regionDataCount, sysErr := eFenceMeshDao.LoadAllEFenceMesh(ctx, region, zoneInfoMap, meshMap)
	if sysErr != nil {
		logger.CtxLogErrorf(ctx, "list mesh data by whole tab failed|region[%s]|err[%s]", region, sysErr.Msg)
		return regionDataCount, errors.New(sysErr.Msg)
	}
	// 上报zone维度数据量
	for zoneKey, meshCount := range zoneInfoMap {
		logger.CtxLogInfof(ctx, "e_fence mesh count per zone|region[%s]|zone_key[%s]|count[%d]", region, zoneKey, meshCount)
		reportMap := map[string]string{"region": region, "zone_key": zoneKey}
		_ = metrics.SummaryObserve(constant.MetricsEFenceCacheMeshCountByFullTab, float64(meshCount), reportMap)
	}

	return regionDataCount, nil
}

func dumpMeshByZoneInfo(ctx *utils.CommonContext, zoneInfoList []*polygon.EFencePolygonTab, region string, meshMap map[string]interface{}) (int, error) {
	var regionDataCount int
	eFenceMeshDao := mesh.NewEFenceMeshDao()
	for i, zoneInfo := range zoneInfoList {
		zoneDataCount, sysErr := eFenceMeshDao.LoadEFenceMeshByZoneInfo(ctx, region, zoneInfo.ZoneId, zoneInfo.DataVersion, zoneInfo.LayerId, meshMap)
		if sysErr != nil {
			logger.CtxLogErrorf(ctx, "list mesh data failed|region[%s]|zone_id[%s]|version[%s]|err[%s]", region, zoneInfo.ZoneId, zoneInfo.DataVersion, sysErr.Msg)
			return regionDataCount, errors.New(sysErr.Msg)
		}

		// 上报zone维度数据量
		regionDataCount += zoneDataCount
		logger.CtxLogInfof(ctx, "[%d]e_fence data count zone|region[%s]|zone_id[%s]|version[%s]|count[%d]|total by now[%d]", i, region, zoneInfo.ZoneId, zoneInfo.DataVersion, zoneDataCount, regionDataCount)
		reportMap := map[string]string{"region": region, "zone_id": zoneInfo.ZoneId}
		_ = metrics.SummaryObserve(constant.MetricsEFenceCacheMeshCount, float64(zoneDataCount), reportMap)
	}

	return regionDataCount, nil
}

func (db *DumpManager) DumpEFencePolygon(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	eFencePolyMap := make(map[string]interface{})
	ctx := utils.NewCommonCtx(context.Background())
	// 从配置读取可用的市场
	regionList := config.GetMutableConf(ctx).EFenceConfig.GetSupportRegionList()
	queryMap := make(map[string]interface{})
	queryMap["handle_status"] = eFenceConstant.ZoneHandleStatusInUsage
	batchSize := 100

	for _, region := range regionList {
		var zoneInfoList []*polygon.EFencePolygonTab
		tableName := polygon.GetEFencePolygonTableName(region)

		// 分批查出来这个region的所有zone
		lcosErr := common.SearchTableAllDataWithBatch(ctx, tableName, &zoneInfoList, queryMap, batchSize)
		if lcosErr != nil {
			return nil, errors.New(lcosErr.Msg)
		}

		var count int
		for _, zoneInfo := range zoneInfoList {
			polyDetail, err := geopolygon.UnmarshalPoly([]byte(zoneInfo.Geometry))
			if err != nil {
				logger.CtxLogErrorf(ctx, "UnmarshalPoly fail|region[%s]|zoneId[%s]|version[%s]|layerId[%s]|err[%s]|geometry[%s]", region, zoneInfo.ZoneId, zoneInfo.DataVersion, zoneInfo.LayerId, err.Error(), zoneInfo.Geometry)
				return nil, err
			}

			cacheKey := polygon.EFencePolyKey(region, zoneInfo.ZoneId, zoneInfo.DataVersion, zoneInfo.LayerId)
			eFencePolyMap[cacheKey] = polyDetail
			count++
		}

		// 打印region维度数据量
		logger.CtxLogInfof(ctx, "e_fence poly count zone|region[%s]|count[%d]", region, count)
	}

	return eFencePolyMap, nil
}

func (db *DumpManager) DumpEFenceWhitelist(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	batch := config.GetDBDumpBatchSize(ctx)
	var allData []*location_whitelist.EFenceLocationWhitelistTab
	err := common.GetAllDataBatch(ctx, &location_whitelist.EFenceLocationWhitelistTab{}, &allData, batch)
	if err != nil {
		return nil, errors.New(err.Msg)
	}

	result := make(map[string]interface{})
	for _, v := range allData {
		key := location_whitelist.GetLocationWhitelistUniqKey(v.LocationId, v.LayerId)
		result[key] = v
	}

	return result, nil
}

func (db *DumpManager) DumpEFenceLineToggle(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	batch := config.GetDBDumpBatchSize(ctx)
	var allData []*line_toggle.EFenceLineToggleTab
	err := common.GetAllDataBatch(ctx, &line_toggle.EFenceLineToggleTab{}, &allData, batch)
	if err != nil {
		return nil, errors.New(err.Msg)
	}
	result := make(map[string]interface{})
	for _, v := range allData {
		key := line_toggle.GetLineToggleUniqKey(v.LineId)
		result[key] = v
	}

	return result, nil
}

func (db *DumpManager) DumpLogisticLaneInfo(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	dumpConf := config.GetMutableConf(ctx).LogisticLaneInfoCacheConfig
	if dumpConf.Disable {
		return map[string]interface{}{}, nil
	}

	// 加载所需市场的所有链路数据
	cacheMap := make(map[string]interface{})
	for _, region := range dumpConf.GetDumpRegion() {
		// 请求LPS获取当前市场所有渠道下的所有lane code
		productLaneCodeLists, err := product_service.ListLaneCodesByRegion(ctx, region)
		if err != nil {
			return nil, errors.New(err.Msg)
		}
		regionLaneCodeList := make([]string, 0)
		for _, productLaneCodeList := range productLaneCodeLists {
			regionLaneCodeList = append(regionLaneCodeList, productLaneCodeList.LaneCodes...)
		}

		// 分批请求LFS获取链路信息
		laneInfoMap, err := lfs_service.NewLFSService(ctx, region).BatchQueryLaneInfoWithPaging(ctx, regionLaneCodeList, dumpConf.GetDumpBatchSize())
		if err != nil {
			return nil, errors.New(err.Msg)
		}
		for laneCode, laneInfo := range laneInfoMap {
			cacheMap[laneCode] = laneInfo
		}
	}
	return cacheMap, nil
}

func (db *DumpManager) DumpActualPointInfo(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	dumpConf := config.GetMutableConf(ctx).ActualPointInfoCacheConfig
	if dumpConf.Disable {
		return map[string]interface{}{}, nil
	}

	// 加载全市场的所有实际点数据
	actualPointIdList, err := lls_service.GetAllActualPointId(ctx)
	if err != nil {
		return nil, errors.New(err.Msg)
	}
	actualPointInfoMap, err := lls_service.GetActualPointsMapWithPaging(ctx, actualPointIdList, dumpConf.GetDumpBatchSize())
	if err != nil {
		return nil, errors.New(err.Msg)
	}
	cacheMap := make(map[string]interface{}, len(actualPointInfoMap))
	for actualPointId, actualPointInfo := range actualPointInfoMap {
		cacheMap[actualPointId] = actualPointInfo
	}
	return cacheMap, nil
}

func (db *DumpManager) DumpLogisticProductInfo(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	dumpConf := config.GetMutableConf(ctx).LogisticProductInfoCacheConfig
	if dumpConf.Disable {
		return map[string]interface{}{}, nil
	}

	cacheMap := make(map[string]interface{})
	for _, region := range dumpConf.GetDumpRegion() {
		region = strings.ToUpper(region)
		channelInfoList, err := product_service.GetAllChannelsInfoByRegion(ctx, region)
		if err != nil {
			return nil, errors.New(err.Msg)
		}

		for _, channelInfo := range channelInfoList {
			cacheMap[strconv.Itoa(channelInfo.ChannelId)] = &product_service.ProductInfo{
				ProductId:   channelInfo.ChannelId,
				Name:        channelInfo.Name,
				FromCountry: channelInfo.FromCountry,
				Country:     channelInfo.Country,
			}
		}
	}
	return cacheMap, nil
}

func (db *DumpManager) DumpProductEDTConfigTab(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	ctx := utils.NewCommonCtx(context.Background())
	var productEdtConfigs []*edt_config.ProductEdtConfigTab
	batchSize := config.GetDBDumpBatchSize(ctx)
	LCOSError := common.GetAllDataBatch(ctx, edt_config.ProductEdtConfigTabHook, &productEdtConfigs, batchSize)
	if LCOSError != nil {
		return nil, errors.New(LCOSError.Msg)
	}

	ret := make(map[string]interface{}, len(productEdtConfigs))
	for _, productEdtConfig := range productEdtConfigs {
		key := fmt.Sprintf("%s-%d", productEdtConfig.Region, productEdtConfig.ProductId)
		ret[key] = productEdtConfig
	}
	return ret, nil
}

// DumpLogisticLinePredefinedRoute LineId:GroupId => FromAreaId:ToAreaId => route
func (db *DumpManager) DumpLogisticLinePredefinedRoute(dumpParam map[string]interface{}) (map[string]interface{}, error) {
	var (
		ctx       = utils.NewCommonCtx(context.Background())
		batchSize = config.GetDBDumpBatchSize(ctx)
		dataList  []*predefined_route_model.LogisticLinePredefinedRouteTab
		dataMap   = make(map[string]interface{})
	)

	if err := common.GetAllDataBatch(ctx, predefined_route_model.LogisticLinePredefinedRouteTabHook, &dataList, batchSize); err != nil {
		return nil, errors.New(err.Msg)
	}
	for _, data := range dataList {
		route := data.ToLite()

		groupKey := route.GenGroupKey()
		obj, ok := dataMap[groupKey]
		if !ok {
			obj = make(map[string]*predefined_route_model.LogisticLinePredefinedRouteTabLite)
		}
		routeMap := obj.(map[string]*predefined_route_model.LogisticLinePredefinedRouteTabLite)
		routeMap[route.GenRouteKey()] = route
		dataMap[groupKey] = routeMap
	}
	return dataMap, nil
}
