package cache

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	lls_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"strings"
	"time"

	scorm "git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/geopolygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/data_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf_detail"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/postal_code_to_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/station_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tw_store"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/redislib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	model11 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	model15 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/holiday"
	model17 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_pickup_config"
	model18 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_timeslot"
	model19 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
	model20 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	model16 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/recurring_holiday"
	model14 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/timeslot"
	model5 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	operation_route_model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	model4 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	model2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	model8 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	model9 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	model13 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	model12 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	model3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	model10 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	model6 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	model7 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	effective_rule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	site_serviceable_area_basic_conf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	site_serviceable_area_cep_range "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	site_serviceable_area_location "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	site_serviceable_area_postcode "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_holiday"
	tpl_id_line_id_ref2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
)

func StartUp(conf *config.Config) error {
	redisConf := conf.RedisConf
	if config.NeedLatamConfig("") {
		redisConf = conf.LatamRedisConf
	}
	logger.LogErrorf("init config: StartUp, redisConf value is:%v", redisConf)

	if err := redislib.InitRedisCache(redisConf); err != nil {
		return err
	}
	if err := InitLocalCache(); err != nil {
		return err
	}
	start := time.Now() // nolint
	if err := localcache.LoadLocalCache(); err != nil {
		return err
	}
	logger.LogInfof("localcache init, time cost:%v", time.Since(start))
	return nil
}

func InitRefreshCache(conf *config.Config) error {
	redisConf := conf.RedisConf
	if config.NeedLatamConfig("") {
		redisConf = conf.LatamRedisConf
	}
	if err := redislib.InitRedisCache(redisConf); err != nil {
		return err
	}

	if err := dbhelper.SetLogisticsCoreServiceConnection(conf.DBLogisticCoreService); err != nil {
		return err
	} else {
		InitDBCallbackRefreshCache(dbhelper.LCOSRWConnection.Write())
	}
	return nil
}

func InitAndLoadLocalCache() error {
	if err := InitLocalCache(); err != nil {
		return err
	}
	start := time.Now() // nolint
	if err := localcache.LoadLocalCache(); err != nil {
		return err
	}
	logger.LogInfof("localcache init, time cost:%v", time.Since(start))
	return nil
}

func InitLocalCache() error {
	dumpManager := DumpManager{}
	logger.LogInfo("InitLocalCache start.")
	cacheLoadStartTime := time.Now() // nolint
	defer func() {
		logger.LogInfof("InitLocalCache done, duration=%.3fs", time.Since(cacheLoadStartTime).Seconds())
	}()
	commonLocalcacheList := make([]*localcache.Conf, 0)
	lcosLocalcacheList := make([]*localcache.Conf, 0)
	timeLocalcacheList := make([]*localcache.Conf, 0)
	branchLocalcacheList := make([]*localcache.Conf, 0)
	stationLocalcacheList := make([]*localcache.Conf, 0)
	onlyCheckoutLocalcacheList := make([]*localcache.Conf, 0)

	// 步骤1：加载公共的localcache
	{
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.PickupGroupTabNamespace, dumpManager.DumpPickupGroupTable, nil, &model20.PickupGroupTab{}, 0))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.PickupGroupLineIDRefTabNamespace, dumpManager.DumpPickupGroupLineIDRefTable, nil, &model20.PickupGroupLineIDRefTab{}, 0))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.PickupConfTabNamespace, dumpManager.DumpPickupConfTable, nil, &model19.PickupConfTab{}, 0))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.PickupTimeslotTabNamespace, dumpManager.DumpTimeslotTable, nil, &model14.PickupTimeslotTab{}, 0))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.HolidayTabNamespace, dumpManager.DumpHolidayTable, nil, &model15.LogisticHolidayTab{}, 0))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.RecurringHolidayTabNamespace, dumpManager.DumpWeekendsTable, nil, &model16.RecurringHolidayTab{}, 0))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.OpenLogisticPickupConfTabNamespace, dumpManager.DumpOpenLogisticPickupConfTable, nil, &model17.OpenLogisticPickupConfTab{}, 0))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.OpenLogisticPickupTimeslotTabNamespace, dumpManager.DumpOpenLogisticPickupTimeslotTable, nil, &model18.OpenLogisticPickupTimeslotTab{}, 0))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.TPLIDLineIDTabNamespace, dumpManager.DumpTPLIDLineIDRefTable, nil, &tpl_id_line_id_ref2.TPLLineIDRefTab{}, 0))
		// pickup conf special settings
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.PickupConfSpecialSettingsTabNamespace, dumpManager.DumpPickupSpecialSettingsTable, nil, &model19.PickupConfSpecialSettingsTab{}, 30*time.Minute))

		// period task
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.SlsHolidayNamespace, dumpManager.DumpSlsHoliday, nil, &sls_holiday.HolidayTab{}, 30*time.Minute))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.SlsRecurringHolidayNamespace, dumpManager.DumpSlsRecurringHoliday, nil, &sls_holiday.RecurringHolidayTab{}, 30*time.Minute))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.LPSHolidayNamespace, dumpManager.DumpLPSHoliday, nil, &lps_holiday.ProductHolidayTab{}, 30*time.Minute))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.LPSRecurringHolidayNamespace, dumpManager.DumpLPSRecurringHoliday, nil, &lps_holiday.ProductRecurringHolidayTab{}, 30*time.Minute))
		commonLocalcacheList = append(commonLocalcacheList, localcache.NewConf(constant.TWWeightSizeNamespace, dumpManager.DumpLogisticTWSizeInfo, nil, &model11.LogisticSizeTab{}, 30*time.Minute))
	}

	// 步骤2：加载lcos使用的localcache
	{
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LinePackageLimitNamespace, dumpManager.DumpLinePackageLimitTable, nil, &model.LinePackageLimitTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.ProductPackageLimitNamespace, dumpManager.DumpProductPackageLimitTable, nil, &model11.ProductPackageLimitTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineBasicServiceableConfNamespace, dumpManager.DumpBasicServiceableConfTable, nil, &model2.LineBasicServiceableConfTab{}, 0))

		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineScenarioConfNamespace, dumpManager.DumpScenarioConfTable, nil, &model3.LineCommonServiceableScenarioConfTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineAbilityConfNamespace, dumpManager.DumpAbilityConfTable, nil, &model2.LineCollectDeliverAbilityTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineAreaLocationRefTabNamespace, dumpManager.DumpAreaLocationTable, nil, &model5.LogisticLineServiceableAreaLocationRefTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineOperationOriginLocationTabNamespace, dumpManager.DumpOperationOriginLocationTable, nil, &model6.LogisticLineOperationServiceableOriginLocationTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineOperationDestLocationTabNamespace, dumpManager.DumpOperationDestLocationTable, nil, &model6.LogisticLineOperationServiceableDestLocationTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineOperationOriginPostcodeTabNamespace, dumpManager.DumpOperationOriginPostcodeTable, nil, &model7.LogisticLineOperationServiceableOriginPostcodeTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineOperationDestPostcodeTabNamespace, dumpManager.DumpOperationDestPostcodeTable, nil, &model7.LogisticLineOperationServiceableDestPostcodeTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineOperationServiceableConfNamespace, dumpManager.DumpOperationConfTable, nil, &model10.LogisticLineOperationServiceableConfTab{}, 0))

		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineBasicServiceableGroupRefNamespace, dumpManager.DumpBasicServiceableGroupRefTable, nil, &model13.LineBasicServiceableGroupRefTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineCollectDeliverGroupNamespace, dumpManager.DumpCollectDeliverGroupTable, nil, &model12.LineCollectDeliverGroupConfTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineSupportCollectDeliverGroupNamespace, dumpManager.DumpSupportCollectDeliverGroupTable, nil, ([]string)(nil), 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineRouteTabNamespace2, dumpManager.DumpRouteTable2, nil, &model4.LineServiceableRouteTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineBlacklistRouteTabNamespace, dumpManager.DumpRouteBlacklistTable, nil, &model4.LineServiceableBlackListRouteTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineOperationRouteTabNamespace, dumpManager.DumpOperationRouteTable, nil, &operation_route_model.LineOperationRouteTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineOperationLocationTabNamespace, dumpManager.DumpOperationLocationTable, nil, &model6.LineOperationServiceableLocationTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineOperationPostcodeTabNamespace, dumpManager.DumpOperationPostcodeTable, nil, &model7.LineOperationServiceablePostcodeTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineBasicCepRangeTabNamespace, dumpManager.DumpLineBasicCepRangeTable, nil, &cep_range.LineBasicServiceableCepRangeTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineOperationCepRangeTabNamespace, dumpManager.DumpLineOperationCepRangeTable, nil, &cep_range.LineOperationServiceableCepRangeTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.SiteServiceableAreaBasicConfTabNamespace, dumpManager.DumpLogisticSiteServiceableAreaBasicConf, nil, &site_serviceable_area_basic_conf.LogisticSiteBasicServiceableConfTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.ServiceableAreaEffectiveRuleTabNamespace, dumpManager.DumpServiceableEffectiveRuleTable, nil, &effective_rule.ServiceableEffectiveRuleTab{}, 2*time.Minute))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.DeliveryInstructionBasicConfAndDetailNamespace, dumpManager.DumpDeliveryInstructionBasicConfAndDetailTables, nil, &delivery_conf_detail.DeliveryInstructionBasicConfDetailTab{}, 5*time.Minute))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.DeliveryInstructionWhitelistLocationNamespace, dumpManager.DumpDeliveryInstructionWhitelistLocationTable, nil, &delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.DeliveryInstructionWhitelistCepRangeNamespace, dumpManager.DumpDeliveryInstructionWhitelistCepRangeTable, nil, &delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LogisticProductServiceableZoneNamespace, dumpManager.DumpProductServiceableZone, nil, &product_serviceable_zone.LogisticProductServiceableZoneTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LogisticProductServiceableCepGroupNamespace, dumpManager.DumpProductServiceableCepGroup, nil, &product_serviceable_zone.LogisticProductServiceableCepGroupTab{}, 0))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LogisticShopServiceableZoneNamespace, dumpManager.DumpShopServiceableZone, nil, &shop_serviceable_zone.LogisticShopServiceableZoneTab{}, 0))

		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LogisticLaneInfoNamespace, dumpManager.DumpLogisticLaneInfo, nil, &lfs_service.LaneCodeStruct{}, config.GetMutableConf(context.Background()).LogisticLaneInfoCacheConfig.GetDumpInterval()))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.ActualPointInfoNamespace, dumpManager.DumpActualPointInfo, nil, &lls_protobuf.ActualPointInfo{}, config.GetMutableConf(context.Background()).ActualPointInfoCacheConfig.GetDumpInterval()))
		lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LogisticLinePredefinedRouteNamespace, dumpManager.DumpLogisticLinePredefinedRoute, nil, &predefined_route_model.LogisticLinePredefinedRouteTabLite{}, 0))

		for i := 0; i < constant.BASICDBTABLENUM; i++ {
			tableNameSuffix := fmt.Sprintf("%08d", i)
			dumpParam := map[string]interface{}{
				"index": i,
			}
			lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineBasicLocationTabNamespace+tableNameSuffix, dumpManager.DumpBasicLocationTable, dumpParam, &model8.LineBasicServiceableLocationTab{}, 0))
			lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.LineBasicPostcodeTabNamespace+tableNameSuffix, dumpManager.DumpBasicPostcodeTable, dumpParam, &model9.LineBasicServiceablePostcodeTab{}, 0))

			lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.SiteServiceableAreaCepRangeTabNamespace+tableNameSuffix, dumpManager.DumpLogisticSiteServiceableAreaCepRange, dumpParam, &site_serviceable_area_cep_range.LogisticSiteBasicServiceableCepRangeTab{}, 0))
			lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.SiteServiceableAreaLocationTabNamespace+tableNameSuffix, dumpManager.DumpLogisticSiteServiceableAreaLocation, dumpParam, &site_serviceable_area_location.LogisticSiteBasicServiceableLocationTab{}, 0))
			lcosLocalcacheList = append(lcosLocalcacheList, localcache.NewConf(constant.SiteServiceableAreaPostcodeTabNamespace+tableNameSuffix, dumpManager.DumpLogisticSiteServiceableAreaPostcode, dumpParam, &site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{}, 0))
		}
	}

	// 步骤3：加载time使用的localcache
	{
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTManualUpdatePostcodeDataTabNamespace, dumpManager.DumpCDTManualUpdatePostcodeData, nil, &manual_update_rule.CdtManualUpdatePostcodeDataTab{}, 0))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTManualUpdateRecordTabNamespace, dumpManager.DumpCDTManualUpdateRecordData, nil, &manual_update_rule.CdtManualUpdateRecordTab{}, 0))

		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTManualManipulationLocationRuleTabNamespace, dumpManager.DumpCDTManualManipulationLocationRuleData, nil, &manual_manipulation_rule.CDTManualManipulationLocationRuleTab{}, 0))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTManualManipulationPostcodeRuleTabNamespace, dumpManager.DumpCDTManualManipulationPostcodeRuleData, nil, &manual_manipulation_rule.CDTManualManipulationPostcodeRuleTab{}, 0))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTManualManipulationZipcodeRuleTabNamespace, dumpManager.DumpCDTManualManipulationZipcodeRuleData, nil, &manual_manipulation_rule.CDTManualManipulationZipcodeRuleTab{}, 0))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTManualManipulationRuleRecordTabNamespace, dumpManager.DumpCDTManualManipulationRuleRecordData, nil, &manual_manipulation_record.CDTManualManipulationRuleRecordTab{}, 0))

		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTAutoUpdateRuleTabNamespace, dumpManager.DumpAutoUpdateRuleData, nil, &auto_update_rule.CDTAutoUpdateRuleTab{}, 0))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTAutoUpdateCalculateDataVersionTabNamespace, dumpManager.DumpAutoUpdateCalculateDataVersion, nil, &auto_update_data.CDTAutoUpdateCalculateDataVersionTab{}, 0))

		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTManualManipulationRouteLocationRuleTabNamespace, dumpManager.DumpCdtManualManipulationRouteLocationData, nil, &manual_manipulation_rule.CDTManualManipulationRouteLocationRuleTab{}, 0))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTManualManipulationRouteZipcodeRuleTabNamespace, dumpManager.DumpCdtManualManipulationRoutePostcodeData, nil, &manual_manipulation_rule.CDTManualManipulationRouteZipcodeRuleTab{}, 0))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.CDTManualManipulationRoutePostcodeRuleTabNamespace, dumpManager.DumpCdtManualManipulationRouteCepRangeData, nil, &manual_manipulation_rule.CDTManualManipulationRoutePostcodeRuleTab{}, 0))

		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.LogisticCdtAbTestRuleNamespace, dumpManager.DumpCdtAbTestRule, nil, &cdt_ab_test.CdtAbTestRule{}, 0))

		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.AggregateMaskedChannelCdtRuleTabNamespace, dumpManager.DumpAggregateMaskedChannelRule, nil, &aggregate_masked_channel_cdt.AggregateMaskedChannelCdtTab{}, 5*time.Minute))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.AutomatedVolumeGenerationRuleTabNamespace, dumpManager.DumpAutomatedVolumeGenerationRule, nil, &automated_volume.AutomatedVolumeGenerationRuleTab{}, 0))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.AutomatedVolumeGenerationDataVersionTabNamespace, dumpManager.DumpAutomatedVolumeGenerationDataVersion, nil, &automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab{}, 0))

		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.LogisticProductInfoNamespace, dumpManager.DumpLogisticProductInfo, nil, &product_service.ProductInfo{}, config.GetMutableConf(context.Background()).LogisticProductInfoCacheConfig.GetDumpInterval()))
		timeLocalcacheList = append(timeLocalcacheList, localcache.NewConf(constant.ProductEDTConfigTabNamespace, dumpManager.DumpProductEDTConfigTab, nil, &edt_config.ProductEdtConfigTab{}, 3*time.Minute))
	}

	// 步骤4：加载branch使用的localcache
	{
		// branch 和 store 数据
		branchLocalcacheList = append(branchLocalcacheList, localcache.NewConf(constant.LogisticBranchInfoNamespace, dumpManager.DumpLogisticBranchInfo, nil, &branch_info.LogisticBranchInfoTab{}, 0))
		branchLocalcacheList = append(branchLocalcacheList, localcache.NewConf(constant.LogisticBranchGroupNamespace, dumpManager.DumpLogisticBranchGroup, nil, &branch_group.LogisticBranchGroupTab{}, 0))
		branchLocalcacheList = append(branchLocalcacheList, localcache.NewConf(constant.PostalCodeToGeo, dumpManager.DumpPostalCodeGeo, nil, &postal_code_to_geo.PostalCodeToGeo{}, 0))
		branchLocalcacheList = append(branchLocalcacheList, localcache.NewConf(constant.ThirdPartyConvenienceStoreNamespace, dumpManager.DumpTWStore, nil, &tw_store.LogisticThirdPartyConvenienceStoreTab{}, 0))
		branchLocalcacheList = append(branchLocalcacheList, localcache.NewConf(constant.LogisticOldStoreIdxMapNamespace, dumpManager.DumpTWStoreMap, nil, &tw_store.LogisticOldStoreIdxMapTab{}, 0))
	}

	{
		// station 数据，属于branch服务，目前部分逻辑在lcos-task，部分在branch-grpc
		stationLocalcacheList = append(stationLocalcacheList, localcache.NewConf(constant.LogisticStationInfoNamespace, dumpManager.DumpLogisticStation, nil, &station_conf.StationCacheInfo{}, 0))
		stationLocalcacheList = append(stationLocalcacheList, localcache.NewConf(constant.LogisticAllStationIdNamespace, dumpManager.DumpLogisticAllStationId, nil, []uint64{}, 0))
	}

	// 只有checkout grpc用的缓存
	{
		// 电子围栏数据
		onlyCheckoutLocalcacheList = append(onlyCheckoutLocalcacheList, localcache.NewConf(constant.EFencePolygonNamespace, dumpManager.DumpEFencePolygon, nil, geopolygon.PolyDetail{}, 0))
		onlyCheckoutLocalcacheList = append(onlyCheckoutLocalcacheList, localcache.NewConf(constant.EFenceWhitelistNamespace, dumpManager.DumpEFenceWhitelist, nil, location_whitelist.EFenceLocationWhitelistTab{}, 0))
		onlyCheckoutLocalcacheList = append(onlyCheckoutLocalcacheList, localcache.NewConf(constant.EFenceMeshNamespace, dumpManager.DumpEFenceMesh, nil, mesh.ZoneMeshInfoList{}, 0))
		onlyCheckoutLocalcacheList = append(onlyCheckoutLocalcacheList, localcache.NewConf(constant.EFenceLineToggleNamespace, dumpManager.DumpEFenceLineToggle, nil, line_toggle.EFenceLineToggleTab{}, 0))
	}

	if startup.IsLcosProject() && !config.IsTask() { // 非lcos-task，加载common和lcos的缓存内容
		commonLocalcacheList = append(commonLocalcacheList, lcosLocalcacheList...)
		// lcos 加载branch 和 store 数据，待branch迁移出去后废弃
		if !config.IsSearchGrpc() && !config.IsSearchOfflineGrpc() {
			// searchgrpc不会有branch/store流量，固定不加载branch数据
			// grpc和fulfillmentgrpc可能会有残留branch/store流量，迁移完成后废弃。由开关动态控制是否加载数据
			commonLocalcacheList = append(commonLocalcacheList, branchLocalcacheList...)
		}

		// lcos-grpc才加载的缓存
		if config.IsCheckoutGrpc() || config.IsFulfillmentGrpc() {
			commonLocalcacheList = append(commonLocalcacheList, onlyCheckoutLocalcacheList...)
		}
	} else if startup.IsLcosProject() && config.IsTask() { // lcos-task，只加载station的缓存内容 todo SPLPS-13459 tw checkout limit逻辑目前在lcos-task，迁移到branch服务后lcos-task不用加载此缓存
		commonLocalcacheList = stationLocalcacheList
	} else if startup.IsTimeProject() {
		commonLocalcacheList = append(commonLocalcacheList, timeLocalcacheList...)
	} else if startup.IsBranchProject() {
		commonLocalcacheList = append(commonLocalcacheList, branchLocalcacheList...)
		commonLocalcacheList = append(commonLocalcacheList, stationLocalcacheList...)
	} else if config.IsMonoOffline() {
		if config.IsMonoTime() {
			commonLocalcacheList = append(commonLocalcacheList, timeLocalcacheList...) // 时效
		}
		if config.IsMonoSA() {
			commonLocalcacheList = append(commonLocalcacheList, lcosLocalcacheList...) // 服务范围
		}
	} else if startup.IsLPSProject() {
		commonLocalcacheList = append(commonLocalcacheList, timeLocalcacheList...)
	}

	return localcache.InitCacheManager(time.Hour, commonLocalcacheList...)
}

func InitDBCallbackRefreshCache(db *scorm.OrmDB) {
	enableTransaction := func(db scorm.SQLCommon) bool {
		return !db.GetConfig().SkipDefaultTransaction
	}

	_ = db.Callback().Update().After("gorm:update").Register("cache:after_update", RefreshCache)
	_ = db.Callback().Create().After("gorm:create").Register("cache:after_create", RefreshCache)
	_ = db.Callback().Delete().After("gorm:delete").Register("cache:after_delete", RefreshCache)
	_ = db.Callback().Create().Match(enableTransaction).Register("gorm:commit_or_rollback_create_transaction", RefreshCache)
	_ = db.Callback().Update().Match(enableTransaction).Register("gorm:commit_or_rollback_update_transaction", RefreshCache)
	_ = db.Callback().Delete().Match(enableTransaction).Register("gorm:commit_or_rollback_delete_transaction", RefreshCache)
}

func RefreshCache(db scorm.SQLCommon) {
	// /SPLN-27306 Refresh函数注册的是After的回调，因此若DB操作执行失败，还是会触发Refresh函数，因此这里可以通过db.GetError()来判断一下是否需要刷新缓存.

	// SPLN-21200 route删除掉自动刷新，改为手动刷新
	// SPLN-21782 branch删除自动刷新，改为手动刷新
	// SPLN-20771 route black list 手动刷新
	if strings.ToUpper(db.GetStatement().Table) == constant.LineRouteTabNamespace2 || strings.ToUpper(db.GetStatement().Table) == constant.LogisticBranchInfoNamespace || strings.ToUpper(db.GetStatement().Table) == constant.LineBlacklistRouteTabNamespace {
		logger.LogInfof("RefreshCache skip, namespace=%s", strings.ToUpper(db.GetStatement().Table))
		return
	}
	// SPLN-23000 line cep range改为手动刷新，避免删除后直接触发刷新
	// SPLN-22821 ops cep range 剔除自动刷新，改为手动
	if strings.ToUpper(db.GetStatement().Table) == constant.LineBasicCepRangeTabNamespace || strings.ToUpper(db.GetStatement().Table) == constant.LineOperationCepRangeTabNamespace {
		logger.LogInfof("RefreshCache skip, namespace=%s", strings.ToUpper(db.GetStatement().Table))
		return
	}
	// SPLN-20857 site cep range剔除自动刷新，改为手动刷新
	if strings.ToUpper(db.GetStatement().Table) == constant.SiteServiceableAreaCepRangeTabNamespace {
		logger.LogInfof("RefreshCache skip, namespace=%s", strings.ToUpper(db.GetStatement().Table))
		return
	}
	// 避免死循环
	if db.GetStatement().Table == data_version.DataVersionTableName || db.GetStatement().Table == data_version.DataVersionLogTableName {
		logger.LogInfof("RefreshCache skip, namespace=%s", strings.ToUpper(db.GetStatement().Table))
		return
	}
	logger.LogInfof("RefreshCache done, namespace=%s", strings.ToUpper(db.GetStatement().Table))

	// SPLN-27306 Gorm底层在执行 update/create/delet 等语句时会默认开启事务.
	// 勾子函数中的更新version操作会新起一个DB，并且开启事务，这个更新version的操作没有和之前的写DB放到一个事务中，可能会存在一些隐患:
	// 1.这里可能会出现DB写成功，但是version更新失败最终缓存未能成功刷新.
	// 2.如果DB操作也是操作的version表，两个事务可能会产生死锁的情况
	_, _ = localcache.IncrLocalCacheVersion(db.GetStatement().Context, strings.ToUpper(db.GetStatement().Table))
}
