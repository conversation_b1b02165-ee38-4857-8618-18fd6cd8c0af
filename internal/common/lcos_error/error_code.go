package lcos_error

type RetcodeMessage struct {
	RetCode int32  `json:"retcode"`
	Msg     string `json:"message"`
}

func NewRetcode(retcode int32, msg string) *RetcodeMessage {
	return &RetcodeMessage{RetCode: retcode, Msg: msg}
}

const (
	// 内部错误码
	DBReadErrorCode  int32 = -1001
	DBWriteErrorCode int32 = -1002
)

const (
	// 外部错误码
	SuccessCode int32 = 0
	FailCode    int32 = -1

	// 业务框架错误
	SchemaParamsErrorCode       int32 = -1000001
	ServerErrorCode             int32 = -1000002
	RequestTooFrequentErrorCode int32 = -1000003
	JsonDecodeErrorCode         int32 = -1000004
	NotFoundRequestIdErrorCode  int32 = -1000008
	InvalidRequestIdErrorCode   int32 = -1000009
	LoopOverMaxSize             int32 = -1000010
	DeliveryPrefixErrorCode     int32 = -1000011
	FunctionUnSupported         int32 = -1000012
	InvalidS3HostErrorCode      int32 = -1000013

	// jwt鉴权错误
	NotFoundJwtDataErrorCode   int32 = -1001000
	NotFoundAccountErrorCode   int32 = -1001001
	NotFoundTimestampErrorCode int32 = -1001002
	InvalidAccountErrorCode    int32 = -1001003
	InvalidTimestampErrorCode  int32 = -1001004
	JwtDecodeErrorCode         int32 = -1001005
	JwtHeaderDecodeError       int32 = -1001006

	// 中间件错误
	DBReadWriteErrorCode         int32 = -1002000
	RefreshLocalCacheErrorCode   int32 = -1002001
	RedisReadWriteErrorCode      int32 = -1002002
	LocalCacheReadWriteErrorCode int32 = -1002007
	HBaseReadWriteErrorCode      int32 = -1002008
	SaturnNeedToRetryErrorCode   int32 = -1002009
	OptimisticLocking            int32 = -1002010

	// 内部服务I/O异常
	FileIOErrorCode int32 = -1003001

	LocationServiceIOErrorCode int32 = -1004001

	HTTPRequestErrorCode        int32 = -1006007
	HTTPS3DownloadFileErrorCode int32 = -1006008

	// 服务范围业务逻辑错误
	NotSupportPickupErrorCode     int32 = -2001000
	NotSupportCodPickupErrorCode  int32 = -2001001
	NotSupportDeliverErrorCode    int32 = -2001002
	NotSupportCodDeliverErrorCode int32 = -2001003

	NotSupportCollectAbilityErrorCode int32 = -2001004
	NotSupportDeliverAbilityErrorCode int32 = -2001005
	DistanceCalculateErrorCode        int32 = -2001006
	DistanceExceedMaxErrorCode        int32 = -2001007
	DistanceExceedMinErrorCode        int32 = -2001008
	RouteCheckErrorCode               int32 = -2001009
	FmAbilityErrorCode                int32 = -2001010

	NotSupportPickupAddressErrorCode      int32 = -2001011
	NotSupportDeliverAddressErrorCode     int32 = -2001012
	NotSupportPickupPostcodeErrorCode     int32 = -2001013
	NotSupportDeliverPostcodeErrorCode    int32 = -2001014
	NotSupportCodPickupAddressErrorCode   int32 = -2001015
	NotSupportCodDeliverAddressErrorCode  int32 = -2001016
	NotSupportCodPickupPostcodeErrorCode  int32 = -2001017
	NotSupportCodDeliverPostcodeErrorCode int32 = -2001018

	LineRouteNotFoundErrorCode int32 = -2001019

	// 服务范围line重合报错
	LineServiceAbleOverlap             int32 = -2001020
	NotFoundLaneCodeErrorCode          int32 = -2001021
	NotFoundLaneEffectiveRuleErrorCode int32 = -2001022
	PredefinedRouteCheckErrorCode      int32 = -2001023

	// 重量计算/限制业务逻辑错误
	CalculateWeightFullyFailedErrorCode           int32 = -2002000
	CheckPackageLimitFullyFailedErrorCode         int32 = -2002001
	CalculateWeightPartialFailedErrorCode         int32 = -2002002
	CheckPackageLimitPartialFailedErrorCode       int32 = -2002003
	CheckProductValidatWeightLimitFailedErrorCode int32 = -2002004
	ConvertWeightParamsErrorCode                  int32 = -2002005
	TWSizeNotFound                                int32 = -2002006
	CalculateFormulaError                         int32 = -2002007
	ParamsError                                   int32 = -2002008

	//
	NotFoundLineErrorCode                 int32 = -2003004
	NotFoudCollectDeliverGroupIdErrorCode int32 = -2003005
	NotHitCollectDeliverGroupIdErrorCode  int32 = -2003006

	// 地址服务错误
	LocationServerErrorCode   int32 = -2005001
	NotFoundLocationErrorCode int32 = -2005002

	// pickup 错误
	PickupDayIsHolidayErrorCode               int32 = -2006000
	CurrentTimeOverCutoffTimeErrorCode        int32 = -2006001
	NotFoundTimeslotErrorCode                 int32 = -2006002
	NotFoundAnyTimeslotErrorCode              int32 = -2006017
	NotFoundPickupGroupErrorCode              int32 = -2006005
	PickGroupsNotSameErrorCode                int32 = -2006006
	NotFoundLineServiceableBasicConfErrorCode int32 = -2006007
	NotFoundPickupConfErrorCode               int32 = -2006008
	NotGetPickupDaysErrorCode                 int32 = -2006009
	PickupConfErrorCode                       int32 = -2006010
	LineNotValidErrorCode                     int32 = -2006011
	PickupDayIsWeekendErrorCode               int32 = -2006012
	LineNotSupportPickupUp                    int32 = -2006013
	WeekdayNotValidError                      int32 = -2006014
	WeekendNotFoundError                      int32 = -2006015
	HolidayNotFoundError                      int32 = -2006016

	// branch错误
	NotFoundBranchError       int32 = -2017000
	NotFoundBranchGroupError  int32 = -2017001
	BranchNotMatchGroupError  int32 = -2017002
	FindLngLatFromZipcodeFail int32 = -2017003

	// spex
	SpexCommonError int32 = -2016000

	// site serviceable area
	NotFoundSiteServiceableAreaBasicConfErrorCode     int32 = -2018001
	SiteServiceableAreaLocationLevelNotValidErrorCode int32 = -2018002
	SiteServiceableAreaLocationOverlapErrorCode       int32 = -2018003
	SiteServiceableAreaPostcodeOverlapErrorCode       int32 = -2018004
	SiteServiceableAreaCepRangeOverlapErrorCode       int32 = -2018005
	NotFoundActualPointErrorCode                      int32 = -2018006
	ActualPointNotValidErrorCode                      int32 = -2018007
	NotFoundSiteErrorCode                             int32 = -2018008
	SiteServiceableAreaCheckFail                      int32 = -2018009
	SiteServiceableLocationHistoryCheckFail           int32 = -2018010

	// sa task
	MinTotalNumberLessThanCriteria      int32 = -2019000
	MaxDeletionNumberLargerThanCriteria int32 = -2019001
	NotFoundSATaskConfErrorCode         int32 = -2019002
	SATaskNotValidErrorCode             int32 = -2019003
	SATaskSendEmailErrorCode            int32 = -2019004
	SATaskLockErrorCode                 int32 = -2019005
	SAGetTPLServiceableAreaErrorCode    int32 = -2019006
	NotFoundSATaskRecordErrorCode       int32 = -2019007
	SATaskRecordExpiredErrorCode        int32 = -2019008
	SARequestRateLimitErrorCode         int32 = -2019009

	// station
	TransLongitudeFail                  int32 = -2020000
	TransLatitudeFail                   int32 = -2020001
	TransStationLocFail                 int32 = -2020002
	TransCheckoutLimitFail              int32 = -2020003
	SaveStationFail                     int32 = -2020004
	NotFoundStationErr                  int32 = -2020005
	ExportStationFail                   int32 = -2020006
	OrderExtLogisticInfoIsNil           int32 = -2020007
	OrderExtBuyerAddressIsNIl           int32 = -2020008
	HDCheckAbilityErr                   int32 = -2020009
	HDGetStationErr                     int32 = -2020010
	TransDistanceThresholdFail          int32 = -2020011
	DBNotExistData                      int32 = -2020012
	RecordOrderIdToRedisErr             int32 = -2020013
	BMOANotUpdateDB                     int32 = -2020014
	UpdateStationFailed                 int32 = -2020015
	UpdateStationBuyerFailed            int32 = -2020016
	CreateStationBuyerFailed            int32 = -2020017
	HandleStationBuyerRelationFailed    int32 = -2020018
	FindStationBuyerFailed              int32 = -2020019
	CalculateStationBuyerRelationFailed int32 = -2020020
	GetCoordinateErr                    int32 = -2020021

	BatchGetDistanceCacheErr        int32 = -2020022
	DataProcessCreatePool           int32 = -2020023
	CreateAddressRevisionTaskFailed int32 = -2020024
	GetAddressRevisionTaskFailed    int32 = -2020025
	UpdateAddressRevisionTaskFailed int32 = -2020026
	BatchCreateRevisionDataFailed   int32 = -2020027
	GetRevisionDataFailed           int32 = -2020028
	BatchUpdateRevisionDataFailed   int32 = -2020029
	RevisionFileHandleFailed        int32 = -2020030

	GetStationVolumeRecordErr  int32 = -2020024
	SaveStationVolumeRecordErr int32 = -2020025

	// ADMIN 接口错误
	BatchCreateLocationErrorCode                        int32 = -3002001
	BatchGetAreaLocationErrorCode                       int32 = -3002002
	DownloadFileErrorCode                               int32 = -3002003
	NotFoundBasicConfErrorCode                          int32 = -3002004
	NotFoundOperationConfErrorCode                      int32 = -3002005
	NotFoundAreaErrorCode                               int32 = -3002006
	NotFoundRouteErrorCode                              int32 = -3002007
	NotFoundBasicOriginLocationErrorCode                int32 = -3002008
	NotFoundBasicDestLocationErrorCode                  int32 = -3002009
	NotFoundBasicOriginPostcodeErrorCode                int32 = -3002010
	NotFoundBasicDestPostcodeErrorCode                  int32 = -3002011
	NotFoundOperationOriginLocationErrorCode            int32 = -3002012
	NotFoundOperationDestLocationErrorCode              int32 = -3002013
	NotFoundOperationOriginPostcodeErrorCode            int32 = -3002014
	NotFoundOperationDestPostcodeErrorCode              int32 = -3002015
	NotFoundAreaLocationRefErrorCode                    int32 = -3002016
	NotFoundScenarioConfErrorCode                       int32 = -3002017
	CreateAreaLocationErrorCode                         int32 = -3002018
	UploadFileErrorCode                                 int32 = -3002019
	CollectDeliverGroupConflictErrorCode                int32 = -3002020
	NotFoundBasicLocationErrorCode                      int32 = -3002021
	NotFoundBasicPostcodeErrorCode                      int32 = -3002022
	NotFoundOperationLocationErrorCode                  int32 = -3002023
	NotFoundOperationPostcodeErrorCode                  int32 = -3002024
	NotFoundGroupInLocationOrPostcodeErrorCode          int32 = -3002025
	SupportOperationLocationButNotSupportBasicErrorCode int32 = -3002026 // location支持运营层，但是基础层不支持
	SupportOperationPostcodeButNotSupportBasicErrorCode int32 = -3002027 // postcode支持运营层，但是基础层不支持
	SupportCepRangeButNotSupportBasicErrorCode          int32 = -3002028
	NotFoundCollectDeliverGroupErrorCode                int32 = -3002029
	CreateRouteErrorCode                                int32 = -3002030
	LPSGetScenarioErrorCode                             int32 = -3002031

	CreateRuleErrorCode                  int32 = -3003001
	UpdateRuleErrorCode                  int32 = -3003002
	GetRuleErrorCode                     int32 = -3003003
	NotFoundLaneServiceableRuleErrorCode int32 = -3003004
	ExportLaneCodeErrorCode              int32 = -3003005

	NotFoundPackageLimitRuleErrorCode int32 = -3006001
	CepRangeDuplicate                 int32 = -3006002
	IdNotExit                         int32 = -3006003

	// CDT
	NotFoundManualManipulationRuleErrorCode       int32 = -4006001
	TaskNotAllowedToEnableErrorCode               int32 = -4006002
	TaskNotAllowedToDisableErrorCode              int32 = -4006003
	CdtUploadTaskAlreadyExistErrorCode            int32 = -4006004
	CdtUploadTaskStatusNotAllowedErrorCode        int32 = -4006005
	CdtAddressNotFoundErrorCode                   int32 = -4006006
	CdtCannotSendEmailErrorCode                   int32 = -4006007
	RecordNotFoundErrorCode                       int32 = -4006008
	ChannelNotFoundErrorCode                      int32 = -4006009
	ParseFileErrorCode                            int32 = -4006010
	CepRangeNotFoundErrorCode                     int32 = -4006011
	NotAllowedToUpdateDataTimeErrorCode           int32 = -4006012
	NotFoundAutoUpdateCalculationVersionErrorCode int32 = -4006013
	NotFoundCdtInfoErrorCode                      int32 = -4006014
	TriggerAutoUpdateRuleJobError                 int32 = -4006015
	PostcodeNotFoundErrorCode                     int32 = -4006016
	NotFoundAutoUpdateRuleErrorCode               int32 = -4006017
	NotFoundManualUpdateDataErrorCode             int32 = -4006018
	NotFoundLaneManualManipulationErrorCode       int32 = -4006019
	NotFoundAutoVolumeCalculationVersionErrorCode int32 = -4006020
	NotFoundFChannelVolumeErrorCode               int32 = -4006021
	NotFoundCDTRuleErrorCode                      int32 = -4006022

	ChannelHasIntersectEffectTimePeriod int32 = -4006017
	SomeAutoRuleFail                    int32 = -4006018
	ManiRuleOnlyDisableAllowDelete      int32 = -4006018
	// tw store
	StoreNotFoundError int32 = -4007000

	// edd
	EDDOnGoingCalculationAllFailed      int32 = -4008000
	EDDOnGoingPushingAllFailed          int32 = -4008001
	EDDOnGoingPushingPartialSuccess     int32 = -4008002
	EDDOnGoingCalculationPartialSuccess int32 = -4008003
	EDDHistoryNotFound                  int32 = -4008004
	EDDNotValid                         int32 = -4008005
	OnlyCanUpdateDDLError               int32 = -4008006

	AlgoModelUpdateError int32 = -4008007
	AlgoCannotSwitchAlgo int32 = -4008008
	AlgoAbtestGroupError int32 = -4008009

	//delivery instruction
	FindDeliveryCategoryFailed int32 = 4009000

	EDDAutoUpdateRuleCannotCreateError int32 = -4008004
	EDDAutoUpdateRuleCreateWarning     int32 = -4008005

	ScheduledJobError   int32 = -5001000
	SyncItemCdtError    int32 = -5002000
	SyncItemVolumeError int32 = -5002001

	// electronic fence
	SdkGeoHashError int32 = -6001000

	SyncMPLRedisError    int32 = -5002002
	SyncMPLRedisDelError int32 = -5002003

	// EDT Rule Error
	EdtTypeError        int32 = -6000000
	SubEdtTypeError     int32 = -6000001
	MultiFormulaError   int32 = -6000002
	SecondFormulaError  int32 = -6000003
	BuyerIdCheckError   int32 = -6000004
	EffectiveTimeError  int32 = -6000005
	ChannelIDError      int32 = -6000006
	DataNotFound        int32 = -6000007
	OperatingHoursError int32 = -6000008
)

var ErrorCodeMapper = map[int32]int32{
	// 内外部转换的map，暂时没有用到
}

var RetCodeToMessMapper = map[int32]string{
	SuccessCode:                 "success",
	SchemaParamsErrorCode:       "Invalid params.",
	ServerErrorCode:             "Service unavailable.",
	RequestTooFrequentErrorCode: "Requests are too frequent. Please do it later.",
	DeliveryPrefixErrorCode:     "",
	InvalidS3HostErrorCode:      "Invalid S3 Host.",

	NotFoundJwtDataErrorCode:   "Jwt is Required. ",
	NotFoundAccountErrorCode:   "Account is Required. ",
	NotFoundTimestampErrorCode: "Timestamp is required. ",
	InvalidAccountErrorCode:    "Account is invalid. ",
	InvalidTimestampErrorCode:  "Timestamp is invalid. ",
	JwtDecodeErrorCode:         "Jwt format error or signature error. ",
	JwtHeaderDecodeError:       "Jwt header format error or signature error. ",

	DBReadWriteErrorCode:         "DB data error. ",
	RefreshLocalCacheErrorCode:   "Service refresh cache failed. ",
	RedisReadWriteErrorCode:      "Redis data error. ",
	LocalCacheReadWriteErrorCode: "Local cache data error. ",

	NotSupportPickupErrorCode:     "Location or postcode not support pickup. ",
	NotSupportCodPickupErrorCode:  "Location or postcode not support cod pickup. ",
	NotSupportDeliverErrorCode:    "Location or postcode not support deliver. ",
	NotSupportCodDeliverErrorCode: "Location or postcode not support cod deliver. ",

	NotSupportCollectAbilityErrorCode:     "Line collect ability not support. ",
	NotSupportDeliverAbilityErrorCode:     "Line deliver ability not support. ",
	DistanceCalculateErrorCode:            "Distance check failed. ",
	DistanceExceedMaxErrorCode:            "Distance exceed line max limit. ",
	DistanceExceedMinErrorCode:            "Distance exceed line min limit. ",
	RouteCheckErrorCode:                   "Route check failed. ",
	NotSupportPickupAddressErrorCode:      "Line collect address not support pickup. ",
	NotSupportDeliverAddressErrorCode:     "Line deliver address not support deliver. ",
	NotSupportPickupPostcodeErrorCode:     "Line collect postcode not support pickup. ",
	NotSupportDeliverPostcodeErrorCode:    "Line deliver postcode not support deliver. ",
	NotSupportCodPickupAddressErrorCode:   "Line collect address not support cod pickup. ",
	NotSupportCodDeliverAddressErrorCode:  "Line deliver address not support cod deliver. ",
	NotSupportCodPickupPostcodeErrorCode:  "Line collect postcode not support cod pickup. ",
	NotSupportCodDeliverPostcodeErrorCode: "Line deliver postcode not support cod deliver. ",
	PredefinedRouteCheckErrorCode:         "Predefined route check failed. ",

	CalculateWeightFullyFailedErrorCode:           "Calculate weight all failed. ",
	CheckPackageLimitFullyFailedErrorCode:         "Check package limit all failed",
	CalculateWeightPartialFailedErrorCode:         "Calculate weight Partial failed. ",
	CheckPackageLimitPartialFailedErrorCode:       "Check package limit Partial failed",
	CheckProductValidatWeightLimitFailedErrorCode: "Check product validate weight formula size failed",
	ConvertWeightParamsErrorCode:                  "convert weight limit params error",
	NotFoundPackageLimitRuleErrorCode:             "Not found limit rule. ",

	FileIOErrorCode: "File IO error. ",

	LocationServerErrorCode:    "Location service server error. ",
	NotFoundLocationErrorCode:  "Location Not Found. ",
	LocationServiceIOErrorCode: "Location service I/O error. ",

	BatchCreateLocationErrorCode:                        "Batch create location error. ",
	BatchGetAreaLocationErrorCode:                       "Batch get area location ref error. ",
	DownloadFileErrorCode:                               "Download file error. ",
	NotFoundBasicConfErrorCode:                          "Not found basic serviceable_area config. ",
	NotFoundOperationConfErrorCode:                      "Not found operation serviceable_area config. ",
	NotFoundAreaErrorCode:                               "Not found area. ",
	NotFoundRouteErrorCode:                              "Not found route. ",
	NotFoundBasicOriginLocationErrorCode:                "Not found basic origin location. ",
	NotFoundBasicDestLocationErrorCode:                  "Not found basic dest location. ",
	NotFoundBasicOriginPostcodeErrorCode:                "Not found basic origin postcode. ",
	NotFoundBasicDestPostcodeErrorCode:                  "Not found basic dest postcode. ",
	NotFoundOperationOriginLocationErrorCode:            "Not found operation origin location. ",
	NotFoundOperationDestLocationErrorCode:              "Not found operation dest location. ",
	NotFoundOperationOriginPostcodeErrorCode:            "Not found operation origin postcode. ",
	NotFoundOperationDestPostcodeErrorCode:              "Not found operation dest postcode. ",
	NotFoundAreaLocationRefErrorCode:                    "Not found area location ref. ",
	NotFoundScenarioConfErrorCode:                       "Not found scenario conf. ",
	CreateAreaLocationErrorCode:                         "Create area location ref error. ",
	UploadFileErrorCode:                                 "Upload file error. ",
	SupportOperationLocationButNotSupportBasicErrorCode: "location operation support but basic not support",
	SupportOperationPostcodeButNotSupportBasicErrorCode: "postcode operation support but basic not support",

	NotFoundLineErrorCode: "Line Not Found. ",

	NotFoundPickupGroupErrorCode: "Pickup Group Not Found",

	CreateRuleErrorCode:                  "Create Serviceable Area Rule Error. ",
	UpdateRuleErrorCode:                  "Update Serviceable Area Rule Error. ",
	NotFoundLaneServiceableRuleErrorCode: "Get Lane Serviceable Rule Error. ",

	FindDeliveryCategoryFailed: "Get Delivery Category Error. ",

	ScheduledJobError: "Scheduled Job Error. ",

	SyncItemCdtError: "Sync Item CDT Error. ",
}
