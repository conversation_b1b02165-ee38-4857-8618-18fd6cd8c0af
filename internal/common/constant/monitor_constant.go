package constant

const (
	GormContextKey              = "catCtx"
	CatContextTransKey          = "catCtxTrans"
	GinMonContextKey            = "__monctx"
	CatModuleRedis              = "Redis"
	CatModuleMemoryCache        = "MemoryCache"
	CatModulePeriodMemoryCache  = "MemoryPeriodMemoryCache"
	CatModuleBackUpCache        = "MemoryBackup"
	CatLocalCacheUseInfo        = "LocalCacheUseInfo"
	CdtGetInfo                  = "CdtGetInfo"
	AggregateCdtGetInfoPDP      = "AggregateCdtGetInfoPDP"
	AggregateCdtGetInfoItemCard = "AggregateCdtGetInfoItemCard"
	CdtOverMaximum              = "CdtOverMaximum"
	CdtEqualZero                = "CdtEqualZero"
	CannotFindCdtByAutoRule     = "CannotFindCdtByAutoRule"
	CdtGetPenetrationInfo       = "CdtGetPenetrationInfo"
	UnLockFailed                = "UnlockFailed"
	CatSyncBranch               = "SyncBranch"
	CatS3BranchUpload           = "S3BranchUpload"
	CannotFindVolumeByAutoRule  = "CannotFindVolumeByAutoRule"

	CdtDbQueryTimes    = "CdtDbQueryTimes"    // report cdt query times
	VolumeDbQueryTimes = "VolumeDbQueryTimes" // report volume query times

	CatSyncStoreFailed = "SyncStoreFailed" // report sync store failed

	CatApolloConfigError = "ApolloConfigError"
	CatEDDReport         = "EDDReport" // report EDD
	CatEDDAlgo           = "EDDAlgo"
	CatEDDAbTest         = "EDDAbtest"
	CatReverseEDDAlgo    = "EDDReverseAlgo"
	CatReverseEDDDefault = "EDDReverseDefault"

	CatPackageLimitByPass = "CatPackageLimitByPass"

	CatPickupWindowExtend      = "PickupWindowExtend"
	CatPickupWindowExtendError = "PickupWindowExtendError"

	CatPackageLimitCheckFallback = "PackageLimitCheckFallback"

	CatEDDReachLastRetry          = "EDDReachLastRetry" // retry max retry
	CatOMS                        = "URL.oms"           // OMS report
	CatServiceableRule            = "ServiceableRule"
	CatServiceableRuleV2          = "ServiceableRuleV2"
	CatServiceableFulfillmentRule = "ServiceableFulfillmentRule"
	CatServiceableRerouteRule     = "ServiceableRerouteRule"

	CatModuleCheckLocationServiceableRoute  = "CheckLocationServiceableRoute"
	CatModuleCheckOperationServiceableRoute = "CheckOperationServiceableRoute"

	CatBranchSwitch = "BranchSwitch"
	SyncBranchData  = "SyncBranchData"

	CatScheduledUpload = "ScheduledUpload"

	CatSpxServiceableAreaReport = "SpxServiceableAreaReport"

	CatSyncStation  = "SyncStation"
	CatStationQuery = "StationQuery"

	WriteMplRedisReport = "WriteMplRedisReport"

	CatEddDisableNonworkingDaysReport = "EddDisableNonworkingDaysReport"

	CatFallbackEdd = "FallbackEDD"
)

const (
	StatusSuccess  = "0"
	StatusError    = "error"
	StatusPanic    = "panic"
	StatusExpired  = "expired"
	StatusLru      = "lru"
	StatusNotExist = "not-exist"
	StatusMiss     = "miss"
	StatusHotKey   = "hot-key"
	StatusNotEqual = "not-equal" // 用于切换比对的不一致错误
)

const (
	StatusEddCalculateError   = "calculate_edd_error"
	StatusEddCheckError       = "check_edd_error"
	StatusEddPushError        = "push_edd_error"
	StatusEddSaveHistoryError = "save_edd_history_error"
)

const (
	CatModuleURL          = "URL"
	CatModuleAPI          = "API"
	CatModuleMySQL        = "MySQL"
	CatModuleGRpc         = "gRpc"
	CatModulePanic        = "panic"
	CatModuleHBase        = "HBase"
	CatModuleLruCache     = "LruCache"
	CatModuleLayeredCache = "LayeredCache"
	CatModuleSpex         = "SpexService"
	CatModuleLocalCache   = "LocalCache"
	CatModuleS3           = "S3"
	CatModuleS3Host       = "S3Host"
)

const (
	LocalCacheRefreshSwitch = "chassis.cache_refresh.enabled"
	CdtMaximum              = "chassis.cdt.cdt_maximum" // cdt异常值上限

	ReportAdvancedHours = "mutable_application.cdt.report_advanced_hours" // advance hours before report alert for cdt version
)

const (
	MetricNamespaceLoad           = "namespace_load_count"
	MetricReqStatus               = "request_status"
	MetricCdtDataSyncCount        = "cdt_data_sync_count"
	CheckCdtAutoUpdateTaskUpdated = "check_cdt_auto_update_task_updated"
	SyncAutoUpdateTaskStatus      = "sync_auto_update_task_status"
	MetricLineStatus              = "line_rsp_status"
	MetricLaneStatus              = "lane_rsp_status"
	MetricProductStatus           = "product_rsp_status"
	MetricPickupGroupStatus       = "pickup_grpup_rsp_status"
	MetricDbRowStatus             = "db_rows_status"
	MetricServiceAbleByLine       = "service_able_by_line"
	MetricTimeServiceFunc         = "time_service_func"
	MetricLocalcacheCount         = "local_cache_count"
	MetricLocalcacheTime          = "local_cache_time"

	// EDD Metrics Report
	MetricsEDDPrecisionReport        = "edd_precision_report"         // gauge
	MetricsEDDPrecisionCounterReport = "edd_precision_counter_report" // counter
	MetricsEDDReport                 = "edd_report"                   // gauge
	MetricsEDDCounterReport          = "edd_counter_report"           // counter
	MetricsCDTMaxReport              = "cdt_max_report"
	MetricsCDTMinReport              = "cdt_min_report"
	MetricsRealTimeReport            = "real_time_report"
	MetricsEDDDelayQueueReport       = "edd_delay_queue_report"
	MetricsVolumeReport              = "volume_report"

	// SPLN-31587 diff report
	MetricsEDDDiffReport = "edd_diff_report"

	// branch
	MetricsBranchStatusReport = "branch_status_report"

	MetricsSyncItemCdtDataReport       = "sync_item_cdt_data_report"
	MetricsBatchItemCdtReport          = "batch_item_cdt_report"
	MetricsCmpItemCdtReport            = "cmp_item_cdt_report"
	MetricsSyncItemVolumeDataReport    = "sync_item_volume_data_report"
	MetricsStationUpdateVolumeCostTime = "station_update_volume_cost_time"

	// station
	MetricsCheckAbilityResultReport        = "station_check_ability_result"
	MetricsMatchOptimalStationResultReport = "match_optimal_station_result"

	// electronic fence
	MetricsElectronicFenceReport           = "electronic_fence_report"           // 上报电子围栏结果
	MetricsEFenceAndServiceCheckDiffReport = "e_fence_service_check_diff_report" // 上报服务范围校验通过但是电子围栏校验失败的数量

	// SPLN-35905
	MetricInstallationLineStatus = "installation_line_rsp_status"
)

const (
	// api
	MetricDataFailureNotify       = "data_failure_notify"        // data推送失败
	MetricChangeReportStatus      = "change_report_status"       // 线上变更上报状态：success、missing key fields、failure
	MetricSiteServiceableCheck    = "site_serviceable_check"     // 点服务范围校验
	MetricVolumeDataFailureNotify = "data_volume_failure_notify" // data推送失败

)

// 服务范围的核心方法
const (
	GetServiceableWithCheckFlag = "GetServiceableWithCheckFlag"
)

// core logic for time service
const (
	GetCourierDeliveryTimeByProduct = "GetCourierDeliveryTimeByProduct"
)

const (
	CatModuleThirdPartyLocalCache = "ThirdPartyLocalCache"
)

const (
	CatModuleGreySwitch = "GreySwitch"
)

// ItemCard 相关func
const (
	CalcNonAutoCdt      = "CalcNonAutoCdt"
	CalcAutoCdt         = "CalcAutoCdt"
	CalcNeedMChannelCdt = "CalcNeedMChannelCdt"
	CalcNeedVolume      = "CalcNeedVolume"
	CalcOld             = "CalcOld"
)

const (
	CatModuleIsBrIdc  = "IsBrIdc"
	EventNameIsBrIdc  = "br_idc"
	EventNameNotBrIdc = "not_br_idc"
)

// station相关上报
const (
	// module
	CatModuleStation = "Station"

	// interface
	CheckAbilityPanic                     = "CheckAbilityPanic"
	GetHDStationPanic                     = "GetHDStationPanic"
	GetStationByStationDistanceLib        = "GetStationByStationDistanceLib"
	GetStationByStationDistanceLibFailure = "GetStationByStationDistanceLibFailure"
	GetStationByDirectDistance            = "GetStationByDistance"
	GetStationByMatrixDistance            = "GetStationByMatrixDistance"
	NoStationAroundAddress                = "NoStationAroundAddress"
	TimeoutFallBack                       = "TimeoutFallBack"
	TimeoutGetStationByDirectDistance     = "TimeoutGetStationByDirectDistance"
	TimeoutNotSupported                   = "TimeoutNotSupported"
	GetCoordinateFromGoogleError          = "GetCoordinateFromGoogleFailure"
	GetNoneNearlyStation                  = "GetNoneNearlyStation"
	MatchHubFail                          = "MatchHubFail"
	MatchHubSuccess                       = "MatchHubSuccess"
	SaveBuyerAddressFailure               = "SaveBuyerAddressFailure"
	GetStationCurrentVolumeError          = "GetStationCurrentVolumeError"
	GetStationByOrderIdIsZero             = "GetStationByOrderIdIsZero"
	StationInfoNotExists                  = "StationInfoNotExists"
	RematchStationIdIsZero                = "RematchStationIdIsZero"
	SaveOrderStationInfoFail              = "SaveOrderStationInfoFail"
	StationTemporarilyClosed              = "StationTemporarilyClosed"
	GetGoogleCoordinateIsNil              = "GetGoogleCoordinateIsNil"
	GetMaxStationDistanceIsZero           = "GetMaxStationDistanceIsZero"
	GetOptimalStationQuickSuccess         = "GetOptimalStationQuickSuccess"
	GetMaxDistanceFromMysql               = "GetMaxDistanceFromMysql"
	OrderNoRelateStationRecord            = "OrderNoRelateStationRecord"

	// check ability相关链路上报
	CatCheckAbilityModule = "StationCheckAbility"

	CheckAbilityRequestTotal                  = "CheckAbilityRequestTotal"
	CheckAbilityCheckRequestTotal             = "CheckAbilityCheckRequestTotal"
	CheckAbilityTimeoutDefaultRequestTotal    = "CheckAbilityTimeoutDefaultRequestTotal"
	CheckAbilityByCacheResultTotal            = "CheckAbilityByCacheResultTotal"
	CheckAbilityByRealTimeResultTotal         = "CheckAbilityByRealTimeResultTotal"
	CheckAbilityGetCoordinateFailTotal        = "CheckAbilityGetCoordinateFailTotal"
	CheckAbilityGetNearbyStationFailTotal     = "CheckAbilityGetNearbyStationFailTotal"
	CheckAbilityNearbyStationIdListIsNilTotal = "CheckAbilityNearbyStationIdListIsNilTotal"
	CheckAbilityGetStationConfErrTotal        = "CheckAbilityGetStationConfErrTotal"
	CheckAbilityCheckByLineDistanceTotal      = "CheckAbilityCheckByLineDistanceTotal"
	CheckAbilityByDrivingDistanceTotal        = "CheckAbilityByDrivingDistanceTotal"
	CheckAbilityTimeoutUnknownSupportTotal    = "CheckAbilityTimeoutUnknownSupportTotal"

	// match optimal station相关链路上报
	CatMatchOptimalModule = "StationMatchOptimal"

	MatchOptimalRequestTotal                                  = "MatchOptimalRequestTotal"
	MatchOptimalMatchRequestTotal                             = "MatchOptimalMatchRequestTotal"
	MatchOptimalGetAvailableStationErrorTotal                 = "MatchOptimalGetAvailableStationErrorTotal"
	MatchOptimalGetAvailableStationIsNilTotal                 = "MatchOptimalGetAvailableStationIsNilTotal"
	MatchOptimalGetAvailableStationSuccessTotal               = "MatchOptimalGetAvailableStationSuccessTotal"
	MatchOptimalFilterAvailableStationErrTotal                = "MatchOptimalFilterAvailableStationErrTotal"
	MatchOptimalFilterAvailableResultIsNilTotal               = "MatchOptimalFilterAvailableResultIsNilTotal"
	MatchOptimalFilterOptimalStationIsNilTotal                = "MatchOptimalFilterOptimalStationIsNilTotal"
	MatchOptimalSuccessMatchStationTotal                      = "MatchOptimalSuccessMatchStationTotal"
	MatchOptimalGetAvailableStationRequestTotal               = "MatchOptimalGetAvailableStationRequestTotal"
	MatchOptimalGetAvailableStationTimeoutDefaultRequestTotal = "MatchOptimalGetAvailableStationTimeoutDefaultRequestTotal"
	MatchOptimalGetStationFromCacheResultTotal                = "MatchOptimalGetStationFromCacheResultTotal"
	MatchOptimalGetStationFromRealResultErrTotal              = "MatchOptimalGetStationFromRealResultErrTotal"
	MatchOptimalGetStationFromRealResultSuccessTotal          = "MatchOptimalGetStationFromRealResultSuccessTotal"
	MatchOptimalGetStationGetGoogleCoordinateErrTotal         = "MatchOptimalGetStationGetGoogleCoordinateErrTotal"
	MatchOptimalGetStationGetNearStationErrTotal              = "MatchOptimalGetStationGetNearStationErrTotal"
	MatchOptimalGetStationGetStationConfErrTotal              = "MatchOptimalGetStationGetStationConfErrTotal"
	MatchOptimalGetStationFromDrivingDistanceSuccessTotal     = "MatchOptimalGetStationFromDrivingDistanceSuccessTotal"
	MatchOptimalGetStationQuickSuccessTotal                   = "MatchOptimalGetStationQuickSuccessTotal"
	MatchOptimalGetStationNoStationAroundAddressTotal         = "MatchOptimalGetStationNoStationAroundAddressTotal"
)

// 地址匹配相关上报
const (
	// Module
	CatHomeDeliveryAddressMatch = "HomeDeliveryAddressMatch"
	// interface
	GetBuyerDrivingDistance            = "GetBuyerDrivingDistance"
	GetBuyerDrivingDistanceFromRedis   = "GetBuyerDrivingDistanceFromRedis"
	GetBuyerDrivingDistanceFromMysql   = "GetBuyerDrivingDistanceFromMysql"
	GetBuyerAddressCoordinate          = "GetBuyerAddressCoordinate"
	GetBuyerAddressCoordinateFromMap   = "GetBuyerAddressCoordinateFromMap"
	SearchNearbyStation                = "SearchNearbyStation"
	SearchNearbyStationFromRedis       = "SearchNearbyStationFromRedis"
	SearchNearbyStationFromEs          = "SearchNearbyStationFromEs"
	GetDrivingDistanceMatrix           = "GetDrivingDistanceMatrix"
	GetDrivingDistanceMatrixFromCache  = "GetDrivingDistanceMatrixFromCache"
	GetDrivingDistanceMatrixFromMap    = "GetDrivingDistanceMatrixFromMap"
	HitDistanceMysqlFlowControl        = "HitDistanceMysqlFlowControl"
	GetGoogleCoordinateForMatchAddress = "GetGoogleCoordinateForMatchAddress"
	MatchAddressFromGoogle             = "MatchAddressFromGoogle"
	GetDistanceMatrixFromGoogle        = "GetDistanceMatrixFromGoogle"
	SaveBuyerDistanceToRedis           = "SaveBuyerDistanceToRedis"
	SaveBuyerDistanceToMySQL           = "SaveBuyerDistanceToMySQL"
	SaveBuyerDistanceSubmitFailed      = "SaveBuyerDistanceSubmitFailed"
	FirstTimeSyncMysqlData             = "FirstTimeSyncMysqlData"
	FirstTimeSyncRedisData             = "FirstTimeSyncRedisData"
	ParseDistanceAndCoordinateCache    = "ParseDistanceAndCoordinateCache"
	ParseDistanceCacheWithNewlyStruct  = "ParseDistanceCacheWithNewlyStruct"
	BatchQueryDistanceCacheByAddrId    = "BatchQueryDistanceCacheByAddrId"
	BatchGetDistinctBuyerDistanceData  = "BatchGetDistinctBuyerDistanceData"
	UpdateStationSyncData              = "UpdateStationSyncData"
	HandleStationBuyerRelation         = "HandleStationBuyerRelation"
	CalculateStationBuyerDistance      = "CalculateStationBuyerDistance"
	FileCount                          = "FileCount"
	SetGeoCodeCache                    = "SetGeoCodeCache"
	GetGeoCodeCache                    = "GetGeoCodeCache"
	CallGoogleMatrixFromScheduledJob   = "CallGoogleMatrixFromScheduledJob"
	DistanceZero                       = "DistanceZero"
	StationDistanceThresholdZero       = "StationDistanceThresholdZero"
	TriggerStationBuyerRelation        = "TriggerStationBuyerRelation"
	SendDataInsertTaskFailed           = "SendDataInsertTaskFailed"
	HandleSingleRevisionFileFailed     = "HandleSingleRevisionFileFailed"
	AddressRevisionDataHandleError     = "AddressRevisionDataHandleError"
	ShadowGetBuyerAddressCoordinate    = "ShadowGetBuyerAddressCoordinate"
	ShadowSetBuyerAddressCoordinate    = "ShadowSetBuyerAddressCoordinate"
	SetBuyerAddressCoordinate          = "SetBuyerAddressCoordinate"
	ShadowGetDrivingDistanceMatrix     = "ShadowGetDrivingDistanceMatrix"
	ShadowSetDrivingDistanceMatrix     = "ShadowSetDrivingDistanceMatrix"
	SetDrivingDistanceMatrix           = "SetDrivingDistanceMatrix"
	ShadowSaveBuyerDistance            = "ShadowSaveBuyerDistance"
	SaveBuyerDistance                  = "SaveBuyerDistance"
)

// 电子围栏Cat上报
const (
	EventNameEFenceSuccess = "Success"

	CatModuleEFencePreHandle           = "EFencePreHandle"
	EventNameEFencePreHandlePolygon    = "GetPolygon"
	EventNameEFencePreHandleMesh       = "DoMesh"
	EventNameEFencePreHandleActivate   = "Activate"
	EventNameEFencePreHandleOnlyExpire = "OnlyExpire"
	EventNameEfencePreHandleRecallSpx  = "BatchGetPolygonDetail"

	CatModuleEFenceCache           = "EFenceCache"
	EventNameEFenceCacheMeshDump   = "MeshDump"
	EventNameEFenceCacheIncVersion = "IncVersion"

	MetricsEFenceCacheMeshCount          = "efence_mesh_count"             // mesh数据量上报，处理到每个zone就上报
	MetricsEFenceCacheMeshCountByFullTab = "efence_mesh_count_by_full_tab" // mesh加载全表的数据量上报
	MetricsEFenceCacheMeshSuccessCount   = "efence_mesh_success_count"     // mesh数据量上报，整个缓存处理成功才上报，因此只有总数

	CatModuleEFenceUnHandled = "EFenceUnHandled"
	EventNameEFenceInit      = "Init"

	CatModuleEFenceForwardMesh = "EFenceForwardMesh"
	EventNameEFenceForwardMesh = "ForwardMesh"

	CatModuleEFenceCheckOut = "EFenceCheckout"
	EventNameEFenceLayer    = "EFenceLayer"
)

const (
	S3EventNameClientNotFound = "ClientNotFound"
	S3EventNameDownloadFail   = "DownloadFail"
)

const (
	SyncBranchEventNameGetBranchFail     = "GetBranchFail"
	SyncBranchEventNameSaveBranchFail    = "SaveBranchFail"
	SyncBranchEventNameSyncBranchSuccess = "SyncBranchSuccess"
)

const (
	CatModuleDisableMChannelHandle = "DisableMChannelHandle"
	EventNameDisableMChannel       = "DisableMChannel"
)

// edt cat上报
const (
	EDTError            = "EdtError"
	EdtRuleScheduleFail = "RuleScheduleFail"
)

const (
	EddPushMonitor                     = "edd_push_monitor"
	EddPushMonitor_EddEventField       = "edd_push_event"
	EddPushMonitor_EddPushField        = "edd_push_event"
	EddPushMonitor_OrderEddPushField   = "order_edd_push"
	EddPushMonitor_AlgoEddPushField    = "algo_edd_push"
	EddPushMonitor_StaticsEddPushField = "statics_edd_push"
)
