package constant

var (
	LcosApiLocalcacheCachePrefix = "Lcos:api:localcache"
	LcosRedisRefreshListener     = "Lcos:api:localcache:refresh"
	LcosRedisCacheLoadStatus     = "Lcos:api:cache:loadstatus"
)

// namespace名称要与数据库表名保持一致
const (
	LinePackageLimitNamespace        = "LOGISTIC_LINE_PACKAGE_LIMIT_TAB"
	TWWeightSizeNamespace            = "LOGISTIC_SIZE_TAB"
	ProductPackageLimitNamespace     = "LOGISTIC_PRODUCT_PACKAGE_LIMIT_TAB"
	LineScenarioConfNamespace        = "LOGISTIC_LINE_SCENARIO_CONF_TAB"
	LineAbilityConfNamespace         = "LOGISTIC_LINE_COLLECT_DELIVER_ABILITY_TAB"
	LineRouteTabNamespace            = "LOGISTIC_LINE_SERVICEABLE_ROUTE_TAB"
	LineRouteTabNamespace2           = "LINE_SERVICEABLE_AREA_ROUTE_TAB"
	LineBlacklistRouteTabNamespace   = "LINE_SERVICEABLE_BLACKLIST_ROUTE_TAB"
	LineOperationRouteTabNamespace   = "LINE_OPERATION_ROUTE_TAB"
	LineAreaLocationRefTabNamespace  = "LOGISTIC_LINE_SERVICEABLE_AREA_LOCATION_REF_TAB"
	LineCollectDeliverGroupNamespace = "LINE_COLLECT_DELIVER_GROUP_CONF_TAB"

	LineOperationServiceableConfNamespace   = "LOGISTIC_LINE_OPERATION_SERVICEABLE_CONF_TAB"
	LineOperationOriginLocationTabNamespace = "LOGISTIC_LINE_OPERATION_SERVICEABLE_ORIGIN_LOCATION_TAB"
	LineOperationDestLocationTabNamespace   = "LOGISTIC_LINE_OPERATION_SERVICEABLE_DEST_LOCATION_TAB"
	LineOperationOriginPostcodeTabNamespace = "LOGISTIC_LINE_OPERATION_SERVICEABLE_ORIGIN_POSTCODE_TAB"
	LineOperationDestPostcodeTabNamespace   = "LOGISTIC_LINE_OPERATION_SERVICEABLE_DEST_POSTCODE_TAB"
	LineOperationLocationTabNamespace       = "LINE_OPERATION_SERVICEABLE_LOCATION_TAB"
	LineOperationPostcodeTabNamespace       = "LINE_OPERATION_SERVICEABLE_POSTCODE_TAB"

	LineBasicServiceableConfNamespace     = "LOGISTIC_LINE_BASIC_SERVICEABLE_CONF_TAB"
	LineBasicServiceableGroupRefNamespace = "LINE_BASIC_SERVICEABLE_GROUP_REF_TAB"
	LineBasicLocationTabNamespace         = "LINE_BASIC_SERVICEABLE_LOCATION_TAB_"
	LineBasicPostcodeTabNamespace         = "LINE_BASIC_SERVICEABLE_POSTCODE_TAB_"

	LineBasicCepRangeTabNamespace     = "LINE_BASIC_SERVICEABLE_CEP_RANGE_TAB"
	LineOperationCepRangeTabNamespace = "LINE_OPERATION_SERVICEABLE_CEP_RANGE_TAB"

	// 特殊的namespace， 通过LINE_COLLECT_DELIVER_GROUP_CONF_TAB，拆解支持的collect_deliver
	LineSupportCollectDeliverGroupNamespace = "LINE_COLLECT_DELIVER_SUPPORT_GROUP_CONF_TAB"

	PickupGroupTabNamespace          = "LOGISTIC_PICKUP_GROUP_TAB"
	PickupGroupLineIDRefTabNamespace = "LOGISTIC_PICKUP_GROUP_LINE_ID_REF_TAB"

	PickupConfTabNamespace                = "LOGISTIC_PICKUP_CONF_TAB"
	PickupConfSpecialSettingsTabNamespace = "LOGISTIC_PICKUP_SPECIAL_SETTINGS_TAB"
	PickupTimeslotTabNamespace            = "LOGISTIC_PICKUP_TIMESLOT_TAB"
	HolidayTabNamespace                   = "LOGISTIC_HOLIDAY_TAB"
	RecurringHolidayTabNamespace          = "LOGISTIC_RECURRING_HOLIDAY_TAB"

	OpenLogisticPickupConfTabNamespace     = "LOGISTIC_OPEN_LOGISTIC_PICKUP_CONF_TAB"
	OpenLogisticPickupTimeslotTabNamespace = "LOGISTIC_OPEN_LOGISTIC_PICKUP_TIMESLOT_TAB"

	LogisticBranchGroupNamespace = "LOGISTIC_BRANCH_GROUP_TAB"
	LogisticBranchInfoNamespace  = "LOGISTIC_BRANCH_INFO_TAB"

	PostalCodeToGeo         = "POSTAL_CODE_TO_GEO"
	TPLIDLineIDTabNamespace = "LOGISTIC_3PL_ID_LINE_ID_REF_TAB"

	CDTAutoUpdateRuleTabNamespace                 = "LOGISTIC_CDT_AUTO_UPDATE_RULE_TAB"
	CDTAutoUpdateCalculateDataTabNamespace        = "LOGISTIC_CDT_AUTO_UPDATE_CALCULATE_DATA_TAB_"
	CDTAutoUpdateCalculateDataVersionTabNamespace = "LOGISTIC_CDT_AUTO_UPDATE_CALCULATE_DATA_VERSION_TAB"

	CDTManualManipulationLocationRuleTabNamespace = "LOGISTIC_CDT_MANUAL_MANIPULATION_LOCATION_RULE_TAB"
	CDTManualManipulationPostcodeRuleTabNamespace = "LOGISTIC_CDT_MANUAL_MANIPULATION_POSTCODE_RULE_TAB"
	CDTManualManipulationZipcodeRuleTabNamespace  = "LOGISTIC_CDT_MANUAL_MANIPULATION_ZIPCODE_RULE_TAB"
	CDTManualManipulationRuleRecordTabNamespace   = "LOGISTIC_CDT_MANUAL_MANIPULATION_RULE_RECORD_TAB"

	CDTManualManipulationRouteLocationRuleTabNamespace = "LOGISTIC_CDT_MANUAL_MANIPULATION_ROUTE_LOCATION_RULE_TAB"
	CDTManualManipulationRouteZipcodeRuleTabNamespace  = "LOGISTIC_CDT_MANUAL_MANIPULATION_ROUTE_ZIPCODE_RULE_TAB"
	CDTManualManipulationRoutePostcodeRuleTabNamespace = "LOGISTIC_CDT_MANUAL_MANIPULATION_ROUTE_POSTCODE_RULE_TAB"

	CDTManualUpdatePostcodeDataTabNamespace = "LOGISTIC_CDT_MANUAL_UPDATE_POSTCODE_DATA_TAB"
	CDTManualUpdateRecordTabNamespace       = "LOGISTIC_CDT_MANUAL_UPDATE_RECORD_TAB"

	ProductEDTConfigTabNamespace = "LOGISTIC_PRODUCT_EDT_CONFIG"

	AggregateMaskedChannelCdtRuleTabNamespace        = "AGGREGATE_MASKED_CHANNEL_CDT_RULE_TAB"
	AutomatedVolumeGenerationRuleTabNamespace        = "AUTOMATED_VOLUME_GENERATION_RULE_TAB"
	AutomatedVolumeGenerationDataTabNamespace        = "AUTOMATED_VOLUME_GENERATION_DATA_"
	AutomatedVolumeGenerationDataVersionTabNamespace = "AUTOMATED_VOLUME_GENERATION_VERSION_TAB"

	// sls holiday
	SlsHolidayNamespace          = "HOLIDAY_TAB"
	SlsRecurringHolidayNamespace = "RECURRING_HOLIDAY_TAB"

	// lps holiday
	LPSHolidayNamespace          = "PRODUCT_HOLIDAY_TAB"
	LPSRecurringHolidayNamespace = "PRODUCT_RECURRING_HOLIDAY_TAB"

	// tw store
	ThirdPartyConvenienceStoreNamespace = "LOGISTIC_THIRD_PARTY_CONVENIENCE_STORE_TAB"
	LogisticOldStoreIdxMapNamespace     = "LOGISTIC_OLD_STORE_IDX_MAP_TAB"

	// site serviceable area
	SiteServiceableAreaBasicConfTabNamespace = "LOGISTIC_SITE_BASIC_SERVICEABLE_CONF_TAB"
	SiteServiceableAreaCepRangeTabNamespace  = "LOGISTIC_SITE_BASIC_SERVICEABLE_CEP_RANGE_TAB_"
	SiteServiceableAreaLocationTabNamespace  = "LOGISTIC_SITE_BASIC_SERVICEABLE_LOCATION_TAB_"
	SiteServiceableAreaPostcodeTabNamespace  = "LOGISTIC_SITE_BASIC_SERVICEABLE_POSTCODE_TAB_"

	// serviceable area rule
	ServiceableAreaEffectiveRuleTabNamespace = "LANE_EFFECTIVE_SERVICEABLE_RULE_TAB"

	LineServiceableRouteNamespace         = "LINE_SERVICEABLE_ROUTE_TAB"
	LineAreaTabNamespace                  = "LOGISTIC_LINE_SERVICEABLE_AREA_TAB"
	BranchTaskRecordNamespace             = "LOGISTIC_BRANCH_TASK_RECORD_TAB"
	CDTAutoUpdateRulePostCodeTabNamespace = "LOGISTIC_CDT_AUTO_UPDATE_RULE_POSTCODE_TAB"
	LogisticsProductCtimeTabNamespace     = "LOGISTIC_PRODUCT_CTIME_TAB"
	SATaskConfigurationNamespace          = "LOGISTIC_SA_TASK_CONFIGURATION_TAB"
	SATaskRecordNamespace                 = "LOGISTIC_SA_TASK_RECORD_TAB"

	// delivery instruction
	DeliveryInstructionBasicConfAndDetailNamespace = "DELIVERY_INSTRUCTION_BASIC_CONF_AND_DETAIL_TAB"
	DeliveryInstructionWhitelistLocationNamespace  = "DELIVERY_INSTRUCTION_WHITELIST_LOCATION_TAB"
	DeliveryInstructionWhitelistCepRangeNamespace  = "DELIVERY_INSTRUCTION_WHITELIST_CEP_RANGE_TAB"

	LogisticCdtAbTestRuleNamespace = "LOGISTIC_CDT_AB_TEST_RULE_TAB"

	// station
	LogisticStationInfoNamespace  = "LOGISTIC_STATION_INFO_TAB"
	LogisticAllStationIdNamespace = "LOGISTIC_ALL_STATION_ID"

	LogisticProductServiceableZoneNamespace     = "LOGISTIC_PRODUCT_SERVICEABLE_ZONE_TAB"
	LogisticProductServiceableCepGroupNamespace = "LOGISTIC_PRODUCT_SERVICEABLE_CEP_GROUP_TAB"
	LogisticShopServiceableZoneNamespace        = "LOGISTIC_SHOP_SERVICEABLE_ZONE_TAB"

	// e_fence
	EFencePolygonNamespace    = "E_FENCE_POLYGON_TAB"
	EFenceMeshNamespace       = "E_FENCE_MESH_TAB" // 电子围栏网格化结果，目前是全市场都在一个缓存中，ID加载完大约1.8GB，BR大约0.2GB，将来如果多市场累积数据量太大，可以按市场拆分缓存
	EFenceWhitelistNamespace  = "E_FENCE_LOCATION_WHITELIST_TAB"
	EFenceLineToggleNamespace = "E_FENCE_LINE_TOGGLE_TAB"

	LogisticLaneInfoNamespace = "LOGISTIC_LANE_INFO"
	ActualPointInfoNamespace  = "ACTUAL_POINT_INFO"

	LogisticProductInfoNamespace = "LOGISTIC_PRODUCT_INFO"

	LogisticLinePredefinedRouteNamespace = "LOGISTIC_LINE_PREDEFINED_ROUTE_TAB"
)
