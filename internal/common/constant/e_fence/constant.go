package e_fence

import "strings"

// 将SPX的station type映射成用户可读的文字
var StationTypeMap = map[int32]string{
	0:  "Admin",
	1:  "RDC",
	2:  "DC",
	3:  "Hub",
	4:  "Station",
	5:  "ThirdPart",
	6:  "SortingCenter",
	7:  "FirstMileHub",
	8:  "P2P",
	9:  "External",
	12: "AllMileHub",
}

// SPX推过来的zone状态，Active状态的是需要新增并处理的，Disable状态的是需要废弃的
const (
	ZoneStatusActive  = 0
	ZoneStatusDisable = 1
)

// SPX 推送的模式
const (
	NotifyModeIncrement = 0 // 发生变更时增量推送，同一批数据会事务处理
	NotifyModeFull      = 1 // 定时全量推送，用于最终一致性兜底
)

// zone在电子围栏模块中的使用状态，Init:原始数据还未处理完成，InUsage:已经在生效使用，Expired:已经废弃
const (
	ZoneHandleStatusInit    = 0
	ZoneHandleStatusInUsage = 1
	ZoneHandleStatusExpired = 2
	ZoneHandleStatusCleaned = 3 // 对应的mesh数据被清理
)

// 接口收到zone信息之后，如果超出这个时间范围，handleStatus仍然没有变成InUsage，则下次再收到的时候重新发起处理
const ZoneHandleTimeMax = 60 * 40

// 导出zone信息的文件模板
const HubZoneDataFileName = "hub_zone_data.xlsx"

// 做geohash的最小和最大编码长度
const GeohashEncodingSizeMin = 5
const GeohashEncodingSizeMax = 7

// Notify接口的特殊控制字段
const (
	ForceHandleDefault      = 0 // 默认逻辑
	ForceHandleDoNotPull    = 1 // 接口传过来就是完整的数据，不需要调用SPX拉取详细信息
	ForceHandleRefreshCache = 2 // 刷新电子围栏缓存
)

var EFenceWhitelistExportFileTitle = []string{
	"Country",
	"SPX Order Account",
	"State/Province",
	"City",
	"District",
	"Street",
}

var EFenceWhitelistUploadFileTitle = []string{
	"Country",
	"SPX Order Account",
	"State/Province",
	"City",
	"District",
	"Street",
	"Action",
}

const (
	NotSupportLmHubZone = 0
	SupportLmHubZone    = 1
)

const (
	EFenceCheckPickup  = 1
	EFenceCheckDeliver = 2
	EFenceCheckBoth    = 3
)

const (
	DefaultDeleteMeshLimit = 1000
	DefaultDeleteZoneLimit = 2
)

const (
	WhitelistAdd    = 0
	WhitelistDelete = -1
)

// 电子围栏校验结果上报
const (
	ReportNotCheck          = ""
	ReportMissGeoInfo       = "miss_geo_info"      // 校验失败，缺失经纬度
	ReportMissLocationInfo  = "miss_location_info" // 校验失败，缺失地址信息
	ReportNotSupport        = "not_support"        // 校验失败, 不在电子围栏范围
	ReportLocationWhiteList = "whitelist"          // 检验成功, 使用Location 白名单
	ReportLineNotConfig     = "line_no_conf"       // 检验成功, line 没有配置默认成功
	ReportFullCover         = "full_cover"         // 检验成功, 经纬度被网格全覆盖
	ReportUseRay            = "use_ray"            // 检验成功, 走进射线法
)

var SuccessReportCode = []string{ReportNotCheck, ReportMissGeoInfo, ReportMissLocationInfo, ReportNotSupport, ReportLocationWhiteList, ReportLineNotConfig, ReportFullCover, ReportUseRay}

const (
	ServiceCheckNotSupport    = "0-1" // 电子围栏支持，服务范围不支持,  因为是先做的服务范围校验，所有用0-1象形表示
	ElectronicFenceNotSupport = "1-0" // 服务范围支持，电子围栏不支持
)

const (
	CheckAreaServiceable = 1
	GenerateMesh         = 2
)

var ValidRegionMap = map[string]bool{
	"SG": true,
	"TW": true,
	"MY": true,
	"PH": true,
	"ID": true,
	"VN": true,
	"TH": true,
	"BR": true,
	"MX": true,
	"CO": true,
	"CL": true,
}

func IsValidRegion(region string) bool {
	return ValidRegionMap[strings.ToUpper(region)]
}

const (
	RWMeshTabV2Global      = 2
	RWMeshTabV2ExceptCache = 1
)

const DefaultLayer string = "0"
const TwDefaultLayer string = "1"

const RegionTW = "tw"

const DefaultLayerName = "Normal"
const TwDefaultLayerName = "Driver Zone"
const InActiveLayerName = "Inactive"
