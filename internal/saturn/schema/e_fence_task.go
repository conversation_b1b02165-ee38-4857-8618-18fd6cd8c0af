package schema

import (
	jsoniter "github.com/json-iterator/go"
)

type EFenceGenerateMeshMessage struct {
	NotifyMode     int32             `json:"notify_mode"`
	Region         string            `json:"region"`
	NewVersionList []VersionItem     `json:"new_version_list"` // 增量推送的时候，只需要把这个列表里面的所有zone捞出来，处理完成后放在事务里面落库即可
	OldVersionList []VersionItem     `json:"old_version_list"` // 新版本生效的时候，这个列表中的旧版本同时失效
	NewZoneList    []*EFenceZoneInfo `json:"new_zone_list"`    // 全量兜底的时候用，因为这个请求0是查缺补漏，可能每个version只有几个不按照 NewVersionList 去找zone，而是精确查出来指定的zone
}

type EFenceZoneInfo struct {
	ZoneId      string `json:"zone_id"`
	DataVersion string `json:"data_version"`
	Geometry    string `json:"geometry,omitempty"`
	Operator    string `json:"operator"`

	LayerId string `json:"layer_id"`
}

type VersionItem struct {
	DataVersion string `json:"data_version"`
	LayerId     string `json:"layer_id"`
	Operator    string `json:"operator"`
}

func TransferVersionItemList(versionList []VersionItem) []*VersionItem {
	resultList := make([]*VersionItem, 0)
	for _, v := range versionList {
		if v.DataVersion == "" {
			continue
		}
		resultList = append(resultList, &VersionItem{DataVersion: v.DataVersion})
	}

	return resultList
}

func (z *EFenceZoneInfo) String() string {
	data, _ := jsoniter.Marshal(z)
	return string(data)
}
