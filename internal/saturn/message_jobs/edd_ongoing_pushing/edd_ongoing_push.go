package edd_ongoing_pushing

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/task_api"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/datetime"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/math"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_rule_conf"
	edd_update_task_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_task_conf"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	saturnConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/message_jobs/edd_push"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/tools"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_pushing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/emailhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcos_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/oms_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/waybill_center_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	jsoniter "github.com/json-iterator/go"
	"html/template"
	"path"
	"regexp"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

//EDDPushingTask

type EddOnGoingPushingJob struct {
	eddHistoryDao        edd_history.EddHistoryDAO
	eddUpdateRuleConfDao edd_update_rule_conf.EDDUpdateRuleConfTabDAO
	eddUpdateTaskConfDao edd_update_task_conf2.EDDUpdateTaskConfTabDAO
	s3Service            s3_service.S3Service

	// SPLN-31383
	eddAutoUpdateDao edd_auto_update_rule.EddAutoUpdateRuleDao
}

func (e *EddOnGoingPushingJob) SendMail(ctx utils.LCOSContext, taskTab *edd_update_task_conf2.EddUpdateTaskConfTab, region string, operatorList []string, resultFileName string) *lcos_error.LCOSError {

	logger.CtxLogInfof(ctx, "ready to send email to operators:[%s] for task_id:[%d]", strings.Join(operatorList, ","), taskTab.ID)

	data := &schema.PushingEmailData{
		TaskID: taskTab.ID,
		URL:    utils.GenerateOpsUrl(ctx, region),
	}

	if taskTab.PushingStatus == edd_constant.EDDPushingSuccess {
		data.EmailTitle = edd_constant.PushingSuccessEmailTitle
		data.EmailContent = fmt.Sprintf(edd_constant.PushingSuccessEmailContent, data.TaskID, data.URL)
	} else {
		data.EmailTitle = edd_constant.PushingFailedEmailTitle
		data.EmailContent = fmt.Sprintf(edd_constant.PushingFailedEmailContent, data.TaskID, data.URL)
	}

	filePath := path.Join(pathutil.GetProjectAbsolutePath(), "templates/html/", edd_constant.EDDManualUpdateEmailTemplate)
	tpl, err := template.ParseFiles(filePath)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("error parsing email template file|file_path=%s", filePath))
	}
	buf := new(bytes.Buffer) //实现了读写方法的可变大小的字节缓冲
	executeErr := tpl.Execute(buf, data)
	if executeErr != nil {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("error parsing email template file|error=%s", executeErr.Error()))
	}

	// 发送邮件
	err = emailhelper.SendEmailV2(buf.String(), data.EmailTitle, "text/html", resultFileName, operatorList, nil, nil)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SATaskSendEmailErrorCode, fmt.Sprintf("error sending email|error=%v", err.Error()))
	}
	return nil
}

// FilterEDDInfoByCalculatingResultForFirstEDDCalculation
// fill edd history for trigger first calculation
func (e *EddOnGoingPushingJob) FilterEDDInfoByCalculatingResultForFirstEDDCalculation(ctx utils.LCOSContext, parsedEDD []*edd_update_task_conf.RawEDDOnGoingOrderInfo, region string) (map[string]*lfs_service.LogisticOrderData, *lcos_error.LCOSError) {
	// get sls tn list
	var slsTNList []string
	for _, singleEDD := range parsedEDD {
		if len(singleEDD.ErrorMessage) == 0 { // get all info from LFS
			slsTNList = append(slsTNList, singleEDD.SLSTN)
		}
	}

	// call lfs
	// request lfs
	lfsService := lfs_service.NewLFSService(ctx, region)
	resultMap, lcosErr := lfsService.BatchGetLogisticOrderDataBySloTnForEDDWithConcurrency(ctx, slsTNList, config.GetEDDOnGoingConfig(ctx).WaybillCenterMaxConcurrency)
	if lcosErr != nil {
		return nil, lcosErr
	}

	nowTime := utils.GetTimestamp(ctx)
	for _, singleEDD := range parsedEDD {
		if len(singleEDD.ErrorMessage) == 0 && isEDDTriggeredByManualUpdate(singleEDD.TriggerFirstEDDCalculation) {
			if waybillItem, ok := resultMap[singleEDD.SLSTN]; ok {
				newEddMin, err := parseStringToRTimestamp(singleEDD.NewEddMin, region)
				if err != nil {
					singleEDD.ErrorMessage = err.Error()
					continue
				}
				newEddMax, err := parseStringToRTimestamp(singleEDD.NewEddMax, region)
				if err != nil {
					singleEDD.ErrorMessage = err.Error()
					continue
				}
				var eddProcess *edd_history.EddProcess
				if len(singleEDD.EddProcess) != 0 {
					_ = jsoniter.UnmarshalFromString(singleEDD.EddProcess, &eddProcess)
				}
				singleEDD.CleanedData = &edd_history.EddHistoryTab{
					ForderID:    waybillItem.ForderId,
					OrderSN:     waybillItem.OrderSN,
					ProductID:   singleEDD.Product,
					IsFirstTime: constant.TRUE, // the first to push edd
					EventTime:   int64(nowTime),
					ResourceID:  singleEDD.LineID,
					SlsTN:       singleEDD.SLSTN,
					LmTN:        singleEDD.LMTN,
					EddMin:      newEddMin,
					Edd:         newEddMax,
					BuyerAddress: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(int(waybillItem.BuyerAddress.StateLocationId)),
						CityLocationId:     utils.NewInt(int(waybillItem.BuyerAddress.CityLocationId)),
						DistrictLocationId: utils.NewInt(int(waybillItem.BuyerAddress.DistrictLocationId)),
						PostalCode:         utils.NewString(waybillItem.BuyerAddress.PostCode),
					},
					SellerAddress: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(int(waybillItem.SellerAddress.StateLocationId)),
						CityLocationId:     utils.NewInt(int(waybillItem.SellerAddress.CityLocationId)),
						DistrictLocationId: utils.NewInt(int(waybillItem.SellerAddress.DistrictLocationId)),
						PostalCode:         utils.NewString(waybillItem.SellerAddress.PostCode),
					},
					NewEddProcess: eddProcess,
					UpdateType:    edd_constant.UpdateAll,
				}
			}
		}
	}
	logger.CtxLogInfof(ctx, "successfully handle trigger first edd calculation")
	return resultMap, nil
}

// FilterEDDInfoByCalculatingResult
// check whether has new edd pushed between calculation start time to now
func (e *EddOnGoingPushingJob) FilterEDDInfoByCalculatingResult(ctx utils.LCOSContext, parsedEDDO []*edd_update_task_conf.RawEDDOnGoingOrderInfo, calculatingStartTime int64, eddTaskID uint64, region string) *lcos_error.LCOSError {
	for _, singleEDD := range parsedEDDO {
		if len(singleEDD.ErrorMessage) == 0 {

			eddMinAdjustment, err := strconv.ParseInt(singleEDD.EddMinAdjustment, 10, 64)
			if err != nil {
				singleEDD.ErrorMessage = err.Error()
				continue
			}
			eddMaxAdjustment, err := strconv.ParseInt(singleEDD.EddMaxAdjustment, 10, 64)
			if err != nil {
				singleEDD.ErrorMessage = err.Error()
				continue
			}

			newEddMin, err := parseStringToRTimestamp(singleEDD.NewEddMin, region)
			if err != nil {
				singleEDD.ErrorMessage = err.Error()
				continue
			}
			newEddMax, err := parseStringToRTimestamp(singleEDD.NewEddMax, region)
			if err != nil {
				singleEDD.ErrorMessage = err.Error()
				continue
			}

			lastEddMin, err := parseStringToRTimestamp(singleEDD.LastEddMin, region)
			if err != nil {
				singleEDD.ErrorMessage = err.Error()
				continue
			}
			lastEddMax, err := parseStringToRTimestamp(singleEDD.LastEddMax, region)
			if err != nil {
				singleEDD.ErrorMessage = err.Error()
				continue
			}

			eddMinDiff, err := strconv.ParseInt(singleEDD.EddMinDiff, 10, 64)
			if err != nil {
				singleEDD.ErrorMessage = err.Error()
				continue
			}
			eddMaxDiff, err := strconv.ParseInt(singleEDD.EddMaxDiff, 10, 64)
			if err != nil {
				singleEDD.ErrorMessage = err.Error()
				continue
			}

			var eddProcess *edd_history.EddProcess
			if len(singleEDD.EddProcess) != 0 {
				_ = jsoniter.UnmarshalFromString(singleEDD.EddProcess, &eddProcess)
			}

			singleEDD.CleanedData = &edd_history.EddHistoryTab{
				ProductID:        singleEDD.Product,
				IsFirstTime:      constant.FALSE,
				ResourceID:       singleEDD.LineID,
				SlsTN:            singleEDD.SLSTN,
				LmTN:             singleEDD.LMTN,
				EddMinAdjustment: int(eddMinAdjustment),
				Adjustment:       int(eddMaxAdjustment),
				EddMinExtension:  int(eddMinDiff),
				EddExtension:     int(eddMaxDiff),
				EDDUpdateTaskID:  eddTaskID,
				EddMin:           newEddMin,
				Edd:              newEddMax,
				LastEddMin:       lastEddMin,
				LastEDD:          lastEddMax,
				NewEddProcess:    eddProcess,
				DataType:         edd_constant.ManualUpdateEdd,
			}

			allHistories, lcosErr := e.eddHistoryDao.GetEDDHistoryBySlsTn(ctx, singleEDD.SLSTN, edd_history.TableName(ctx, ""), true)
			if lcosErr != nil {
				errMsg := fmt.Sprintf("error trying to get history info for sls_tn:[%s]", singleEDD.SLSTN)
				singleEDD.ErrorMessage = errMsg
				logger.CtxLogErrorf(ctx, errMsg)
				continue
			}
			histories, _ := edd_pushing.FilterEddHistories(allHistories)

			// check whether histories are empty
			if len(histories) <= 0 {
				if !isEDDTriggeredByManualUpdate(singleEDD.TriggerFirstEDDCalculation) {
					singleEDD.ErrorMessage = fmt.Sprintf("sls tn:[%s], lm_tn:[%s] has not been pushed edd yet", singleEDD.SLSTN, singleEDD.LMTN)
				}
				continue
			}

			// sort by ctime
			sort.SliceStable(histories, func(i, j int) bool {
				return histories[i].Ctime > histories[j].Ctime
			})

			// check whether has edd push after calculation time
			eddPushedAfterCalculateStart := false
			var eddPushedTimeAfterCalculateStart int64 = 0
			for _, singleHistory := range histories {
				if singleHistory.Ctime >= calculatingStartTime {
					eddPushedAfterCalculateStart = true
					eddPushedTimeAfterCalculateStart = singleHistory.Ctime
					break
				}
			}
			if eddPushedAfterCalculateStart {
				singleEDD.ErrorMessage = fmt.Sprintf("slstn:[%s] has edd updated at [%s], while current calculate starts at [%s]", singleEDD.SLSTN, pickup.TransferTimeStampToTime(uint32(eddPushedTimeAfterCalculateStart), region).Format("2006-01-02 15:04:05"), pickup.TransferTimeStampToTime(uint32(calculatingStartTime), region).Format("2006-01-02 15:04:05"))
				continue
			}

			// SPLN-31383 find the latest auto update event as the current manual update edd to get ddl cdt info
			// if cannot find auto update event, will use pickup done(0) as current event
			for _, singleHistory := range histories {
				if singleHistory.IsAutoUpdateEDD() {
					singleEDD.CleanedData.UpdateEvent = singleHistory.GetUpdateEvent()
					singleEDD.CleanedData.DeliverBuyerID = singleHistory.DeliverBuyerID
					break
				}
			}
			// get ddl update times
			singleEDD.DDLUpdateTimes = edd_history.GetDDLUpdateTimes(allHistories)

			if isEDDTriggeredByManualUpdate(singleEDD.TriggerFirstEDDCalculation) { // need to call lfs to get info
				continue
			}

			currentTimesForEDDUpdate, err := strconv.ParseInt(singleEDD.CurrentTimesForEDDUpdate, 10, 64)
			if err != nil {
				singleEDD.ErrorMessage = err.Error()
				continue
			}

			singleEDD.CleanedData.SlsTN = histories[0].SlsTN
			singleEDD.CleanedData.LmTN = histories[0].LmTN
			singleEDD.CleanedData.ForderID = histories[0].ForderID
			singleEDD.CleanedData.EddRecalculationCount = int(currentTimesForEDDUpdate + 1) // need to add one for push
			singleEDD.CleanedData.IsFirstTime = constant.FALSE
			singleEDD.CleanedData.IsLM = histories[0].IsLM
			singleEDD.CleanedData.SellerAddress = histories[0].SellerAddress
			singleEDD.CleanedData.BuyerAddress = histories[0].BuyerAddress
			singleEDD.CleanedData.UpdateType = edd_constant.UpdateAll
		}
	}
	logger.CtxLogInfof(ctx, "successfully filter data from hbase")
	return nil
}

func (e *EddOnGoingPushingJob) validateEddInfo(ctx utils.LCOSContext, nowTime int64, eddTaskConfMap map[string]*edd_update_rule_conf.EddUpdateRuleConfigTab, parsedEDDO []*edd_update_task_conf.RawEDDOnGoingOrderInfo) *lcos_error.LCOSError {
	for _, singleEdd := range parsedEDDO {

		if len(singleEdd.ErrorMessage) > 0 {
			continue
		}

		singleEdd.CheckAndSetEddMinAvailable()
		if singleEdd.EddMinAvailable() {
			if singleEdd.CleanedData.EddMin < nowTime {
				singleEdd.CleanedData.EddMin = nowTime
			}
			if datetime.DateAfter(singleEdd.CleanedData.EddMin, singleEdd.CleanedData.Edd, utils.GetCID()) {
				singleEdd.CleanedData.Edd = singleEdd.CleanedData.EddMin
			}
			if datetime.IsSameDay(singleEdd.CleanedData.EddMin, singleEdd.CleanedData.Edd, utils.GetCID()) {
				singleEdd.CleanedData.EddMin = 0
				singleEdd.CheckAndSetEddMinAvailable()
			}
		}
		if singleEdd.CleanedData.Edd <= nowTime {
			singleEdd.ErrorMessage = "New edd max is earlier than date of recalculation"
			continue
		}

		if isEDDTriggeredByManualUpdate(singleEdd.TriggerFirstEDDCalculation) { // no need to check, simply push
			continue
		}

		// check whether old edd is less than now
		if singleEdd.CleanedData.LastEDD <= nowTime {
			// Last EDD（当前展示在ODP页面上的EDD）已经迟到
			if !config.AllowLateEddUpdate(ctx, singleEdd.Product, singleEdd.CleanedData.DeliverBuyerID) {
				singleEdd.ErrorMessage = "Recalculation date is later than previous edd"
				continue
			}
			singleEdd.CleanedData.IsLateEddUpdate = constant.TRUE
			_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "LateEddUpdate", constant.StatusSuccess, "")
		}

		// check whether current product contains conf
		if _, ok := eddTaskConfMap[singleEdd.Product]; !ok {
			singleEdd.ErrorMessage = "The Product used by the current order without EDD update times configuration"
			continue
		}

		singleEdd.EddDiffThreshold = eddTaskConfMap[singleEdd.Product].GetThreshold()
		// check whether current threshold has reached
		if singleEdd.CleanedData.EddRecalculationCount > int(eddTaskConfMap[singleEdd.Product].MaxEddReupdateTimes) {
			singleEdd.ErrorMessage = "SLSTN has exceeded max number of times for EDD update"
			continue
		}

		// check whether edd reach threshold
		if singleEdd.EddMinAvailable() {
			if !(math.Abs(int64(singleEdd.CleanedData.EddMinExtension)) >= int64(eddTaskConfMap[singleEdd.Product].EDDThresholdMin) && math.Abs(int64(singleEdd.CleanedData.EddMinExtension)) < int64(eddTaskConfMap[singleEdd.Product].EDDThresholdMax)) {
				singleEdd.ErrorMessage = "Difference between recalculated EDD min and previous EDD min is not between Difference Threshold Min and  Difference Threshold Max"
				continue
			}
		}
		if !(math.Abs(int64(singleEdd.CleanedData.EddExtension)) > int64(eddTaskConfMap[singleEdd.Product].EDDThresholdMin) && math.Abs(int64(singleEdd.CleanedData.EddExtension)) < int64(eddTaskConfMap[singleEdd.Product].EDDThresholdMax)) {
			singleEdd.ErrorMessage = "Difference between recalculated EDD max and previous EDD max is not between Difference Threshold Min and  Difference Threshold Max"
			continue
		}
	}
	return nil
}

func (e *EddOnGoingPushingJob) pushToDataWithConcurrency(ctx utils.LCOSContext, eddInfoList []*edd_update_task_conf.RawEDDOnGoingOrderInfo, productMap map[string]*edd_update_rule_conf.EddUpdateRuleConfigTab, concurrency int, region string) *lcos_error.LCOSError {
	logger.CtxLogInfof(ctx, "ready to push to data with concurrency:[%d], region:[%s]", concurrency, region)

	maxCurrencyChan := make(chan struct{}, concurrency)
	var waitGroup sync.WaitGroup
	for _, singleRequest := range eddInfoList {
		if len(singleRequest.ErrorMessage) > 0 {
			continue
		}
		maxCurrencyChan <- struct{}{}
		waitGroup.Add(1)
		go func(request *edd_update_task_conf.RawEDDOnGoingOrderInfo) {
			if lcosErr := e.eddHistoryDao.CreateEDDHistoryBySlsTn(ctx, request.CleanedData, edd_history.TableName(ctx, region)); lcosErr != nil {
				request.ErrorMessage = lcosErr.Msg
			} else {
				// update edd update times
				request.CurrentTimesForEDDUpdate = strconv.Itoa(request.CleanedData.EddRecalculationCount)
				request.RemainingTimesForEDDUpdate = strconv.Itoa(int(productMap[request.CleanedData.ProductID].MaxEddReupdateTimes) - request.CleanedData.EddRecalculationCount)
				request.EddDiffThreshold = productMap[request.CleanedData.ProductID].GetThreshold()

				// SPLN-31383 update edd to data
				_ = edd_pushing.HandleDataSync(ctx, request.CleanedData)
			}
			<-maxCurrencyChan
			waitGroup.Done()
		}(singleRequest)
	}

	waitGroup.Wait()
	return nil
}

func (e *EddOnGoingPushingJob) pushToOmsWithConcurrency(ctx utils.LCOSContext, eddInfoList []*edd_update_task_conf.RawEDDOnGoingOrderInfo, concurrent int, region string) *lcos_error.LCOSError {

	logger.CtxLogInfof(ctx, "ready to push to oms with concurrency:[%d], region:[%s]", concurrent, region)

	omsService := oms_service.NewOmsService(ctx, region)

	maxCurrencyChan := make(chan struct{}, concurrent)
	var waitGroup sync.WaitGroup
	for _, singleRequest := range eddInfoList {
		if len(singleRequest.ErrorMessage) > 0 {
			continue
		}
		maxCurrencyChan <- struct{}{}
		waitGroup.Add(1)
		go func(request *edd_update_task_conf.RawEDDOnGoingOrderInfo) {
			var reason int
			if isEDDTriggeredByManualUpdate(request.TriggerFirstEDDCalculation) {
				reason = edd_push.EDDUpdateReasonSystem
			} else {
				reason = edd_push.EDDUpdateReasonReupdate
			}
			lcosErr := omsService.UpdateEDDInfo(ctx, &oms_service.UpdateEddInfoReq{
				ForderId:        request.CleanedData.ForderID,
				Country:         region,
				ConsignmentNo:   request.CleanedData.SlsTN,
				EddMin:          request.CleanedData.EddMin,
				EddMax:          request.CleanedData.Edd,
				EDDUpdateReason: reason,
			})
			if lcosErr != nil {
				request.ErrorMessage = lcosErr.Msg
			}
			<-maxCurrencyChan
			waitGroup.Done()
		}(singleRequest)
	}

	waitGroup.Wait()

	return nil
}

func (e *EddOnGoingPushingJob) GetOrderByWaybillCenterWithConcurrency(ctx utils.LCOSContext, eddInfoList []*edd_update_task_conf.RawEDDOnGoingOrderInfo, batchSize, maxConcurrency int, region string) (map[string]*waybill_center_service.LaneCodeInfo, *lcos_error.LCOSError) {
	var slsTN []string
	for _, singleEDD := range eddInfoList {
		if len(singleEDD.ErrorMessage) == 0 {
			slsTN = append(slsTN, singleEDD.CleanedData.SlsTN)
		}
	}

	// request waybill center
	waybillCenter := waybill_center_service.NewWaybillCenterService(ctx, region)
	resultMap, lcosErr := waybillCenter.BatchGetOrderInfoWithConcurrency(ctx, slsTN, batchSize, maxConcurrency)
	if lcosErr != nil {
		return nil, lcosErr
	}

	return resultMap, nil
}

// handle single order ddl, will be trigger with concurrent
func (e *EddOnGoingPushingJob) handleSingleWaybillInfo(ctx utils.LCOSContext, eddInfo *edd_update_task_conf.RawEDDOnGoingOrderInfo, orderInfo *lfs_service.LogisticOrderData, laneCodeInfo *waybill_center_service.LaneCodeInfo, eddAutoUpdateRule *edd_auto_update_rule.EddAutoUpdateRule, region string) *lcos_error.LCOSError {
	productID := orderInfo.LogisticProductId
	slsTN := eddInfo.SLSTN
	buyerLocationID := orderInfo.BuyerAddress.CityLocationId
	ddlUpdateTimes := eddInfo.DDLUpdateTimes

	logger.CtxLogInfof(ctx, "ready to handle edd waybill ddl|sls_tn=[%s], product_id=[%s]", slsTN, productID)

	// handle ddl info for sls tn

	// 1. call time service to get ddl
	updateEvent := eddInfo.CleanedData.GetUpdateEvent()
	nextEventUpdate, err := eddAutoUpdateRule.FindNextPreemptiveEvent(updateEvent)
	if err != nil {
		logger.CtxLogInfof(ctx, "err=[%s]|sls_tn=[%s], product_id=[%s]", err.Error(), eddInfo.CleanedData.SlsTN, eddInfo.CleanedData.ProductID)
		return nil
	}
	lineList := laneCodeInfo.GetFollowingLineIDList()
	laneCode := strings.ReplaceAll(laneCodeInfo.LaneCode, "/", "|")
	laneCode = edd_pushing.DealWithLaneCode(laneCode, updateEvent, eddAutoUpdateRule.IsCB == constant.TRUE)
	productInfo := &cdt_calculation.CdtProductInfo{
		QueryID:    "1",
		ProductID:  productID,
		IsCB:       uint8(orderInfo.CbFlag),
		IsSiteLine: constant.TRUE, // only for line site system
		Region:     region,
		SellerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.SellerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.SellerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.SellerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.SellerAddress.PostCode),
		},
		BuyerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.BuyerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.BuyerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.BuyerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.BuyerAddress.PostCode),
		},
		LaneCode: laneCode,
		LineList: lineList,

		UpdateEvent: updateEvent,
		NextEvent:   nextEventUpdate.Event,
	}
	lcosService := lcos_service.NewLCOSService(ctx, region)
	nowTime := utils.GetTimestamp(ctx)
	eddInfoReturned, lcosErr := lcosService.GetEddCalculationInfo(ctx, productInfo, orderInfo.ForderId, nowTime)
	if lcosErr != nil {
		return lcosErr
	}
	if eddInfoReturned.GetRespHeader().GetRetcode() != 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot find cdt info for product:[%s]", productInfo.ProductID)
	}

	// 2. call data sdk to get ddl
	ddlCDT := edd_pushing.GetDDLCDTFromEDDResponse(eddInfoReturned, nextEventUpdate.DeadlineMethod)
	ddl, lcosErr := callDataSDKToGetDDL(ctx, int64(nowTime), eddInfo.CleanedData.Edd, nextEventUpdate.DeadlineMethod, eddAutoUpdateRule.CheckpointFrequency, ddlCDT, region, eddInfoReturned.GetEddInfo().GetHoliday(), eddInfoReturned.GetEddInfo().GetWeekend())
	if lcosErr != nil {
		return lcosErr
	}

	// 3. generate edd waiting info
	waybill := edd_pushing.HandleWaybillDDL(ctx, productID, slsTN, buyerLocationID, ddlCDT, ddl, ddlUpdateTimes, updateEvent, nextEventUpdate.Event, region, task_api.GetTaskService().EddWaybillDelayQueue)
	eddInfo.CleanedData.WaitingWaybillInfo = waybill
	return nil
}

func (e *EddOnGoingPushingJob) handleEddWaybillConcurrent(ctx utils.LCOSContext, eddInfoList []*edd_update_task_conf.RawEDDOnGoingOrderInfo, productConfig map[string]*edd_auto_update_rule.EddAutoUpdateRule, lfsOrderInfoMap map[string]*lfs_service.LogisticOrderData, eddConfig config.EDDOnGoingConfig, region string) *lcos_error.LCOSError {

	logger.CtxLogInfof(ctx, "ready to handle ddl info")

	// 1. get waybill info
	laneInfoMap, lcosErr := e.GetOrderByWaybillCenterWithConcurrency(ctx, eddInfoList, eddConfig.WaybillCenterBatchSize, eddConfig.WaybillCenterMaxConcurrency, region)
	if lcosErr != nil {
		return lcosErr
	}

	// 2. handle ddl info
	maxCurrencyChan := make(chan struct{}, eddConfig.DDLHandleConcurrency)
	var waitGroup sync.WaitGroup
	for _, singlEDDInfo := range eddInfoList {
		if len(singlEDDInfo.ErrorMessage) > 0 {
			continue
		}
		tmpSlsTN := singlEDDInfo.CleanedData.SlsTN
		productID := singlEDDInfo.CleanedData.ProductID
		laneCodeInfo, ok1 := laneInfoMap[tmpSlsTN]
		lfsOrderInfo, ok2 := lfsOrderInfoMap[tmpSlsTN]
		productEddConfig, ok3 := productConfig[productID]

		if !(ok1 && ok2 && ok3) {
			continue
		}

		maxCurrencyChan <- struct{}{}
		waitGroup.Add(1)
		go func(tmpEddInfo *edd_update_task_conf.RawEDDOnGoingOrderInfo, tmpOrderInfo *lfs_service.LogisticOrderData, tmpLaneCodeInfo *waybill_center_service.LaneCodeInfo, tmpEddAutoUpdateRule *edd_auto_update_rule.EddAutoUpdateRule, region string) {
			if ddlLcosErr := e.handleSingleWaybillInfo(ctx, tmpEddInfo, tmpOrderInfo, tmpLaneCodeInfo, tmpEddAutoUpdateRule, region); ddlLcosErr != nil {
				// will not consider push to waybill center error as error
				logger.CtxLogErrorf(ctx, "sls_tn:[%s] handle edd ddl error:%s", tmpEddInfo.CleanedData.SlsTN, lcosErr.Msg)
			}
			<-maxCurrencyChan
			waitGroup.Done()
		}(singlEDDInfo, lfsOrderInfo, laneCodeInfo, productEddConfig, region)
	}

	waitGroup.Wait()
	return nil
}

func (e *EddOnGoingPushingJob) pushToWaybillCenterConcurrent(ctx utils.LCOSContext, eddInfoList []*edd_update_task_conf.RawEDDOnGoingOrderInfo, concurrent int, region string) *lcos_error.LCOSError {
	logger.CtxLogInfof(ctx, "ready to push to waybill center with concurrency:[%d]", concurrent)

	waybillCenterService := waybill_center_service.NewWaybillCenterService(ctx, region)

	maxCurrencyChan := make(chan struct{}, concurrent)
	var waitGroup sync.WaitGroup
	for _, singleRequest := range eddInfoList {
		if len(singleRequest.ErrorMessage) > 0 {
			continue
		}
		maxCurrencyChan <- struct{}{}
		waitGroup.Add(1)
		go func(request *edd_update_task_conf.RawEDDOnGoingOrderInfo) {
			if lcosErr := waybillCenterService.PushEDD(ctx, &waybill_center_service.PushEDDInfo{
				SlsTN: request.CleanedData.SlsTN,
				EDD:   request.CleanedData.Edd,
			}); lcosErr != nil {
				// will not consider push to waybill center error as error
				logger.CtxLogErrorf(ctx, "sls_tn:[%s] push to waybill center error:%s", request.CleanedData.SlsTN, lcosErr.Msg)
			}
			<-maxCurrencyChan
			waitGroup.Done()
		}(singleRequest)
	}

	waitGroup.Wait()

	return nil
}

func (e *EddOnGoingPushingJob) generateEDDPushingS3Report(ctx utils.LCOSContext, orderList []*edd_update_task_conf.RawEDDOnGoingOrderInfo, region string) (*schema.FileInfo, bool, bool, *lcos_error.LCOSError) {
	isAllFailed := true
	isAllSuccess := true

	templateFilePath := path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/edd", edd_constant.ExportCalculationResultTemplateName)

	// open template
	f, err := excelize.OpenFile(templateFilePath)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file: %s", templateFilePath)
		logger.CtxLogErrorf(ctx, errMsg)
		return nil, false, false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	// 在第一列写错误信息
	for index, exportData := range orderList {
		// 从第二行开始写
		// 解析时间为前端的格式
		var errorList []error
		if len(exportData.ErrorMessage) == 0 {
			isAllFailed = false // there is at least one is successful
		} else {
			isAllSuccess = false // there is at lease one failed
			style, fillErr := f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{"#FFFF00"},
					Pattern: 1,
				},
			})
			if fillErr != nil {
				logger.CtxLogErrorf(ctx, "cannot generate style:%s", fillErr.Error())
			} else {
				styleErr := f.SetCellStyle("Sheet1", fmt.Sprintf("A%d", index+2), fmt.Sprintf("Q%d", index+2), style)
				if styleErr != nil {
					logger.CtxLogErrorf(ctx, "cannot generate style:%s", styleErr.Error())
				}
			}
		}

		errorList = append(errorList,
			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", index+2), exportData.SLSTN),
			f.SetCellValue("Sheet1", fmt.Sprintf("B%d", index+2), exportData.LMTN),
			f.SetCellValue("Sheet1", fmt.Sprintf("C%d", index+2), exportData.OrderSN),
			f.SetCellValue("Sheet1", fmt.Sprintf("D%d", index+2), exportData.Product),
			f.SetCellValue("Sheet1", fmt.Sprintf("E%d", index+2), exportData.LineID),
			f.SetCellValue("Sheet1", fmt.Sprintf("F%d", index+2), exportData.ParcelStatus),
			f.SetCellValue("Sheet1", fmt.Sprintf("G%d", index+2), exportData.NewEddMin),
			f.SetCellValue("Sheet1", fmt.Sprintf("H%d", index+2), exportData.LastEddMin),
			f.SetCellValue("Sheet1", fmt.Sprintf("I%d", index+2), exportData.NewEddMax),
			f.SetCellValue("Sheet1", fmt.Sprintf("J%d", index+2), exportData.LastEddMax),
			f.SetCellValue("Sheet1", fmt.Sprintf("K%d", index+2), exportData.EddMinAdjustment),
			f.SetCellValue("Sheet1", fmt.Sprintf("L%d", index+2), exportData.EddMaxAdjustment),
			f.SetCellValue("Sheet1", fmt.Sprintf("M%d", index+2), exportData.EddMinNonWorkingDays),
			f.SetCellValue("Sheet1", fmt.Sprintf("N%d", index+2), exportData.EddMinNonWorkingDaysList),
			f.SetCellValue("Sheet1", fmt.Sprintf("O%d", index+2), exportData.EddMaxNonWorkingDays),
			f.SetCellValue("Sheet1", fmt.Sprintf("P%d", index+2), exportData.EddMaxNonWorkingDaysList),
			f.SetCellValue("Sheet1", fmt.Sprintf("Q%d", index+2), exportData.EddDiffThreshold),
			f.SetCellValue("Sheet1", fmt.Sprintf("R%d", index+2), exportData.EddMinDiff),
			f.SetCellValue("Sheet1", fmt.Sprintf("S%d", index+2), exportData.EddMaxDiff),
			f.SetCellValue("Sheet1", fmt.Sprintf("T%d", index+2), exportData.CurrentTimesForEDDUpdate),
			f.SetCellValue("Sheet1", fmt.Sprintf("U%d", index+2), exportData.RemainingTimesForEDDUpdate),
			f.SetCellValue("Sheet1", fmt.Sprintf("V%d", index+2), exportData.TriggerFirstEDDCalculation),
			f.SetCellValue("Sheet1", fmt.Sprintf("W%d", index+2), exportData.ErrorMessage),
			f.SetCellValue("Sheet1", fmt.Sprintf("X%d", index+2), exportData.EddProcess),
		)

		for _, err1 := range errorList {
			if err1 != nil {
				errMsg := fmt.Sprintf("cannot write error msg to file|error_msg=%s", err1.Error())
				logger.CtxLogErrorf(ctx, errMsg)
				return nil, false, false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
			}
		}
	}

	// 以当前的时间戳生成临时文件
	tmpFileName := fmt.Sprintf("/tmp/%s-%s.xlsx", "edd-pushing-result", pickup.GetCurrentTime(ctx, region).Format("20060102150405"))

	// 将空格替换为中划线，防止文件名可能存在的空格，导致文件无法导出
	reg := regexp.MustCompile(" +")
	tmpFileName = reg.ReplaceAllString(tmpFileName, "-")

	err = f.SaveAs(tmpFileName)
	if err != nil {
		logger.CtxLogErrorf(ctx, err.Error())
		return nil, false, false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	// 上传文件
	fileUrl, fileName, fileSizeStr, lcosErr := e.s3Service.UploadFileToS3(ctx, config.GetConf(ctx).BasicServiceReportS3Config.AccessKeyID, config.GetConf(ctx).BasicServiceReportS3Config.BucketKey, tmpFileName, config.GetConf(ctx).BasicServiceReportS3Config.TimeOut, config.GetConf(ctx).BasicServiceReportS3Config.ExpirationDays, edd_constant.RootEDDDirname)
	return &schema.FileInfo{
		FileUrl:      fileUrl,
		FileName:     fileName,
		FileSize:     fileSizeStr,
		TempFileName: tmpFileName,
	}, isAllSuccess, isAllFailed, lcosErr
}

func (e *EddOnGoingPushingJob) generateErrorS3Report(ctx utils.LCOSContext, lcosErr *lcos_error.LCOSError, region string) (*schema.FileInfo, *lcos_error.LCOSError) {
	templateFilePath := path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/edd", edd_constant.ExportErrorTemplateName)

	// open template
	f, err := excelize.OpenFile(templateFilePath)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file: %s", templateFilePath)
		logger.CtxLogErrorf(ctx, errMsg)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	err = f.SetCellValue("Sheet1", fmt.Sprintf("A%d", 2), lcosErr.Msg)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	// 以当前的时间戳生成临时文件
	tmpFileName := fmt.Sprintf("/tmp/%s-%s.xlsx", "error-result", pickup.GetCurrentTime(ctx, region).Format("20060102150405"))

	// 将空格替换为中划线，防止文件名可能存在的空格，导致文件无法导出
	reg := regexp.MustCompile(" +")
	tmpFileName = reg.ReplaceAllString(tmpFileName, "-")

	err = f.SaveAs(tmpFileName)
	if err != nil {
		logger.CtxLogErrorf(ctx, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	// 上传文件
	fileUrl, fileName, fileSizeStr, lcosErr := e.s3Service.UploadFileToS3(ctx, config.GetConf(ctx).BasicServiceReportS3Config.AccessKeyID, config.GetConf(ctx).BasicServiceReportS3Config.BucketKey, tmpFileName, config.GetConf(ctx).BasicServiceReportS3Config.TimeOut, config.GetConf(ctx).BasicServiceReportS3Config.ExpirationDays, edd_constant.RootEDDDirname)
	return &schema.FileInfo{
		FileUrl:  fileUrl,
		FileName: fileName,
		FileSize: fileSizeStr,
	}, lcosErr
}

func (e *EddOnGoingPushingJob) eddOngoingPushing(ctx utils.LCOSContext, eddInfoListUrl string, eddTaskID uint64, region string, calculationStartTime int64) (*schema.FileInfo, *lcos_error.LCOSError) {
	// get config
	eddConfig := config.GetEDDOnGoingConfig(ctx)

	// 1. download from uss server
	filePath, lcosErr := serviceable_util.DownloadFileFromS3(ctx, eddInfoListUrl)
	if lcosErr != nil {
		return nil, lcosErr
	}
	logger.CtxLogInfof(ctx, "successfully download file url:[%s] from S3 service", eddInfoListUrl)

	// 2. parse s3 file
	f, err1 := excelize.OpenFile(filePath)
	if err1 != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err1.Error())
	}
	eddInfoList, lcosErr := parseEDDInfoList(f)
	if lcosErr != nil {
		return nil, lcosErr
	}
	logger.CtxLogInfof(ctx, "successfully parse file url:[%s] ", eddInfoListUrl)

	var productList []string
	productMap := make(map[string]*edd_update_rule_conf.EddUpdateRuleConfigTab)
	productExistMap := make(map[string]bool)
	for _, singleEdd := range eddInfoList {
		if _, ok := productExistMap[singleEdd.Product]; !ok {
			productList = append(productList, singleEdd.Product)
			productExistMap[singleEdd.Product] = true
		}
	}
	productRuleList, lcosErr := e.eddUpdateRuleConfDao.SearchEDDUpdateRuleConf(ctx, map[string]interface{}{"product_id in": productList})
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, singleProduct := range productRuleList {
		productMap[singleProduct.ProductID] = singleProduct
	}

	// 3. check edd history， check whether has edd push between calculatingStartTime to now
	if lcosErr = e.FilterEDDInfoByCalculatingResult(ctx, eddInfoList, calculationStartTime, eddTaskID, region); lcosErr != nil {
		return nil, lcosErr
	}
	// for trigger first calculation, need to call lfs to get info
	var lfsOrderInfoMap map[string]*lfs_service.LogisticOrderData
	if lfsOrderInfoMap, lcosErr = e.FilterEDDInfoByCalculatingResultForFirstEDDCalculation(ctx, eddInfoList, region); lcosErr != nil {
		return nil, lcosErr
	}

	// if channel support hour level ，need to adjust edd min = edd max and edd value to hour level
	hourLevelEDDChannelMap := config.GetHourLevelEDDChannelList(ctx)
	for _, info := range eddInfoList {
		if _, ok := hourLevelEDDChannelMap[info.CleanedData.ProductID]; ok && info.CleanedData.Edd != 0 {
			eddTime := time.Unix(info.CleanedData.Edd, 0).In(utils.GetTimezoneLocation(region))
			info.CleanedData.Edd = GetNextHourDateTime(eddTime, region).Unix()
			info.CleanedData.EddMin = info.CleanedData.Edd
		}
	}

	// 4. validate edd info
	nowTime := utils.GetTimestamp(ctx)
	if lcosErr = e.validateEddInfo(ctx, int64(nowTime), productMap, eddInfoList); lcosErr != nil {
		return nil, lcosErr
	}
	logger.CtxLogInfof(ctx, "successfully validate edd info")

	// 5. try to push to oms
	if lcosErr = e.pushToOmsWithConcurrency(ctx, eddInfoList, eddConfig.OMSMaxConcurrency, region); lcosErr != nil {
		return nil, lcosErr
	}
	logger.CtxLogInfof(ctx, "successfully push to oms")

	// SPLN-31383 update ddl info
	productEddMap := make(map[string]*edd_auto_update_rule.EddAutoUpdateRule)
	eddAutoRuleList, lcosErr := e.eddAutoUpdateDao.ListAllEddAutoUpdateRules(ctx, map[string]interface{}{"product_id in": productList, "enable_status": constant.TRUE})
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, singleEddProduct := range eddAutoRuleList {
		productEddMap[singleEddProduct.ProductId] = singleEddProduct
	}
	if lcosErr = e.handleEddWaybillConcurrent(ctx, eddInfoList, productEddMap, lfsOrderInfoMap, eddConfig, region); lcosErr != nil { // use the concurrency of oms
		return nil, lcosErr
	}

	// 6. save in data
	if lcosErr = e.pushToDataWithConcurrency(ctx, eddInfoList, productMap, eddConfig.DataMaxConcurrency, region); lcosErr != nil {
		return nil, lcosErr
	}
	logger.CtxLogInfof(ctx, "successfully push to data")

	// 7. generate s3 file
	fileInfo, isAllSuccess, isAllFailed, lcosErr := e.generateEDDPushingS3Report(ctx, eddInfoList, region)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if isAllFailed {
		return fileInfo, lcos_error.NewLCOSError(lcos_error.EDDOnGoingPushingAllFailed, "push edd all failed")
	}
	if !isAllSuccess {
		return fileInfo, lcos_error.NewLCOSError(lcos_error.EDDOnGoingPushingPartialSuccess, "push edd partial success")
	}
	return fileInfo, nil
}

func (e *EddOnGoingPushingJob) doEddOngoingPushing(ctx utils.LCOSContext, data *schema.PushingEventData) {
	defer func() {
		if e := recover(); e != nil {
			stack := utils.Stack(5)
			errMsg := fmt.Sprintf("err:%s, stack:%s", e, debug.Stack())
			_ = monitor.ReportEvent(constant.CatModulePanic, constant2.EDDPushingTask, constant.StatusPanic, errMsg)
			logger.CtxLogErrorf(ctx, "[Recovery]panic recovered:\n%s\n%s\n%s", e, stack, debug.Stack())
		}
	}()

	region := strings.ToUpper(utils.GetCID())
	var tempFileName string

	pushingStartTime := utils.GetTimestamp(ctx)
	fileInfo, lcosErr := e.eddOngoingPushing(ctx, data.EDDInfoListUrl, data.EDDTaskID, region, data.EddTaskConf.CalculationStartTime)
	tools.MonitorPushEddTask(ctx, saturnConstant.EDDPushingTask, "", 0, lcosErr)
	pushEndTime := utils.GetTimestamp(ctx)

	updatedMap := map[string]interface{}{
		"pushing_start_time": pushingStartTime,
		"pushing_end_time":   pushEndTime,
	}

	if fileInfo != nil {
		tempFileName = fileInfo.TempFileName
	}
	if lcosErr != nil {
		if lcosErr.RetCode == lcos_error.EDDOnGoingPushingAllFailed { // all sls tn failed, update task to computing failed, and related file info
			updatedMap["pushing_status"] = edd_constant.EDDPushingFailed
			updatedMap["pushing_result"] = fileInfo.FileUrl // nolint
		} else if lcosErr.RetCode == lcos_error.EDDOnGoingPushingPartialSuccess {
			updatedMap["pushing_status"] = edd_constant.EDDPushingPartialSuccess
			updatedMap["pushing_result"] = fileInfo.FileUrl // nolint
		} else {
			updatedMap["pushing_status"] = edd_constant.EDDPushingFailed
			errorFileInfo, uploadErr := e.generateErrorS3Report(ctx, lcosErr, region)
			if uploadErr != nil {
				logger.CtxLogErrorf(ctx, "cannot upload error file, error=%s", uploadErr.Msg)
			} else {
				updatedMap["pushing_result"] = errorFileInfo.FileUrl
				tempFileName = errorFileInfo.TempFileName
			}
		}
	} else {
		updatedMap["pushing_status"] = edd_constant.EDDPushingSuccess
		updatedMap["pushing_result"] = fileInfo.FileUrl
	}

	models, dbErr := e.eddUpdateTaskConfDao.SearchEDDUpdateTaskConf(ctx, map[string]interface{}{"id": data.EDDTaskID})
	if dbErr == nil && len(models) > 0 {
		operatorList := []string{models[0].Operator}
		if data.Operator != models[0].Operator && len(data.Operator) > 0 {
			updatedMap["operator"] = data.Operator
			operatorList = append(operatorList, data.Operator)
		}

		// update models info
		models[0].PushingStatus = uint8(updatedMap["pushing_status"].(int))
		models[0].PushingResult = updatedMap["pushing_result"].(string)

		emailError := e.SendMail(ctx, models[0], region, operatorList, tempFileName)
		if emailError != nil {
			logger.CtxLogErrorf(ctx, emailError.Msg)
		}
	}

	updateError := e.eddUpdateTaskConfDao.UpdateEDDUpdateTaskConf(ctx, map[string]interface{}{"id": data.EDDTaskID}, updatedMap)
	if updateError != nil {
		logger.CtxLogErrorf(ctx, "update edd task conf failed:[%s]", updateError.Msg)
	}

}

func (e *EddOnGoingPushingJob) Name() string {
	return saturnConstant.EDDPushingTask
}

func (e *EddOnGoingPushingJob) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	req := &schema.PushingEventData{}
	err := jsoniter.Unmarshal(message.MsgText, req)
	if err != nil {
		errMsg := fmt.Sprintf("message unmarshal failed | msg_text=%s | err=%s", string(message.MsgText), err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{
			Retcode: -1,
			Message: errMsg,
		}
	}
	ctx = utils.NewLogContext(ctx, req.RequestID)
	lcosCtx := utils.NewCommonCtx(ctx)
	e.doEddOngoingPushing(lcosCtx, req)
	return &saturn.SaturnReply{
		Retcode: int(lcos_error.SuccessCode),
		Message: "success",
	}
}

func NewEDDOnGoingPushJob() *EddOnGoingPushingJob {
	return &EddOnGoingPushingJob{
		eddHistoryDao:        task_api.GetTaskService().EddHistoryDao,
		eddUpdateRuleConfDao: task_api.GetTaskService().EddUpdateRuleConfDao,
		eddUpdateTaskConfDao: task_api.GetTaskService().EddUpdateTaskConfDao,
		s3Service:            task_api.GetTaskService().S3Service,
		eddAutoUpdateDao:     task_api.GetTaskService().EddAutoUpdateConfDao,
	}
}

func parseStringToRTimestamp(dateString string, region string) (int64, error) {
	dateString = strings.TrimSpace(dateString)
	if len(dateString) <= 0 {
		return 0, nil
	}
	location, err := time.ParseInLocation("2006-01-02 15:04:05", dateString, utils.GetTimezoneLocation(region))
	if err != nil {
		return 0, err
	}
	return location.Unix(), nil
}

func GetNextHourDateTime(current time.Time, region string) time.Time {
	if current.Minute() == 0 && current.Second() == 0 && current.Nanosecond() == 0 { // 对应整点，此时不需要加1小时
		return current
	}
	return time.Date(current.Year(), current.Month(), current.Day(), current.Hour()+1, 0, 0, 0, utils.GetTimezoneLocation(region))
}
