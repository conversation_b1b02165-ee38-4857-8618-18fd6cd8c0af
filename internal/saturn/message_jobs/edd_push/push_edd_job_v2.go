package edd_push

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	saturnConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	jsoniter "github.com/json-iterator/go"
)

/*
SPLN-28290
Using the same business logic, but different parse logic comparing to the previous version
previous topic: sls-lts-push-standard-tracking-event-{env}-{cid}
new topic: ssc-sls-tracking-event-{env}-{cid}
*/

type eddPushJob struct {
	lmEddPushJob *LmEDDPushJob
}

func transferTextToStruct(data []byte) (*schema.StandardTrackingEventData, error) {
	req := &schema.TriggerEvent{}
	err := jsoniter.Unmarshal(data, req)
	if err != nil {
		return nil, err
	}
	return transferToOldStruct(req)
}

func transferToOldStruct(req *schema.TriggerEvent) (*schema.StandardTrackingEventData, error) {
	if req.MsgContentValue == nil || req.TriggerRuleValue == nil {
		return nil, errors.New("message is not valid")
	}

	return &schema.StandardTrackingEventData{
		EventId:   req.MsgId,
		EventType: EDDEvent,
		EventTime: req.MsgTs,
		Data: &schema.LatestTrackingUpdateEventData{
			SloId: req.MsgContentValue.SloID,
			SlsTn: req.MsgContentValue.SloTN,
			Trackings: []*schema.Tracking{
				{
					ResourceId:        req.TriggerRuleValue.ResourceId,
					TrackingCode:      req.TriggerRuleValue.TrackingCode,
					Description:       req.MsgContentValue.TrackingDesc,
					ResourceStatus:    req.TriggerRuleValue.ResourceStatus,
					ResourceSubStatus: req.TriggerRuleValue.ResourceSubStatus,
					ActualTime:        req.TriggerRuleValue.UpdateTime,
				},
			},
			ClientId: req.TriggerRuleValue.ClientId,
		},
	}, nil
}

func (l *eddPushJob) Name() string {
	return saturnConstant.EDDTask
}

func (b *eddPushJob) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	// parse the string into struct first
	newReq, err := transferTextToStruct(message.MsgText)
	if err != nil {
		errMsg := fmt.Sprintf("message unmarshal failed | msg_text=%s | err=%s", string(message.MsgText), err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{
			Retcode: -1,
			Message: errMsg,
		}
	}

	if !needHandleMsg(ctx, newReq) {
		logger.CtxLogInfof(ctx, "check no need handle msg | msg_text=%s", string(message.MsgText))
		return &saturn.SaturnReply{
			Retcode: int(lcos_error.SuccessCode),
			Message: "no need handle",
		}
	}

	logger.CtxLogInfof(ctx, "ready to deal with sls tn|sls_tn=[%s]", newReq.Data.SlsTn)
	message.MsgText = []byte(utils.MarshToStringWithoutError(newReq))
	saturnReply := b.lmEddPushJob.MsgHandle(ctx, message)
	return saturnReply
}

func NewEDDPushJob() *eddPushJob {
	return &eddPushJob{
		lmEddPushJob: NewLMEDDPushJob(),
	}
}

func needHandleMsg(ctx context.Context, req *schema.StandardTrackingEventData) bool {
	if req == nil {
		logger.CtxLogErrorf(ctx, "check need handle msg fail, req is nil")
		return false
	}

	// open cb 单不触发 edd 计算
	if req.Data != nil && req.Data.ClientId == OpenCbOrderClientId {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "NoNeedHandle_OpenCbOrder", constant.StatusSuccess, "")
		logger.CtxLogInfof(ctx, "open cb order no need handle, slo_id:[%d], sls_tn:[%s]", req.Data.SloId, req.Data.SlsTn)
		return false
	}

	return true
}
