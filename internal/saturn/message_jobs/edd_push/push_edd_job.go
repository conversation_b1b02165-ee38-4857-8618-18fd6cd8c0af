package edd_push

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/tools"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_pushing"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"

	edd_auto_update "git.garena.com/shopee/bg-logistics/algo/sls/edd-auto-update"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/task_api"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache_with_ttl"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/edd_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	saturnConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_delay_queue"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcos_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lts_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/oms_service"
	jsoniter "github.com/json-iterator/go"
)

const EDDEvent = 1000
const TCOutboundWhs = "F299"
const noUsedId = 0

var productEDDAutoUpdateRuleLruCache *cache.LruCache

func init() {
	productEDDAutoUpdateRuleLruCache, _ = cache.NewLruCache(cache.ProductEddAutoUpdateLruName, &edd_auto_update_rule.EddAutoUpdateRule{})
}

type LmEDDPushJob struct {
	eddAutoUpdateDao edd_auto_update_rule.EddAutoUpdateRuleDao

	delayQueueService edd_delay_queue.EddWaybillDelayQueue
}

func (b *LmEDDPushJob) getProductEddConf(ctx utils.LCOSContext, productID string) (*edd_auto_update_rule.EddAutoUpdateRule, *lcos_error.LCOSError) {
	cacheKey := productID
	if cacheVal, ok1 := productEDDAutoUpdateRuleLruCache.Get(ctx, cacheKey); ok1 {
		if cacheResult, ok2 := cacheVal.(edd_auto_update_rule.EddAutoUpdateRule); ok2 {
			logger.CtxLogInfof(ctx, "successfully get cached product EddAutoUpdateRule by cache key:[%s]|rule=[%s]", cacheKey, utils.MarshToStringWithoutError(cacheResult))
			if cacheResult.Id != 0 {
				return &cacheResult, nil
			} else {
				return nil, nil
			}
		}
	}

	rules, lcosErr := b.eddAutoUpdateDao.ListAllEddAutoUpdateRules(ctx, map[string]interface{}{"product_id": productID, "enable_status": constant.ENABLED})
	if lcosErr != nil {
		return nil, lcosErr
	}
	// will not consider as error when cannot find product level edd auto update rule
	if len(rules) == 0 {
		productEDDAutoUpdateRuleLruCache.Add(ctx, cacheKey, edd_auto_update_rule.EddAutoUpdateRule{})
		return nil, nil
	}
	productEDDAutoUpdateRuleLruCache.Add(ctx, cacheKey, *rules[0])
	return rules[0], nil
}

// HandleEDDTask will handle two ways edd update
// for edd auto update, need sls tn, errorTrackingCodeList, addedTracking, edd update type
// for edd preemptive update, need sls tn, edd update type, update event
func (b *LmEDDPushJob) HandleEDDTask(ctx utils.LCOSContext, slsTN string, errorTrackingCodeList []string, addedTracking []*schema.Tracking, eddUpdateType edd_auto_update.EddUpdateType, preemptiveUpdateEvent uint8) *lcos_error.LCOSError {

	// 3. get history tracking to check tws outbound event and whether contains error code
	region := strings.ToUpper(utils.GetCID())
	ltsService, lcosErr := lts_service.NewLTSService(region)
	if lcosErr != nil {
		lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
		return lcosErr
	}
	historyTrackings, lcosErr := ltsService.RetrieveTracking(ctx, slsTN, region)
	if lcosErr != nil {
		lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode // need to retry
		return lcosErr
	}
	sort.SliceStable(historyTrackings, func(i, j int) bool {
		return historyTrackings[i].ActualTime < historyTrackings[j].ActualTime
	})
	if lcosErr = edd_pushing.VerifyHistoryTracking(ctx, historyTrackings, errorTrackingCodeList); lcosErr != nil {
		return lcosErr
	}

	// 4. get lfs order to get product info
	orderInfo, lcosErr := edd_pushing.FetchLfsOrderInfo(ctx, region, slsTN)
	if lcosErr != nil {
		lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
		return lcosErr
	}

	// 5. get lane code
	laneInfo, lcosErr := edd_pushing.FetchWaybillLaneInfo(ctx, region, slsTN)
	if lcosErr != nil {
		lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
		return lcosErr
	}
	var laneCode string
	var lineList []*cdt_calculation.LineInfo
	if config.GetLaneCodeEnabledFlag(ctx) {
		laneCode = laneInfo.LaneCode
		laneCode = strings.ReplaceAll(laneCode, "/", "|")
	}
	lineList = laneInfo.GetFollowingLineIDList()
	deliverBuyerID := laneInfo.DeliverUserID

	// 如果是Direct Delivery渠道，令EDD=EDT，并只需要更新一次
	if edd_pushing.IsDirectDeliveryProduct(ctx, orderInfo.LogisticProductId) {
		return pushDirectDeliveryEdd(ctx, region, orderInfo, addedTracking, historyTrackings, slsTN, eddUpdateType, preemptiveUpdateEvent)
	}

	// 获取edd auto update rule
	eddAutoUpdateRule, lcosErr := b.getProductEddConf(ctx, orderInfo.LogisticProductId)
	if lcosErr != nil {
		lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
		return lcosErr
	}

	// 尝试使用 AlgoEddJob 进行检查开关，调用 algo 预测 edd
	eddService, lcosErr := edd_pushing.NewEDDJobByAlgo(ctx, addedTracking, historyTrackings, orderInfo, slsTN, eddUpdateType, preemptiveUpdateEvent, lineList, laneInfo.IsReturnOrder(), eddAutoUpdateRule)
	if lcosErr != nil {
		return lcosErr
	}

	// 直接获取 algo 结果，判断是否需要兜底旧流程
	algoEdd, algoErr := eddService.SwitchGetEDDInfoByAlgo(ctx, region, laneCode, deliverBuyerID, lineList)
	tools.MonitorPushEddJob(ctx, strconv.Itoa(int(eddUpdateType)), eddService.GetUpdateEventString(), eddService.GetEddRetryTimes(ctx), orderInfo.LogisticProductId, constant.EddPushMonitor_AlgoEddPushField, algoErr)
	// 调用 algo 失败，则兜底走旧流程
	if algoErr != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDAlgo, "SwitchGetEDDInfoByAlgo", algoErr.Msg, algoErr.Msg)

		// 判断是否需要重试(algo 特定错误码允许重试)
		if algoErr.RetCode == lcos_error.SaturnNeedToRetryErrorCode {
			return algoErr
		}

		// Reverse EDD 不需要走旧流程，直接报错
		if eddService.GetAlgoReverseFlag() {
			return algoErr
		}

		// 不需要重试的错误，兜底走旧流程
		eddService, lcosErr = edd_pushing.NewEDDJob(ctx, addedTracking, historyTrackings, orderInfo, eddAutoUpdateRule, slsTN, eddUpdateType, preemptiveUpdateEvent)
		if lcosErr != nil {
			return lcosErr
		}
	} else {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDAlgo, "SwitchGetEDDInfoByAlgo", constant.StatusSuccess, "")
	}

	// 6. get event time of update event
	actualTime, lcosErr := eddService.GetEventTime(ctx)
	if lcosErr != nil {
		return lcosErr
	}

	// 7. check if edd or lm edd has been pushed
	lcosErr = eddService.CheckCanPushEDD(ctx)
	if lcosErr != nil {
		return lcosErr
	}
	lcosErr = eddService.CheckCanHandleWaitingWaybill(ctx, laneInfo, region, b.delayQueueService)
	if lcosErr != nil {
		return lcosErr
	}

	// 9. call lcos to calculate edd
	var eddInfoParsed *lcos_service.EddInfo
	if algoEdd != nil {
		// 切换 Algo 模型预测 EDD
		eddInfoParsed = algoEdd
	} else {
		laneCode = edd_pushing.DealWithLaneCode(laneCode, eddService.GetUpdateEvent(), orderInfo.CbFlag == int32(constant.TRUE))
		eddInfoParsed, lcosErr = eddService.SwitchGetEDDInfoByDataSDK(ctx, region, laneCode, actualTime, deliverBuyerID, lineList)
		tools.MonitorPushEddJob(ctx, strconv.Itoa(int(eddUpdateType)), eddService.GetUpdateEventString(), eddService.GetEddRetryTimes(ctx), orderInfo.LogisticProductId, constant.EddPushMonitor_StaticsEddPushField, lcosErr)
		if lcosErr != nil {
			return lcosErr
		}
	}

	// 9.1 first edd limit by edt
	lcosErr = eddService.EddFirstPushLimit(ctx, region, eddInfoParsed)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "edd first push limit err is:+v", lcosErr)
	}

	// 10. check and get edd for pushing
	eddMin, eddMax, updateType, err := eddService.CheckAndGetEddForPushing(ctx, eddInfoParsed.GetEddMin(), eddInfoParsed.GetEddMax(), region, deliverBuyerID, orderInfo.LogisticProductId)
	if err != nil && !edd_pushing.IsDDLOnlyError(err) {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "AutoUpdateStatus", constant.StatusEddCheckError, err.Msg)
		return err
	}

	if err == nil {
		if pErr := pushEddToOms(ctx, slsTN, orderInfo.ForderId, region, eddMin, eddMax, eddService); pErr != nil {
			return pErr
		}
	}

	if eddInfoParsed.DDLAvailable() && !edd_util.CheckCanUpdateEddMax(updateType) && !eddService.GetAlgoFlag() { // algo 流程不需要
		// re-calculate ddl with current edd when cannot update edd max
		ddlOnlyEddInfoParsed, lcosErr := eddService.CallDataSDK(ctx, eddInfoParsed.CdtProductInfo, eddInfoParsed.EddCalculationInfo, actualTime, region, true)
		if lcosErr != nil {
			return lcosErr
		}
		eddInfoParsed.DDLCdt = ddlOnlyEddInfoParsed.GetDDLCdt()
		eddInfoParsed.DDL = ddlOnlyEddInfoParsed.GetDDL()
	}

	// 12. add ddl to delay queue, will not retry, even when failed
	waitingWaybill := eddService.HandleWaybillDDL(ctx, eddInfoParsed.GetDDLCdt(), eddInfoParsed.GetDDL(), region, b.delayQueueService)

	// 13. store edd
	eddRecord, sErr := storeEdd(ctx, eddMin, eddMax, eddService, eddInfoParsed.GetEddProcess(), updateType, waitingWaybill, deliverBuyerID, err)
	if sErr != nil {
		return sErr
	}

	// 14. sync edd to data (optional)
	if err == nil {
		syncEddToData(ctx, eddRecord, eddMax, actualTime, orderInfo.LogisticProductId, eddService.GetUpdateEventString())
	} else {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "AutoUpdateStatus", constant.StatusEddCheckError, err.Msg)
	}

	return err
}

// full push lm edd logic
func (b *LmEDDPushJob) pushLMEDDTask(ctx utils.LCOSContext, data *schema.StandardTrackingEventData) *lcos_error.LCOSError {
	if data.Data == nil || len(data.Data.Trackings) == 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "not find valid trackings")
	}

	pushedTrackingInfoStr, _ := jsoniter.MarshalToString(data)

	if data.EventType != EDDEvent {
		errMsg := "event type is not edd, skip"
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}

	logger.CtxLogInfof(ctx, "ready to deal with tracking list:[%s]", pushedTrackingInfoStr)

	errorTrackingList := config.GetNewErrorTrackingCodeList(ctx)
	logger.CtxLogInfof(ctx, "configured error tracking code is:[%s]", strings.Join(errorTrackingList, ","))

	// 1. check whether new added tracking is error code
	if lcosErr := edd_pushing.VerifyTrackingList(ctx, data.Data.Trackings, errorTrackingList); lcosErr != nil {
		return lcosErr
	}
	// 2. check whether new added tracking code is edd event
	if lcosErr := edd_pushing.CheckIsEddTrackingEvent(ctx, data.Data.Trackings); lcosErr != nil {
		return lcosErr
	}
	return b.HandleEDDTask(ctx, data.Data.SlsTn, errorTrackingList, data.Data.Trackings, edd_auto_update.UpdateOnEvent, 0)
}

// lm edd task
func (b *LmEDDPushJob) doLMEDDPushTask(ctx utils.LCOSContext, data *schema.StandardTrackingEventData) *lcos_error.LCOSError {

	defer func() {
		if e := recover(); e != nil {
			stack := utils.Stack(5)
			errMsg := fmt.Sprintf("err:%s, stack:%s", e, debug.Stack())
			_ = monitor.ReportEvent(constant.CatModulePanic, saturnConstant.LMEDDTask, constant.StatusPanic, errMsg)
			logger.CtxLogErrorf(ctx, "[Recovery]panic recovered:\n%s\n%s\n%s", e, stack, debug.Stack())
		}
	}()

	logger.CtxLogInfof(ctx, "ready to run push edd task|slo_id=%s", data.Data.SlsTn)

	lcosErr := b.pushLMEDDTask(ctx, data)
	if lcosErr != nil {
		logger.CtxLogInfof(ctx, "failed to push edd task, error=%s, err_code=%d", lcosErr.Msg, lcosErr.RetCode)
		return lcosErr
	} else {
		logger.CtxLogInfof(ctx, "finish pushing edd task|slo_id=%s", data.Data.SlsTn)
		return nil
	}
}

func pushEddToOms(ctx utils.LCOSContext, slsTN string, forderId uint64, region string, eddMin, eddMax int64, eddService *edd_pushing.EDDJob) *lcos_error.LCOSError {
	// 11. for request to call oms
	omsReq := &oms_service.UpdateEddInfoReq{
		ConsignmentNo:   slsTN,
		ForderId:        forderId,
		Country:         region,
		EddMax:          eddMax,
		EDDUpdateReason: EDDUpdateReasonSystem,
	}
	if eddService != nil && eddService.EddMinAvailable() {
		// only push edd min when available
		omsReq.EddMin = eddMin
	}
	if eddService != nil && eddService.GetAlgoReverseFlag() {
		omsReq.EddType = EDDTypeReverse
	}
	omsService := oms_service.NewOmsService(ctx, region)
	lcosErr := omsService.UpdateEDDInfo(ctx, omsReq)
	if lcosErr != nil {
		lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "AutoUpdateStatus", constant.StatusEddPushError, lcosErr.Msg)
		return lcosErr
	}
	return nil
}

func storeEdd(ctx utils.LCOSContext, eddMin, eddMax int64, eddService *edd_pushing.EDDJob, eddProcess *edd_history.EddProcess, updateType uint8, eddWaybill *edd_delay_queue.EddWaybill, deliverBuyerID uint64, err *lcos_error.LCOSError) (*edd_history.EddHistoryTab, *lcos_error.LCOSError) {
	eddRecord, lcosErr := eddService.GenerateEDDHistory(ctx, eddMin, eddMax, eddProcess, updateType, eddWaybill, deliverBuyerID, edd_pushing.IsDDLOnlyError(err), constant.FALSE)
	if lcosErr != nil {
		logger.CtxLogInfof(ctx, "cannot generate edd history, err=[%s]|product_id=[%s],sls_tn=[%s], update_event=[%s]", lcosErr.Msg, eddService.GetProductID(), eddService.GetSlsTN(), eddService.GetUpdateEventString())
	} else {
		if lcosErr = eddService.StoreEDDInfo(ctx, eddRecord); lcosErr != nil {
			lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
			_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "AutoUpdateStatus", constant.StatusEddSaveHistoryError, lcosErr.Msg)
			return nil, lcosErr
		}
	}

	return eddRecord, nil
}

func syncEddToData(ctx utils.LCOSContext, eddRecord *edd_history.EddHistoryTab, eddMax, actualTime int64, productId, updateEvent string) {
	go edd_pushing.HandleDataSync(ctx, eddRecord)

	_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "AutoUpdateStatus", constant.StatusSuccess, "success")

	// report EDD-Pickup Done time
	diffDaysBetweenPickupDoneAndEDD := float64(eddMax-actualTime) / 24 / 3600
	_ = metrics.GaugeSet(constant.MetricsEDDReport, diffDaysBetweenPickupDoneAndEDD, map[string]string{"product_id": productId, "update_event": updateEvent})
	_ = metrics.CounterIncr(constant.MetricsEDDCounterReport, map[string]string{"product_id": productId, "update_event": updateEvent})

}

func pushDirectDeliveryEdd(
	ctx utils.LCOSContext,
	region string,
	orderInfo *lfs_service.LogisticOrderData,
	addedTrackingList []*schema.Tracking,
	historyTrackingList []*lts_service.TrackingData,
	slsTN string,
	eddUpdateType edd_auto_update.EddUpdateType,
	preemptiveUpdateEvent uint8) *lcos_error.LCOSError {

	// 1. 判断是否已推送过EDD
	eddJob, lcosErr := edd_pushing.NewEDDJob(ctx, addedTrackingList, historyTrackingList, orderInfo, nil, slsTN, eddUpdateType, preemptiveUpdateEvent)
	if lcosErr != nil {
		return lcosErr
	}
	actualTime, lcosErr := eddJob.GetEventTime(ctx)
	if lcosErr != nil {
		return lcosErr
	}
	eddJob.LoadEDDHistories(ctx)
	if len(eddJob.GetNonDDLOnlyEDDHistories()) != 0 {
		logger.CtxLogInfof(ctx, "already pushed edd for direct delivery product|region=%s, sls_tn=%s", region, slsTN)
		return nil
	}

	// 2. 调用Order获取EDT和SBD，令EDD=max(EDT, SBD)
	_, eddMax, err := eddJob.GetEDDInfoForDirectDelivery(ctx, region, orderInfo.OrderSN)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get edd for direct delivery product error|region=%s, sls_tn=%s, order_sn=%s, cause=%s", region, slsTN, orderInfo.OrderSN, err.Msg)
		return err
	}

	// 3. 推送EDD结果到OMS
	if pushErr := pushEddToOms(ctx, slsTN, orderInfo.ForderId, region, 0, eddMax, nil); pushErr != nil {
		logger.CtxLogErrorf(ctx, "push direct delivery edd to oms err:%v", pushErr)
		return pushErr
	}
	logger.CtxLogInfof(ctx, "push direct delivery edd to oms done, forder id:%d, max edt:%d", orderInfo.ForderId, eddMax)

	// 4. 存EDD history
	eddRecord, storeErr := storeEdd(ctx, 0, eddMax, eddJob, nil, edd_constant.UpdateEddMax, nil, noUsedId, nil)
	if storeErr != nil {
		return storeErr
	}

	// 5. Sync Data
	syncEddToData(ctx, eddRecord, eddMax, actualTime, orderInfo.LogisticProductId, eddJob.GetUpdateEventString())
	return nil
}

func (b *LmEDDPushJob) Name() string {
	return saturnConstant.LMEDDTask
}

func (b *LmEDDPushJob) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	req := &schema.StandardTrackingEventData{}
	err := jsoniter.Unmarshal(message.MsgText, req)
	if err != nil {
		errMsg := fmt.Sprintf("message unmarshal failed | msg_text=%s | err=%s", string(message.MsgText), err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{
			Retcode: int(lcos_error.FailCode),
			Message: errMsg,
		}
	}

	var slsTN string
	if req.Data != nil {
		slsTN = req.Data.SlsTn
	}

	retryTimes := message.RetryTimes()

	// 将当前 edd 重试时间传递到任务中，上报重试次数
	ctx = context.WithValue(ctx, constant.EDDRetryTimesKey, retryTimes)
	_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "RetryTimes", strconv.Itoa(retryTimes), "")

	// 增加logid，方便日志记录
	logRequestID := "|" + req.EventId + ":" + slsTN + ":" + strconv.Itoa(retryTimes)
	ctx = utils.NewLogContext(ctx, logRequestID)
	lcosContext := utils.NewCommonCtx(ctx)

	// check if over max retry times, need to report if so
	if message.IsLastExecution() {
		interfaceName := slsTN
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReachLastRetry, interfaceName, constant.StatusError, logRequestID)
	}

	logger.CtxLogInfof(ctx, "ready to deal with sls_tn:[%s] at retry times:[%d]", slsTN, retryTimes)

	lcosErr := b.doLMEDDPushTask(lcosContext, req)
	// edd上报
	tools.MonitorPushEddTask(ctx, saturnConstant.LMEDDTask, "", retryTimes, lcosErr)
	if lcosErr != nil {
		if lcosErr.RetCode == lcos_error.SaturnNeedToRetryErrorCode {
			logger.CtxLogInfof(ctx, "task failed, will retry, err=%s", lcosErr.Msg)
			return &saturn.SaturnReply{
				Retcode: int(lcos_error.FailCode),
				Message: lcosErr.Msg,
			}
		}
	}
	return &saturn.SaturnReply{
		Retcode: int(lcos_error.SuccessCode),
		Message: "success",
	}
}

func NewLMEDDPushJob() *LmEDDPushJob {
	return &LmEDDPushJob{
		eddAutoUpdateDao:  task_api.GetTaskService().EddAutoUpdateConfDao,
		delayQueueService: task_api.GetTaskService().EddWaybillDelayQueue,
	}
}
