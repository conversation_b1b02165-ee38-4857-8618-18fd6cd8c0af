package edd_preemptive_pushing

import (
	"context"
	"fmt"
	edd_auto_update "git.garena.com/shopee/bg-logistics/algo/sls/edd-auto-update"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	saturnConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/message_jobs/edd_push"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/tools"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_delay_queue"
	jsoniter "github.com/json-iterator/go"
	uuid "github.com/satori/go.uuid"
	"strings"
)

func NewEddPreemptivePushingJob() *EddPreemptivePushingJob {
	return &EddPreemptivePushingJob{}
}

type EddPreemptivePushingJob struct{}

func (e *EddPreemptivePushingJob) Name() string {
	return saturnConstant.EDDPreemptivePushingTask
}

func (e *EddPreemptivePushingJob) handleEDDPreemptive(ctx utils.LCOSContext, slsTN string, updateEvent uint8, updateEventStr string) *lcos_error.LCOSError {
	// 1. get error tracking code list
	errorTrackingList := config.GetNewErrorTrackingCodeList(ctx)
	logger.CtxLogInfof(ctx, "configured error tracking code is:[%s]", strings.Join(errorTrackingList, ","))

	eddJob := edd_push.NewLMEDDPushJob()
	if lcosErr := eddJob.HandleEDDTask(ctx, slsTN, errorTrackingList, nil, edd_auto_update.UpdateOnDeadline, updateEvent); lcosErr != nil {
		logger.CtxLogErrorf(ctx, "failed to do edd preemptive update|sls_tn=[%s], update_event=[%s], error_message=[%s]", slsTN, updateEventStr, lcosErr.Msg)
		return lcosErr
	}
	logger.CtxLogInfof(ctx, "finishing edd preemptive update|sls_tn=[%s], update_event=[%s]", slsTN, updateEventStr)
	return nil
}

func (e *EddPreemptivePushingJob) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {

	logID := uuid.NewV4().String() // nolint
	ctx = utils.NewLogContext(ctx, "|"+logID)
	lcosCtx := utils.NewCommonCtx(ctx)

	logger.CtxLogErrorf(lcosCtx, "EddPreemptivePushingJob receive message: %s", string(message.MsgText))

	var waybill edd_delay_queue.EddWaybill
	if err := jsoniter.Unmarshal(message.MsgText, &waybill); err != nil {
		return &saturn.SaturnReply{
			Retcode: -1,
			Message: fmt.Sprintf("invalid message: %s", string(message.MsgText)),
		}
	}

	slsTN := waybill.SlsTN
	updateEvent := waybill.UpdateEvent
	updateEventStr, _ := edd_constant.GetUpdateEventString(updateEvent)
	retryTimes := message.RetryTimes()

	logger.CtxLogInfof(lcosCtx, "ready to deal with edd preemptive at retry times:[%d]|sls_tn=[%s], update_event=[%s]", retryTimes, slsTN, updateEventStr)

	lcosErr := e.handleEDDPreemptive(lcosCtx, slsTN, updateEvent, updateEventStr)
	tools.MonitorPushEddTask(ctx, saturnConstant.EDDPreemptivePushingTask, "", retryTimes, lcosErr)
	if lcosErr != nil {
		if lcosErr.RetCode == lcos_error.SaturnNeedToRetryErrorCode {
			logger.CtxLogInfof(lcosCtx, "task failed, will retry, error=[%s]", lcosErr.Msg)
			return &saturn.SaturnReply{
				Retcode: -1,
				Message: lcosErr.Msg,
			}
		}
	}

	return &saturn.SaturnReply{
		Retcode: int(lcos_error.SuccessCode),
		Message: "success",
	}
}
