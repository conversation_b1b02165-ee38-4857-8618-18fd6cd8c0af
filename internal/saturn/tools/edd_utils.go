package tools

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"strconv"
	"strings"
)

// parseSequenceStringListToUint8List
// transfer sequence string list to uint8
func parseSequenceStringListToUint8List(ctx context.Context) []uint8 {
	sequenceList := config.GetEDDTrackingSequence(ctx)
	returnedSequence := make([]uint8, 0, len(sequenceList))
	for _, singleSequence := range sequenceList {
		if singleSequence == edd_constant.PickupDoneString {
			returnedSequence = append(returnedSequence, edd_constant.PickupDone)
		} else {
			returnedSequence = append(returnedSequence, edd_constant.EventMap[singleSequence])
		}
	}
	return returnedSequence
}

// return events that are later than or equal to current event
func getLatterUpdateEventSequence(ctx context.Context, updateEvent uint8) []uint8 {
	var updateEventIndex int
	allEventList := parseSequenceStringListToUint8List(ctx)
	for index, singleEvent := range allEventList {
		if singleEvent == updateEvent {
			updateEventIndex = index
			break
		}
	}
	return allEventList[updateEventIndex:]
}

// ContainsLatterAutoUpdateEvent
// whether edd history contains next auto update event
// for example, once soc inbound event edd pushed, any event after soc inbound, include soc inbound push will fail
// preemptive update will not block any other edd push
// return message and flag
// when flag is true, message will not be empty, indicate the latter event
func ContainsLatterAutoUpdateEvent(ctx context.Context, histories []*edd_history.EddHistoryTab, updateEvent uint8) (string, bool) {
	seriesEvent := edd_constant.GetSeriesUpdateEvent(updateEvent) // 如果updateEvent属于ShippedOut系列，转成ShippedOut来做判断
	latterSequence := getLatterUpdateEventSequence(ctx, seriesEvent)
	for _, singleHistory := range histories {
		historySeriesEvent := edd_constant.GetSeriesUpdateEvent(singleHistory.UpdateEvent) // 如果singleHistory.UpdateEvent属于ShippedOut系列，转成ShippedOut来做判断

		if utils.CheckInUint8(historySeriesEvent, latterSequence) && singleHistory.IsPreemptive != constant.TRUE { // only auto update edd will block later sequence
			updateEventStr, _ := edd_constant.GetUpdateEventString(singleHistory.UpdateEvent) // 这里打印msg仍然使用真实的updateEvent
			currentUpdateEventStr, _ := edd_constant.GetUpdateEventString(updateEvent)
			return fmt.Sprintf("[%s]auto update edd has already been pushed, cannot push [%s]edd", updateEventStr, currentUpdateEventStr), true
		}
	}
	return "", false
}

func MonitorPushEddTask(ctx context.Context, updateType string, event string, retryTimes int, reply *lcos_error.LCOSError) {
	region := strings.ToLower(utils.GetCID())
	saturnReply := 0

	metricsData := map[string]string{
		"region":      region,
		"status":      strconv.Itoa(saturnReply),
		"update_type": updateType,
		"event":       event,
		"product_id":  "",
		"retry_times": strconv.Itoa(retryTimes),
	}
	if reply != nil {
		saturnReply = int(reply.RetCode)
		metricsData["status"] = strconv.Itoa(saturnReply)
	}
	if saturnReply != int(lcos_error.SaturnNeedToRetryErrorCode) {
		metricsData["field"] = constant.EddPushMonitor_OrderEddPushField
		_ = metrics.CounterAdd(constant.EddPushMonitor, 1, metricsData)
	}
	metricsData["field"] = constant.EddPushMonitor_EddPushField
	// 1.监控上报
	_ = metrics.CounterAdd(constant.EddPushMonitor, 1, metricsData)
}

func MonitorPushEddJob(ctx context.Context, updateType string, event string, retryTimes int, productId, field string, reply *lcos_error.LCOSError) {
	region := strings.ToLower(utils.GetCID())
	saturnReply := 0

	metricsData := map[string]string{
		"region":      region,
		"status":      strconv.Itoa(saturnReply),
		"update_type": updateType,
		"event":       event,
		"product_id":  productId,
		"field":       field,
		"retry_times": strconv.Itoa(retryTimes),
	}
	if reply != nil {
		saturnReply = int(reply.RetCode)
		metricsData["status"] = strconv.Itoa(saturnReply)
	}

	// 1.监控上报
	_ = metrics.CounterAdd(constant.EddPushMonitor, 1, metricsData)
}
