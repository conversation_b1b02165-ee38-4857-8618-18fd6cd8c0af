package calculate_weight

import (
	"context"
	"fmt"
	"git.garena.com/peng.li/sls-package-calculate/formula/service/calculate_formula"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/local_formula"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	jsoniter "github.com/json-iterator/go"
)

type CalculateWeightServiceInterface interface {
	CalculateWeight(ctx utils.LCOSContext, req *pb.CalculateWeightRequest) (*pb.CalculateWeightResponse, *lcos_error.LCOSError)
	BatchCalculateWeight(ctx utils.LCOSContext, req *pb.BatchCalculateWeightRequest) (*pb.BatchCalculateWeightResponse, *lcos_error.LCOSError)
	BatchCalculateProductValidateWeight(ctx utils.LCOSContext, req *pb.BatchCalculateProductValidateWeightRequest) (*pb.BatchCalculateProductValidateWeightResponse, *lcos_error.LCOSError)
	CalculateFormula(ctx context.Context, req *pb.CalculateFormulaRequest) (*pb.CalculateFormulaResponse, *lcos_error.LCOSError)
	CheckSkuParams(ctx context.Context, skus ...*pb.SkuInfo) *lcos_error.LCOSError
	CheckOrderParams(ctx context.Context, orderInfo *pb.OrderInfo) *lcos_error.LCOSError
}

type calculateWeightService struct {
	productPackageLimitDao model.ProductPackageLimitDAO
	parcelLibService       parcel_library.LogisticParcelLibraryService
}

func NewCalculateWeightService(productPackageLimitDao model.ProductPackageLimitDAO, parcelLibService parcel_library.LogisticParcelLibraryService) *calculateWeightService {
	return &calculateWeightService{
		productPackageLimitDao: productPackageLimitDao,
		parcelLibService:       parcelLibService,
	}
}

func (service *calculateWeightService) CalculateWeight(ctx utils.LCOSContext, req *pb.CalculateWeightRequest) (*pb.CalculateWeightResponse, *lcos_error.LCOSError) {
	resp := &pb.CalculateWeightResponse{}
	var defaultWeight float64 = 0

	if err := service.CheckSkuParams(ctx, req.SkuInfo...); err != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
		return resp, nil
	}
	if err := service.CheckOrderParams(ctx, req.OrderInfo); err != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
		return resp, nil
	}

	calculateParam := local_formula.ConvertInputToCalculateInput(req.BaseInfo.VolumetricFactor, req.OrderInfo, req.SkuInfo, req.BaseInfo.ConditionParams, req.BaseInfo.ParcelLimits)
	calculateParam.NeedLog = true
	if checkErr := calculate_formula.CheckSkuInfo(calculateParam.SkuParam, "length", "width", "height"); checkErr == nil {
		// 检查长宽高是否缺失，没有缺失则进行重排
		if err := calculate_formula.SortSkuInfo(calculateParam.SkuParam); err != nil {
			resp.ResultWeight = &defaultWeight
			msg := "sort sku error: " + err.Error()
			resp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.CalculateWeightFullyFailedErrorCode, msg)
			return resp, nil
		}
	}

	resultWeight, err := calculate_formula.CalculateResultByFormulaNumber(*req.BaseInfo.Formula, calculateParam)
	// Print logs for data recovery on the algorithm side
	if calculateParam.DataAlgoLogMsg != "" {
		logger.CtxLogErrorf(ctx, "%s", calculateParam.DataAlgoLogMsg)
		calculateParam.DataAlgoLogMsg = ""
	}

	if err != nil {
		resp.ResultWeight = &defaultWeight
		msg := "calculate weight error: " + err.Error()
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.CalculateWeightFullyFailedErrorCode, msg)
		return resp, nil
	}

	resp.ResultWeight = &resultWeight
	resp.RespHeader = http.GrpcSuccessRespHeader()

	return resp, nil
}

func (service *calculateWeightService) CalculateFormula(ctx context.Context, req *pb.CalculateFormulaRequest) (*pb.CalculateFormulaResponse, *lcos_error.LCOSError) {
	resp := &pb.CalculateFormulaResponse{}
	// if err := service.CheckSkuParams(ctx, req.SkuInfo...); err != nil {
	// 	resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
	// 	return resp, nil
	// }
	// if err := service.CheckOrderParams(ctx, req.OrderInfo); err != nil {
	// 	resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
	// 	return resp, nil
	// }
	calculateParam := local_formula.ConvertInputToCalculateInput(req.BaseInfo.VolumetricFactor, req.OrderInfo, req.SkuInfo, nil, req.BaseInfo.ParcelLimits)
	calculateParam.NeedLog = false // lcs调用，不打印算法的日志
	if checkErr := calculate_formula.CheckSkuInfo(calculateParam.SkuParam, "length", "width", "height"); checkErr == nil {
		// 检查长宽高是否缺失，没有缺失则进行重排
		if err := calculate_formula.SortSkuInfo(calculateParam.SkuParam); err != nil {
			msg := "sort sku error: " + err.Error()
			logger.CtxLogErrorf(ctx, msg)
			return nil, lcos_error.NewLCOSError(lcos_error.ParamsError, msg)
		}
	}

	mapResult := make(map[uint32]*pb.CalculateFormulaResult)
	for _, f := range req.BaseInfo.Formula {
		result, err := calculate_formula.CalculateResultByFormulaNumber(f, calculateParam)
		if err != nil {
			msg := "calculate formula error: " + err.Error()
			rspHeader := http.GrpcErrorRespHeaderWithParam(lcos_error.CalculateFormulaError, msg)
			rspHeader.RequestId = req.ReqHeader.RequestId
			forResult := &pb.CalculateFormulaResult{
				RespHeader: rspHeader,
			}
			mapResult[f] = forResult
		} else {
			rspHeader := http.GrpcSuccessRespHeader()
			rspHeader.RequestId = req.ReqHeader.RequestId
			forResult := &pb.CalculateFormulaResult{
				RespHeader: rspHeader,
				Result:     &result,
			}
			mapResult[f] = forResult
		}
	}
	resp.RespHeader = http.GrpcSuccessRespHeader()
	resp.FormulaResult = mapResult
	return resp, nil
}

func (service *calculateWeightService) BatchCalculateWeight(ctx utils.LCOSContext, req *pb.BatchCalculateWeightRequest) (*pb.BatchCalculateWeightResponse, *lcos_error.LCOSError) {
	var defaultWeight float64 = 0
	isAllFailed := true
	isAllSuccess := true
	calculateRes := make([]*pb.BatchCalculateResult, 0, len(req.CalculateInfo))

	for _, calculateInfo := range req.CalculateInfo {
		for _, skuGroup := range calculateInfo.SkuGroup {
			if err := service.CheckSkuParams(ctx, skuGroup.SkuInfo...); err != nil {
				return &pb.BatchCalculateWeightResponse{
					RespHeader:      http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
					CalculateResult: calculateRes,
				}, nil
			}
		}
	}

	for _, calculateInfo := range req.CalculateInfo {
		groupResList := make([]*pb.BatchCalculateSkuGroupResult, 0, len(calculateInfo.SkuGroup))
		baseInfo := calculateInfo.BaseInfo
		for _, skuGroup := range calculateInfo.SkuGroup {
			errMsg := ""
			singleGroupRes := &pb.BatchCalculateSkuGroupResult{
				GroupId:          skuGroup.GroupId,
				CalculateMessage: &errMsg,
			}
			skuInfo := skuGroup.SkuInfo
			calculateParam := &calculate_formula.CalculateFormulaInputParam{
				VolumetricFactor: baseInfo.VolumetricFactor,
				OrderParam:       &calculate_formula.CalculateFormulaOrderParam{},
				SkuParam:         local_formula.ConvertSkuInfo(skuInfo),
				ConditionParam:   local_formula.ConvertConditionInfo(baseInfo.ConditionParams),
				ParcelLimits:     local_formula.ConvertParcelLimitsInfo(baseInfo.ParcelLimits),
				NeedLog:          true,
			}
			if checkErr := calculate_formula.CheckSkuInfo(calculateParam.SkuParam, "length", "width", "height"); checkErr == nil {
				// 检查长宽高是否缺失，没有缺失则进行重排
				if err := calculate_formula.SortSkuInfo(calculateParam.SkuParam); err != nil {
					singleGroupRes.ResultWeight = &defaultWeight
					errMsg = "sort sku info failed: " + err.Error()
					groupResList = append(groupResList, singleGroupRes)
					isAllSuccess = false
					continue
				}
			}

			resultWeight, err := calculate_formula.CalculateResultByFormulaNumber(*baseInfo.Formula, calculateParam)
			// Print logs for data recovery on the algorithm side
			if calculateParam.DataAlgoLogMsg != "" {
				logger.CtxLogErrorf(ctx, "%s", calculateParam.DataAlgoLogMsg)
				calculateParam.DataAlgoLogMsg = ""
			}
			if err != nil {
				singleGroupRes.ResultWeight = &defaultWeight
				errMsg = "calculate weight failed: " + err.Error()
				isAllSuccess = false
			} else {
				singleGroupRes.ResultWeight = &resultWeight
				errMsg = "success"
				isAllFailed = false
			}
			groupResList = append(groupResList, singleGroupRes)
		}
		calculateRes = append(calculateRes, &pb.BatchCalculateResult{
			CalculateId: calculateInfo.CalculateId,
			GroupResult: groupResList,
		})
	}
	if isAllSuccess {
		return &pb.BatchCalculateWeightResponse{
			RespHeader:      http.GrpcSuccessRespHeader(),
			CalculateResult: calculateRes,
		}, nil
	} else {
		if isAllFailed {
			return &pb.BatchCalculateWeightResponse{
				RespHeader:      http.GrpcErrorRespHeader(lcos_error.CalculateWeightFullyFailedErrorCode),
				CalculateResult: calculateRes,
			}, nil
		} else {
			return &pb.BatchCalculateWeightResponse{
				RespHeader:      http.GrpcErrorRespHeader(lcos_error.CalculateWeightPartialFailedErrorCode),
				CalculateResult: calculateRes,
			}, nil
		}
	}
}

func (service *calculateWeightService) BatchCalculateProductValidateWeight(ctx utils.LCOSContext, req *pb.BatchCalculateProductValidateWeightRequest) (*pb.BatchCalculateProductValidateWeightResponse, *lcos_error.LCOSError) {
	var resultList = make([]*pb.SingleProductValidateWeightResult, 0, len(req.ReqInfoList))
	for _, singleReq := range req.ReqInfoList {
		var defaultWeight float64 = 0
		var singleResp *pb.SingleProductValidateWeightResult

		// 产品重量限制规则条数不满足限制
		itemCode := lcos_error.SuccessCode
		msg := lcos_error.RetCodeToMessMapper[itemCode]
		productLimitLists, lcosError := service.productPackageLimitDao.GetPackageLimitModelsByProductIdUseCache(ctx, *singleReq.BaseInfo.ProductId)
		if lcosError != nil {
			singleResp = genSingleProductValidateWeightResp(&defaultWeight, &lcosError.RetCode, &lcosError.Msg, singleReq.BaseInfo.QueryId, singleReq.BaseInfo.ProductId)
			resultList = append(resultList, singleResp)
			continue
		}

		// 获取重量规则
		weightLimitRule, countError := getProductWeightLimitRule(productLimitLists)
		if countError != nil {

			singleResp = genSingleProductValidateWeightResp(&defaultWeight, &countError.RetCode, &countError.Msg, singleReq.BaseInfo.QueryId, singleReq.BaseInfo.ProductId)
			resultList = append(resultList, singleResp)
			continue
		}

		region := weightLimitRule.Region
		if weightLimitRule.UseParcelLibrary && !config.IsParcelLibraryDowngraded(ctx, region) {
			// parcel library可用，则尝试获取parcel library数据
			parcelInfos, _, err := service.parcelLibService.GetParcelLibraryDataBySkuInfos(ctx, region, singleReq.GetSkuInfo(), singleReq.GetBaseInfo().GetBuyerPurchaseTime())
			if err == nil && len(parcelInfos) > 0 {
				singleResp = genSingleProductValidateWeightResp(&parcelInfos[0].AccurateWeight, &itemCode, &msg, singleReq.BaseInfo.QueryId, singleReq.BaseInfo.ProductId)
				resultList = append(resultList, singleResp)
				continue
			}
			// 获取parcel数据失败，则使用listing数据做兜底计算
		}

		// 获取三边限制
		sideLimits := local_formula.GetSizeLimitFromProductRules(productLimitLists)

		// 生成公式需要的入参
		volumetricFactor := weightLimitRule.VolumetricFactor
		// 获取db condition数据，转换为condition参数
		conditionParams := make([]*pb.ConditionParams, 0)
		if len(weightLimitRule.ConditionFormulaParams) != 0 {
			err := jsoniter.Unmarshal([]byte(weightLimitRule.ConditionFormulaParams), &conditionParams)
			if err != nil {
				itemCode = lcos_error.ConvertWeightParamsErrorCode
				msg = lcos_error.RetCodeToMessMapper[itemCode]
				singleResp = genSingleProductValidateWeightResp(&defaultWeight, &itemCode, &msg, singleReq.BaseInfo.QueryId, singleReq.BaseInfo.ProductId)
				resultList = append(resultList, singleResp)
				continue
			}
		}
		calculateParam := local_formula.ConvertInputToCalculateInput(&volumetricFactor, singleReq.OrderInfo, singleReq.SkuInfo, conditionParams, sideLimits)
		calculateParam.NeedLog = true
		if checkErr := calculate_formula.CheckSkuInfo(calculateParam.SkuParam, "length", "width", "height"); checkErr == nil {
			// 检查长宽高是否缺失，没有缺失则进行重排
			if err := calculate_formula.SortSkuInfo(calculateParam.SkuParam); err != nil {
				itemCode := lcos_error.CalculateWeightFullyFailedErrorCode
				msg := "sort sku error: " + err.Error()
				singleResp = genSingleProductValidateWeightResp(&defaultWeight, &itemCode, &msg, singleReq.BaseInfo.QueryId, singleReq.BaseInfo.ProductId)
				resultList = append(resultList, singleResp)
				continue
			}
		}

		resultWeight, err := calculate_formula.CalculateResultByFormulaNumber(weightLimitRule.Formula, calculateParam)
		// Print logs for data recovery on the algorithm side
		if calculateParam.DataAlgoLogMsg != "" {
			logger.CtxLogInfof(ctx, "%s", calculateParam.DataAlgoLogMsg)
			calculateParam.DataAlgoLogMsg = ""
		}

		if err != nil {
			itemCode = lcos_error.CalculateWeightFullyFailedErrorCode
			msg = "calculate weight error: " + err.Error()
			singleResp = genSingleProductValidateWeightResp(&defaultWeight, &itemCode, &msg, singleReq.BaseInfo.QueryId, singleReq.BaseInfo.ProductId)
			resultList = append(resultList, singleResp)
			continue
		}
		singleResp = genSingleProductValidateWeightResp(&resultWeight, &itemCode, &msg, singleReq.BaseInfo.QueryId, singleReq.BaseInfo.ProductId)
		resultList = append(resultList, singleResp)
	}

	resp := &pb.BatchCalculateProductValidateWeightResponse{
		RespHeader: http.GrpcErrorRespHeader(lcos_error.SuccessCode),
		ResultList: resultList,
	}
	return resp, nil
}

// 生成单个产品校验重量resp
func genSingleProductValidateWeightResp(resultWeight *float64, itemCode *int32, itemMsg *string, queryId *uint32, productId *string) *pb.SingleProductValidateWeightResult {
	var singleResp = &pb.SingleProductValidateWeightResult{
		ResultWeight: resultWeight,
		ItemCode:     itemCode,
		ItemMsg:      itemMsg,
		QueryId:      queryId,
		ProductId:    productId,
	}
	return singleResp
}

// 获取产品校验重量限制
func getProductWeightLimitRule(productWeightLimits []*model.ProductPackageLimitTab) (*model.ProductPackageLimitTab, *lcos_error.LCOSError) {
	count := 0
	var index int
	var weightLimitArr []*model.ProductPackageLimitTab
	if len(productWeightLimits) == 0 {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.CheckProductValidatWeightLimitFailedErrorCode)
	}
	for i, productWeightLimit := range productWeightLimits {
		// 满足重量公式的rule类型
		if (productWeightLimit.RuleType >= constant.ActualWeightExtremumRule && productWeightLimit.RuleType <= constant.ActualWeightAndVolumetricWeightRatioRule) &&
			productWeightLimit.LimitFlag == constant.ENABLED {
			count++
			index = i
			weightLimitArr = append(weightLimitArr, productWeightLimit)
		}
	}
	if count != constant.ProcuctWeightLimitRecord {
		weightLimitBytes, _ := jsoniter.Marshal(weightLimitArr)
		logger.LogInfof("more than one weight limit: %s", weightLimitBytes)
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.CheckProductValidatWeightLimitFailedErrorCode)
	}
	return productWeightLimits[index], nil
}

func (service *calculateWeightService) CheckSkuParams(ctx context.Context, skus ...*pb.SkuInfo) *lcos_error.LCOSError {
	for _, v := range skus {
		shouldBeCheckFields := []*float64{
			v.ItemPrice, v.ItemPriceUsd, v.Length, v.Weight, v.Width, v.Height,
		}

		for _, field := range shouldBeCheckFields {
			if field != nil && *field < 0 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode,
					fmt.Sprintf("SkuInfo:[%v] exist negative number", v.String()),
				)
			}
		}
	}
	return nil
}

func (service *calculateWeightService) CheckOrderParams(ctx context.Context, orderInfo *pb.OrderInfo) *lcos_error.LCOSError {
	if orderInfo == nil {
		return nil
	}

	shouldBeCheckFields := []*float64{
		orderInfo.OrderActualWeight, orderInfo.OrderVolumetricWeight, orderInfo.OrderLength, orderInfo.OrderHeight, orderInfo.OrderWidth,
	}

	for _, field := range shouldBeCheckFields {
		if field != nil && *field < 0 {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode,
				fmt.Sprintf("OrderInfo:[%v] exist negative number",
					orderInfo.String()),
			)
		}
	}

	return nil
}
