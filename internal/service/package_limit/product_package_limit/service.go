package product_package_limit

import (
	"fmt"
	"git.garena.com/peng.li/sls-package-calculate/formula/service/calculate_formula"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/product_package_limit"
	parcel_library2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/local_formula"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/package_limit_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/gogo/protobuf/proto"
	"math"
	"sort"
	"strconv"
	"strings"
)

type ProductPackageLimitServiceInterface interface {
	BatchCreatePackageLimitModel(ctx utils.LCOSContext, request *product_package_limit.CreateOrUpdateProductPackageLimitRequest) ([]*model.ProductPackageLimitTab, *lcos_error.LCOSError)
	BatchUpdatePackageLimitModel(ctx utils.LCOSContext, request *product_package_limit.CreateOrUpdateProductPackageLimitRequest) ([]*model.ProductPackageLimitTab, *lcos_error.LCOSError)
	CreateOrUpdatePackageLimitModel(ctx utils.LCOSContext, request *product_package_limit.CreateOrUpdateProductPackageLimitRequest) ([]*model.ProductPackageLimitTab, *lcos_error.LCOSError)
	GetPackageLimitByProductId(ctx utils.LCOSContext, lineId string) ([]*model.ProductPackageLimitTab, *lcos_error.LCOSError)

	GetProductRule(ctx utils.LCOSContext, productId string) ([]*pb.RuleInfoDetail, *lcos_error.LCOSError)
	BatchGetProductRule(ctx utils.LCOSContext, productIds []string) (map[string][]*model.ProductPackageLimitTab, *lcos_error.LCOSError)
	CheckProductRule(ctx utils.LCOSContext, lineId string, skuInfos []*pb.SkuInfo, orderInfo *pb.OrderInfo, buyerPurchaseTime uint32) (*pb.SingleCheckProductRuleResult, *lcos_error.LCOSError)
	GetTWSizeInfos(ctx utils.LCOSContext, request *pb.GetTWSizeInfosRequest) (*pb.GetTWSizeInfosResponse, *lcos_error.LCOSError)
	GetProductSideLimits(ctx utils.LCOSContext, productIdList []string) (map[string]*pb.SizeLimitInfo, *lcos_error.LCOSError)
	// CheckProductRuleAndCalculateVolumetricWeight 购物车拆单场景，校验渠道维度重量规则并计算model体积重
	CheckProductRuleAndCalculateVolumetricWeight(ctx utils.LCOSContext, productId string, skuInfos []*pb.SkuInfo, orderInfo *pb.OrderInfo) (*pb.SingleProductCheckResult, *lcos_error.LCOSError)
	BatchCheckProductRuleForParcelLib(ctx utils.LCOSContext, details *pb.BatchCheckProductRuleForParcelLibRequest) (*pb.BatchCheckProductRuleForParcelLibResponse, *lcos_error.LCOSError)
}

type ProductPackageLimitService struct {
	productPackageLimitDAO model.ProductPackageLimitDAO
	parcelLibService       parcel_library.LogisticParcelLibraryService
}

func (service *ProductPackageLimitService) BatchCheckProductRuleForParcelLib(ctx utils.LCOSContext, details *pb.BatchCheckProductRuleForParcelLibRequest) (*pb.BatchCheckProductRuleForParcelLibResponse, *lcos_error.LCOSError) {
	products := strings.Split(config.GetMutableConf(ctx).ParcelLibraryConfig.SimulationProductMap[details.GetRegion()], ",")
	if len(details.ParcelDetail) < 1 {
		return nil, nil
	}
	results := make([]*pb.CheckResult, 0, len(details.ParcelDetail))
	for _, parcel := range details.GetParcelDetail() {
		productResults := make([]*pb.ProductCheckResult, 0, len(products))
		for _, product := range products {
			limitEntries, _ := service.productPackageLimitDAO.GetPackageLimitModelsByProductIdUseCache(ctx, product)
			productResults = append(productResults, checkEntries(limitEntries, parcel, product))
		}
		results = append(results, &pb.CheckResult{CheckResults: productResults, CombinationId: parcel.CombinationId, Version: parcel.Version})
	}
	return &pb.BatchCheckProductRuleForParcelLibResponse{Results: results, RespHeader: http.GrpcSuccessRespHeader()}, nil
}

const (
	checkSuccess = 0
	checkFailure = 1
)

func checkEntries(limitEntries []*model.ProductPackageLimitTab, parcel *pb.ParcelItem, productId string) *pb.ProductCheckResult {
	result := &pb.ProductCheckResult{
		ProductId: &productId,
	}
	result.Message = proto.String("No rule for this product")
	result.Result = proto.Int32(checkFailure)
	if len(limitEntries) < 1 {
		return result
	}

	weight, err := strconv.ParseFloat(parcel.GetWeight(), 64)
	if err != nil {
		result.Message = proto.String(fmt.Sprintf("parse weight failed with err: %v", err))
		return result
	}
	length, err := strconv.ParseFloat(parcel.GetLength(), 64)
	if err != nil {
		result.Message = proto.String(fmt.Sprintf("parse length failed with err: %v", err))
		return result
	}
	width, err := strconv.ParseFloat(parcel.GetWidth(), 64)
	if err != nil {
		result.Message = proto.String(fmt.Sprintf("parse width failed with err: %v", err))
		return result
	}
	height, err := strconv.ParseFloat(parcel.GetHeight(), 64)
	if err != nil {
		result.Message = proto.String(fmt.Sprintf("parse height failed with err: %v", err))
		return result
	}
	checkRes := true
	for _, entry := range limitEntries {
		commonEntry := package_limit_util.ConvertProductTabToCommonTab(entry)
		switch entry.RuleType {
		case constant.ActualWeightAndVolumetricWeightExtremumRule:
			checkResult := package_limit_util.CheckSingleRuleType(commonEntry, weight)
			if checkResult.GetSingleCheckResult() != 0 {
				result.Message = proto.String("check weight failed")
				checkRes = false
				break
			}
		case constant.MaxLengthExtremumRule:
			checkResult := package_limit_util.CheckSingleRuleType(commonEntry, length)
			if checkResult.GetSingleCheckResult() != 0 {
				result.Message = proto.String("check length failed")
				checkRes = false
				break
			}
		case constant.MaxWidthExtremumRule:
			checkResult := package_limit_util.CheckSingleRuleType(commonEntry, width)
			if checkResult.GetSingleCheckResult() != 0 {
				result.Message = proto.String("check width failed")
				checkRes = false
				break
			}
		case constant.HeightSumExtremumRule:
			checkResult := package_limit_util.CheckSingleRuleType(commonEntry, height)
			if checkResult.GetSingleCheckResult() != 0 {
				result.Message = proto.String("check height failed")
				checkRes = false
				break
			}
		}
	}
	if checkRes {
		result.Result = proto.Int32(checkSuccess)
		result.Message = proto.String("check success")
	}
	return result
}

func NewProductPackageLimitService(productPackageLimitDAO model.ProductPackageLimitDAO, parcelLibService parcel_library.LogisticParcelLibraryService) *ProductPackageLimitService {
	return &ProductPackageLimitService{
		productPackageLimitDAO: productPackageLimitDAO,
		parcelLibService:       parcelLibService,
	}
}

func (service *ProductPackageLimitService) BatchCreatePackageLimitModel(ctx utils.LCOSContext, request *product_package_limit.CreateOrUpdateProductPackageLimitRequest) ([]*model.ProductPackageLimitTab, *lcos_error.LCOSError) {
	if err := service.CheckCanCreatePackageLimit(ctx, request.ProductId); err != nil {
		return nil, err
	}

	limitModels := fillLimitModels(request)
	return service.productPackageLimitDAO.BatchCreatePackageLimitModel(ctx, limitModels)
}

func (service *ProductPackageLimitService) BatchUpdatePackageLimitModel(ctx utils.LCOSContext, request *product_package_limit.CreateOrUpdateProductPackageLimitRequest) ([]*model.ProductPackageLimitTab, *lcos_error.LCOSError) {
	if err := service.CheckCanUpdatePackageLimit(ctx, request.ProductId); err != nil {
		return nil, err
	}

	limitModels := fillLimitModels(request)

	fc := func() *lcos_error.LCOSError {
		if err := service.productPackageLimitDAO.DeletePackageLimitModelsByProductId(ctx, request.ProductId); err != nil {
			return err
		}
		if _, err := service.productPackageLimitDAO.BatchCreatePackageLimitModel(ctx, limitModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return limitModels, nil
}

func (service *ProductPackageLimitService) CreateOrUpdatePackageLimitModel(ctx utils.LCOSContext, request *product_package_limit.CreateOrUpdateProductPackageLimitRequest) ([]*model.ProductPackageLimitTab, *lcos_error.LCOSError) {
	limitModels := fillLimitModels(request)

	fc := func() *lcos_error.LCOSError {
		if err := service.productPackageLimitDAO.DeletePackageLimitModelsByProductId(ctx, request.ProductId); err != nil {
			return err
		}
		if _, err := service.productPackageLimitDAO.BatchCreatePackageLimitModel(ctx, limitModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return limitModels, nil
}

func fillLimitModels(request *product_package_limit.CreateOrUpdateProductPackageLimitRequest) []*model.ProductPackageLimitTab {

	var limitModels []*model.ProductPackageLimitTab
	for i := 0; i < len(request.PackageLimits); i++ {
		if request.PackageLimits[i].SortFlag == nil {
			// 如果为空，赋一个无意义的默认值
			defaultFlag := constant.DISABLED
			request.PackageLimits[i].SortFlag = &defaultFlag
		}
		conditionParamsString := package_limit_util.ConvertConditionParamsToJsonString(request.PackageLimits[i].ConditionFormulaParams)
		limitModels = append(limitModels, &model.ProductPackageLimitTab{
			ProductId:              request.ProductId,
			Region:                 request.Region,
			RuleType:               request.PackageLimits[i].RuleType,
			LimitFlag:              *request.PackageLimits[i].LimitFlag,
			MaxWeight:              request.PackageLimits[i].MaxWeight,
			MinWeight:              request.PackageLimits[i].MinWeight,
			MaxSize:                request.PackageLimits[i].MaxSize,
			MinSize:                request.PackageLimits[i].MinSize,
			MaxLength:              request.PackageLimits[i].MaxLength,
			MinLength:              request.PackageLimits[i].MinLength,
			MaxWidth:               request.PackageLimits[i].MaxWidth,
			MinWidth:               request.PackageLimits[i].MinWidth,
			MaxHeight:              request.PackageLimits[i].MaxHeight,
			MinHeight:              request.PackageLimits[i].MinHeight,
			MaxSymbol:              request.PackageLimits[i].MaxSymbol,
			MinSymbol:              request.PackageLimits[i].MinSymbol,
			Formula:                request.PackageLimits[i].Formula,
			VolumetricFactor:       request.PackageLimits[i].VolumetricFactor,
			SortFlag:               *request.PackageLimits[i].SortFlag,
			ConditionFormulaParams: conditionParamsString,
			UseParcelLibrary:       request.UseParcelLibrary,
			ByPass:                 request.ByPass,
			ByPassPurchaseTime:     request.ByPassPurchaseTime,
		})
	}
	return limitModels
}

func (service *ProductPackageLimitService) CheckCanCreatePackageLimit(ctx utils.LCOSContext, productId string) *lcos_error.LCOSError {
	// 如果线已经有了包裹限制，不允许在创建，只能更新
	l, err := service.productPackageLimitDAO.GetPackageLimitModelsByProductId(ctx, productId)
	if err != nil {
		return err
	}
	if len(l) != 0 {
		logger.LogErrorf("create failed, productId already has package limits|productId=%s", productId)
		return lcos_error.NewLCOSError(lcos_error.NotFoundPackageLimitRuleErrorCode, "create failed, productId already has package limits")
	}

	return nil
}

func (service *ProductPackageLimitService) CheckCanUpdatePackageLimit(ctx utils.LCOSContext, productId string) *lcos_error.LCOSError {
	// 如果线包裹限制，不允许更新，只能创建
	l, err := service.productPackageLimitDAO.GetPackageLimitModelsByProductId(ctx, productId)
	if err != nil {
		return err
	}

	if len(l) == 0 {
		logger.LogErrorf("update failed, productId does not have package limits|productId=%s", productId)
		return lcos_error.NewLCOSError(lcos_error.NotFoundPackageLimitRuleErrorCode, "update failed, productId does not have package limits")
	}

	return nil
}

func (service *ProductPackageLimitService) GetPackageLimitByProductId(ctx utils.LCOSContext, lineId string) ([]*model.ProductPackageLimitTab, *lcos_error.LCOSError) {
	limitModels, err := service.productPackageLimitDAO.GetPackageLimitModelsByProductId(ctx, lineId)
	if err != nil {
		return nil, err
	}

	return limitModels, nil
}

func (service *ProductPackageLimitService) GetProductRule(ctx utils.LCOSContext, productId string) ([]*pb.RuleInfoDetail, *lcos_error.LCOSError) {
	limitModels, err := service.productPackageLimitDAO.GetPackageLimitModelsByProductIdUseCache(ctx, productId)
	if err != nil {
		return nil, err
	}
	if len(limitModels) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundPackageLimitRuleErrorCode, "can not find package limits")
	}

	ruleInfos := make([]*pb.RuleInfoDetail, 0, len(limitModels))
	for _, rule := range limitModels {
		ruleType := uint32(rule.RuleType)
		limitFlag := uint32(rule.LimitFlag)
		byPassPurchaseTime := uint32(rule.ByPassPurchaseTime)
		ruleInfos = append(ruleInfos, &pb.RuleInfoDetail{
			RuleType:           &ruleType,
			LimitFlag:          &limitFlag,
			MaxWeight:          &rule.MaxWeight,
			MinWeight:          &rule.MinWeight,
			MaxSize:            &rule.MaxSize,
			MinSize:            &rule.MinSize,
			MaxLength:          &rule.MaxLength,
			MinLength:          &rule.MinLength,
			MaxWidth:           &rule.MaxWidth,
			MinWidth:           &rule.MinWidth,
			MaxHeight:          &rule.MaxHeight,
			MinHeight:          &rule.MinHeight,
			UseParcelLib:       &rule.UseParcelLibrary,
			ByPass:             &rule.ByPass,
			ByPassPurchaseTime: &byPassPurchaseTime,
		})
	}
	return ruleInfos, nil
}

func (service *ProductPackageLimitService) CheckProductRule(ctx utils.LCOSContext, productId string, skuInfos []*pb.SkuInfo, orderInfo *pb.OrderInfo, buyerPurchaseTime uint32) (*pb.SingleCheckProductRuleResult, *lcos_error.LCOSError) {
	// 1. 获取渠道package limit配置
	packageLimits, err := service.productPackageLimitDAO.GetPackageLimitModelsByProductIdUseCache(ctx, productId)
	if err != nil {
		return &pb.SingleCheckProductRuleResult{}, err
	}
	if len(packageLimits) == 0 {
		return &pb.SingleCheckProductRuleResult{}, lcos_error.NewLCOSError(lcos_error.NotFoundPackageLimitRuleErrorCode, "can not find package limits")
	}

	// 2. 尝试基于sku列表和下单时间匹配parcel尺寸和重量
	parcelInfos, useListingFallback := service.FetchParcelLibraryData(ctx, packageLimits, skuInfos, buyerPurchaseTime)
	if len(parcelInfos) == 0 || useListingFallback {
		// 有两个场景需要校验listing数据：
		// 1）不存在parcel数据
		// 2）存在parcel数据，但所有版本parcel数据校验失败，并且允许使用listing兜底
		parcelInfos = append(parcelInfos, parcel_library2.EmptyParcelLibraryData)
	}
	if len(parcelInfos) > 1 && !config.AllowParcelLibraryFallback(ctx) {
		parcelInfos = parcelInfos[:1] // 存在多个版本时，若不允许兜底，则仅使用第一个版本校验
	}

	// 3. 校验订单包裹是否超过渠道限制
	// 两种校验场景：
	// 1）存在parcel数据：则按照以下顺序兜底，parcel(v3) -> parcel(v2) -> parcel(v1) -> listing（如果useListingFallback为true）
	// 2）不存在parcel数据：则使用listing数据校验
	var (
		checkResult  uint32
		limitDetails []*pb.RuleLimitDetail
		useFallback  bool
	)
	for _, parcelInfo := range parcelInfos {
		if useFallback {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatPackageLimitCheckFallback, fmt.Sprintf("product_id:%s,lcos_version:%d,wbc_version:%d", productId, parcelInfo.GetLcosVersion(), parcelInfo.GetWbcVersion()), constant.StatusError, "")
		}

		checkResult, limitDetails = service.checkProductRule(ctx, productId, packageLimits, skuInfos, orderInfo, parcelInfo, buyerPurchaseTime)
		if checkResult == uint32(constant.SUCCESS) {
			// 校验通过，直接返回
			logger.CtxLogInfof(ctx, "check product rule success with parcel info|product_id=%s, lcos_version=%d, wbc_version=%d", productId, parcelInfo.GetLcosVersion(), parcelInfo.GetWbcVersion())
			break
		}
		// 基于parcel数据校验失败，则尝试使用旧版本parcel数据兜底
		logger.CtxLogErrorf(ctx, "check product rule failed with parcel info|product_id=%s, lcos_version=%d, wbc_version=%d", productId, parcelInfo.GetLcosVersion(), parcelInfo.GetWbcVersion())
		useFallback = true
	}

	return &pb.SingleCheckProductRuleResult{
		ProductId:   &productId,
		CheckResult: &checkResult,
		LimitDetail: limitDetails,
	}, nil
}

func (service *ProductPackageLimitService) checkProductRule(ctx utils.LCOSContext, productId string, packageLimits []*model.ProductPackageLimitTab, skuInfos []*pb.SkuInfo, orderInfo *pb.OrderInfo, parcelInfo *parcel_library2.LogisticParcelLibraryData, buyerPurchaseTime uint32) (uint32, []*pb.RuleLimitDetail) {
	var limitDetails []*pb.RuleLimitDetail
	var checkResult = uint32(constant.SUCCESS)
	sideLimits := local_formula.GetSizeLimitFromProductRules(packageLimits)
	var cachePaecelSize *calculate_formula.Size = nil // 缓存算法计算出的包裹大小结果

	// 分别校验订单包裹的尺寸和重量是否超过渠道限制
	for _, packageLimit := range packageLimits {
		if packageLimit.LimitFlag == constant.DISABLED {
			logger.LogInfof("product_id|%s no need to check rule type %d", productId, packageLimit.RuleType)
			continue
		}

		if service.checkIfNeedByPass(packageLimit, buyerPurchaseTime) {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatPackageLimitByPass, fmt.Sprintf("%s:%d", productId, packageLimit.RuleType), constant.StatusSuccess, "")
			logger.LogInfof("product_d|%s no need to check rule type %d, bypass[%v], bypass purchase time[%v], buyer purchase time[%v]", productId, packageLimit.RuleType, packageLimit.ByPass, packageLimit.ByPassPurchaseTime, buyerPurchaseTime)
			continue
		}

		// 获取包裹的长/宽/高/重量信息。优先获取parcel维度数据，parcel数据不存在则基于listing数据通过公式计算parcel数据
		calculateResult := parcelInfo.GetParcelData(packageLimit.RuleType)
		if package_limit_util.IsWeightLimitRule(packageLimit.RuleType) && utils.CheckInUint32(packageLimit.Formula, config.GetMutableConf(ctx).PackageLimitConfig.VolumetricWeightFormulas) {
			// 使用体积重公式时，校验重=max(体积重，实际重)，仅当体积重和实际重都为0时才使用listing数据兜底
			// parcel_volumetric_weight = parcel_length * parcel_width * parcel_height / volumetric_factor
			// parcel_validate_weight = max(parcel_weight, parcel_volumetric_weight)
			volumetricWeight := package_limit_util.CalculateBaseVolumetricWeight(parcelInfo.GetAccurateLength(), parcelInfo.GetAccurateWidth(), parcelInfo.GetAccurateHeight(), packageLimit.VolumetricFactor)
			calculateResult = math.Max(calculateResult, volumetricWeight)
		}
		if calculateResult <= 0 {
			// 不存在parcel数据，使用listing数据通过公式计算包裹尺寸和重量
			ruleType := uint32(packageLimit.RuleType)

			// 构建公式输入参数
			calculateParam := local_formula.ConvertProductDbDataToCalculateInput(skuInfos, orderInfo, packageLimit, sideLimits)
			calculateParam.NeedLog = true
			// 长宽高重排，最长边作为长，最短边作为高
			if checkErr := calculate_formula.CheckSkuInfo(calculateParam.SkuParam, "length", "width", "height"); checkErr == nil {
				// 检查长宽高是否缺失，没有缺失则进行重排
				if sortErr := calculate_formula.SortSkuInfo(calculateParam.SkuParam); sortErr != nil {
					msg := fmt.Sprintf("product_id|%s sort sku failed, ruleType|%d, err|%s", productId, packageLimit.RuleType, sortErr.Error())
					limitDetails = append(limitDetails, package_limit_util.GenerateFailedLimitDetail(msg, ruleType, 0))
					checkResult = uint32(constant.FAILED)
					continue
				}
			}
			calculateParam.CacheParcelSize = cachePaecelSize

			// 根据公式计算结果
			var err error
			calculateResult, err = calculate_formula.CalculateResultByFormulaNumber(packageLimit.Formula, calculateParam)
			// Print logs for data recovery on the algorithm side
			if calculateParam.DataAlgoLogMsg != "" {
				logger.CtxLogInfof(ctx, "%s", calculateParam.DataAlgoLogMsg)
				calculateParam.DataAlgoLogMsg = ""
			}
			if err != nil {
				msg := fmt.Sprintf("product_id|%s calculate failed, ruleType|%d, err|%s", productId, packageLimit.RuleType, err.Error())
				limitDetails = append(limitDetails, package_limit_util.GenerateFailedLimitDetail(msg, ruleType, calculateResult))
				checkResult = uint32(constant.FAILED)
				continue
			}
			cachePaecelSize = calculateParam.CacheParcelSize
		}

		// 校验包裹尺寸或重量是否超过渠道配置
		singleRuleResult := package_limit_util.CheckSingleRuleType(package_limit_util.ConvertProductTabToCommonTab(packageLimit), calculateResult)
		if *singleRuleResult.SingleCheckResult == uint32(constant.FAILED) {
			checkResult = uint32(constant.FAILED)
		}
		limitDetails = append(limitDetails, singleRuleResult)
	}

	return checkResult, limitDetails
}

func (service *ProductPackageLimitService) checkIfNeedByPass(packageLimit *model.ProductPackageLimitTab, buyerPurchaseTime uint32) bool {
	var needByPass bool

	if packageLimit == nil || buyerPurchaseTime == 0 || packageLimit.ByPassPurchaseTime == 0 {
		return needByPass
	}

	if packageLimit.ByPass && packageLimit.ByPassPurchaseTime >= int64(buyerPurchaseTime) {
		needByPass = true
	}

	return needByPass
}

func (service *ProductPackageLimitService) BatchGetProductRule(ctx utils.LCOSContext, productIds []string) (map[string][]*model.ProductPackageLimitTab, *lcos_error.LCOSError) {
	result := make(map[string][]*model.ProductPackageLimitTab)
	for _, productID := range productIds {
		if len(productID) == 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "product id cannot be empty")
		}
		productPackageLimitTabs, lcosErr := service.productPackageLimitDAO.GetPackageLimitModelsByProductIdUseCache(ctx, productID)
		if lcosErr != nil {
			return nil, lcosErr
		}
		result[productID] = productPackageLimitTabs
	}
	return result, nil
}

func (service *ProductPackageLimitService) GetTWSizeInfos(ctx utils.LCOSContext, request *pb.GetTWSizeInfosRequest) (*pb.GetTWSizeInfosResponse, *lcos_error.LCOSError) {
	sizeInfoMap, lcosErr := service.productPackageLimitDAO.GetTWSizeInfosByProductIdUseCache(ctx, request.GetProductId())
	if lcosErr != nil {
		return nil, lcosErr
	}
	var sizeInfos []*pb.TWSizeInfo
	if request.GetSizeId() != "" {
		if _, ok := sizeInfoMap[request.GetSizeId()]; ok {
			size := sizeInfoMap[*request.SizeId].(*model.LogisticSizeTab)
			sizeInfos = append(sizeInfos, &pb.TWSizeInfo{
				Id:           &size.ID,
				ProductId:    &size.ProductId,
				SizeId:       &size.SizeId,
				Name:         &size.Name,
				Description:  &size.Description,
				Country:      &size.Country,
				Unit:         &size.Unit,
				MaxSize:      &size.MaxSize,
				MinSize:      &size.MinSize,
				DefaultPrice: &size.DefaultPrice,
			})
		} else {
			logger.LogInfof("sizeId:%s not found", request.GetSizeId())
			return nil, lcos_error.NewLCOSError(lcos_error.TWSizeNotFound, "size not found")
		}

	} else {
		for _, sizeInfo := range sizeInfoMap {
			value := sizeInfo.(*model.LogisticSizeTab)
			sizeInfos = append(sizeInfos, &pb.TWSizeInfo{
				Id:           &value.ID,
				ProductId:    &value.ProductId,
				SizeId:       &value.SizeId,
				Name:         &value.Name,
				Description:  &value.Description,
				Country:      &value.Country,
				Unit:         &value.Unit,
				MaxSize:      &value.MaxSize,
				MinSize:      &value.MinSize,
				DefaultPrice: &value.DefaultPrice,
			})
		}
	}
	// 重排 sizeInfos 顺序，避免流量回放顺序差异
	sort.Slice(sizeInfos, func(i, j int) bool {
		return sizeInfos[i].GetId() < sizeInfos[j].GetId()
	})
	return &pb.GetTWSizeInfosResponse{
		RespHeader: http.GrpcErrorRespHeader(lcos_error.SuccessCode),
		SizeInfos:  sizeInfos,
	}, nil
}

func (service *ProductPackageLimitService) GetProductSideLimits(ctx utils.LCOSContext, productIdList []string) (map[string]*pb.SizeLimitInfo, *lcos_error.LCOSError) {
	productLimits, lcosErr := service.productPackageLimitDAO.GetSideLimitsByProductListUseCache(ctx, productIdList)
	if lcosErr != nil {
		return nil, lcosErr
	}

	result := make(map[string]*pb.SizeLimitInfo)
	for productId, limits := range productLimits {
		sideLimits := local_formula.GetSizeLimitFromProductRules(limits)
		result[productId] = sideLimits
	}

	return result, nil
}

func (service *ProductPackageLimitService) CheckProductRuleAndCalculateVolumetricWeight(ctx utils.LCOSContext, productId string, skuInfos []*pb.SkuInfo, orderInfo *pb.OrderInfo) (*pb.SingleProductCheckResult, *lcos_error.LCOSError) {
	// 1. 获取渠道下的所有重量校验规则
	rules, err := service.productPackageLimitDAO.GetPackageLimitModelsByProductIdUseCache(ctx, productId)
	if err != nil {
		return nil, err
	}
	if len(rules) == 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "product package limit rules not found for %s", productId)
	}

	// 2. 基于重量规则列表获取包裹的尺寸限制
	sideLimits := local_formula.GetSizeLimitFromProductRules(rules)

	// 3. 遍历重量校验规则进行校验
	var productCheckResult = constant.SUCCESS           // 渠道校验结果，只要有一个规则校验失败渠道整体就失败
	var ruleCheckResultList []*pb.SingleRuleCheckResult // 每一个规则的校验结果列表
	var cachePaecelSize *calculate_formula.Size = nil   // 缓存算法计算出的包裹大小结果
	for _, rule := range rules {
		// 3.1 判断规则是否开启校验
		if rule.LimitFlag == constant.DISABLED {
			logger.CtxLogInfof(ctx, "skip product package limit check|product_id=%s, rule_type=%d", productId, rule.RuleType)
			continue
		}

		// 3.2 校验是否是重量规则，且公式是否包含体积重
		var isVolumetricWeightRule bool
		if package_limit_util.IsWeightLimitRule(rule.RuleType) && utils.CheckInUint32(rule.Formula, config.GetMutableConf(ctx).PackageLimitConfig.VolumetricWeightFormulas) {
			isVolumetricWeightRule = true
		}

		// 3.3 转换sdk请求参数
		calculateParam := local_formula.ConvertProductDbDataToCalculateInput(skuInfos, orderInfo, rule, sideLimits)
		calculateParam.NeedLog = true
		// 默认开启sorting，那么需要对商品长宽高进行重排
		if checkErr := calculate_formula.CheckSkuInfo(calculateParam.SkuParam, "length", "width", "height"); checkErr == nil {
			// 检查长宽高是否缺失，没有缺失则进行重排
			if sortErr := calculate_formula.SortSkuInfo(calculateParam.SkuParam); sortErr != nil {
				productCheckResult = constant.FAILED
				msg := fmt.Sprintf("sort sku info error|product_id=%s, rule_type=%d, err=%s", productId, rule.RuleType, sortErr.Error())
				ruleCheckResultList = append(ruleCheckResultList, package_limit_util.GenerateSingleRuleCheckResult(rule, isVolumetricWeightRule, constant.FAILED, msg, 0))
				continue
			}
		}
		calculateParam.CacheParcelSize = cachePaecelSize

		// 3.4 根据公式计算结果
		calculateResult, err := calculate_formula.CalculateResultByFormulaNumber(rule.Formula, calculateParam)
		if err != nil {
			productCheckResult = constant.FAILED
			msg := fmt.Sprintf("calculate formula error|product_id=%s, rule_type=%d, err=%s", productId, rule.RuleType, err.Error())
			ruleCheckResultList = append(ruleCheckResultList, package_limit_util.GenerateSingleRuleCheckResult(rule, isVolumetricWeightRule, constant.FAILED, msg, 0))
			continue
		}
		if calculateParam.DataAlgoLogMsg != "" {
			logger.CtxLogInfof(ctx, calculateParam.DataAlgoLogMsg)
			calculateParam.DataAlgoLogMsg = ""
		}
		cachePaecelSize = calculateParam.CacheParcelSize

		// 3.5 根据公式计算结果校验阈值
		ruleLimitDetail := package_limit_util.CheckSingleRuleType(package_limit_util.ConvertProductTabToCommonTab(rule), calculateResult)
		if ruleLimitDetail.GetSingleCheckResult() == uint32(constant.FAILED) {
			productCheckResult = constant.FAILED
		}
		ruleCheckResult := package_limit_util.GenerateSingleRuleCheckResult(rule, isVolumetricWeightRule, uint8(ruleLimitDetail.GetSingleCheckResult()), ruleLimitDetail.GetReason(), calculateResult)
		if isVolumetricWeightRule {
			// 如果是体积重公式，则需要额外计算每个sku的体积重
			volumetricWeightInfoList := make([]*pb.VolumetricWeightInfo, 0, len(skuInfos))
			for i, skuInfo := range skuInfos {
				skuParam := calculateParam.SkuParam[i]
				volumetricWeight := package_limit_util.CalculateBaseVolumetricWeight(*skuParam.Length, *skuParam.Width, *skuParam.Height, rule.VolumetricFactor)
				volumetricWeightInfoList = append(volumetricWeightInfoList, &pb.VolumetricWeightInfo{
					ItemId:              skuInfo.ItemId,
					CategoryId:          skuInfo.CategoryId,
					ModelId:             skuInfo.ModelId,
					VolumetricWeight:    utils.NewFloat64(volumetricWeight),
					VolumetricWeightInt: utils.NewUint32(uint32(math.Round(volumetricWeight))),
				})
			}
			ruleCheckResult.VolumetricWeightInfo = volumetricWeightInfoList
		}
		ruleCheckResultList = append(ruleCheckResultList, ruleCheckResult)
	}

	return &pb.SingleProductCheckResult{
		ProductId:       utils.NewString(productId),
		CheckResult:     utils.NewUint32(uint32(productCheckResult)),
		RuleCheckDetail: ruleCheckResultList,
	}, nil
}

func (service *ProductPackageLimitService) FetchParcelLibraryData(ctx utils.LCOSContext, packageLimits []*model.ProductPackageLimitTab, skuInfos []*pb.SkuInfo, buyerPurchaseTime uint32) ([]*parcel_library2.LogisticParcelLibraryData, bool) {
	// 1. 检查是否启用parcel library
	var (
		region       string
		useParcelLib bool
	)
	for _, packageLimit := range packageLimits {
		region = packageLimit.Region
		if packageLimit.LimitFlag == constant.TRUE && packageLimit.UseParcelLibrary {
			useParcelLib = true
			break
		}
	}
	if !useParcelLib || config.IsParcelLibraryDowngraded(ctx, region) {
		return nil, false
	}

	// 2. 获取parcel library数据
	parcelInfos, allowListingFallback, err := service.parcelLibService.GetParcelLibraryDataBySkuInfos(ctx, region, skuInfos, buyerPurchaseTime)
	if err != nil {
		return nil, false
	}
	return parcelInfos, allowListingFallback
}
