package product_package_limit

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

func generateFailedLimitDetail(errMsg string, ruleType uint32) *pb.RuleLimitDetail {
	logger.LogErrorf(errMsg)
	singleCheckResult := uint32(constant.FAILED)
	return &pb.RuleLimitDetail{
		RuleType:          &ruleType,
		SingleCheckResult: &singleCheckResult,
		Reason:            &errMsg,
	}
}

func generateSuccessLimitDetail(ruleType uint32) *pb.RuleLimitDetail {
	singleCheckResult := uint32(constant.SUCCESS)
	msg := "success"
	return &pb.RuleLimitDetail{
		RuleType:          &ruleType,
		SingleCheckResult: &singleCheckResult,
		Reason:            &msg,
	}
}
