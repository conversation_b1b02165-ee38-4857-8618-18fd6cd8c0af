package parcel_library

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	Logger "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/waybill_center_service"
	wbc_checkout_sdk "git.garena.com/shopee/bg-logistics/logistics/logistics-waybill-center/wbc-checkout-sdk/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-waybill-center/wbc-checkout-sdk/dto/bulky_tag_dto"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/golang/protobuf/proto"
	"strings"
)

type LogisticParcelLibraryService interface {
	NotifyLogisticParcelLibraryVersion(ctx utils.LCOSContext, region string, fileList []string) *lcos_error.LCOSError
	BatchCreateOrUpdateParcelLibraryData(ctx utils.LCOSContext, region string, dataList []*parcel_library.LogisticParcelLibraryData) (int, *lcos_error.LCOSError)
	BatchGetParcelLibraryData(ctx utils.LCOSContext, region string, skusCombinationIdList []uint64) (map[uint64]*parcel_library.ParcelLibData, *lcos_error.LCOSError)

	GetCombinationIdFromSkuList(ctx utils.LCOSContext, skuInfos []*pb.SkuInfo) (uint64, *lcos_error.LCOSError)
	ListAllUpcomingParcelLibraryVersions(ctx utils.LCOSContext, region string) ([]*parcel_library.LogisticParcelLibraryVersionTab, *lcos_error.LCOSError)
	UpdateParcelLibraryVersion(ctx utils.LCOSContext, version *parcel_library.LogisticParcelLibraryVersionTab) *lcos_error.LCOSError

	GetParcelLibraryDataBySkuInfos(ctx utils.LCOSContext, region string, skuInfos []*pb.SkuInfo, buyerPurchaseTime uint32) ([]*parcel_library.LogisticParcelLibraryData, bool, *lcos_error.LCOSError)
	BatchGetParcelLibraryDataBySkuInfos(ctx utils.LCOSContext, request *pb.BatchGetParcelLibraryDataBySkuInfosRequest) (*pb.BatchGetParcelLibraryDataBySkuInfosResponse, *lcos_error.LCOSError)
	BatchGetLatestParcelLibraryDataBySkuInfos(ctx utils.LCOSContext, queries []*pb.ParcelLibraryQuery) (map[string]*parcel_library.LogisticParcelLibraryData, *lcos_error.LCOSError)
}

func NewLogisticParcelLibraryService(dao parcel_library.LogisticParcelLibraryDao) *logisticParcelLibraryService {
	return &logisticParcelLibraryService{
		dao: dao,
	}
}

type logisticParcelLibraryService struct {
	dao parcel_library.LogisticParcelLibraryDao
}

func (l *logisticParcelLibraryService) BatchGetParcelLibraryDataBySkuInfos(ctx utils.LCOSContext, request *pb.BatchGetParcelLibraryDataBySkuInfosRequest) (*pb.BatchGetParcelLibraryDataBySkuInfosResponse, *lcos_error.LCOSError) {
	result := make([]*pb.CalculateResult, 0, len(request.Orders))
	// 传入多个order，每个order对应几组sku info
	for _, order := range request.Orders {
		orderResult := pb.CalculateResult{CalculateId: order.CalculateId}
		orderResult.OrderResult = make([]*pb.GroupParcelSizeInfo, 0, len(order.SkuGroup))
		combinationIds := make([]uint64, 0, len(order.SkuGroup))
		mapGroupIdCombinationId := make(map[string]uint64)
		for _, skuInfos := range order.SkuGroup {
			combinationId, err := l.GetCombinationIdFromSkuList(ctx, skuInfos.SkuInfo)
			if err != nil {
				return nil, err
			}
			combinationIds = append(combinationIds, combinationId)
			mapGroupIdCombinationId[skuInfos.GetGroupId()] = combinationId
		}
		existsMap, err := l.dao.BatchGetParcelLibraryDataBySkusCombinationId(ctx, request.GetRegion(), combinationIds)
		if err != nil {
			return nil, err
		}
		for _, skuInfos := range order.SkuGroup {
			ret := &pb.GroupParcelSizeInfo{
				GroupId:               skuInfos.GroupId,
				AccurateLength:        proto.String(""),
				AccurateWidth:         proto.String(""),
				AccurateHeight:        proto.String(""),
				AccurateWeight:        proto.String(""),
				WeightFinAccuracy:     proto.String(""),
				VolumetricFinAccuracy: proto.String(""),
			}
			if parcelData, ok := existsMap[mapGroupIdCombinationId[skuInfos.GetGroupId()]]; ok && parcelData.Success {
				ret.AccurateLength = proto.String(fmt.Sprintf("%v", parcelData.Data.AccurateLength))
				ret.AccurateWidth = proto.String(fmt.Sprintf("%v", parcelData.Data.AccurateWidth))
				ret.AccurateHeight = proto.String(fmt.Sprintf("%v", parcelData.Data.AccurateHeight))
				ret.AccurateWeight = proto.String(fmt.Sprintf("%v", parcelData.Data.AccurateWeight))
				ret.WeightFinAccuracy = proto.String(fmt.Sprintf("%v", parcelData.Data.WeightFinAccuracy))
				ret.VolumetricFinAccuracy = proto.String(fmt.Sprintf("%v", parcelData.Data.VolumetricFinAccuracy))
			}
			orderResult.OrderResult = append(orderResult.OrderResult, ret)
		}
		result = append(result, &orderResult)
	}
	return &pb.BatchGetParcelLibraryDataBySkuInfosResponse{OrderResult: result, RespHeader: http.GrpcSuccessRespHeader()}, nil
}

func (l *logisticParcelLibraryService) ListAllUpcomingParcelLibraryVersions(ctx utils.LCOSContext, region string) ([]*parcel_library.LogisticParcelLibraryVersionTab, *lcos_error.LCOSError) {
	region = strings.ToUpper(region)

	versions, err := l.dao.ListLogisticParcelLibraryVersionByParams(ctx, map[string]interface{}{
		"region":        region,
		"enable_status": constant.DISABLED,
	}, "notify_time ASC")
	if err != nil {
		return nil, err
	}
	return versions, nil
}

func (l *logisticParcelLibraryService) UpdateParcelLibraryVersion(ctx utils.LCOSContext, version *parcel_library.LogisticParcelLibraryVersionTab) *lcos_error.LCOSError {
	return l.dao.UpdateLogisticParcelLibraryVersion(ctx, version)
}

func (l *logisticParcelLibraryService) NotifyLogisticParcelLibraryVersion(ctx utils.LCOSContext, region string, fileList []string) *lcos_error.LCOSError {
	region = strings.ToUpper(region)

	dataFileList := make([]*parcel_library.DataFile, 0, len(fileList))
	for _, fileUrl := range fileList {
		dataFileList = append(dataFileList, &parcel_library.DataFile{
			FileUrl: fileUrl,
			Done:    false,
		})
	}
	version := &parcel_library.LogisticParcelLibraryVersionTab{
		Region:       region,
		DataFileList: dataFileList,
		EnableStatus: constant.DISABLED, // 0-未处理
		NotifyTime:   utils.GetTimestamp(ctx),
	}
	return l.dao.CreateLogisticParcelLibraryVersion(ctx, version)
}

func (l *logisticParcelLibraryService) BatchCreateOrUpdateParcelLibraryData(ctx utils.LCOSContext, region string, dataList []*parcel_library.LogisticParcelLibraryData) (int, *lcos_error.LCOSError) {
	if len(dataList) == 0 {
		return 0, nil
	}

	// 1. 获取可能被更新的存量parcel lib数据
	skusCombinationIdList := make([]uint64, 0, len(dataList))
	for _, data := range dataList {
		skusCombinationIdList = append(skusCombinationIdList, data.SkusCombinationId)
	}
	existsMap, err := l.dao.BatchGetParcelLibraryDataBySkusCombinationId(ctx, region, skusCombinationIdList)
	if err != nil {
		return 0, lcos_error.NewLCOSErrorf(err.RetCode, "get parcel data from redis error|region=%s, cause=%s", region, err.Msg)
	}

	// 2. 获取允许更新的parcel lib数据列表
	updateList := make([]*parcel_library.LogisticParcelLibraryData, 0, len(dataList))
	for _, newData := range dataList {
		oldData, ok := existsMap[newData.SkusCombinationId]
		if !ok {
			// 组合为新增，可以更新
			updateList = append(updateList, newData)
			continue
		}
		if !oldData.Success {
			// 组合不存在或者格式不合法，可以更新
			updateList = append(updateList, newData)
			continue
		}
		if newData.Version > oldData.Data.Version {
			// 新数据的版本晚于已有数据，可以更新
			updateList = append(updateList, newData)
			continue
		}
		// 新数据的生效时间不早于已有数据，不允许更新
	}

	// 3. 更新parcel lib数据到redis
	if err = l.dao.BatchCreateOrUpdateParcelLibraryData(ctx, region, updateList); err != nil {
		return 0, err
	}
	return len(updateList), nil
}

func (l *logisticParcelLibraryService) BatchGetParcelLibraryData(ctx utils.LCOSContext, region string, skusCombinationIdList []uint64) (map[uint64]*parcel_library.ParcelLibData, *lcos_error.LCOSError) {
	return l.dao.BatchGetParcelLibraryDataBySkusCombinationId(ctx, region, skusCombinationIdList)
}

func (l *logisticParcelLibraryService) GetCombinationIdFromSkuList(ctx utils.LCOSContext, skuInfos []*pb.SkuInfo) (uint64, *lcos_error.LCOSError) {
	skus := make([]*bulky_tag_dto.Sku, 0, len(skuInfos))
	for _, skuInfo := range skuInfos {
		skus = append(skus, &bulky_tag_dto.Sku{
			ItemId:   int64(skuInfo.GetItemId()),
			ModelId:  int64(skuInfo.GetModelId()),
			Quantity: int32(skuInfo.GetQuantity()),
		})
	}
	combinationId, err := wbc_checkout_sdk.GetSkusCombinationId(ctx, "", "", skus)
	if err != nil {
		return 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	return combinationId, nil
}

func (l *logisticParcelLibraryService) GetParcelLibraryDataBySkuInfos(ctx utils.LCOSContext, region string, skuInfos []*pb.SkuInfo, buyerPurchaseTime uint32) ([]*parcel_library.LogisticParcelLibraryData, bool, *lcos_error.LCOSError) {
	var (
		parcelInfos          []*parcel_library.LogisticParcelLibraryData
		allowListingFallback bool
	)

	// 1. 基于sku列表生成combination id
	combinationId, err := l.GetCombinationIdFromSkuList(ctx, skuInfos)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get parcel library data failed, generate combination id error|sku_infos=%s, cause=%s", utils.MarshToStringWithoutError(skuInfos), err.Msg)
		return nil, false, err
	}

	// 2. 从codis或者wbc获取parcel lib数据
	if buyerPurchaseTime != 0 {
		// 需要通过买家下单时间从wbc获取当时生效版本parcel lib数据
		client := waybill_center_service.NewWayBillCenterStaticService(region, config.GetConf(ctx).WBCStaticService.MaxRetryTimes)
		var wbcErr *lcos_error.LCOSError
		parcelInfos, allowListingFallback, wbcErr = client.QueryParcelDataByTime(ctx, combinationId, buyerPurchaseTime)
		if wbcErr != nil {
			logger.CtxLogErrorf(ctx, "get parcel library data failed, get data from wbc error|region=%s, combination_id=%d, buyer_purchase_time=%d, cause=%s", region, combinationId, buyerPurchaseTime, wbcErr.Msg)
			return nil, false, wbcErr
		}
	} else {
		// 从lcos codis获取最新版本的parcel lib数据
		retMap, lcosErr := l.dao.BatchGetParcelLibraryDataBySkusCombinationIdUsingCache(ctx, region, []uint64{combinationId})
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "get parcel library data failed, get data from codis error|region=%s, combination_id=%d, buyer_purchase_time=%d, cause=%s", region, combinationId, buyerPurchaseTime, lcosErr.Msg)
			return nil, false, lcosErr
		}
		data, ok := retMap[combinationId]
		if !ok {
			msg := "key not found"
			logger.CtxLogErrorf(ctx, "get parcel library data failed, get data from codis error|region=%s, combination_id=%d, buyer_purchase_time=%d, cause=%s", region, combinationId, buyerPurchaseTime, msg)
			return nil, false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, msg)
		}
		if !data.Success {
			logger.CtxLogErrorf(ctx, "get parcel library data failed, get data from codis error|region=%s, combination_id=%d, buyer_purchase_time=%d, cause=%s", region, combinationId, buyerPurchaseTime, data.Message)
			return nil, false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, data.Message)
		}
		parcelInfos = []*parcel_library.LogisticParcelLibraryData{data.Data}
	}
	Logger.CtxLogInfof(ctx, "get parcel library data success|region=%s, combination_id=%d, parcel_data=%s", region, combinationId, Logger.JsonStringForInfoLog(ctx, parcelInfos))

	// 3. 清洗数据
	for _, parcelInfo := range parcelInfos {
		// 长宽高必须同时存在，有一个缺失则全部置0
		if parcelInfo.AccurateLength <= 0 || parcelInfo.AccurateWidth <= 0 || parcelInfo.AccurateHeight <= 0 {
			parcelInfo.AccurateLength = 0
			parcelInfo.AccurateWidth = 0
			parcelInfo.AccurateHeight = 0
		}
	}
	return parcelInfos, allowListingFallback, nil
}

func (l *logisticParcelLibraryService) BatchGetLatestParcelLibraryDataBySkuInfos(ctx utils.LCOSContext, queries []*pb.ParcelLibraryQuery) (map[string]*parcel_library.LogisticParcelLibraryData, *lcos_error.LCOSError) {
	var (
		retMap              = make(map[string]*parcel_library.LogisticParcelLibraryData, len(queries)) // unique_id => PL
		keyMap              = make(map[string]uint64)                                                  // unique_id => combination_id
		redisCombinationMap = make(map[string][]uint64)                                                // region => combination_id list
		redisDataMap        = make(map[string]map[uint64]*parcel_library.ParcelLibData)                // region => combination_id => PL
	)

	// 1. 生成组合ID
	for _, query := range queries {
		combinationId, err := l.GetCombinationIdFromSkuList(ctx, query.GetSkuList())
		if err != nil {
			logger.CtxLogErrorf(ctx, "get parcel library data failed, generate combination id error|sku_list=%s, cause=%s", utils.MarshToStringWithoutError(query.GetSkuList()), err.Msg)
			continue
		}
		keyMap[query.GetUniqueId()] = combinationId // 保存每个query对应的combination id
		redisCombinationMap[query.GetRegion()] = append(redisCombinationMap[query.GetRegion()], combinationId)
	}

	// 2. 从redis获取当前正在生效的PL数据
	for region, combinationList := range redisCombinationMap {
		dataMap, err := l.dao.BatchGetParcelLibraryDataBySkusCombinationIdUsingCache(ctx, region, combinationList)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get parcel library data failed, query parcel library from redis error|region=%s, cause=%s", region, err.Msg)
			continue
		}
		redisDataMap[region] = dataMap
	}
	for _, query := range queries {
		regionDataMap, ok := redisDataMap[query.GetRegion()]
		if !ok {
			continue
		}
		data := regionDataMap[keyMap[query.GetUniqueId()]]
		if data == nil {
			continue
		}
		if !data.Success {
			continue
		}
		retMap[query.GetUniqueId()] = data.Data
	}
	return retMap, nil
}
