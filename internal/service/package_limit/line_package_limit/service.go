package line_package_limit

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	parcel_library2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	"math"
	"os"
	"time"

	"git.garena.com/peng.li/sls-package-calculate/formula/service/calculate_formula"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/local_formula"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/package_limit_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

type LinePackageLimitServiceInterface interface {
	BatchCreatePackageLimitModel(ctx utils.LCOSContext, request *line_package_limit.CreateOrUpdateLinePackageLimitRequest) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError)
	BatchCreatePackageLimitModelWithDraft(ctx utils.LCOSContext, request *line_package_limit.CreateOrUpdateLinePackageLimitRequest) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError)
	BatchUpdatePackageLimitModel(ctx utils.LCOSContext, request *line_package_limit.CreateOrUpdateLinePackageLimitRequest) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError)
	CreateOrUpdatePackageLimitModel(ctx utils.LCOSContext, request *line_package_limit.CreateOrUpdateLinePackageLimitRequest) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError)
	// GetPackageLimitList(ctx utils.LCOSContext, request *protocol.GetPackageLimitListRequest) (*GetPackageLimitListResponse, *utils.LCOSError)
	GetPackageLimitByLineId(ctx utils.LCOSContext, lineId string) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError)
	DeletePackageLimitByLineId(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError
	UploadPackageLimitModel(ctx utils.LCOSContext, request *common_protocol.LinePackageLimitUploadRequest) *lcos_error.LCOSError

	CheckLineRule(ctx utils.LCOSContext, lineId string, skuInfos []*pb.SkuInfo, orderInfo *pb.OrderInfo, buyerPurchaseTime uint32) (*pb.SingleCheckLineRuleResult, *lcos_error.LCOSError)
	GetLineRule(ctx utils.LCOSContext, lineId string) ([]*pb.RuleInfoDetail, *lcos_error.LCOSError)
}

type LinePackageLimitService struct {
	linePackageLimitDAO model.LinePackageLimitDAO
	parcelLibService    parcel_library.LogisticParcelLibraryService
}

func NewLinePackageLimitService(linePackageLimitDAO model.LinePackageLimitDAO, parcelLibService parcel_library.LogisticParcelLibraryService) *LinePackageLimitService {
	return &LinePackageLimitService{
		linePackageLimitDAO: linePackageLimitDAO,
		parcelLibService:    parcelLibService,
	}
}

func (service *LinePackageLimitService) BatchCreatePackageLimitModel(ctx utils.LCOSContext, request *line_package_limit.CreateOrUpdateLinePackageLimitRequest) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError) {
	if err := service.CheckCanCreatePackageLimit(ctx, request.LineId); err != nil {
		return nil, err
	}

	limitModels := fillLimitModels(request)
	return service.linePackageLimitDAO.BatchCreatePackageLimitModel(ctx, limitModels)
}

func (service *LinePackageLimitService) BatchCreatePackageLimitModelWithDraft(ctx utils.LCOSContext, request *line_package_limit.CreateOrUpdateLinePackageLimitRequest) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError) {
	if err := service.CheckCanCreatePackageLimitWithDraft(ctx, request.LineId); err != nil {
		return nil, err
	}

	limitModels := fillLimitModels(request)
	return service.linePackageLimitDAO.BatchCreatePackageLimitModel(ctx, limitModels)
}

func (service *LinePackageLimitService) BatchUpdatePackageLimitModel(ctx utils.LCOSContext, request *line_package_limit.CreateOrUpdateLinePackageLimitRequest) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError) {
	if err := service.CheckCanUpdatePackageLimit(ctx, request.LineId); err != nil {
		return nil, err
	}

	limitModels := fillLimitModels(request)

	fc := func() *lcos_error.LCOSError {
		if err := service.linePackageLimitDAO.DeletePackageLimitModelsByLineId(ctx, request.LineId); err != nil {
			return err
		}
		if _, err := service.linePackageLimitDAO.BatchCreatePackageLimitModel(ctx, limitModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return limitModels, nil
}

func (service *LinePackageLimitService) CreateOrUpdatePackageLimitModel(ctx utils.LCOSContext, request *line_package_limit.CreateOrUpdateLinePackageLimitRequest) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError) {
	exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId)
	if e != nil {
		return nil, e
	}
	if !exists {
		logger.LogErrorf("update failed, line_id not exist|lineId=%s", request.LineId)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}
	limitModels := fillLimitModels(request)

	fc := func() *lcos_error.LCOSError {
		if err := service.linePackageLimitDAO.DeletePackageLimitModelsByLineId(ctx, request.LineId); err != nil {
			return err
		}
		if _, err := service.linePackageLimitDAO.BatchCreatePackageLimitModel(ctx, limitModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return limitModels, nil
}

func fillLimitModels(request *line_package_limit.CreateOrUpdateLinePackageLimitRequest) []*model.LinePackageLimitTab {

	var limitModels []*model.LinePackageLimitTab
	for i := 0; i < len(request.PackageLimits); i++ {
		if request.PackageLimits[i].SortFlag == nil {
			// 如果为空，赋一个无意义的默认值
			defaultFlag := constant.DISABLED
			request.PackageLimits[i].SortFlag = &defaultFlag
		}
		conditionParamsString := package_limit_util.ConvertConditionParamsToJsonString(request.PackageLimits[i].ConditionFormulaParams)
		limitModels = append(limitModels, &model.LinePackageLimitTab{
			LineId:                 request.LineId,
			Region:                 request.Region,
			RuleType:               request.PackageLimits[i].RuleType,
			LimitFlag:              *request.PackageLimits[i].LimitFlag,
			MaxWeight:              request.PackageLimits[i].MaxWeight,
			MinWeight:              request.PackageLimits[i].MinWeight,
			MaxSize:                request.PackageLimits[i].MaxSize,
			MinSize:                request.PackageLimits[i].MinSize,
			MaxLength:              request.PackageLimits[i].MaxLength,
			MinLength:              request.PackageLimits[i].MinLength,
			MaxWidth:               request.PackageLimits[i].MaxWidth,
			MinWidth:               request.PackageLimits[i].MinWidth,
			MaxHeight:              request.PackageLimits[i].MaxHeight,
			MinHeight:              request.PackageLimits[i].MinHeight,
			MaxSymbol:              request.PackageLimits[i].MaxSymbol,
			MinSymbol:              request.PackageLimits[i].MinSymbol,
			Formula:                request.PackageLimits[i].Formula,
			VolumetricFactor:       request.PackageLimits[i].VolumetricFactor,
			SortFlag:               *request.PackageLimits[i].SortFlag,
			ConditionFormulaParams: conditionParamsString,
			ByPass:                 request.ByPass,
			ByPassPurchaseTime:     request.ByPassPurchaseTime,
			Operator:               request.Operator, // 填充修改者，方便知道谁最近修改了规则
			UseParcelLibrary:       request.UseParcelLibrary,
		})
	}
	return limitModels
}

func (service *LinePackageLimitService) CheckCanCreatePackageLimit(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError {
	// 如果线已经有了包裹限制，不允许在创建，只能更新
	l, err := service.linePackageLimitDAO.GetPackageLimitModelsByLineId(ctx, lineId)
	if err != nil {
		return err
	}
	if len(l) != 0 {
		logger.LogErrorf("create failed, line_id already has package limits|lineId=%s", lineId)
		return lcos_error.NewLCOSError(lcos_error.NotFoundPackageLimitRuleErrorCode, "create failed, line_id already has package limits")
	}

	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, lineId)
	if err != nil {
		return err
	}
	if !exists {
		logger.LogErrorf("create failed, line_id not exist|lineId=%s", lineId)
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}
	return nil
}

func (service *LinePackageLimitService) CheckCanCreatePackageLimitWithDraft(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError {
	// 如果线已经有了包裹限制，不允许在创建，只能更新
	l, err := service.linePackageLimitDAO.GetPackageLimitModelsByLineId(ctx, lineId)
	if err != nil {
		return err
	}
	if len(l) != 0 {
		logger.LogErrorf("create failed, line_id already has package limits|lineId=%s", lineId)
		return lcos_error.NewLCOSError(lcos_error.NotFoundPackageLimitRuleErrorCode, "create failed, line_id already has package limits")
	}

	return nil
}

func (service *LinePackageLimitService) CheckCanUpdatePackageLimit(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError {
	// 如果线包裹限制，不允许更新，只能创建
	l, err := service.linePackageLimitDAO.GetPackageLimitModelsByLineId(ctx, lineId)
	if err != nil {
		return err
	}

	if len(l) == 0 {
		logger.LogErrorf("update failed, line_id does not have package limits|lineId=%s", lineId)
		return lcos_error.NewLCOSError(lcos_error.NotFoundPackageLimitRuleErrorCode, "update failed, line_id does not have package limits")
	}

	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, lineId)
	if err != nil {
		return err
	}
	if !exists {
		logger.LogErrorf("update failed, line_id not exist|lineId=%s", lineId)
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}
	return nil
}

func (service *LinePackageLimitService) GetPackageLimitByLineId(ctx utils.LCOSContext, lineId string) ([]*model.LinePackageLimitTab, *lcos_error.LCOSError) {
	limitModels, err := service.linePackageLimitDAO.GetPackageLimitModelsByLineId(ctx, lineId)
	if err != nil {
		return nil, err
	}

	return limitModels, nil
}

func (service *LinePackageLimitService) DeletePackageLimitByLineId(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError {
	err := service.linePackageLimitDAO.DeletePackageLimitModelsByLineId(ctx, lineId)
	if err != nil {
		return err
	}
	return nil
}

func (service *LinePackageLimitService) GetLineRule(ctx utils.LCOSContext, lineId string) ([]*pb.RuleInfoDetail, *lcos_error.LCOSError) {
	limitModels, err := service.linePackageLimitDAO.GetPackageLimitModelsByLineIdUseCache(ctx, lineId)
	if err != nil {
		return nil, err
	}
	if len(limitModels) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundPackageLimitRuleErrorCode, "can not find package limits")
	}

	ruleInfos := make([]*pb.RuleInfoDetail, 0, len(limitModels))
	for _, rule := range limitModels {
		ruleType := uint32(rule.RuleType)
		limitFlag := uint32(rule.LimitFlag)
		byPassPurchaseTime := uint32(rule.ByPassPurchaseTime)
		ruleInfos = append(ruleInfos, &pb.RuleInfoDetail{
			RuleType:           &ruleType,
			LimitFlag:          &limitFlag,
			MaxWeight:          &rule.MaxWeight,
			MinWeight:          &rule.MinWeight,
			MaxSize:            &rule.MaxSize,
			MinSize:            &rule.MinSize,
			MaxLength:          &rule.MaxLength,
			MinLength:          &rule.MinLength,
			MaxWidth:           &rule.MaxWidth,
			MinWidth:           &rule.MinWidth,
			MaxHeight:          &rule.MaxHeight,
			MinHeight:          &rule.MinHeight,
			UseParcelLib:       &rule.UseParcelLibrary,
			ByPass:             &rule.ByPass,
			ByPassPurchaseTime: &byPassPurchaseTime,
		})
	}
	return ruleInfos, nil
}

func (service *LinePackageLimitService) CheckLineRule(ctx utils.LCOSContext, lineId string, skuInfos []*pb.SkuInfo, orderInfo *pb.OrderInfo, buyerPurchaseTime uint32) (*pb.SingleCheckLineRuleResult, *lcos_error.LCOSError) {
	// 1. 获取线package limit配置
	packageLimits, err := service.linePackageLimitDAO.GetPackageLimitModelsByLineIdUseCache(ctx, lineId)
	if err != nil {
		return &pb.SingleCheckLineRuleResult{}, err
	}
	if len(packageLimits) == 0 {
		return &pb.SingleCheckLineRuleResult{}, lcos_error.NewLCOSError(lcos_error.NotFoundPackageLimitRuleErrorCode, "can not find package limits")
	}

	// 2. 尝试基于sku列表和下单时间匹配parcel尺寸和重量
	parcelInfos, useListingFallback := service.FetchParcelLibraryData(ctx, packageLimits, skuInfos, buyerPurchaseTime)
	if len(parcelInfos) == 0 || useListingFallback {
		parcelInfos = append(parcelInfos, parcel_library2.EmptyParcelLibraryData)
	}
	if len(parcelInfos) > 1 && !config.AllowParcelLibraryFallback(ctx) {
		parcelInfos = parcelInfos[:1] // 存在多个版本时，若不允许兜底，则仅使用第一个版本校验
	}

	// 3. 校验订单包裹是否超过渠道限制
	// 两种校验场景：
	// 1）存在parcel数据：则按照以下顺序兜底，parcel(v3) -> parcel(v2) -> parcel(v1) -> listing（如果useListingFallback为true）
	// 2）不存在parcel数据：则使用listing数据校验
	var (
		// 校验结果
		checkResult  uint32
		limitDetails []*pb.RuleLimitDetail
		useFallback  bool
	)
	for _, parcelInfo := range parcelInfos {
		if useFallback {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatPackageLimitCheckFallback, fmt.Sprintf("line_id:%s,lcos_version:%d,wbc_version:%d", lineId, parcelInfo.GetLcosVersion(), parcelInfo.GetWbcVersion()), constant.StatusError, "")
		}

		checkResult, limitDetails = service.checkLineRule(ctx, lineId, packageLimits, skuInfos, orderInfo, parcelInfo, buyerPurchaseTime)
		if checkResult == uint32(constant.SUCCESS) {
			logger.CtxLogInfof(ctx, "check line rule success with parcel info|line_id=%s, lcos_version=%d, wbc_version=%d", lineId, parcelInfo.GetLcosVersion(), parcelInfo.GetWbcVersion())
			// 校验通过，直接返回
			break
		}
		// 校验失败，尝试使用旧版本parcel数据或者listing数据兜底。配置变更或者checkout时lcos和wbc最新版本不一致可能走到此分支
		logger.CtxLogErrorf(ctx, "check line rule failed with parcel info|line_id=%s, lcos_version=%d, wbc_version=%d", lineId, parcelInfo.GetLcosVersion(), parcelInfo.GetWbcVersion())
		useFallback = true
	}

	return &pb.SingleCheckLineRuleResult{
		LineId:      &lineId,
		CheckResult: &checkResult,
		LimitDetail: limitDetails,
	}, nil
}

func (service *LinePackageLimitService) checkLineRule(ctx utils.LCOSContext, lineId string, packageLimits []*model.LinePackageLimitTab, skuInfos []*pb.SkuInfo, orderInfo *pb.OrderInfo, parcelInfo *parcel_library2.LogisticParcelLibraryData, buyerPurchaseTime uint32) (uint32, []*pb.RuleLimitDetail) {
	var limitDetails []*pb.RuleLimitDetail
	var checkResult = uint32(constant.SUCCESS)
	sideLimits := local_formula.GetSizeLimitFromLineRules(packageLimits)
	var cachePaecelSize *calculate_formula.Size = nil

	for _, packageLimit := range packageLimits {
		if packageLimit.LimitFlag == constant.DISABLED {
			logger.LogInfof("line_id|%s no need to check rule type %d", lineId, packageLimit.RuleType)
			continue
		}

		if service.checkIfNeedByPass(packageLimit, buyerPurchaseTime) {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatPackageLimitByPass, fmt.Sprintf("%s:%d", lineId, packageLimit.RuleType), constant.StatusSuccess, "")
			logger.LogInfof("line_id|%s no need to check rule type %d, bypass[%v], bypass purchase time[%v], buyer purchase time[%v]", lineId, packageLimit.RuleType, packageLimit.ByPass, packageLimit.ByPassPurchaseTime, buyerPurchaseTime)
			continue
		}

		// 获取包裹的长/宽/高/重量信息。优先获取parcel维度数据，parcel数据不存在则基于listing数据通过公式计算parcel数据
		calculateResult := parcelInfo.GetParcelData(packageLimit.RuleType)
		if package_limit_util.IsWeightLimitRule(packageLimit.RuleType) && utils.CheckInUint32(packageLimit.Formula, config.GetMutableConf(ctx).PackageLimitConfig.VolumetricWeightFormulas) {
			// 使用体积重公式时，校验重=max(体积重，实际重)，仅当体积重和实际重都为0时才使用listing数据兜底
			// parcel_volumetric_weight = parcel_length * parcel_width * parcel_height / volumetric_factor
			// parcel_validate_weight = max(parcel_weight, parcel_volumetric_weight)
			volumetricWeight := package_limit_util.CalculateBaseVolumetricWeight(parcelInfo.GetAccurateLength(), parcelInfo.GetAccurateWidth(), parcelInfo.GetAccurateHeight(), packageLimit.VolumetricFactor)
			calculateResult = math.Max(calculateResult, volumetricWeight)
		}
		if calculateResult <= 0 {
			// 不存在parcel数据，使用listing数据通过公式计算包裹尺寸和重量
			ruleType := uint32(packageLimit.RuleType)

			calculateParam := local_formula.ConvertLineDbDataToCalculateInput(orderInfo, skuInfos, packageLimit, sideLimits)
			calculateParam.NeedLog = true
			// 默认需要重排商品长宽高
			if checkErr := calculate_formula.CheckSkuInfo(calculateParam.SkuParam, "length", "width", "height"); checkErr == nil {
				// 检查长宽高是否缺失，没有缺失则进行重排
				if err := calculate_formula.SortSkuInfo(calculateParam.SkuParam); err != nil {
					msg := fmt.Sprintf("line_id|%s sort sku failed, ruleType|%d, err|%s", lineId, packageLimit.RuleType, err.Error())
					limitDetails = append(limitDetails, package_limit_util.GenerateFailedLimitDetail(msg, ruleType, 0))
					checkResult = uint32(constant.FAILED)
					continue
				}
			}
			calculateParam.CacheParcelSize = cachePaecelSize

			// 根据公式计算结果
			var err error
			calculateResult, err = calculate_formula.CalculateResultByFormulaNumber(packageLimit.Formula, calculateParam)
			// Print logs for data recovery on the algorithm side
			if calculateParam.DataAlgoLogMsg != "" {
				logger.CtxLogErrorf(ctx, "%s", calculateParam.DataAlgoLogMsg)
				calculateParam.DataAlgoLogMsg = ""
			}
			if err != nil {
				msg := fmt.Sprintf("line_id|%s calculate failed, ruleType|%d, err|%s", lineId, packageLimit.RuleType, err.Error())
				limitDetails = append(limitDetails, package_limit_util.GenerateFailedLimitDetail(msg, ruleType, calculateResult))
				checkResult = uint32(constant.FAILED)
				continue
			}
			cachePaecelSize = calculateParam.CacheParcelSize
		}

		singleRuleResult := package_limit_util.CheckSingleRuleType(package_limit_util.ConvertLineTabToCommonTab(packageLimit), calculateResult)
		if *singleRuleResult.SingleCheckResult == uint32(constant.FAILED) {
			checkResult = uint32(constant.FAILED)
		}
		limitDetails = append(limitDetails, singleRuleResult)
	}

	return checkResult, limitDetails
}

func (service *LinePackageLimitService) UploadPackageLimitModel(ctx utils.LCOSContext, request *common_protocol.LinePackageLimitUploadRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_origin_postcode.xlsx")
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_origin_postcode.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	addPackageModelsMap := make(map[string][]*model.LinePackageLimitTab)
	// 只解析前9行数据
	for rows.Next() && lineNum <= 10 {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId)
		if e != nil {
			return e
		}
		if !exists {
			logger.LogErrorf("update failed, line_id not exist|lineId=%s", request.LineId)
			return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
		}

		if rowStruct, err := package_limit_util.ParsePackageRowData(lineNum, row); err != nil {
			return err
		} else {
			if err := package_limit_util.CheckPackageRowData(lineNum, rowStruct); err != nil {
				return err
			}
			packageModel := &model.LinePackageLimitTab{
				LineId:                 request.LineId,
				Region:                 request.Region,
				RuleType:               rowStruct.RoleType,
				LimitFlag:              rowStruct.LimitFlag,
				MaxWeight:              rowStruct.MaxWeight,
				MinWeight:              rowStruct.MinWeight,
				MaxSize:                rowStruct.MaxSize,
				MinSize:                rowStruct.MinSize,
				MaxLength:              rowStruct.MaxLength,
				MinLength:              rowStruct.MinLength,
				MaxWidth:               rowStruct.MaxWidth,
				MinWidth:               rowStruct.MinWidth,
				MaxHeight:              rowStruct.MaxHeight,
				MinHeight:              rowStruct.MinHeight,
				MaxSymbol:              rowStruct.MaxSymbol,
				MinSymbol:              rowStruct.MinSymbol,
				VolumetricFactor:       rowStruct.VolumetricFactor,
				SortFlag:               rowStruct.SortFlag,
				Formula:                rowStruct.Formula,
				ConditionFormulaParams: rowStruct.ConditionFormulaParams,
				UseParcelLibrary:       false, // 上传line package limit场景，目前应该没有使用，默认不开启parcel lib，如果需要再编辑打开
				ByPass:                 false,
				ByPassPurchaseTime:     0,
			}
			addPackageModelsMap[request.LineId] = append(addPackageModelsMap[request.LineId], packageModel)
		}
	}

	fc := func() *lcos_error.LCOSError {
		service.linePackageLimitDAO.DeletePackageLimitModelsByLineId(ctx, request.LineId)
		for lineId, addPostcodeModels := range addPackageModelsMap {
			println(lineId, addPostcodeModels)
			if _, err := service.linePackageLimitDAO.BatchCreatePackageLimitModel(ctx, addPostcodeModels); err != nil {
				return err
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}

func (service *LinePackageLimitService) FetchParcelLibraryData(ctx utils.LCOSContext, packageLimits []*model.LinePackageLimitTab, skuInfos []*pb.SkuInfo, buyerPurchaseTime uint32) ([]*parcel_library2.LogisticParcelLibraryData, bool) {
	// 1. 检查是否启用parcel library
	var (
		region       string
		useParcelLib bool
	)
	for _, packageLimit := range packageLimits {
		region = packageLimit.Region
		if region == "XX" {
			// line package limit的region字段可能为XX
			region = utils.GetRegionFromResourceId(packageLimit.LineId)
		}
		if packageLimit.LimitFlag == constant.TRUE && packageLimit.UseParcelLibrary {
			useParcelLib = true
			break
		}
	}
	if !useParcelLib || config.IsParcelLibraryDowngraded(ctx, region) {
		return nil, false
	}

	// 2. 获取parcel library数据
	parcelInfos, allowListingFallback, err := service.parcelLibService.GetParcelLibraryDataBySkuInfos(ctx, region, skuInfos, buyerPurchaseTime)
	if err != nil {
		return nil, false
	}
	return parcelInfos, allowListingFallback
}

func (service *LinePackageLimitService) checkIfNeedByPass(packageLimit *model.LinePackageLimitTab, buyerPurchaseTime uint32) bool {
	var needByPass bool

	if packageLimit == nil || buyerPurchaseTime == 0 || packageLimit.ByPassPurchaseTime == 0 {
		return needByPass
	}

	if packageLimit.ByPass && packageLimit.ByPassPurchaseTime >= int64(buyerPurchaseTime) {
		needByPass = true
	}

	return needByPass
}
