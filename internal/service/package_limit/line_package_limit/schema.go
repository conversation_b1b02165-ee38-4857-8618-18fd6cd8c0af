package line_package_limit

type (
	GetPackageLimitListResponse struct {
		PageNO int                       `json:"pageno"`
		Total  int                       `json:"total"`
		Count  int                       `json:"count"`
		List   []*SinglePackageLimitData `json:"list"`
	}

	SinglePackageLimitData struct {
		LineId                                          string `json:"line_id"`
		LineName                                        string `json:"line_name"`
		ActualWeightExtremumRuleFlag                    uint8  `json:"actual_weight_extremum_rule_flag"`
		VolumetricWeightExtremumRuleFlag                uint8  `json:"volumetric_weight_extremum_rule_flag"`
		ActualWeightAndVolumetricWeightExtremumRuleFlag uint8  `json:"max_weight_extremum_rule_flag"`
		ActualWeightAndVolumetricWeightRatioRuleFlag    uint8  `json:"weight_ratio_rule_flag"`
		MaxSizeExtremumRuleFlag                         uint8  `json:"max_size_extremum_rule_flag"`
		SizeSumExtremumRuleFlag                         uint8  `json:"size_sum_extremum_rule_flag"`
		LengthExtremumRuleFlag                          uint8  `json:"length_extremum_rule_flag"`
		WidthExtremumRuleFlag                           uint8  `json:"width_extremum_rule_flag"`
		HeightExtremumRuleFlag                          uint8  `json:"height_extremum_rule_flag"`
		CTime                                           uint32 `json:"ctime"`
		MTime                                           uint32 `json:"mtime"`
		LineStatus                                      uint8  `json:"line_status"`
	}
)
