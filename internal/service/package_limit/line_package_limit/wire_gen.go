// Code generated by Wire. DO NOT EDIT.

//go:generate wire
//+build !wireinject

package line_package_limit

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
)

// Injectors from wire.go:

func InitLinePackageLimitService() *LinePackageLimitService {
	linePackageLimitDAO := line_package_limit.NewLinePackageLimitDAO()
	linePackageLimitService := &LinePackageLimitService{
		linePackageLimitDAO: linePackageLimitDAO,
	}
	return linePackageLimitService
}
