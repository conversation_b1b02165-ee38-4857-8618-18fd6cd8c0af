package line_package_limit

import (
	"os"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/line_package_limit"
	line_package_limit2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/test/sqlmock"
	limit_data "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/test/testdata/database/package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/test/tools"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/stretchr/testify/assert"
)

func TestMain(m *testing.M) {
	fc := tools.InitBaseEnv()
	defer fc()
	os.Exit(m.Run())
}

func Test_BatchCreatePackageLimitModel(t *testing.T) {
	tests := []struct {
		name       string
		preHook    func() (*LinePackageLimitService, func())
		assertFunc func(t *testing.T, got0 []*line_package_limit2.LinePackageLimitTab, got1 *lcos_error.LCOSError)
		ctx        utils.LCOSContext
		request    *line_package_limit.CreateOrUpdateLinePackageLimitRequest
	}{
		// write your code
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			obj, deferFunc := tt.preHook()
			defer deferFunc()
			got0, got1 := obj.BatchCreatePackageLimitModel(tt.ctx, tt.request)
			tt.assertFunc(t, got0, got1)
		})
	}
}

func Test_BatchUpdatePackageLimitModel(t *testing.T) {
	tests := []struct {
		name       string
		preHook    func() (*LinePackageLimitService, func())
		assertFunc func(t *testing.T, got0 []*line_package_limit2.LinePackageLimitTab, got1 *lcos_error.LCOSError)
		ctx        utils.LCOSContext
		request    *line_package_limit.CreateOrUpdateLinePackageLimitRequest
	}{
		// write your code
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			obj, deferFunc := tt.preHook()
			defer deferFunc()
			got0, got1 := obj.BatchUpdatePackageLimitModel(tt.ctx, tt.request)
			tt.assertFunc(t, got0, got1)
		})
	}
}

func Test_CreateOrUpdatePackageLimitModel(t *testing.T) {
	tests := []struct {
		name       string
		preHook    func() (*LinePackageLimitService, func())
		assertFunc func(t *testing.T, got0 []*line_package_limit2.LinePackageLimitTab, got1 *lcos_error.LCOSError)
		ctx        utils.LCOSContext
		request    *line_package_limit.CreateOrUpdateLinePackageLimitRequest
	}{
		// write your code
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			obj, deferFunc := tt.preHook()
			defer deferFunc()
			got0, got1 := obj.CreateOrUpdatePackageLimitModel(tt.ctx, tt.request)
			tt.assertFunc(t, got0, got1)
		})
	}
}

func Test_CheckCanCreatePackageLimit(t *testing.T) {
	tests := []struct {
		name       string
		preHook    func() (*LinePackageLimitService, func())
		assertFunc func(t *testing.T, got0 *lcos_error.LCOSError)
		ctx        utils.LCOSContext
		lineId     string
	}{
		// write your code
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			obj, deferFunc := tt.preHook()
			defer deferFunc()
			got0 := obj.CheckCanCreatePackageLimit(tt.ctx, tt.lineId)
			tt.assertFunc(t, got0)
		})
	}
}

func Test_CheckCanUpdatePackageLimit(t *testing.T) {
	tests := []struct {
		name       string
		preHook    func() (*LinePackageLimitService, func())
		assertFunc func(t *testing.T, got0 *lcos_error.LCOSError)
		ctx        utils.LCOSContext
		lineId     string
	}{
		// write your code
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			obj, deferFunc := tt.preHook()
			defer deferFunc()
			got0 := obj.CheckCanUpdatePackageLimit(tt.ctx, tt.lineId)
			tt.assertFunc(t, got0)
		})
	}
}

func Test_GetPackageLimitByLineId(t *testing.T) {
	tests := []struct {
		name       string
		preHook    func() (*LinePackageLimitService, func())
		assertFunc func(t *testing.T, got0 []*line_package_limit2.LinePackageLimitTab, got1 *lcos_error.LCOSError)
		ctx        utils.LCOSContext
		lineId     string
	}{
		// write your code
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			obj, deferFunc := tt.preHook()
			defer deferFunc()
			got0, got1 := obj.GetPackageLimitByLineId(tt.ctx, tt.lineId)
			tt.assertFunc(t, got0, got1)
		})
	}
}

func Test_GetLineRule(t *testing.T) {
	tests := []struct {
		name       string
		preHook    func() (*LinePackageLimitService, func())
		assertFunc func(t *testing.T, got0 []*lcos_protobuf.RuleInfoDetail, got1 *lcos_error.LCOSError)
		ctx        utils.LCOSContext
		lineId     string
	}{
		// write your code
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			obj, deferFunc := tt.preHook()
			defer deferFunc()
			got0, got1 := obj.GetLineRule(tt.ctx, tt.lineId)
			tt.assertFunc(t, got0, got1)
		})
	}
}

func Test_CheckLineRule(t *testing.T) {
	// 设置localcache
	sqlMocker := sqlmock.NewMock()
	sqlMocker.SetGormDB()
	sqlMocker.ExpectSelect().WillReturnGormModels(limit_data.AllRule)
	localcache.LoadForUnitTest(constant.LinePackageLimitNamespace, nil)
	defer func() {
		sqlMocker.Finish(t)
	}()

	tests := []struct {
		name       string
		preHook    func() (*LinePackageLimitService, func())
		assertFunc func(t *testing.T, got0 *lcos_protobuf.SingleCheckLineRuleResult, got1 *lcos_error.LCOSError)
		ctx        utils.LCOSContext
		lineId     string
		skuInfos   []*lcos_protobuf.SkuInfo
		orderInfo  *lcos_protobuf.OrderInfo
	}{
		{
			"包裹维度重量校验 公式3001 校验不通过",
			func() (*LinePackageLimitService, func()) {
				obj := InitLinePackageLimitService()
				return obj, func() {
				}
			},
			func(t *testing.T, got0 *lcos_protobuf.SingleCheckLineRuleResult, got1 *lcos_error.LCOSError) {
				if assert.Empty(t, got1) {
					assert.EqualValues(t, constant.FAILED, *got0.CheckResult)
					assert.EqualValues(t, constant.ActualWeightExtremumRule, *got0.LimitDetail[0].RuleType)
					assert.EqualValues(t, constant.FAILED, *got0.LimitDetail[0].SingleCheckResult)
				}
			},
			utils.NewCommonCtx(nil),
			"LVN666",
			nil,
			&lcos_protobuf.OrderInfo{
				OrderActualWeight:     utils.NewFloat64(20000),
				OrderVolumetricWeight: nil,
				OrderLength:           utils.NewFloat64(30),
				OrderWidth:            utils.NewFloat64(20),
				OrderHeight:           utils.NewFloat64(10),
			},
		},
		{
			"包裹维度重量校验 公式3001 校验通过",
			func() (*LinePackageLimitService, func()) {
				obj := InitLinePackageLimitService()
				return obj, func() {
				}
			},
			func(t *testing.T, got0 *lcos_protobuf.SingleCheckLineRuleResult, got1 *lcos_error.LCOSError) {
				if assert.Empty(t, got1) {
					assert.EqualValues(t, constant.SUCCESS, *got0.CheckResult)
				}
			},
			utils.NewCommonCtx(nil),
			"LVN666",
			nil,
			&lcos_protobuf.OrderInfo{
				OrderActualWeight:     utils.NewFloat64(99),
				OrderVolumetricWeight: nil,
				OrderLength:           utils.NewFloat64(30),
				OrderWidth:            utils.NewFloat64(20),
				OrderHeight:           utils.NewFloat64(10),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			obj, deferFunc := tt.preHook()
			defer deferFunc()
			got0, got1 := obj.CheckLineRule(tt.ctx, tt.lineId, tt.skuInfos, tt.orderInfo, 0)
			tt.assertFunc(t, got0, got1)
		})
	}
}
