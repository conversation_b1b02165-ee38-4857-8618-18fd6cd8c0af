//go:generate wire
//go:build wireinject
// +build wireinject

package line_package_limit

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	"github.com/google/wire"
)

func InitLinePackageLimitService() *LinePackageLimitService {
	wire.Build(
		wire.Struct(new(LinePackageLimitService), "*"),
		line_package_limit.LinePackageLimitDAOProviderSet,
	)

	return nil
}
