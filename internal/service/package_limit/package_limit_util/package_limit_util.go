package package_limit_util

import (
	"encoding/json"
	"fmt"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"math"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/local_formula"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type ConditionFormula struct {
	KeySymbol uint8   `json:"key_symbol"`
	KeyParam  float64 `json:"key_param"`
}

type CommonPackageLimitModel struct {
	RuleType               uint8
	LimitFlag              uint8
	MaxWeight              float64
	MinWeight              float64
	MaxSize                float64
	MinSize                float64
	MaxLength              float64
	MinLength              float64
	MaxWidth               float64
	MinWidth               float64
	MaxHeight              float64
	MinHeight              float64
	MaxSymbol              uint8
	MinSymbol              uint8
	VolumetricFactor       uint32
	SortFlag               uint8
	Formula                uint32
	ConditionFormulaParams string
}

func ConvertConditionParamsToJsonString(conditionParams []*common_protocol.ConditionFormulaParams) string {
	if len(conditionParams) == 0 {
		return ""
	}
	jsonRequest, err := json.Marshal(conditionParams)
	if err != nil {
		logger.LogErrorf("json marshal error: %s", err.Error())
		return ""
	}
	return string(jsonRequest)
}

func GenerateFailedLimitDetail(errMsg string, ruleType uint32, calculateResult float64) *pb.RuleLimitDetail {
	logger.LogInfof(errMsg)
	singleCheckResult := uint32(constant.FAILED)
	return &pb.RuleLimitDetail{
		RuleType:          &ruleType,
		SingleCheckResult: &singleCheckResult,
		Reason:            &errMsg,
		CalculateResult:   &calculateResult,
	}
}

func GenerateSuccessLimitDetail(ruleType uint32, calculateResult float64) *pb.RuleLimitDetail {
	singleCheckResult := uint32(constant.SUCCESS)
	msg := "success"
	return &pb.RuleLimitDetail{
		RuleType:          &ruleType,
		SingleCheckResult: &singleCheckResult,
		Reason:            &msg,
		CalculateResult:   &calculateResult,
	}
}

func ConvertLineTabToCommonTab(tab *line_package_limit.LinePackageLimitTab) *CommonPackageLimitModel {
	return &CommonPackageLimitModel{
		RuleType:               tab.RuleType,
		LimitFlag:              tab.LimitFlag,
		MaxWeight:              tab.MaxWeight,
		MinWeight:              tab.MinWeight,
		MaxSize:                tab.MaxSize,
		MinSize:                tab.MinSize,
		MaxLength:              tab.MaxLength,
		MinLength:              tab.MinLength,
		MaxWidth:               tab.MaxWidth,
		MinWidth:               tab.MinWidth,
		MaxHeight:              tab.MaxHeight,
		MinHeight:              tab.MinHeight,
		MaxSymbol:              tab.MaxSymbol,
		MinSymbol:              tab.MinSymbol,
		VolumetricFactor:       tab.VolumetricFactor,
		SortFlag:               tab.SortFlag,
		Formula:                tab.Formula,
		ConditionFormulaParams: tab.ConditionFormulaParams,
	}
}

func ConvertProductTabToCommonTab(tab *product_package_limit.ProductPackageLimitTab) *CommonPackageLimitModel {
	return &CommonPackageLimitModel{
		RuleType:               tab.RuleType,
		LimitFlag:              tab.LimitFlag,
		MaxWeight:              tab.MaxWeight,
		MinWeight:              tab.MinWeight,
		MaxSize:                tab.MaxSize,
		MinSize:                tab.MinSize,
		MaxLength:              tab.MaxLength,
		MinLength:              tab.MinLength,
		MaxWidth:               tab.MaxWidth,
		MinWidth:               tab.MinWidth,
		MaxHeight:              tab.MaxHeight,
		MinHeight:              tab.MinHeight,
		MaxSymbol:              tab.MaxSymbol,
		MinSymbol:              tab.MinSymbol,
		VolumetricFactor:       tab.VolumetricFactor,
		SortFlag:               tab.SortFlag,
		Formula:                tab.Formula,
		ConditionFormulaParams: tab.ConditionFormulaParams,
	}
}

func CheckSingleRuleType(packageLimit *CommonPackageLimitModel, calculateResult float64) *pb.RuleLimitDetail {
	ruleType := uint32(packageLimit.RuleType)
	maxValue, minValue := getExtremumValueByRuleType(packageLimit)
	maxCompareResult := local_formula.CompareWith(packageLimit.MaxSymbol, calculateResult, maxValue)
	minCompareResult := local_formula.CompareWith(packageLimit.MinSymbol, calculateResult, minValue)
	if !maxCompareResult {
		msg := fmt.Sprintf("check rule|%d |ruleName| %s failed, %f exceed max value %f", ruleType, constant.LinePackageLimitRuleType[packageLimit.RuleType], calculateResult, maxValue)
		return GenerateFailedLimitDetail(msg, ruleType, calculateResult)
	} else if !minCompareResult {
		msg := fmt.Sprintf("check rule|%d |ruleName| %s failed, %f less than min value %f", ruleType, constant.LinePackageLimitRuleType[packageLimit.RuleType], calculateResult, minValue)
		return GenerateFailedLimitDetail(msg, ruleType, calculateResult)
	} else {
		return GenerateSuccessLimitDetail(ruleType, calculateResult)
	}
}

func getExtremumValueByRuleType(packageLimit *CommonPackageLimitModel) (float64, float64) {
	var maxValue float64
	var minValue float64

	switch packageLimit.RuleType {
	case constant.ActualWeightExtremumRule, constant.VolumetricWeightExtremumRule, constant.ActualWeightAndVolumetricWeightExtremumRule, constant.ActualWeightAndVolumetricWeightRatioRule:
		maxValue = packageLimit.MaxWeight
		minValue = packageLimit.MinWeight
	case constant.MaxSizeExtremumRule, constant.SizeSumExtremumRule:
		maxValue = packageLimit.MaxSize
		minValue = packageLimit.MinSize
	case constant.MaxLengthExtremumRule:
		maxValue = packageLimit.MaxLength
		minValue = packageLimit.MinLength
	case constant.MaxWidthExtremumRule:
		maxValue = packageLimit.MaxWidth
		minValue = packageLimit.MinWidth
	case constant.HeightSumExtremumRule:
		maxValue = packageLimit.MaxHeight
		minValue = packageLimit.MinHeight
	}
	return maxValue, minValue
}

type CommonPackageRowData struct {
	RoleType               uint8
	LimitFlag              uint8
	MaxSymbol              uint8
	MaxWeight              float64
	MaxSize                float64
	MaxLength              float64
	MaxWidth               float64
	MaxHeight              float64
	MinSymbol              uint8
	MinWeight              float64
	MinSize                float64
	MinLength              float64
	MinWidth               float64
	MinHeight              float64
	VolumetricFactor       uint32
	Formula                uint32
	SortFlag               uint8
	ConditionFormulaParams string
}

func ParsePackageRowData(lineNum int, rowData []string) (*CommonPackageRowData, *lcos_error.LCOSError) {
	roleTypeSplit := strings.Split(rowData[0], "-")
	roleType, err := strconv.Atoi(roleTypeSplit[0])
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+",RuleType: "+rowData[0]+", split failure!")
	}

	limitFlagSplit := strings.Split(rowData[1], "-")
	limitFlag, err := strconv.Atoi(limitFlagSplit[0])
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", LimitFlag: "+rowData[1]+", split failure!")
	}

	maxSymbolSplit := strings.Split(rowData[2], "-")
	if maxSymbolSplit[0] == "" {
		maxSymbolSplit[0] = "0"
	} else if maxSymbolSplit[0] == "0" {
		maxSymbolSplit[0] = "3"
	} else if maxSymbolSplit[0] == "1" {
		maxSymbolSplit[0] = "4"
	}
	maxSymbol, err := strconv.Atoi(maxSymbolSplit[0])
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", MaxSymbol: "+rowData[2]+", split failure!")
	}

	maxWeight, _ := strconv.ParseFloat(rowData[3], 64)
	maxSize, _ := strconv.ParseFloat(rowData[4], 64)
	maxLength, _ := strconv.ParseFloat(rowData[5], 64)
	maxWidth, _ := strconv.ParseFloat(rowData[6], 64)
	maxHeight, _ := strconv.ParseFloat(rowData[7], 64)

	minSymbolSplit := strings.Split(rowData[8], "-")
	if minSymbolSplit[0] == "" {
		minSymbolSplit[0] = "0"
	}
	minSymbol, err := strconv.Atoi(minSymbolSplit[0])
	if err != nil && limitFlag == 1 {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", MinSymbol: "+rowData[8]+", split failure!")
	}

	minWeight, _ := strconv.ParseFloat(rowData[9], 64)
	minSize, _ := strconv.ParseFloat(rowData[10], 64)
	minLength, _ := strconv.ParseFloat(rowData[11], 64)
	minWidth, _ := strconv.ParseFloat(rowData[12], 64)
	minHeight, _ := strconv.ParseFloat(rowData[13], 64)

	volumetricFactorSplit := strings.Split(rowData[14], "-")
	volumetricFactor, err := strconv.Atoi(volumetricFactorSplit[0])
	if err != nil && limitFlag == 1 {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", VolumetricFactor: "+rowData[14]+", is not digit!")
	}

	formulaSplit := strings.Split(rowData[15], "-")
	formula, err := strconv.Atoi(formulaSplit[0])
	if err != nil && limitFlag == 1 {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", Formula: "+rowData[15]+", formula error!")
	}
	sortFlagSplit := strings.Split(rowData[16], "-")
	sortFlag, err := strconv.Atoi(sortFlagSplit[0])
	if err != nil && limitFlag == 1 {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", SortFlag: "+rowData[16]+", split failure!")
	}
	var conditionFormulaParams string
	if len(rowData) >= 18 {
		conditionFormulaParams = rowData[17]
		if rowData[17] != "" && roleType == 4 {
			var conditionFormula []*ConditionFormula
			err = json.Unmarshal([]byte(rowData[17]), &conditionFormula)
			if err != nil {
				return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", conditionFormula: "+rowData[17]+", Unmarshal failure!")
			}
			for _, condition := range conditionFormula {
				if _, ok := constant.ComparisonSymbolType[condition.KeySymbol]; !ok {
					return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", conditionFormula: "+rowData[17]+", KeySymbol is not exist!")
				}
			}
		}
	}
	return &CommonPackageRowData{
		RoleType:               uint8(roleType),
		LimitFlag:              uint8(limitFlag),
		MaxSymbol:              uint8(maxSymbol),
		MaxWeight:              maxWeight,
		MaxSize:                maxSize,
		MaxLength:              maxLength,
		MaxWidth:               maxWidth,
		MaxHeight:              maxHeight,
		MinSymbol:              uint8(minSymbol),
		MinWeight:              minWeight,
		MinSize:                minSize,
		MinLength:              minLength,
		MinWidth:               minWidth,
		MinHeight:              minHeight,
		VolumetricFactor:       uint32(volumetricFactor),
		Formula:                uint32(formula),
		SortFlag:               uint8(sortFlag),
		ConditionFormulaParams: conditionFormulaParams,
	}, nil
}

func CheckPackageRowData(lineNum int, cprd *CommonPackageRowData) *lcos_error.LCOSError {
	//if len(rowData) < 18 {
	//	return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", rowData length not enough: "+strconv.Itoa(len(rowData)))
	//}
	if cprd.MaxLength < cprd.MinLength {
		max := strconv.FormatFloat(cprd.MaxLength, 'f', -1, 64)
		min := strconv.FormatFloat(cprd.MinLength, 'f', -1, 64)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", MaxLength: "+max+",MinLength:"+min+", MaxLength < MinLength!")
	}
	if cprd.MaxWidth < cprd.MinWidth {
		max := strconv.FormatFloat(cprd.MaxWidth, 'f', -1, 64)
		min := strconv.FormatFloat(cprd.MinWidth, 'f', -1, 64)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", MaxWidth: "+max+",MinWidth:"+min+", MaxWidth < MinWidth!")
	}
	if cprd.MaxHeight < cprd.MinHeight {
		max := strconv.FormatFloat(cprd.MaxHeight, 'f', -1, 64)
		min := strconv.FormatFloat(cprd.MinHeight, 'f', -1, 64)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", MaxHeight: "+max+",MinHeight:"+min+", MaxHeight < MinHeight!")
	}
	if cprd.MaxSize < cprd.MinSize {
		max := strconv.FormatFloat(cprd.MaxSize, 'f', -1, 64)
		min := strconv.FormatFloat(cprd.MinSize, 'f', -1, 64)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", MaxSize: "+max+",MinSize:"+min+", MaxSize < MinSize!")
	}
	if cprd.MaxWeight < cprd.MinWeight {
		max := strconv.FormatFloat(cprd.MaxWeight, 'f', -1, 64)
		min := strconv.FormatFloat(cprd.MinWeight, 'f', -1, 64)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", MaxWeight: "+max+",MinWeight:"+min+", MaxWeight < MinWeight!")
	}

	ruleType := cprd.RoleType
	if _, ok := constant.RuleType[ruleType]; !ok {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", RuleType: "+strconv.Itoa(int(ruleType))+", is not exist!")
	}
	limitFlag := cprd.LimitFlag
	if _, ok := constant.LimitFlag[limitFlag]; !ok {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", LimitFlag: "+strconv.Itoa(int(limitFlag))+", is not exist!")
	}
	maxSymbol := cprd.MaxSymbol
	if _, ok := constant.MaxSymbol[maxSymbol]; !ok && maxSymbol != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", MaxSymbol: "+strconv.Itoa(int(maxSymbol))+", is not exist!")
	}

	minSymbol := cprd.MinSymbol
	if _, ok := constant.MinSymbol[minSymbol]; !ok && minSymbol != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", MinSymbol: "+strconv.Itoa(int(minSymbol))+", is not exist!")
	}

	formula := cprd.Formula
	if _, ok := constant.Formula[formula]; !ok && formula != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", Formula: "+strconv.Itoa(int(formula))+", is not exist!")
	}

	volumetricFactor := cprd.VolumetricFactor
	if limitFlag == 1 && volumetricFactor == 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", VolumetricFactor: "+strconv.Itoa(int(formula))+", if limitFlag is 1, volumetricFactor is not allow 0!")
	}
	sortFlag := cprd.SortFlag
	if _, ok := constant.SortFlag[sortFlag]; !ok {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Row: "+strconv.Itoa(lineNum)+", SortFlag: "+strconv.Itoa(int(sortFlag))+", is not exist!")
	}
	return nil
}

func checkSupport(pickupFlag string, codPickupFlag string, deliverFlag string, codDeliverFlag string, lineNum int) (string, string, string, string, *lcos_error.LCOSError) {
	pickupRes := ""
	codPickupRes := ""
	deliverRes := ""
	codDeliverRes := ""
	if strings.ToLower(pickupFlag) == "yes" {
		pickupRes = "1"
	} else if strings.ToLower(pickupFlag) == "no" {
		pickupRes = "0"
	} else {
		return "", "", "", "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", pickup mismatch: "+pickupFlag)
	}
	if strings.ToLower(codPickupFlag) == "yes" {
		codPickupRes = "1"
	} else if strings.ToLower(codPickupFlag) == "no" {
		codPickupRes = "0"
	} else {
		return "", "", "", "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", codPickup mismatch: "+codPickupFlag)
	}

	if strings.ToLower(deliverFlag) == "yes" {
		deliverRes = "1"
	} else if strings.ToLower(deliverFlag) == "no" {
		deliverRes = "0"
	} else {
		return "", "", "", "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", deliver mismatch: "+deliverFlag)
	}
	if strings.ToLower(codDeliverFlag) == "yes" {
		codDeliverRes = "1"
	} else if strings.ToLower(codDeliverFlag) == "no" {
		codDeliverRes = "0"
	} else {
		return "", "", "", "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", codDeliver mismatch: "+codDeliverFlag)
	}
	return pickupRes, codPickupRes, deliverRes, codDeliverRes, nil
}

func CheckSubPackageInfoValid(subPackageInfos []*pb.SubPackageInfo) bool {
	if len(subPackageInfos) == 0 {
		return false
	}
	for _, sub := range subPackageInfos {
		shouldBeCheckFields := []*float64{
			sub.Length, sub.Width, sub.Height, sub.Weight,
		}

		for _, field := range shouldBeCheckFields {
			if field == nil || *field <= 0 {
				return false
			}
		}
	}

	return true
}

func ConvertSubPackageInfo(subPackageInfos []*pb.SubPackageInfo) []*pb.SkuInfo {
	result := []*pb.SkuInfo{}
	var one uint32 = 1
	var zero float64 = 0
	for _, sub := range subPackageInfos {
		t := &pb.SkuInfo{
			Weight:       sub.Weight,
			Quantity:     &one,
			Length:       sub.Length,
			Width:        sub.Width,
			Height:       sub.Height,
			ItemPriceUsd: &zero,
			ItemPrice:    &zero,
		}
		result = append(result, t)
	}
	return result
}

func CalculateBaseVolumetricWeight(length, width, height float64, volumetricFactor uint32) float64 {
	return (length * width * height / float64(volumetricFactor)) * 1000 // 统一体积因子单位到克
}

func IsWeightLimitRule(ruleType uint8) bool {
	return utils.CheckInUint8(ruleType, []uint8{constant.ActualWeightExtremumRule, constant.VolumetricWeightExtremumRule, constant.ActualWeightAndVolumetricWeightExtremumRule, constant.ActualWeightAndVolumetricWeightRatioRule})
}

func GenerateSingleRuleCheckResult(rule *product_package_limit.ProductPackageLimitTab, isVolumetricWeightRule bool, checkResult uint8, errMsg string, calculateResult float64) *pb.SingleRuleCheckResult {
	maxValue, minValue := getExtremumValueByRuleType(ConvertProductTabToCommonTab(rule))
	return &pb.SingleRuleCheckResult{
		RuleType:                utils.NewUint32(uint32(rule.RuleType)),
		CheckResult:             utils.NewUint32(uint32(checkResult)),
		ErrorMessage:            utils.NewString(errMsg),
		CalculateResult:         utils.NewFloat64(calculateResult),
		CalculateResultInt:      utils.NewUint32(uint32(math.Round(calculateResult))),
		LimitThresholdMin:       utils.NewFloat64(minValue),
		LimitThresholdMinInt:    utils.NewUint32(uint32(math.Round(minValue))),
		LimitThresholdMax:       utils.NewFloat64(maxValue),
		LimitThresholdMaxInt:    utils.NewUint32(uint32(math.Round(maxValue))),
		IncludeVolumetricWeight: utils.NewBool(isVolumetricWeightRule),
		Formula:                 utils.NewUint32(rule.Formula),
		VolumetricFactor:        utils.NewUint32(rule.VolumetricFactor),
	}
}
