package data_version

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/data_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/data_version"
)

type DataVersionService interface {
	GetAllDataVersionInfo(ctx utils.LCOSContext) ([]*data_version.DataVersion, *lcos_error.LCOSError)
	IncrDataVersion(ctx utils.LCOSContext, region, dataName string) (int64, *lcos_error.LCOSError)
	QueryDataVersionLogList(ctx utils.LCOSContext, req *protocol.ListDataVersionLogRequest) ([]*data_version.DataVersionLog, uint32, *lcos_error.LCOSError)
}

type dataVersionService struct {
	dataVersionDao data_version.DataVersionDao
}

func NewDataVersionService(dataVersionDao data_version.DataVersionDao) *dataVersionService {
	return &dataVersionService{
		dataVersionDao: dataVersionDao,
	}
}

func (s *dataVersionService) GetAllDataVersionInfo(ctx utils.LCOSContext) ([]*data_version.DataVersion, *lcos_error.LCOSError) {
	datas, err := s.dataVersionDao.GetAllDataVersion(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "RefreshDataVersionInfo fail, err=%v", err)
		return nil, err
	}

	return datas, nil
}

func (s *dataVersionService) genKey(region, dataName string) string {
	return utils.GenKey(":", region, dataName)
}

func (s *dataVersionService) IncrDataVersion(ctx utils.LCOSContext, region, dataName string) (int64, *lcos_error.LCOSError) {
	dataVersion := &data_version.DataVersion{}
	dataVersion.Region = region
	dataVersion.DataName = dataName
	var newVerion int64

	fc := func() *lcos_error.LCOSError {
		lcosErr := s.dataVersionDao.CreateOrUpdateDataVersion(ctx, dataVersion)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "cannot create or update data version for data_name=%s, region=%s, err=%v", dataName, region, lcosErr)
			return lcosErr
		}
		d, lcosErr := s.dataVersionDao.GetDataVerionByRegionAndDataname(ctx, region, dataName)
		if lcosErr != nil || d == nil {
			logger.CtxLogErrorf(ctx, "cannot get data version info for data_name=%s, region=%s,  err=%v, after incr", dataName, region, lcosErr)
			return lcosErr
		}
		newVerion = d.Version
		datalog := &data_version.DataVersionLog{
			Region:   region,
			DVID:     d.ID,
			DataName: dataName,
			Version:  d.Version,
		}
		lcosErr = s.dataVersionDao.CreateDataVersionLog(ctx, datalog)
		if lcosErr != nil || d == nil {
			logger.CtxLogErrorf(ctx, "create data version log fail, err=%v", lcosErr)
			return lcosErr
		}
		return nil
	}

	if err := ctx.Transaction(fc); err != nil {
		return 0, err
	}
	return newVerion, nil
}

func (s *dataVersionService) QueryDataVersionLogList(ctx utils.LCOSContext, req *protocol.ListDataVersionLogRequest) ([]*data_version.DataVersionLog, uint32, *lcos_error.LCOSError) {
	res, total, err := s.dataVersionDao.QueryDataVersionLogList(ctx, *req.DataName, *req.StartTime, *req.EndTime, req.PageNo, req.PageSize)
	if err != nil {
		logger.CtxLogErrorf(ctx, "QueryDataVersionLogList fail, err=%v", err)
		return nil, 0, err
	}
	return res, total, err
}
