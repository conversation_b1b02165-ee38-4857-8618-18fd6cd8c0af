package data_version

import (
	"context"
	"os"
	"path"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/data_version"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/data_version"
	"github.com/stretchr/testify/assert"
)

func TestQueryDataVersionLogList(t *testing.T) {
	// prepare test env
	ctx := utils.NewCommonCtx(context.Background())
	assert := assert.New(t)
	_ = os.Setenv("ENV", "LOCAL")
	_ = os.Setenv("SSC_ENV", strings.ToLower(config.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	err := chassis.Init()
	assert.Nil(err, "init chassis error")
	cfg, err := config.InitConfig(ctx)
	assert.Nil(err, "init config error")
	err = dbhelper.SetLogisticsCoreServiceConnection(cfg.DBLogisticCoreService)
	assert.Nil(err, "init db connection error")
	// prepare test service
	dao := model.NewDataVersionDao()
	svc := NewDataVersionService(dao)
	// test cases
	cases := []*protocol.ListDataVersionLogRequest{
		{DataName: utils.NewString(""), StartTime: utils.NewUint32(0), EndTime: utils.NewUint32(0), PageNo: 2, PageSize: 10},                                 // 测试分页
		{DataName: utils.NewString("LOGISTIC_BRANCH_INFO_TAB"), StartTime: utils.NewUint32(0), EndTime: utils.NewUint32(0), PageNo: 1, PageSize: 10},         // 测试基于DataName筛选
		{DataName: utils.NewString(""), StartTime: utils.NewUint32(1646120947), EndTime: utils.NewUint32(1646124371), PageNo: 1, PageSize: 10},               // 测试基于时间段筛选（两端都设）
		{DataName: utils.NewString(""), StartTime: utils.NewUint32(0), EndTime: utils.NewUint32(1646120965), PageNo: 1, PageSize: 10},                        // 测试基于时间段筛选（只设endTime）
		{DataName: utils.NewString(""), StartTime: utils.NewUint32(1646120947), EndTime: utils.NewUint32(0), PageNo: 1, PageSize: 10},                        // 测试基于时间段筛选（只设startTime）
		{DataName: utils.NewString("LOGISTIC_BRANCH_INFO_TAB"), StartTime: utils.NewUint32(1646124372), EndTime: utils.NewUint32(0), PageNo: 1, PageSize: 5}, // 测试组合查询
	}
	// start testing
	for i, c := range cases {
		data, _, err := svc.QueryDataVersionLogList(ctx, c)
		assert.Nilf(err, "case %d GetDataVersionLogByNameAndCreateTime error: %v", i, err)
		assert.LessOrEqualf(uint32(len(data)), c.PageSize, "case %d query items count not less or equal expect", i)
		for _, logItem := range data {
			if *c.DataName != "" {
				assert.Equalf(*c.DataName, logItem.DataName, "case %d query data name not equal expect", i)
			}
			if *c.StartTime != 0 {
				assert.GreaterOrEqualf(logItem.Ctime, *c.StartTime, "case %d query data ctime not greater or equal expect", i)
			}
			if *c.EndTime != 0 {
				assert.LessOrEqualf(logItem.Ctime, *c.EndTime, "case %d query data ctime not greater or equal expect", i)
			}
		}
	}
}
