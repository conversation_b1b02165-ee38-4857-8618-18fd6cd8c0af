package station

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"

	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/address_revision_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/service_point_geo"
	saturn_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/saturnprovider"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"github.com/jinzhu/copier"
	"github.com/tealeg/xlsx"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/station_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/redislibv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/schema/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/address_library_util"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/goasync"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/limiter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pis_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/station"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/hd_station_cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/station_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/match"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
)

const (
	stationVolumePrefix = "station_volume"
)

type StationService interface {
	CheckAddressListHomeDeliveryAbility(ctx context.Context, request address.GetHdAbilityRequest) (address.GetHdAbilityResponse, error)
	GetHomeDeliveryStation(ctx context.Context, request address.GetHomeDeliveryStationRequest) (address.GetHomeDeliveryStationResponse, error)
	BatchSyncStation(ctx utils.LCOSContext, req *pis_protocol.BatchSyncStationReq) (*pis_protocol.SyncStationResponseData, *lcos_error.LCOSError)
	ListStation(ctx utils.LCOSContext, req *station.ListStationRequest) (*station.ListStationResponse, *lcos_error.LCOSError)
	GetStation(ctx utils.LCOSContext, req *station.GetStationRequest) (*station.GetStationResponse, *lcos_error.LCOSError)
	ExportStation(ctx utils.LCOSContext, req *station.ExportStationRequest) (*station.ExportStationResponse, *lcos_error.LCOSError)
	BatchGetDistanceFromCache(ctx utils.LCOSContext, req *station.BatchGetDistanceReq) ([]address.StationToAddressDrivingDistance, *lcos_error.LCOSError)
	BatchGetDistanceCacheByAddrId(ctx utils.LCOSContext, req *station.BatchGetDistanceReq) ([]address.DistanceCachePair, *lcos_error.LCOSError)
	GetSpxStationInfoByOrderId(ctx utils.LCOSContext, req *address.GetStationByOrderIdRequest) (*pb.GetSpxStationIdInfoByOrderIdResponse, *lcos_error.LCOSError)
	SyncDistanceData(ctx utils.LCOSContext, req *station.SyncDistanceDataReq) *lcos_error.LCOSError
	HandleStationBuyerRelation(ctx utils.LCOSContext, req *station.SyncDistanceDataReq) *lcos_error.LCOSError
	RefreshStationInfoInRedis(ctx utils.LCOSContext, req *station.RefreshStationInfoInRedisReq) ([]*station.RefreshStationInfoInRedisItem, *lcos_error.LCOSError)
	GetCacheVal(ctx utils.LCOSContext, req *station.QueryCacheValReq) ([]*station.QueryCacheValRespItem, *lcos_error.LCOSError)
	GetDistancePartition(ctx utils.LCOSContext, req *station.GetDistancePartitionReq) (*station.GetDistancePartitionRsp, *lcos_error.LCOSError)
	CalculateStationBuyerDistance(ctx utils.LCOSContext, req *station.CalculateStationBuyerDistance) *lcos_error.LCOSError
	SyncAddressRevision(ctx utils.LCOSContext, req *pis_protocol.AddressRevisionReq) *lcos_error.LCOSError
	AddressRevisionDataInsert(ctx utils.LCOSContext, req *schema.AddressRevisionDataInsertMsg) *lcos_error.LCOSError
	AddressRevisionHandle(ctx utils.LCOSContext, req *station.HandleAddressRevisionJobReq) *lcos_error.LCOSError
}

type StationServiceImpl struct {
	HomeDeliveryService   match.HomeDeliveryServiceApi
	HDStationCacheManager hd_station_cache.HDStationCacheManager
	stationDao            station_conf.LogisticStationDao
	s3Service             s3_service.S3Service
	AddressRevisionDao    address_revision_repo.AddressRevisionDao
}

func NewStationServiceImpl(homeDeliveryService match.HomeDeliveryServiceApi,
	hDStationCacheManager hd_station_cache.HDStationCacheManager,
	stationDao station_conf.LogisticStationDao, s3Service s3_service.S3Service, addressRevisionDao address_revision_repo.AddressRevisionDao) *StationServiceImpl {
	return &StationServiceImpl{
		HomeDeliveryService:   homeDeliveryService,
		HDStationCacheManager: hDStationCacheManager,
		stationDao:            stationDao,
		s3Service:             s3Service,
		AddressRevisionDao:    addressRevisionDao,
	}
}

func (s *StationServiceImpl) CheckAddressListHomeDeliveryAbility(ctx context.Context, req address.GetHdAbilityRequest) (address.GetHdAbilityResponse, error) {
	_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityRequestTotal, constant.StatusSuccess, "")
	divisionConfig := config.GetMutableConf(ctx).StationConfig.GetHomeDeliveryWhiteList()
	resList := make([]address.GetHdAbilityAddressResItem, 0)
	needMatchReq := address.GetHdAbilityRequest{
		BuyerId: req.BuyerId,
	}
	addrList := make([]address.GetHdAbilityAddressItem, 0)
	// 1. 过滤待匹配的地址
	for _, v := range req.AddrList {
		if _, exist := divisionConfig[v.AddressL1]; exist && len(v.Address) > 0 {
			addrList = append(addrList, address.GetHdAbilityAddressItem{
				Address:   v.Address,
				AddressL1: v.AddressL1,
				AddressL2: v.AddressL2,
				UniqueKey: v.UniqueKey,
			})
		} else {
			resList = append(resList, address.GetHdAbilityAddressResItem{
				Address:     v.Address,
				AddressL1:   v.AddressL1,
				AddressL2:   v.AddressL2,
				UniqueKey:   v.UniqueKey,
				IsSupportHd: station_constant.UnsupportedHd,
				ReturnType:  station_constant.WhitelistReject,
			})
		}
	}
	needMatchReq.AddrList = addrList
	// 2. 获取mapResult
	mapResult, err := s.GetHomeDeliveryStationList(ctx, needMatchReq)
	for _, addr := range addrList {
		addrId := address_library_util.GetAddressId(ctx, address.DivisionZipcodeEntity{
			Division: address.CreateDivision(addr.AddressL1, addr.AddressL2, "", "", "", 2),
		}, addr.Address)
		isSupportHd := station_constant.UnsupportedHd
		if _, exist := mapResult[addrId]; exist && mapResult[addrId].IsSupportHd != station_constant.UnknownSupport {
			isSupportHd = int(mapResult[addrId].IsSupportHd)
		}
		returnType := mapResult[addrId].ReturnType
		if len(returnType) == 0 {
			returnType = station_constant.DrivingDistanceReject
		}
		resList = append(resList, address.GetHdAbilityAddressResItem{
			Address:     addr.Address,
			AddressL1:   addr.AddressL1,
			AddressL2:   addr.AddressL2,
			UniqueKey:   addr.UniqueKey,
			IsSupportHd: int8(isSupportHd),
			ReturnType:  returnType,
		})
		typeMarker := constant.TypeMarkerNormal
		if ctxhelper.IsShadow(ctx) {
			typeMarker = constant.TypeMarkerShadow
		}
		// 上报业务指标监控-check通过率
		_ = metrics.CounterIncr(constant.MetricsCheckAbilityResultReport, map[string]string{
			"check_result":      strconv.FormatInt(int64(isSupportHd), 10),
			"check_result_type": returnType,
			"type_marker":       typeMarker,
		})
	}
	// 3. 组装返回结果
	res := address.GetHdAbilityResponse{}
	res.AddrList = resList
	// todo 发送异步消息保存到match result这步不需要
	// s.asyncTransferCheckAbilityResultAndSendMessage(ctx, resList)
	res.BuyerId = req.BuyerId
	return res, err
}

func (s *StationServiceImpl) GetHomeDeliveryStation(ctx context.Context, req address.GetHomeDeliveryStationRequest) (address.GetHomeDeliveryStationResponse, error) {
	_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalRequestTotal, constant.StatusSuccess, "")
	logger.CtxLogInfof(ctx, "[GetHomeDeliveryStationGrpc] req=%+v, region=%s", req, utils.GetRegion(ctx))
	// 匹配 获取homeDelivery站点
	res, err := s.GetOptimalHomeDeliveryStation(ctx, address.HomeDeliveryAddressEntity{
		AddressInfo: address.CommonAddress{
			Address:   req.AddressItem.Address,
			AddressL1: req.AddressItem.AddressL1,
			AddressL2: req.AddressItem.AddressL2,
		},
	}, station_constant.CreateOrder)
	if err != nil || len(res.AddressInfo.StationList) == 0 {
		logger.CtxLogErrorf(ctx, "get home delivery station error:%v", err)
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.MatchHubFail, constant.StatusSuccess, "")
		return address.GetHomeDeliveryStationResponse{AddressItem: address.AddressWithStationItem{
			AddressL1: req.AddressItem.AddressL1,
			AddressL2: req.AddressItem.AddressL2,
			Address:   req.AddressItem.Address,
		}}, nil
	}
	_ = monitor.ReportEvent(constant.CatModuleStation, constant.MatchHubSuccess, constant.StatusSuccess, "")
	// CAT细分上报
	s.reportGetHomeDeliveryStationResult(ctx, constant.CatModuleStation, res.AddressInfo)

	// 上报业务指标--上报各个station的运力
	_ = metrics.CounterIncr(constant.MetricsMatchOptimalStationResultReport, map[string]string{
		"station_id": strconv.FormatInt(res.AddressInfo.StationList[0].HdStationId, 10),
	})
	// 返回
	return address.GetHomeDeliveryStationResponse{AddressItem: address.AddressWithStationItem{
		AddressL1:       req.AddressItem.AddressL1,
		AddressL2:       req.AddressItem.AddressL2,
		Address:         req.AddressItem.Address,
		StationId:       uint64(res.AddressInfo.StationList[0].HdStationId),
		DeliveryModType: uint32(res.AddressInfo.StationList[0].DeliveryModType),
		ChannelIds:      res.AddressInfo.StationList[0].ChannelIds,
		AddressLng:      res.AddressInfo.CommonCoordinate.Lng,
		AddressLat:      res.AddressInfo.CommonCoordinate.Lat,
	}}, nil
}

func (s *StationServiceImpl) GetSpxStationInfoByOrderId(ctx utils.LCOSContext, req *address.GetStationByOrderIdRequest) (*pb.GetSpxStationIdInfoByOrderIdResponse, *lcos_error.LCOSError) {
	rsp := &pb.GetSpxStationIdInfoByOrderIdResponse{RespHeader: http.GrpcSuccessRespHeader()}
	// 1.查询调度结果表
	stationAllocation, lcosError := s.stationDao.GetOrderStationAllocationByOrderId(ctx, int64(req.OrderId), req.DeployRegion)
	if lcosError != nil {
		logger.CtxLogErrorf(ctx, "Get station allocation by orderId err=%v", lcosError)
		return rsp, lcosError
	}
	// 2. 如果order_id找不到记录，则需要兜底重新match station_id，order_id为0表示找不到记录
	if stationAllocation.OrderID == 0 {
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.OrderNoRelateStationRecord, constant.StatusSuccess, "order can not found relate station record")
		newStationId, rematchErr := s.RematchHDStation(ctx, req)
		if rematchErr != nil {
			logger.CtxLogErrorf(ctx, "rematch home delivery station error, err=%v", rematchErr)
			return rsp, lcos_error.NewLCOSError(lcos_error.HDGetStationErr, rematchErr.Error())
		}
		nowTime := time.Now().Unix() // nolint
		stationAllocation.OrderID = int64(req.OrderId)
		stationAllocation.StationID = newStationId
		stationAllocation.CTime = nowTime
		stationAllocation.MTime = nowTime
		err1 := s.stationDao.SaveOrderStationAllocation(ctx, stationAllocation, req.DeployRegion)
		if err1 != nil {
			// 不阻塞主流程
			_ = monitor.ReportEvent(constant.CatModuleStation, constant.SaveOrderStationInfoFail, constant.StatusSuccess, "save new order station info fail")
			logger.CtxLogErrorf(ctx, "update order station fail|update order station error, err=%v", err1)
		}
	}
	// 3. StationId=0是正常情况
	if stationAllocation.StationID == 0 {
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetStationByOrderIdIsZero, constant.StatusSuccess, "get station id by order id is zero")
		return rsp, nil
	}
	// 4.从缓存查询stationInfo
	stationInfo, err := s.stationDao.GetStationByStationIdFromCache(ctx, stationAllocation.StationID)
	if err != nil {
		// 4.1如果缓存没有就返回nil，默认返回兜底的station_id = 0
		if err.RetCode == lcos_error.NotFoundStationErr {
			_ = monitor.ReportEvent(constant.CatModuleStation, constant.StationInfoNotExists, constant.StatusSuccess, "station info not exists")
			return rsp, nil
		}
		logger.CtxLogErrorf(ctx, "Get station info by stationId err=%v", lcosError)
		return rsp, err
	}
	// 5. 如果分配的station状态为unavailable, 此时需要重新分配新站点（temp_closed站点不需要重新分配）
	if stationInfo.CheckoutStatus == station_constant.Unavailable {
		newStationId, rematchErr := s.RematchHDStation(ctx, req)
		if rematchErr != nil {
			logger.CtxLogErrorf(ctx, "rematch home delivery station error, err=%v", rematchErr)
			return rsp, lcos_error.NewLCOSError(lcos_error.HDGetStationErr, rematchErr.Error())
		}
		// 保存重排序的station_id
		stationAllocation.StationID = newStationId
		stationAllocation.MTime = time.Now().Unix() // nolint
		err1 := s.stationDao.UpdateOrderStationAllocation(ctx, stationAllocation, req.DeployRegion)
		if err1 != nil {
			// 不阻塞主流程
			_ = monitor.ReportEvent(constant.CatModuleStation, constant.SaveOrderStationInfoFail, constant.StatusSuccess, "save new order station info fail")
			logger.CtxLogErrorf(ctx, "update order station fail|update order station error, err=%v", err1)
		}
		if newStationId == 0 {
			_ = monitor.ReportEvent(constant.CatModuleStation, constant.RematchStationIdIsZero, constant.StatusSuccess, "rematch station id is zero")
			return rsp, nil
		}
		stationInfo, err = s.stationDao.GetStationByStationIdFromCache(ctx, newStationId)
		if err != nil {
			// 3.1如果缓存没有就返回nil
			if err.RetCode == lcos_error.NotFoundStationErr {
				return rsp, nil
			}
			logger.CtxLogErrorf(ctx, "Get station info by stationId err=%v", err)
			return rsp, err
		}
	}
	// 6.转换成response
	convertToGetSpxStationIdInfoByOrderIdResponse(stationInfo, rsp)
	return rsp, nil
}

func (s *StationServiceImpl) RematchHDStation(ctx utils.LCOSContext, req *address.GetStationByOrderIdRequest) (uint64, error) {
	getStationRequest := address.GetHomeDeliveryStationRequest{
		AddressItem: address.AddressItem{
			AddressL1:  req.AddressL1,
			AddressL2:  req.AddressL2,
			Address:    req.Address,
			ShipmentId: strconv.FormatInt(int64(req.OrderId), 10),
		},
	}
	newCtx := ctxhelper.CloneTrace(ctx)
	newCtx = context.WithValue(newCtx, constant.DeployRegionKey, req.DeployRegion)
	getStationResponse, err := s.GetHomeDeliveryStation(newCtx, getStationRequest)
	// 更新station失败，打印错误日志
	if err != nil {
		logger.CtxLogErrorf(newCtx, "rematch home delivery station error, err=%v", err)
		return 0, err
	}
	return getStationResponse.AddressItem.StationId, nil
}

func convertToGetSpxStationIdInfoByOrderIdResponse(stationInfo *station_conf.StationCacheInfo, rsp *pb.GetSpxStationIdInfoByOrderIdResponse) {
	if stationInfo == nil {
		return
	}
	var lineIdList = make([]string, 0, len(stationInfo.Support4PlLineList))
	for _, lineId := range stationInfo.Support4PlLineList {
		lineIdList = append(lineIdList, lineId)
	}
	rsp.RespHeader = http.GrpcSuccessRespHeader()
	rsp.StationId = proto.Uint64(stationInfo.StationId)
	rsp.DeliverMode = proto.Int32(int32(stationInfo.LmDeliveryMode))
	rsp.LineList = lineIdList

}

func (s *StationServiceImpl) GetHomeDeliveryStationList(ctx context.Context, req address.GetHdAbilityRequest) (map[string]address.GetHdAbilityAddressResItem, error) {
	_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityCheckRequestTotal, constant.StatusSuccess, "")
	ctx, cancel := context.WithTimeout(ctx, config.GetMutableConf(ctx).StationConfig.GetHomeDeliveryMatchTimeout())
	defer cancel()
	mapResult := new(map[string]address.GetHdAbilityAddressResItem)
	nearByStationList := new([]address.NearbyStation)
	mapHdConf := new(map[uint64]address.HomeDeliveryConf)
	hdConfMatchCh := make(chan map[string]address.GetHdAbilityAddressResItem, 1)

	goasync.GoAndRecover(func() {
		res := s.GetAddressListHomeDeliveryStation(ctx, req, nearByStationList, mapResult, mapHdConf)
		hdConfMatchCh <- res
	}, ctx)

	for {
		select {
		case r := <-hdConfMatchCh:
			return r, nil
		case <-ctx.Done():
			res, err := s.GetAddressListHomeDeliveryStationListGuaranty(ctx, nearByStationList, mapResult, mapHdConf)
			return res, err
		}
	}
}

func (s *StationServiceImpl) GetAddressListHomeDeliveryStation(ctx context.Context, req address.GetHdAbilityRequest, stationList *[]address.NearbyStation, mapResult *map[string]address.GetHdAbilityAddressResItem, stationMap *map[uint64]address.HomeDeliveryConf) map[string]address.GetHdAbilityAddressResItem {
	var addressInfoList []address.CommonAddress
	mapAddrAbility := make(map[string]address.GetHdAbilityAddressResItem)
	for _, v := range req.AddrList {
		addressInfoList = append(addressInfoList, address.CommonAddress{
			Address:   v.Address,
			AddressL1: v.AddressL1,
			AddressL2: v.AddressL2,
		})
		// 生成addrID
		addrID := address_library_util.GetAddressId(ctx, address.DivisionZipcodeEntity{Division: address.CreateDivision(v.AddressL1, v.AddressL2, "", "", "", 2)}, v.Address)
		mapAddrAbility[addrID] = address.GetHdAbilityAddressResItem{
			Address:     v.Address,
			AddressL1:   v.AddressL1,
			AddressL2:   v.AddressL2,
			UniqueKey:   v.UniqueKey,
			IsSupportHd: station_constant.UnknownSupport,
		}
	}

	// 1. 从redis获取站点到买家地址的行驶距离
	mapAfterFilter, uncheckMapAddAbility := s.getAbilityFromDrivingDistanceList(ctx, req.BuyerId, addressInfoList, mapAddrAbility)
	if len(uncheckMapAddAbility) == 0 {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityByCacheResultTotal, constant.StatusSuccess, "")
		return mapAfterFilter
	}
	_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityByRealTimeResultTotal, constant.StatusSuccess, "")
	// 和spx保持一致，写法很奇怪，后续优化
	// 优化一下
	*mapResult = mapAfterFilter

	// 2. 获取Google经纬度
	addressRadiusList, err := s.getCoordinateListFromGoogle(ctx, req.BuyerId, uncheckMapAddAbility)
	if err != nil || len(addressRadiusList) == 0 {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityGetCoordinateFailTotal, constant.StatusSuccess, "")
		return mapAfterFilter
	}

	// 3. 获取站点地址附近的站点列表
	nearbyStationList, err := s.HomeDeliveryService.SearchNearbyStation(ctx, addressRadiusList)
	// 优化一下
	*stationList = nearbyStationList
	// spx这里是判断len(addressRadiusList) == 0，但从逻辑上来说这里应该是len(nearbyStationList) == 0
	if err != nil || len(nearbyStationList) == 0 {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityGetNearbyStationFailTotal, constant.StatusSuccess, "")
		return mapAfterFilter
	}

	// 4. 获取站点白名单配置
	stationIdList := make([]uint64, 0)
	for _, v := range nearbyStationList {
		for _, l := range v.StationList {
			stationIdList = append(stationIdList, uint64(l.StationID))
		}
	}
	if len(stationIdList) == 0 {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityNearbyStationIdListIsNilTotal, constant.StatusSuccess, "")
		logger.CtxLogInfof(ctx, "get nearby station list empty")
		return mapAddrAbility
	}
	stationTableMap, err := s.getStationConfList(ctx, stationIdList)
	if err != nil || len(stationTableMap) == 0 {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityGetStationConfErrTotal, constant.StatusSuccess, "")
		return mapAfterFilter
	}
	// 优化一下
	*stationMap = stationTableMap

	// qa test,验证超时场景
	// time.Sleep(time.Duration(config2.GetSearchStationTimeOut(ctx)) * time.Millisecond)

	// 5.初筛 获取直线距离
	mapAfterFilter, uncheckMapAddAbility, stationCoordinateList := s.searchDirectNearestStationList(ctx, nearbyStationList, stationTableMap, mapAfterFilter)
	*mapResult = mapAfterFilter
	if len(uncheckMapAddAbility) == 0 {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityCheckByLineDistanceTotal, constant.StatusSuccess, "")
		return mapAfterFilter
	}

	// 6. matrix
	mapAddrAbility = s.getAddressStationMatrix(ctx, stationCoordinateList, stationTableMap, mapAfterFilter)
	*mapResult = mapAfterFilter
	_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityByDrivingDistanceTotal, constant.StatusSuccess, "")

	return mapAddrAbility
}

func (s *StationServiceImpl) GetAddressListHomeDeliveryStationListGuaranty(ctx context.Context, nearByStationList *[]address.NearbyStation, mapResult *map[string]address.GetHdAbilityAddressResItem, stationMap *map[uint64]address.HomeDeliveryConf) (map[string]address.GetHdAbilityAddressResItem, error) {
	_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityTimeoutDefaultRequestTotal, constant.StatusSuccess, "")
	uncheckAddressAbility := s.filterUncheckAddress(*mapResult)
	mapAfterFilter := *mapResult
	// 1.查看0.5倍配置距离内是否有支持的站点
	if nearByStationList != nil {
		higherDistanceRadius := config.GetMutableConf(ctx).StationConfig.GetHigherStraightLineDistanceRadius()
		for _, nearbyStation := range *nearByStationList {
			addrID := address_library_util.GetAddressId(ctx, address.DivisionZipcodeEntity{
				Division: address.CreateDivision(nearbyStation.Address.AddressL1, nearbyStation.Address.AddressL2, "", "", "", 2),
			}, nearbyStation.Address.Address)
			if _, exist := uncheckAddressAbility[addrID]; exist {
				for _, stationCoordinateInfo := range nearbyStation.StationList {
					drivingDistance := utils.GetDistanceFloat(nearbyStation.CenterPoint.Lng, nearbyStation.CenterPoint.Lat, stationCoordinateInfo.Coordinate.Lng, stationCoordinateInfo.Coordinate.Lat)
					stationHDConfig := (*stationMap)[uint64(stationCoordinateInfo.StationID)]
					// station状态为unavailable时肯定不可用，也不会有临时关闭，故没必要查询运力判断
					if stationHDConfig.HdStatus == station_constant.Unavailable {
						continue
					}
					stationVolume := s.GetStationVolume(ctx, stationCoordinateInfo.StationID)
					if stationHDConfig.HdStatus == station_constant.Available && drivingDistance <= float64(stationHDConfig.HdDistance)*higherDistanceRadius && (stationVolume < stationHDConfig.HdMaxCapacity || stationHDConfig.HdMaxCapacity == station_constant.StationCheckoutNoLimit) {
						mapAfterFilter[addrID] = address.GetHdAbilityAddressResItem{
							Address:     nearbyStation.Address.Address,
							AddressL1:   nearbyStation.Address.AddressL1,
							AddressL2:   nearbyStation.Address.AddressL2,
							IsSupportHd: station_constant.SupportedHd,
							ReturnType:  station_constant.LineDistanceFallbackAccept,
						}
						logger.CtxLogInfof(ctx, "[TimeOutDirect]this address is supported;addrId: %v, address: %+v, drivingDistance: %v, stationVolume: %v, stationConf: %+v", addrID, nearbyStation.Address, drivingDistance, stationVolume, stationHDConfig)
						_ = monitor.ReportEvent(constant.CatModuleStation, constant.TimeoutGetStationByDirectDistance, constant.StatusSuccess, "")
						break
					}
					// 为了避免多次for循环，这里判断station是否是暂时不可用，必须放到判断station可用后面
					// 行驶距离在服务范围内&&(运力超过限制且站点可用或站点暂时关闭)
					if drivingDistance >= 0 && drivingDistance <= float64(stationHDConfig.HdDistance)*higherDistanceRadius &&
						((stationVolume >= stationHDConfig.HdMaxCapacity && stationHDConfig.HdMaxCapacity != station_constant.StationCheckoutNoLimit && stationHDConfig.HdStatus == station_constant.Available) || (stationHDConfig.HdStatus == station_constant.TempClosed)) {
						logger.CtxLogInfof(ctx, "[TimeOutDirect]this address is temporarily closed;addrId: %v, address: %+v, drivingDistance: %v, stationVolume: %v, stationConf: %+v", addrID, nearbyStation.Address, drivingDistance, stationVolume, stationHDConfig)
						// 如果没有可用站点，以这个结果为准，有可用站点，会覆盖该结果
						mapAfterFilter[addrID] = address.GetHdAbilityAddressResItem{
							Address:     nearbyStation.Address.Address,
							AddressL1:   nearbyStation.Address.AddressL1,
							AddressL2:   nearbyStation.Address.AddressL2,
							IsSupportHd: station_constant.UnsupportedHd,
							ReturnType:  station_constant.TemporarilyClosed,
						}
					}
				}
			}
		}
	}

	for _, addr := range mapAfterFilter {
		if addr.IsSupportHd == station_constant.UnknownSupport {
			logger.CtxLogInfof(ctx, "[TimeOut]this address %+v is not supported;", addr)
			_ = monitor.AwesomeReportEvent(ctx, constant.CatCheckAbilityModule, constant.CheckAbilityTimeoutUnknownSupportTotal, constant.StatusSuccess, "")
		}
	}
	return mapAfterFilter, nil
}

func (s *StationServiceImpl) getAbilityFromDrivingDistanceList(ctx context.Context, buyerId string, addressInfoList []address.CommonAddress, mapAddrAbility map[string]address.GetHdAbilityAddressResItem) (map[string]address.GetHdAbilityAddressResItem, map[string]address.GetHdAbilityAddressResItem) {
	StationDistanceList, err := s.HomeDeliveryService.GetDrivingDistanceWithHDStation(ctx, buyerId, addressInfoList)
	mapAfterFilter := mapAddrAbility
	if err != nil {
		logger.CtxLogErrorf(ctx, "get station address distance list error err: %v", err)
	}
	logger.CtxLogInfof(ctx, "get buyer address distance list|buyerId: %v, addressInfoList: %+v, stationDistanceList: %+v", buyerId, addressInfoList, StationDistanceList)
	if len(StationDistanceList) == 0 {
		return mapAfterFilter, mapAfterFilter
	}
	stationIdList := make([]uint64, 0)

	for _, addressStation := range StationDistanceList {
		addrId := address_library_util.GetAddressId(ctx, address.DivisionZipcodeEntity{Division: address.CreateDivision(addressStation.Address.AddressL1, addressStation.Address.AddressL2, "", "", "", 2)}, addressStation.Address.Address)
		if !addressStation.IsAddressNotExist && len(addressStation.StationList) == 0 {
			mapAfterFilter[addrId] = address.GetHdAbilityAddressResItem{
				Address:     mapAfterFilter[addrId].Address,
				AddressL1:   mapAfterFilter[addrId].AddressL1,
				AddressL2:   mapAfterFilter[addrId].AddressL2,
				UniqueKey:   mapAfterFilter[addrId].UniqueKey,
				IsSupportHd: station_constant.UnsupportedHd,
				ReturnType:  station_constant.StationLibReject,
			}
		}
		for _, stationDistance := range addressStation.StationList {
			stationIdList = append(stationIdList, uint64(stationDistance.StationID))
		}
	}
	uncheckAddressAbility := s.filterUncheckAddress(mapAfterFilter)

	if len(stationIdList) == 0 {
		return mapAfterFilter, uncheckAddressAbility
	}
	stationDistanceHDConfMap, err := s.getStationConfList(ctx, stationIdList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get station hd conf map error, err=%+v", err)
		return mapAfterFilter, uncheckAddressAbility
	}
	for _, addressStation := range StationDistanceList {
		if !addressStation.IsAddressNotExist {
			// 标记获取的站点列表当前是否可用或者是否是暂时关闭
			stationAbility := false
			addrId := address_library_util.GetAddressId(ctx, address.DivisionZipcodeEntity{Division: address_library_util.CreateDivision(addressStation.Address.AddressL1, addressStation.Address.AddressL2, "", "", "", 2)}, addressStation.Address.Address)
			for _, stationDistance := range addressStation.StationList {
				if stationInfo, exist := stationDistanceHDConfMap[uint64(stationDistance.StationID)]; exist {
					// station状态为unavailable时肯定不可用，也不会有临时关闭，故没必要查询运力判断
					if stationInfo.HdStatus == station_constant.Unavailable {
						continue
					}
					drivingDistance := int32(stationDistance.DrivingDistance)
					stationVolume := s.GetStationVolume(ctx, stationDistance.StationID)
					if stationInfo.HdStatus == station_constant.Available && drivingDistance >= 0 && drivingDistance <= stationInfo.HdDistance && (stationVolume < stationInfo.HdMaxCapacity || stationInfo.HdMaxCapacity == station_constant.StationCheckoutNoLimit) {
						_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetStationByStationDistanceLib, constant.StatusSuccess, "")
						logger.CtxLogInfof(ctx, "[stationLib] this station is supported; address: %+v, address_id: %v, match station: %+v, volume: %v", addressStation.Address, addrId, stationInfo, stationVolume)
						stationAbility = true
						tempResult := mapAddrAbility[addrId]
						tempResult.IsSupportHd = station_constant.SupportedHd
						tempResult.ReturnType = station_constant.StationLibAccept
						mapAfterFilter[addrId] = tempResult
						break
					}
					// 为了避免多次for循环，这里判断station是否是暂时不可用，必须放到判断station可用后面
					// 行驶距离在服务范围内&&(运力超过限制且站点可用或站点暂时关闭)
					if drivingDistance >= 0 && drivingDistance <= stationInfo.HdDistance &&
						((stationVolume >= stationInfo.HdMaxCapacity && stationInfo.HdMaxCapacity != station_constant.StationCheckoutNoLimit && stationInfo.HdStatus == station_constant.Available) || (stationInfo.HdStatus == station_constant.TempClosed)) {
						// 加一个上报
						logger.CtxLogInfof(ctx, "[stationLib] this station is temporarily closed; address: %+v, address_id: %v, match station: %+v, volume: %v", addressStation.Address, addrId, stationInfo, stationVolume)
						_ = monitor.ReportEvent(constant.CatModuleStation, constant.StationTemporarilyClosed, constant.StatusSuccess, "volume exceed limit or station temporarily closed")
						stationAbility = true
						tempResult := mapAddrAbility[addrId]
						tempResult.IsSupportHd = station_constant.UnsupportedHd
						tempResult.ReturnType = station_constant.TemporarilyClosed
						// 如果没有可用站点，以这个结果为准，有可用站点，会覆盖该结果
						mapAfterFilter[addrId] = tempResult
					}
				}
			}
			if !stationAbility {
				_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetStationByStationDistanceLibFailure, constant.StatusError, "")
				logger.CtxLogInfof(ctx, "[stationLib] this address is not supported; address: %+v, address_id: %v", addressStation.Address, addrId)
				tempResult := mapAddrAbility[addrId]
				tempResult.IsSupportHd = station_constant.UnsupportedHd
				tempResult.ReturnType = station_constant.StationLibReject
				mapAfterFilter[addrId] = tempResult
			}
		}
	}
	uncheckAddressAbility = s.filterUncheckAddress(mapAfterFilter)
	return mapAfterFilter, uncheckAddressAbility
}

func (s *StationServiceImpl) filterUncheckAddress(mapAddrAbility map[string]address.GetHdAbilityAddressResItem) map[string]address.GetHdAbilityAddressResItem {
	uncheckAddressAbility := make(map[string]address.GetHdAbilityAddressResItem)
	for addrId, addr := range mapAddrAbility {
		if addr.IsSupportHd == station_constant.UnknownSupport {
			uncheckAddressAbility[addrId] = addr
		}
	}
	return uncheckAddressAbility
}

func (s *StationServiceImpl) getStationConfList(ctx context.Context, stationIdList []uint64) (map[uint64]address.HomeDeliveryConf, error) {
	// 0. 定义返回结果
	homeDeliveryConfMap := make(map[uint64]address.HomeDeliveryConf)
	// 1. 从本地缓存获取站点配置信息
	stationInfoList, err := s.stationDao.BatchGetHdStationConfFromCache(ctx, stationIdList)
	if err != nil {
		logger.CtxLogInfof(ctx, "batch get hd station conf from cache failure, stationIdList:%v, err:%v", stationIdList, err)
		return homeDeliveryConfMap, err
	}
	logger.CtxLogInfof(ctx, "batch get hd station conf from cache success, stationIdList:%v, stationInfoList: %+v", stationIdList, stationInfoList)
	for _, stationInfo := range stationInfoList {
		homeDeliveryConfMap[stationInfo.StationId] = address.HomeDeliveryConf{
			StationId:     stationInfo.StationId,
			HdStatus:      stationInfo.CheckoutStatus,
			HdDistance:    int32(stationInfo.DistanceThreshold),
			HdMaxCapacity: stationInfo.GetHDMaxCapacity(ctx),
		}
	}
	logger.CtxLogInfof(ctx, "batch get hd delivery conf, stationIdList:%v, homeDeliveryConfMap:%+v", stationIdList, homeDeliveryConfMap)
	return homeDeliveryConfMap, nil
}

func (s *StationServiceImpl) getCoordinateListFromGoogle(ctx context.Context, buyerId string, uncheckAddressAbility map[string]address.GetHdAbilityAddressResItem) ([]address.CenterPointWithRadius, error) {
	var addressCoordinateList []address.AddressCoordinate
	var err error
	AddressList := make([]address.CommonAddress, 0)
	for _, needCheckAddress := range uncheckAddressAbility {
		AddressList = append(AddressList, address.CommonAddress{
			Address:   needCheckAddress.Address,
			AddressL1: needCheckAddress.AddressL1,
			AddressL2: needCheckAddress.AddressL2,
		})
	}
	// qa测试，mock经纬度
	if config.GetMutableConf(ctx).StationConfig.NotUseGoogleConfig {
		for _, commonAddress := range AddressList {
			addressCoordinateList = append(addressCoordinateList, address.AddressCoordinate{
				Address: commonAddress,
				Coordinate: address.CommonCoordinate{
					Lat: config.GetMutableConf(ctx).StationConfig.MockGoogleLat,
					Lng: config.GetMutableConf(ctx).StationConfig.MockGoogleLng,
				},
			})
		}
	} else {
		addressCoordinateList, err = s.HomeDeliveryService.GetBuyerAddressCoordinate(ctx, buyerId, AddressList)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get coordinate from Google error: %v", err)
			return []address.CenterPointWithRadius{}, err
		}
	}
	if len(addressCoordinateList) == 0 {
		// 加一个上报
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetGoogleCoordinateIsNil, constant.StatusSuccess, "get none coordinate from Google")
		logger.CtxLogErrorf(ctx, "get none coordinate from Google")
		return []address.CenterPointWithRadius{}, errors.New("none google result")
	}
	logger.CtxLogInfof(ctx, "get coordinate from google;result:%+v", addressCoordinateList)
	distanceMax, err := s.GetDistanceMax(ctx)
	// distanceMax == 0 需要上报并返回err
	if err != nil {
		logger.CtxLogErrorf(ctx, "get distance max error: %v", err)
		distanceMax = float64(station_constant.MaxDistance)
	}
	if distanceMax == 0 {
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetMaxStationDistanceIsZero, constant.StatusSuccess, "get max distance is zero")
		return []address.CenterPointWithRadius{}, errors.New("get max distance is zero")
	}
	logger.CtxLogInfof(ctx, "get max distance success, maxDistance: %v", distanceMax)
	circleRadiusParameter := config.GetMutableConf(ctx).StationConfig.GetCircleRadiusParameter()
	// 初筛选站点
	addressCoordinateWithRadiusList := make([]address.CenterPointWithRadius, 0)
	for _, addressCoordinate := range addressCoordinateList {
		addressCoordinateWithRadiusList = append(addressCoordinateWithRadiusList, address.CenterPointWithRadius{
			CenterPoint: addressCoordinate.Coordinate,
			Address:     addressCoordinate.Address,
			// spx为了性能考虑缩小了半径，但计算行驶距离已经做了并行计算提高性能，和spx确定这里可以去掉缩小系数，和pm确定，做性能压测后，没有性能问题就去掉，有性能问题则保留
			Radius: distanceMax * circleRadiusParameter,
		})
	}
	logger.CtxLogInfof(ctx, "get coordinate from google final result: buyerId: %v, addressList: %+v, coordinateRadiusResult: %+v", buyerId, AddressList, addressCoordinateWithRadiusList)
	return addressCoordinateWithRadiusList, nil
}

// GetDistanceMax 获取station 配置最大距离
func (s *StationServiceImpl) GetDistanceMax(ctx context.Context) (distance float64, err error) {
	// 1.优先获取缓存数据
	distance, err = s.HDStationCacheManager.GetDistanceMaxCache(ctx)
	if err == nil {
		logger.CtxLogInfof(ctx, "GetDistanceMax GetDistanceMaxCache success,distance:%v", distance)
		return distance, nil
	}
	// 2.容灾查询数据库数据
	// 防止缓存异常压垮Mysql
	// 防止缓存异常压垮Mysql，false: 打开限流，true：关闭限流
	if !config.GetMutableConf(ctx).StationConfig.HDMysqlFlowLimitSwitch {
		flowLimiter, err := limiter.GetFlowLimiter(station_constant.SortHdFlowLimiter)
		if err == nil && !flowLimiter.Allow() {
			logger.CtxLogErrorf(ctx, "Hit mysql flow limit, GetDistanceMax,err:%v", err)
			return distance, fmt.Errorf("mysql flow limit")
		}
	}
	logger.CtxLogInfof(ctx, "GetDistanceMax db disaster recovery start")
	distance, err = s.stationDao.GetMaxDistanceConf(ctx)
	if err != nil {
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetMaxDistanceFromMysql, constant.StatusSuccess, "get max distance from mysql")
		logger.CtxLogErrorf(ctx, "GetDistanceMax db disaster recovery fail, err:%v", err)
		return 0, err
	}
	return distance, nil
}

func (s *StationServiceImpl) searchDirectNearestStationList(ctx context.Context, NearbyStationList []address.NearbyStation, stationTableMap map[uint64]address.HomeDeliveryConf, mapAddrAbility map[string]address.GetHdAbilityAddressResItem) (map[string]address.GetHdAbilityAddressResItem, map[string]address.GetHdAbilityAddressResItem, []address.NearbyStation) {
	stationCoordinateList := make([]address.NearbyStation, 0)
	mapAfterFilter := mapAddrAbility
	for _, addressStationInfo := range NearbyStationList {
		addrId := address_library_util.GetAddressId(ctx, address.DivisionZipcodeEntity{
			Division: address.CreateDivision(addressStationInfo.Address.AddressL1, addressStationInfo.Address.AddressL2, "", "", "", 2),
		}, addressStationInfo.Address.Address)
		if len(addressStationInfo.StationList) == 0 {
			tempResult := mapAddrAbility[addrId]
			tempResult.IsSupportHd = station_constant.UnsupportedHd
			tempResult.ReturnType = station_constant.DrivingDistanceReject
			mapAfterFilter[addrId] = tempResult
		}
		needMatrix := true
		for _, stationCoordinateInfo := range addressStationInfo.StationList {
			drivingDistance := utils.GetDistanceFloat(addressStationInfo.CenterPoint.Lng, addressStationInfo.CenterPoint.Lat, stationCoordinateInfo.Coordinate.Lng, stationCoordinateInfo.Coordinate.Lat)
			logger.CtxLogInfof(ctx, "[DirectDistance] searchDirectNearestStationList, driving_distance: %f", drivingDistance)
			stationHDConfig := stationTableMap[uint64(stationCoordinateInfo.StationID)]
			// station状态为unavailable时肯定不可用，也不会有临时关闭，故没必要查询运力判断
			if stationHDConfig.HdStatus == station_constant.Unavailable || stationHDConfig.HdStatus == station_constant.TempClosed {
				continue
			}
			// 查询station的运力
			stationVolume := s.GetStationVolume(ctx, stationCoordinateInfo.StationID)
			// 直线距离小于服务范围*0.3快速成功，此时不能判断是否有快速临时关闭，因为有可能有直线距离大于0.3倍的服务范围的站点是可用的
			if stationHDConfig.HdStatus == station_constant.Available && drivingDistance <= float64(stationHDConfig.HdDistance)*config.GetMutableConf(ctx).StationConfig.GetStraightLineDistanceRadius() && (stationVolume < stationHDConfig.HdMaxCapacity || stationHDConfig.HdMaxCapacity == station_constant.StationCheckoutNoLimit) {
				if drivingDistance >= 0 && drivingDistance <= float64(stationHDConfig.HdDistance) {
					logger.CtxLogInfof(ctx, "[DirectDistance] buyer is support by current station; addrId: %v, buyerAddress: %+v, drivingDistance: %v, stationInfo: %+v, stationVolume: %v", addrId, addressStationInfo.Address, drivingDistance, stationHDConfig, stationVolume)
					_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetStationByDirectDistance, constant.StatusSuccess, "")
					tempResult := mapAddrAbility[addrId]
					tempResult.IsSupportHd = station_constant.SupportedHd
					tempResult.ReturnType = station_constant.LineDistanceAccept
					mapAfterFilter[addrId] = tempResult
					needMatrix = false
					break
				}
			}
		}
		if needMatrix {
			stationCoordinateList = append(stationCoordinateList, addressStationInfo)
		}
	}
	uncheckAddressAbility := s.filterUncheckAddress(mapAfterFilter)
	return mapAfterFilter, uncheckAddressAbility, stationCoordinateList
}

func (s *StationServiceImpl) getAddressStationMatrix(ctx context.Context, stationListCoordinateList []address.NearbyStation, stationTableMap map[uint64]address.HomeDeliveryConf, mapAddrAbility map[string]address.GetHdAbilityAddressResItem) map[string]address.GetHdAbilityAddressResItem {
	var lock sync.Mutex
	var wg sync.WaitGroup
	addressList := make([]address.AddressWithStationEntity, 0)
	saveBuyerStationDistanceList := make([]*address.SaveBuyerDistanceDto, 0)
	nowTime := time.Now().Unix() // nolint
	for _, stationCoordinate := range stationListCoordinateList {
		tmpStationCoordinate := stationCoordinate

		wg.Add(1)
		goasync.GoAndRecover(func() {
			defer wg.Done()
			stationCoordinateList := make([]address.CommonCoordinate, 0)
			for _, v := range tmpStationCoordinate.StationList {
				// stationHDConfig := stationTableMap[uint64(v.StationID)]
				// if stationHDConfig.IsSupportHd == station_constant.SupportedHd {
				// 迁移之后买家地址+服务范围内站点的行驶距离会直接保存到redis，故这里不根据status做剪枝
				stationCoordinateList = append(stationCoordinateList, v.Coordinate)
				// }
			}
			if len(stationCoordinateList) == 0 {
				return
			}
			// 获取行驶距离
			matrixDistanceList, err := s.HomeDeliveryService.GetDrivingDistanceFromMatrix(ctx, stationCoordinateList, []address.CommonCoordinate{tmpStationCoordinate.CenterPoint})
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetDrivingDistanceFromMatrix, tmpStationCoordinate=%+v, err=%v", tmpStationCoordinate, err)
			}
			mapMatrixDistance := make(map[string]float64)
			for _, matrixDistance := range matrixDistanceList {
				if matrixDistance.IsNoResult {
					continue
				}
				inputStr, _ := json.Marshal(matrixDistance.PointPair)
				mapMatrixDistance[string(inputStr)] = matrixDistance.Distance
			}
			tempAddress := address.AddressWithStationEntity{
				CommonAddress: tmpStationCoordinate.Address,
				IsHdSupport:   false,
			}
			// 根据行驶距离查看是否有履约站点
			for _, stationCoordinateInfo := range tmpStationCoordinate.StationList {
				stationHDConfig := stationTableMap[uint64(stationCoordinateInfo.StationID)]
				// station状态为unavailable时肯定不可用，也不会有临时关闭，故没必要查询运力判断
				if stationHDConfig.HdStatus == station_constant.Unavailable {
					continue
				}
				inputStr, _ := json.Marshal(address.PointPair{
					StartPoint: stationCoordinateInfo.Coordinate,
					EndPoint:   tmpStationCoordinate.CenterPoint,
				})
				drivingDistance, found := mapMatrixDistance[string(inputStr)]
				// 需要加运力限制
				stationVolume := s.GetStationVolume(ctx, stationCoordinateInfo.StationID)
				if found && stationHDConfig.HdStatus == station_constant.Available && drivingDistance >= 0 && drivingDistance <= float64(stationHDConfig.HdDistance) && (stationVolume < stationHDConfig.HdMaxCapacity || stationHDConfig.HdMaxCapacity == station_constant.StationCheckoutNoLimit) {
					tempAddress.IsHdSupport = true
					break
				}
				// 存在临时关闭的状态时，还需要往后判断，可能存在可用的station
				if found && drivingDistance >= 0 && drivingDistance <= float64(stationHDConfig.HdDistance) &&
					((stationVolume >= stationHDConfig.HdMaxCapacity && stationHDConfig.HdMaxCapacity != station_constant.StationCheckoutNoLimit && stationHDConfig.HdStatus == station_constant.Available) || (stationHDConfig.HdStatus == station_constant.TempClosed)) {
					tempAddress.ReturnType = station_constant.TemporarilyClosed
				}
			}
			saveBuyerStationDistance := s.convertToSaveBuyerDistanceDto(ctx, tmpStationCoordinate, stationTableMap, mapMatrixDistance, nowTime)
			saveBuyerStationDistanceList = append(saveBuyerStationDistanceList, saveBuyerStationDistance)
			// 加锁
			lock.Lock()
			defer lock.Unlock()
			addressList = append(addressList, tempAddress)
		})
	}
	wg.Wait()
	// 保存新地址行驶距离数据
	// 需要传买家地址详情
	err := s.HomeDeliveryService.SaveBuyerDistance(ctx, saveBuyerStationDistanceList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "save buyer distance error, err=%v", err)
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.SaveBuyerAddressFailure, constant.StatusSuccess, "")
	}
	for _, addressWithStationEntity := range addressList {
		addrId := address_library_util.GetAddressId(ctx, address.DivisionZipcodeEntity{
			Division: address.CreateDivision(addressWithStationEntity.AddressL1, addressWithStationEntity.AddressL2, "", "", "", 2),
		}, addressWithStationEntity.Address)
		tempAddressAbility := mapAddrAbility[addrId]
		if addressWithStationEntity.IsHdSupport {
			_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetStationByMatrixDistance, constant.StatusSuccess, "")
			logger.CtxLogInfof(ctx, "[AddressMatrix] this buyer address is supported; addr_id: %v, address: %+v, check result: %+v", addrId, tempAddressAbility, addressWithStationEntity)
			tempAddressAbility.ReturnType = station_constant.DrivingDistanceAccept
			tempAddressAbility.IsSupportHd = station_constant.SupportedHd
		} else {
			_ = monitor.ReportEvent(constant.CatModuleStation, constant.NoStationAroundAddress, constant.StatusSuccess, "")
			logger.CtxLogInfof(ctx, "[AddressMatrix] this buyer address is not supported; addr_id: %v, address: %+v, check result: %+v", addrId, tempAddressAbility, addressWithStationEntity)
			if addressWithStationEntity.ReturnType != "" {
				tempAddressAbility.ReturnType = addressWithStationEntity.ReturnType
			} else {
				tempAddressAbility.ReturnType = station_constant.DrivingDistanceReject
			}
			tempAddressAbility.IsSupportHd = station_constant.UnsupportedHd
		}
		mapAddrAbility[addrId] = tempAddressAbility
	}
	return mapAddrAbility
}

// BatchSyncStation 批量同步station信息
func (s *StationServiceImpl) BatchSyncStation(ctx utils.LCOSContext, req *pis_protocol.BatchSyncStationReq) (*pis_protocol.SyncStationResponseData, *lcos_error.LCOSError) {
	if len(req.StationList) == 0 {
		return nil, nil
	}
	var res = &pis_protocol.SyncStationResponseData{
		Result: pis_protocol.SuccessCode,
	}
	stationList := req.StationList
	// 1. 参数转换，接口数据转成表数据（根据地址信息查sls location id）
	stationTabList, failList, lcosErr := s.transferToStationTab(ctx, stationList)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "transfer req to station tab list fail, err: %s, req: %+v", lcosErr.Msg, stationList)
		return nil, lcosErr
	}
	res.FailList = append(res.FailList, failList...)
	// 2. 存表
	for _, stationTab := range stationTabList {
		isCreate, lcosErr := s.stationDao.CreateOrUpdate(ctx, stationTab)
		if lcosErr != nil && lcosErr.RetCode != lcos_error.OptimisticLocking {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, fmt.Sprintf("save_fail-%d", stationTab.StationId),
				strconv.FormatInt(int64(lcosErr.RetCode), 10), fmt.Sprintf("%s_%+v", lcosErr.Msg, stationTab))
			logger.LogErrorf("save station into db fail, err[%s], station_id[%s], station[%v]", lcosErr.Msg, stationTab.StationId, stationTab)
			res.FailList = append(res.FailList, &pis_protocol.StationFailData{
				UniqueId:     stationTab.UniqueId,
				StationId:    stationTab.StationId,
				FailedCode:   lcos_error.SaveStationFail,
				FailedReason: fmt.Sprintf("save station fail: %s", lcosErr.Msg),
			})
			continue
		}
		if lcosErr != nil && lcosErr.RetCode == lcos_error.OptimisticLocking { // 乐观锁导致未更新单独上报，需要关注原因
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, fmt.Sprintf("optimistic_lock-%d", stationTab.StationId),
				strconv.FormatInt(int64(lcosErr.RetCode), 10), fmt.Sprintf("%s_%+v", lcosErr.Msg, stationTab))
			msg := fmt.Sprintf("save station into db optimistic fail, err[%s], station_id[%d]", lcosErr.Msg, stationTab.StationId)
			logger.CtxLogErrorf(ctx, msg)
			res.SuccessList = append(res.SuccessList, &pis_protocol.StationSuccessData{
				UniqueId:  stationTab.UniqueId,
				StationId: stationTab.StationId,
				Message:   msg,
			})
			continue
		}
		// 创建/更新station成功后，刷新redis的station geo
		err := redislibv2.GeoAdd(ctx, redislibv2.GetHDStationRedisClient(), service_point_geo.BuildHDServicePointGeoHashCacheKey(ctx), strconv.FormatUint(stationTab.StationId, 10), stationTab.Longitude, stationTab.Latitude)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, fmt.Sprintf("update_station_geo_redis-%d", stationTab.StationId),
				constant.StatusError, fmt.Sprintf("%s_%+v", err.Error(), stationTab))
			logger.CtxLogErrorf(ctx, "Update station geo in redis fail, station id: %d, err: %s", stationTab.StationId, err.Error())
		}
		res.SuccessList = append(res.SuccessList, &pis_protocol.StationSuccessData{
			UniqueId:  stationTab.UniqueId,
			StationId: stationTab.StationId,
		})
		saveType := "update"
		if isCreate {
			saveType = "create"
		}
		_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, fmt.Sprintf("save_success-%s", saveType),
			constant.StatusSuccess, fmt.Sprintf("%s success, station: %+v", saveType, stationTab))
		logger.CtxLogInfof(ctx, "save[%s] station success, station: %+v", saveType, stationTab)
	}
	// set return result: 1-all success, 2- partial fail, 3- all fail
	if len(res.FailList) == len(stationList) {
		res.Result = pis_protocol.FailCode
	} else if len(res.FailList) > 0 {
		res.Result = pis_protocol.PartialSuccessCode
	}
	// 更新最大配送距离
	_, lcosErr = s.refreshStationMaxDistance(ctx)
	if lcosErr != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, "update_max_distance_redis",
			constant.StatusError, lcosErr.Msg)
		logger.CtxLogErrorf(ctx, "Update max distance in redis fail, err: %v", lcosErr)
	}
	// 更新版本号
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LogisticStationInfoNamespace, constant.LogisticAllStationIdNamespace)
	// 上报更新类型
	_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, fmt.Sprintf("sync_type-%s",
		station_constant.StationSyncTypeToStr[station_constant.StationSyncType(req.SyncType)]),
		constant.StatusSuccess, fmt.Sprintf("spx sync task id: %s", req.StationList[0].UniqueId))
	return res, nil
}

func (s *StationServiceImpl) refreshStationMaxDistance(ctx utils.LCOSContext) ([]*station.RefreshStationInfoInRedisItem, *lcos_error.LCOSError) {
	// 刷新最大配送范围
	var maxDistanceStation station_conf.LogisticStationInfoTab
	for _, region := range constant.BRANCH_REGIONS {
		stationTabList, lErr := s.stationDao.SearchStations(ctx, nil, region)
		if lErr != nil {
			logger.CtxLogErrorf(ctx, "Search All Stations fail, err: %s", lErr.Msg)
			return nil, lErr
		}
		for _, stationTab := range stationTabList {
			if stationTab.DistanceThreshold > maxDistanceStation.DistanceThreshold {
				maxDistanceStation = *stationTab
			}
		}
	}

	err := s.HDStationCacheManager.SetDistanceMaxCache(ctx, maxDistanceStation.DistanceThreshold)
	if err != nil {
		logger.CtxLogErrorf(ctx, "SetDistanceMaxCache fail, max distance: %f, err: %s", maxDistanceStation, err)
		return nil, lcos_error.NewLCOSError(lcos_error.RedisReadWriteErrorCode, err.Error())
	}
	logger.CtxLogInfof(ctx, "refresh max distance in redis, station id: %d, max distance: %f", maxDistanceStation.StationId, maxDistanceStation.DistanceThreshold)

	return []*station.RefreshStationInfoInRedisItem{
		{
			Type: station_constant.MaxCapacity,
			Key:  station_constant.MaxCapacity,
			Val:  fmt.Sprintf("station id: %d, max distance: %f", maxDistanceStation.StationId, maxDistanceStation.DistanceThreshold),
		},
	}, nil
}

func (s *StationServiceImpl) transferToStationTab(ctx utils.LCOSContext, stationList []*pis_protocol.StationData) ([]*station_conf.LogisticStationInfoTab, []*pis_protocol.StationFailData, *lcos_error.LCOSError) {
	var stationTabList = make([]*station_conf.LogisticStationInfoTab, 0, len(stationList))
	var failList = make([]*pis_protocol.StationFailData, 0, len(stationList))
	var err error

	for _, item := range stationList {
		stationTab := &station_conf.LogisticStationInfoTab{
			StationId:          item.StationId,
			UniqueId:           item.UniqueId,
			SyncStartTime:      item.SyncStartTime,
			StationName:        item.StationName,
			StationType:        item.StationType,
			LmDeliveryMode:     item.LmDeliveryMode,
			Operator:           item.Operator,
			ContactPerson:      item.ContactPerson,
			ContactPersonEmail: item.ContactPersonEmail,
			ContactPersonPhone: item.ContactPersonPhone,
			Support4PlLineList: item.Support4plLineList,
			Postcode:           item.Postcode,
			Region:             item.Country,
			DetailAddress:      strings.Join([]string{item.State, item.City, item.District, item.Street, item.DetailAddress}, ""),
			CheckoutStatus:     item.CheckoutStatus,
		}
		// transfer checkout limit (string-->int)
		checkoutLimit, lcosErr := transferCheckoutLimit(item.CheckoutLimit)
		if lcosErr != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, "checkout_limit",
				constant.StatusError, lcosErr.Msg)
			logger.LogErrorf("parse checkout limit fail, err[%s], checkout limit[%+v]", err.Error(), item.CheckoutLimit)
			failList = append(failList, &pis_protocol.StationFailData{
				UniqueId:     item.UniqueId,
				StationId:    item.StationId,
				FailedCode:   lcos_error.TransCheckoutLimitFail,
				FailedReason: lcosErr.Msg,
			})
			continue
		}
		stationTab.CheckoutLimit = *checkoutLimit
		// transfer longitude from string to float
		stationTab.Longitude, err = strconv.ParseFloat(item.Longitude, 64)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, fmt.Sprintf("longitude_%d", item.StationId),
				constant.StatusError, fmt.Sprintf("%s_%s", err.Error(), item.Longitude))
			logger.LogErrorf("parse longitude fail, longitude[%s], err[%s], station_id[%d]", item.Longitude, err.Error(), item.StationId)
			failList = append(failList, &pis_protocol.StationFailData{
				UniqueId:     item.UniqueId,
				StationId:    item.StationId,
				FailedCode:   lcos_error.TransLongitudeFail,
				FailedReason: fmt.Sprintf("parse longitude fail[%s], err: %s", item.Longitude, err.Error()),
			})
			continue
		}
		// transfer latitude from string to float
		stationTab.Latitude, err = strconv.ParseFloat(item.Latitude, 64)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, fmt.Sprintf("latitude_%d", item.StationId),
				constant.StatusError, fmt.Sprintf("%s_%s", err.Error(), item.Latitude))
			logger.LogErrorf("parse latitude fail, latitude[%s], err[%s], station_id[%d]", item.Latitude, err.Error(), item.StationId)
			failList = append(failList, &pis_protocol.StationFailData{
				UniqueId:     item.UniqueId,
				StationId:    item.StationId,
				FailedCode:   lcos_error.TransLatitudeFail,
				FailedReason: fmt.Sprintf("parse latitude fail[%s], err: %s", item.Latitude, err.Error()),
			})
			continue
		}
		// transfer distance_threshold from string to float
		stationTab.DistanceThreshold, err = strconv.ParseFloat(item.DistanceThreshold, 64)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, fmt.Sprintf("distance_threshold_%d", item.StationId),
				constant.StatusError, fmt.Sprintf("%s_%s", err.Error(), item.Latitude))
			logger.LogErrorf("parse distance_threshold fail, distance_threshold[%s], err[%s], station_id[%d]", item.DistanceThreshold, err.Error(), item.StationId)
			failList = append(failList, &pis_protocol.StationFailData{
				UniqueId:     item.UniqueId,
				StationId:    item.StationId,
				FailedCode:   lcos_error.TransDistanceThresholdFail,
				FailedReason: fmt.Sprintf("parse distance_threshold fail[%s], err: %s", item.DistanceThreshold, err.Error()),
			})
			continue
		}
		// transfer location info to sls location id, and set the values to the station tab
		var locationList []string
		locLevel := config.GetMutableConf(ctx).StationConfig.StationLocationSyncLevel[strings.ToUpper(item.Country)]
		for i, locationName := range []string{item.State, item.City, item.District, item.Street} {
			if len(locationName) == 0 {
				break
			}
			if locLevel > 0 && i >= locLevel {
				break // sync level limit, using to resolve the in inconsistent location level between SLS and SPX
			}
			locationList = append(locationList, locationName)
		}
		locationRes, err := ops_service.GetUpLocationInfoByName(ctx, &ops_service.GetLocationInfoByNameRequest{
			Country:      item.Country,
			LocationName: locationList,
		})
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStation, fmt.Sprintf("location_id_%d", item.StationId),
				constant.StatusError, fmt.Sprintf("%s_%v", err.Msg, locationList))
			logger.LogErrorf("Get location id failed, location_info[%v], err[%v], station_id[%d]", locationList, err, item.StationId)
			failList = append(failList, &pis_protocol.StationFailData{
				UniqueId:     item.UniqueId,
				StationId:    item.StationId,
				FailedCode:   lcos_error.TransStationLocFail,
				FailedReason: fmt.Sprintf("transfer address into location id fail, err: %s, location_info[%v]", err.Msg, locationList),
			})
			continue
		}
		for k, locationItem := range []*uint64{&stationTab.StateLocationId, &stationTab.CityLocationId, &stationTab.DistrictLocationId, &stationTab.StreetLocationId} {
			if k >= len(locationRes) {
				break
			}
			*locationItem = uint64(locationRes[k].LocationId)
			stationTab.LocationId = *locationItem
		}

		stationTabList = append(stationTabList, stationTab)
	}

	return stationTabList, failList, nil
}

func transferCheckoutLimit(checkoutLimit *pis_protocol.StationCheckoutLimit) (*station_conf.CheckoutLimit, *lcos_error.LCOSError) {
	var (
		checkoutLimitList = make([]int64, 0)
		res               = &station_conf.CheckoutLimit{}
	)
	if checkoutLimit == nil {
		checkoutLimit = &pis_protocol.StationCheckoutLimit{}
	}
	for _, limit := range []string{checkoutLimit.Mon, checkoutLimit.Tue,
		checkoutLimit.Wed, checkoutLimit.Thu, checkoutLimit.Fri,
		checkoutLimit.Sat, checkoutLimit.Sun} {
		if limit == "" {
			// when the user doesn't config the Monday checkout limit of station A, it means there is no limit for station A in Monday
			checkoutLimitList = append(checkoutLimitList, station_constant.StationCheckoutNoLimit)
			continue
		}
		num, err := strconv.Atoi(limit)
		if err != nil {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.ParamsError, "parse checkout limit from string to int fail, limit: %s, err: %s", limit, err.Error())
		}
		checkoutLimitList = append(checkoutLimitList, int64(num))
	}
	for i, limit := range []*int64{&res.Mon, &res.Tue, &res.Wed,
		&res.Thu, &res.Fri, &res.Sat, &res.Sun} {
		*limit = checkoutLimitList[i]
	}

	return res, nil
}

// GetOptimalHomeDeliveryStation 获取最优履约站点
func (s *StationServiceImpl) GetOptimalHomeDeliveryStation(ctx context.Context, addressInfo address.HomeDeliveryAddressEntity, requestSource string) (res address.HomeDeliveryAddressWithStationEntity, err error) {
	_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalMatchRequestTotal, constant.StatusSuccess, "")
	mapStationRejectReason := map[uint64]station_constant.HomeDeliveryStationRejectReason{} // 记录候选站点被过滤的原因
	defer func() {
		if res.AddressInfo.ExtraInfo != nil {
			res.AddressInfo.ExtraInfo.StationRejectReason = mapStationRejectReason
		}
	}()

	// 1.获取潜在站点列表
	res, err = s.getHomeDeliveryStationCandidate(ctx, addressInfo, station_constant.CreateOrder)
	if err != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetAvailableStationErrorTotal, constant.StatusSuccess, "")
		logger.CtxLogErrorf(ctx, "[GetHomeDeliveryStation] get station candidate failed, addressInfo=%+v, err=%v", addressInfo, err)
		return res, err
	}
	if len(res.AddressInfo.StationList) == 0 { // 没有可以履约的潜在站点
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetAvailableStationIsNilTotal, constant.StatusSuccess, "")
		return res, nil
	}
	_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetAvailableStationSuccessTotal, constant.StatusSuccess, "")
	mapStationCandidate := make(map[uint64]address.StationDistanceEntity)
	for _, stationInfo := range res.AddressInfo.StationList {
		mapStationCandidate[uint64(stationInfo.HdStationId)] = stationInfo
	}

	// 2.过滤不符合HomeDelivery要求的站点
	stationIdList, err := s.filterStationByHomeDeliveryConfig(ctx, res.AddressInfo.StationList, mapStationRejectReason)
	if err != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalFilterAvailableStationErrTotal, constant.StatusSuccess, "")
		return res, err
	}
	if len(stationIdList) == 0 { // 没有可以履约的潜在站点
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalFilterAvailableResultIsNilTotal, constant.StatusSuccess, "")
		res.AddressInfo.IsHdSupport = false
		res.AddressInfo.StationList = nil // 潜在站点都不能履约，置空
		return res, nil
	}

	// 4.按站点站点状态和配送类型筛选
	// 判断是否需要deliveryModType逻辑，不需要
	optimalStation := s.selectOptimalStation(ctx, stationIdList, mapStationCandidate, mapStationRejectReason)
	if optimalStation == 0 {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalFilterOptimalStationIsNilTotal, constant.StatusSuccess, "")
		res.AddressInfo.IsHdSupport = false
		res.AddressInfo.StationList = nil // 潜在站点都不能履约，置空
		return res, nil
	}
	finalStation := mapStationCandidate[optimalStation]
	res.AddressInfo.IsHdSupport = true
	res.AddressInfo.StationList = []address.StationDistanceEntity{finalStation}
	_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalSuccessMatchStationTotal, constant.StatusSuccess, "")
	return res, nil
}

func (s *StationServiceImpl) getHomeDeliveryStationCandidate(ctx context.Context, addressInfo address.HomeDeliveryAddressEntity, requestSource string) (address.HomeDeliveryAddressWithStationEntity, error) {
	_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetAvailableStationRequestTotal, constant.StatusSuccess, "")
	var timeout time.Duration
	var onlyAbility bool
	res := address.HomeDeliveryAddressWithStationEntity{}
	var err error
	if requestSource == station_constant.CreateOrder {
		timeout = config.GetMutableConf(ctx).StationConfig.GetHomeDeliveryCreateOrderTimeout()
	} else {
		timeout = config.GetMutableConf(ctx).StationConfig.GetHomeDeliveryMatchTimeout()
		onlyAbility = true
	}
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	tempData := &address.TempDataWithRWLock{}
	hdConfigMatchCh := make(chan address.HomeDeliveryAddressWithStationEntity, 1)

	goasync.GoAndRecover(func() {
		res, err = s.GetAddressItemHomeDeliveryStation(ctx, addressInfo, tempData, onlyAbility)
		hdConfigMatchCh <- res
	}, ctx)

	for {
		select {
		case r := <-hdConfigMatchCh:
			return r, err
		case <-ctx.Done():
			_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetAvailableStationTimeoutDefaultRequestTotal, constant.StatusSuccess, "")
			res, err = s.getAddressListHomeDeliveryStationFallback(ctx, addressInfo, tempData)
			return res, err
		}
	}
}

func (s *StationServiceImpl) GetAddressItemHomeDeliveryStation(ctx context.Context, addressInfo address.HomeDeliveryAddressEntity,
	tempData *address.TempDataWithRWLock, onlyAbility bool) (address.HomeDeliveryAddressWithStationEntity, error) {
	defer func() {
		if e := recover(); e != nil {
			debug.PrintStack()
			_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetHDStationPanic, constant.StatusPanic, fmt.Sprintf("GetAddressItemHomeDeliveryStation panic, err=%v", e))
			logger.CtxLogErrorf(ctx, "GetAddressItemHomeDeliveryStation panic, err=%v", e)
			return
		}
	}()
	res := address.HomeDeliveryAddressWithStationEntity{}
	// 1.从离线计算的数据中获取站点到买家地址的行驶距离
	stationByOffline, hitAddress, err := s.getAddressItemHomeDeliveryStationOffline(ctx, addressInfo)
	if err == nil && hitAddress {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetStationFromCacheResultTotal, constant.StatusSuccess, "")
		logger.CtxLogInfof(ctx, "[GetAddressItemHomeDeliveryStation] address hit offline data, result=%+v", stationByOffline)
		return stationByOffline, nil
	}

	// 2.从实时计算中获取站点到买家地址的行驶距离
	stationByRealtime, err := s.getAddressItemHomeDeliveryStationRealtime(ctx, addressInfo, tempData, onlyAbility)
	if err != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetStationFromRealResultErrTotal, constant.StatusSuccess, "")
		logger.CtxLogErrorf(ctx, "getAddressItemHomeDeliveryStationRealtime failed, addressInfo=%+v, err=%v", addressInfo, err)
		return res, err
	}
	logger.CtxLogInfof(ctx, "[GetAddressItemHomeDeliveryStation] address realtime result=%+v", stationByRealtime)
	_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetStationFromRealResultSuccessTotal, constant.StatusSuccess, "")

	return stationByRealtime, nil
}

// getAddressItemHomeDeliveryStationOffline 通过离线预计算的方式获取站点及行驶距离
func (s *StationServiceImpl) getAddressItemHomeDeliveryStationOffline(ctx context.Context,
	addressInfo address.HomeDeliveryAddressEntity) (address.HomeDeliveryAddressWithStationEntity, bool, error) {
	// 1.从redis获取站点到买家地址的行驶距离
	StationDistanceListResult, err := s.HomeDeliveryService.GetDrivingDistanceWithHDStation(ctx, addressInfo.BuyerId, []address.CommonAddress{addressInfo.AddressInfo})
	if err != nil {
		logger.CtxLogErrorf(ctx, "get station address distance list error err: %v", err)
	} else {
		logger.CtxLogInfof(ctx, "get driving distance and station successfully;station distance list:%+v", StationDistanceListResult)
	}
	// 2.如果获取到旧地址且有服务站点，则返回
	if len(StationDistanceListResult) > 0 {
		StationDistanceList := StationDistanceListResult[0]
		if !StationDistanceList.IsAddressNotExist { // 命中
			return s.getStationFromDrivingDistanceList(ctx, addressInfo, StationDistanceList), true, nil
		}
	}
	return address.HomeDeliveryAddressWithStationEntity{
		BuyerId: addressInfo.BuyerId,
		AddressInfo: address.AddressWithStationEntity{
			CommonAddress: addressInfo.AddressInfo,
			ReturnType:    station_constant.StationLibReject,
		},
	}, false, nil
}

func (s *StationServiceImpl) getStationFromDrivingDistanceList(ctx context.Context, addressInfo address.HomeDeliveryAddressEntity, StationDistanceList address.StationToAddressDrivingDistance) address.HomeDeliveryAddressWithStationEntity {
	extraInfo := &address.MatchResultExtraInfoEntity{LibVersion: 0}
	var stationList []address.StationDistanceEntity
	for _, stationDistance := range StationDistanceList.StationList {
		extraInfo.LibVersion = stationDistance.Version
		stationList = append(stationList, address.StationDistanceEntity{
			HdStationId:  stationDistance.StationID,
			DistanceType: station_constant.DistanceTypeDriving,
			Distance:     uint64(stationDistance.DrivingDistance),
		})
	}
	logger.CtxLogInfof(ctx, "get station distance from redis successfully;request:%+v; candidate station list:%v", addressInfo, stationList)
	return address.HomeDeliveryAddressWithStationEntity{
		BuyerId: addressInfo.BuyerId,
		AddressInfo: address.AddressWithStationEntity{
			CommonAddress:    addressInfo.AddressInfo,
			CommonCoordinate: StationDistanceList.Coordinate,
			ReturnType:       station_constant.StationLibAccept,
			ExtraInfo:        extraInfo,
			StationList:      stationList,
		},
	}
}

// getAddressItemHomeDeliveryStationRealtime 通过实时计算的方式获取站点及行驶距离
func (s *StationServiceImpl) getAddressItemHomeDeliveryStationRealtime(ctx context.Context, addressInfo address.HomeDeliveryAddressEntity,
	tempData *address.TempDataWithRWLock, onlyAbility bool) (address.HomeDeliveryAddressWithStationEntity, error) {
	// 如无法履约，则返回
	ErrorResponse := address.HomeDeliveryAddressWithStationEntity{
		BuyerId: addressInfo.BuyerId,
		AddressInfo: address.AddressWithStationEntity{
			CommonAddress: addressInfo.AddressInfo,
			IsHdSupport:   false,
		},
	}

	// 1.通过google获取经纬度
	addressCoordinateWithRadiusList, standardAddressList, err := s.getCoordinateFromGoogle(ctx, addressInfo)
	if err != nil || len(addressCoordinateWithRadiusList) == 0 {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetStationGetGoogleCoordinateErrTotal, constant.StatusSuccess, "")
		ErrorResponse.AddressInfo.ReturnType = station_constant.Fallback4pl
		return ErrorResponse, nil
	}
	addressPoint := &addressCoordinateWithRadiusList[0].CenterPoint

	// 填充额外信息google
	extraInfo := s.buildExtraInfoGoogle(addressPoint, standardAddressList)
	ErrorResponse.AddressInfo.ExtraInfo = &extraInfo

	// 2.通过经纬度获取附近的站点信息
	addressStationInfo, err := s.getNearByStationFromCoordinate(ctx, addressCoordinateWithRadiusList)
	if err != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetStationGetNearStationErrTotal, constant.StatusSuccess, "")
		ErrorResponse.AddressInfo.ReturnType = station_constant.DrivingDistanceReject
		return ErrorResponse, nil
	}
	// 保存中间数据，超时计算直线距离时需要使用
	// todo 把代码抽象成一个方法
	func() {
		tempData.Lock.Lock() // 加写锁
		defer tempData.Lock.Unlock()

		tempData.BuyerCoordinate = addressPoint
		tempData.BuyerStandardAddress = &extraInfo.GoogleMatchResult.StandardAddress
		tempData.NearbyStation = &addressStationInfo
	}()

	// 3.从redis获取站点配置信息
	stationIdList := make([]uint64, 0)
	for _, stationCoordinateInfo := range addressStationInfo.StationList {
		stationIdList = append(stationIdList, uint64(stationCoordinateInfo.StationID))
	}
	stationDistanceHdConfMap, err := s.getStationConfList(ctx, stationIdList)
	if err != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatMatchOptimalModule, constant.MatchOptimalGetStationGetStationConfErrTotal, constant.StatusSuccess, "")
		logger.CtxLogErrorf(ctx, "get station cache failed;error=%v", err)
		ErrorResponse.AddressInfo.ReturnType = station_constant.Fallback4pl
		return ErrorResponse, nil
	}

	// 4.（特殊逻辑）初筛，判断0.3倍服务范围内有没有站点,如果有的话并且是check ability, 直接返回
	addressCoordinateInfo := addressStationInfo.CenterPointWithRadius
	stationCoordinateList, mapStationDistance := s.searchStationByLineDistance(ctx, addressStationInfo, addressCoordinateInfo, stationDistanceHdConfMap)
	if len(mapStationDistance) > 0 {
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetStationByDirectDistance, constant.StatusSuccess, "")
		logger.CtxLogInfof(ctx, "get address from google and address to station <0.3*config ;request:%+v; candidate station:%+v", addressInfo, mapStationDistance)
		if onlyAbility { // 如果只是能力校验，确认有可以履约的站点就可以提前返回了，不需要继续计算路网距离
			// 上报一下观察是否真的没有使用
			_ = monitor.ReportEvent(constant.CatMatchOptimalModule, constant.MatchOptimalGetStationQuickSuccessTotal, constant.StatusSuccess, "get optimal station quick success.")
			return address.HomeDeliveryAddressWithStationEntity{
				BuyerId: addressInfo.BuyerId,
				AddressInfo: address.AddressWithStationEntity{
					CommonAddress:    addressInfo.AddressInfo,
					CommonCoordinate: *addressPoint,
					ReturnType:       station_constant.LineDistanceAccept,
					ExtraInfo:        &extraInfo,
					StationList:      s.mergeStationDistance(mapStationDistance, nil),
				},
			}, nil
		}
	}

	// 5 继续查询附近站点以及行驶距离
	mapStationDrivingDistance := s.searchStationByDrivingDistance(ctx, *addressPoint, stationCoordinateList, stationDistanceHdConfMap, addressStationInfo)
	if len(mapStationDrivingDistance) > 0 {
		_ = monitor.ReportEvent(constant.CatMatchOptimalModule, constant.MatchOptimalGetStationFromDrivingDistanceSuccessTotal, constant.StatusSuccess, "")
		logger.CtxLogInfof(ctx, "get address from google and address to station matrix distance <config ;request:%+v; candidate station:%+v", addressInfo, mapStationDrivingDistance)
		return address.HomeDeliveryAddressWithStationEntity{
			BuyerId: addressInfo.BuyerId,
			AddressInfo: address.AddressWithStationEntity{
				CommonAddress:    addressInfo.AddressInfo,
				CommonCoordinate: *addressPoint,
				ReturnType:       station_constant.DrivingDistanceAccept,
				ExtraInfo:        &extraInfo,
				StationList:      s.mergeStationDistance(mapStationDistance, mapStationDrivingDistance),
			},
		}, nil
	}

	// 8 没有任何履约站点
	_ = monitor.ReportEvent(constant.CatMatchOptimalModule, constant.MatchOptimalGetStationNoStationAroundAddressTotal, constant.StatusSuccess, "")
	logger.CtxLogInfof(ctx, "there is no station around this address")
	ErrorResponse.AddressInfo.ReturnType = station_constant.DrivingDistanceReject
	return ErrorResponse, nil
}

func (s *StationServiceImpl) getCoordinateFromGoogle(ctx context.Context, addressInfo address.HomeDeliveryAddressEntity) ([]address.CenterPointWithRadius, []string, error) {
	var addressCoordinateList []address.AddressCoordinate
	var err error
	if config.GetMutableConf(ctx).StationConfig.NotUseGoogleConfig {
		// 便于测试的谷歌经纬度
		addressCoordinateList = append(addressCoordinateList, address.AddressCoordinate{
			Coordinate: address.CommonCoordinate{
				Lat: config.GetMutableConf(ctx).StationConfig.MockGoogleLat,
				Lng: config.GetMutableConf(ctx).StationConfig.MockGoogleLng,
			},
		})
	} else {
		addressCoordinateList, err = s.HomeDeliveryService.GetBuyerAddressCoordinate(ctx, addressInfo.BuyerId, []address.CommonAddress{addressInfo.AddressInfo})
		if err != nil {
			_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetCoordinateFromGoogleError, constant.StatusSuccess, "")
			logger.CtxLogErrorf(ctx, "get coordinate from Google error: %v", err)
			return []address.CenterPointWithRadius{}, []string{}, err
		}
		if len(addressCoordinateList) == 0 {
			_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetCoordinateFromGoogleError, constant.StatusSuccess, "")
			logger.CtxLogErrorf(ctx, "get none coordinate from Google")
			return []address.CenterPointWithRadius{}, []string{}, errors.New("none google result")
		}
	}
	logger.CtxLogInfof(ctx, "get coordinate from google;result:%+v", addressCoordinateList)
	distanceMax, err := s.GetDistanceMax(ctx)
	if err != nil || distanceMax == 0 {
		logger.CtxLogErrorf(ctx, "get distance max error: %v", err)
		distanceMax = float64(station_constant.MaxDistance)
	}
	circleRadiusParameter := config.GetMutableConf(ctx).StationConfig.GetCircleRadiusParameter()
	addressCoordinateWithRadiusList := make([]address.CenterPointWithRadius, 0)
	standardAddressList := make([]string, 0)
	for _, addressCoordinate := range addressCoordinateList {
		addressCoordinateWithRadiusList = append(addressCoordinateWithRadiusList, address.CenterPointWithRadius{
			CenterPoint: addressCoordinate.Coordinate,
			// spx为了性能考虑缩小了半径，但计算行驶距离已经做了并行计算提高性能，和spx确定这里可以去掉缩小系数，和pm确定，做性能压测后，没有性能问题就去掉，有性能问题则保留
			Radius:  distanceMax * circleRadiusParameter,
			Address: addressCoordinate.Address,
		})
		standardAddressList = append(standardAddressList, addressCoordinate.StandardAddress)
	}
	return addressCoordinateWithRadiusList, standardAddressList, nil
}

func (s *StationServiceImpl) buildExtraInfoGoogle(addressPoint *address.CommonCoordinate, standardAddressList []string) address.MatchResultExtraInfoEntity {
	res := address.MatchResultExtraInfoEntity{}
	if len(standardAddressList) > 0 {
		res.GoogleMatchResult.StandardAddress = standardAddressList[0]
	}
	res.GoogleMatchResult.Coordinate = *addressPoint
	return res
}

func (s *StationServiceImpl) getNearByStationFromCoordinate(ctx context.Context, addressCoordinateWithRadiusList []address.CenterPointWithRadius) (addressStationInfo address.NearbyStation, err error) {
	stationList, err := s.HomeDeliveryService.SearchNearbyStation(ctx, addressCoordinateWithRadiusList)
	logger.CtxLogInfof(ctx, "get nearby station:%+v", stationList)
	// 如果没有查找到站点直接返回不支持
	if err != nil || len(stationList) == 0 {
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetNoneNearlyStation, constant.StatusSuccess, "")
		logger.CtxLogInfof(ctx, "there is no station near this address;coordinate:%v", addressCoordinateWithRadiusList)
		return addressStationInfo, errors.New("get station not found")
	}

	addressStationInfo = stationList[0]
	// todo 去掉测试的sleep
	// time.Sleep(time.Duration(config.GetMutableConf(ctx).StationConfig.MockGoogleLng.SearchStationTimeOut) * time.Millisecond)
	if len(addressStationInfo.StationList) == 0 {
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetNoneNearlyStation, constant.StatusSuccess, "")
		logger.CtxLogInfof(ctx, "there is no station near this address;coordinate:%v", addressCoordinateWithRadiusList)
		return addressStationInfo, errors.New("get station not found")
	}
	return addressStationInfo, nil
}

func (s *StationServiceImpl) searchStationByLineDistance(ctx context.Context, addressStationInfo address.NearbyStation,
	addressCoordinateInfo address.CenterPointWithRadius, stationDistanceHdConfMap map[uint64]address.HomeDeliveryConf) ([]address.CommonCoordinate, map[int64]address.StationDistanceEntity) {
	mapStationDistance := make(map[int64]address.StationDistanceEntity)
	stationCoordinateList := make([]address.CommonCoordinate, 0)
	// 对候选站点先排一次序，保证输出稳定
	sort.SliceStable(addressStationInfo.StationList, func(i, j int) bool {
		return addressStationInfo.StationList[i].StationID < addressStationInfo.StationList[j].StationID // 可能存在距离相同的情况，把站点ID也排一下
	})
	for _, stationCoordinateInfo := range addressStationInfo.StationList {
		lineDistance := utils.GetDistanceFloat(addressCoordinateInfo.CenterPoint.Lng, addressCoordinateInfo.CenterPoint.Lat, stationCoordinateInfo.Coordinate.Lng, stationCoordinateInfo.Coordinate.Lat)
		stationHDConfig := stationDistanceHdConfMap[uint64(stationCoordinateInfo.StationID)]
		// station状态为unavailable时肯定不可用，也不会有临时关闭，故没必要查询运力判断。快速成功时也不用考虑临时关闭的状态
		if stationHDConfig.HdStatus == station_constant.Unavailable || stationHDConfig.HdStatus == station_constant.TempClosed {
			continue
		}
		stationVolume := s.GetStationVolume(ctx, stationCoordinateInfo.StationID)
		// 这里使用来做剪枝的，所以提前做了station状态判断。这个方法的初衷应该是checkAbility和获取最优站点共用的，但实际上只有获取最优站点使用
		// 这里是需要做状态和距离、运力的校验的，因为这里是剪枝操作，需要保证返回的结果一定可用
		if stationHDConfig.HdStatus == station_constant.Available && lineDistance <= float64(stationHDConfig.HdDistance)*config.GetMutableConf(ctx).StationConfig.GetStraightLineDistanceRadius() && (stationVolume < stationHDConfig.HdMaxCapacity || stationHDConfig.HdMaxCapacity == station_constant.StationCheckoutNoLimit) {
			mapStationDistance[stationCoordinateInfo.StationID] = address.StationDistanceEntity{
				HdStationId:  stationCoordinateInfo.StationID,
				DistanceType: station_constant.DistanceTypeLine,
				Distance:     uint64(lineDistance),
			}
			// 这里不能continue, 因为后面还需要根据经纬度计算路网距离
		}
		// if stationHDConfig.IsSupportHd == station_constant.SupportedHd {
		// 注释station的状态判断，因为迁移后会直接保存买家地址+站点的行驶距离，所以对每个站点都需要计算行驶距离
		stationCoordinateList = append(stationCoordinateList, stationCoordinateInfo.Coordinate)
		// }
	}
	return stationCoordinateList, mapStationDistance
}

func (s *StationServiceImpl) mergeStationDistance(lineDistance, drivingDistance map[int64]address.StationDistanceEntity) []address.StationDistanceEntity {
	for stationID, dDis := range drivingDistance {
		lineDistance[stationID] = dDis // 如果直线距离存在，用行驶距离覆盖掉
	}
	res := make([]address.StationDistanceEntity, 0, len(lineDistance))
	for _, lDis := range lineDistance {
		res = append(res, lDis)
	}
	return res
}

func (s *StationServiceImpl) searchStationByDrivingDistance(ctx context.Context, buyerCoordinate address.CommonCoordinate,
	stationCoordinateList []address.CommonCoordinate, stationDistanceHdConfMap map[uint64]address.HomeDeliveryConf,
	addressStationInfo address.NearbyStation) map[int64]address.StationDistanceEntity {
	matrixDistanceList := make([]address.CommonMatrixDistance, 0)
	mapStationDistance := map[int64]address.StationDistanceEntity{}
	var err error
	if config.GetMutableConf(ctx).StationConfig.NotUseGoogleConfig {
		matrixDistanceList = append(matrixDistanceList, address.CommonMatrixDistance{Distance: config.GetMutableConf(ctx).StationConfig.MatrixDistance})
	} else {
		matrixDistanceList, err = s.HomeDeliveryService.GetDrivingDistanceFromMatrix(ctx, stationCoordinateList, []address.CommonCoordinate{buyerCoordinate})
	}
	if err != nil {
		logger.CtxLogErrorf(ctx, "get station distance by google matrix error:%+v", err)
		return nil
	}
	mapMatrixDistance := make(map[string]float64)
	for _, matrixDistance := range matrixDistanceList {
		if matrixDistance.IsNoResult { // 拿不到结果直接过滤掉
			continue
		}
		inputStr, _ := json.Marshal(matrixDistance.PointPair)
		mapMatrixDistance[string(inputStr)] = matrixDistance.Distance
	}
	// 对候选站点先排一次序，保证输出稳定
	sort.SliceStable(addressStationInfo.StationList, func(i, j int) bool {
		return addressStationInfo.StationList[i].StationID < addressStationInfo.StationList[j].StationID // 可能存在距离相同的情况，把站点ID也排一下
	})
	for _, stationCoordinateInfo := range addressStationInfo.StationList {
		stationHDConfig := stationDistanceHdConfMap[uint64(stationCoordinateInfo.StationID)]
		// station状态为unavailable时肯定不可用，也不会有临时关闭，故没必要查询运力判断
		if stationHDConfig.HdStatus == station_constant.Unavailable || stationHDConfig.HdStatus == station_constant.TempClosed {
			continue
		}
		inputStr, _ := json.Marshal(address.PointPair{
			StartPoint: stationCoordinateInfo.Coordinate,
			EndPoint:   buyerCoordinate,
		})
		drivingDistance, found := mapMatrixDistance[string(inputStr)]
		stationVolume := s.GetStationVolume(ctx, stationCoordinateInfo.StationID)
		// 看一下逻辑，其实这里不需要加状态和距离的判断，在最外层统一做了距离和状态的判断，但和spx逻辑保持一致
		if found && stationHDConfig.HdStatus == station_constant.Available && drivingDistance >= 0 && drivingDistance <= float64(stationHDConfig.HdDistance) && (stationVolume < stationHDConfig.HdMaxCapacity || stationHDConfig.HdMaxCapacity == station_constant.StationCheckoutNoLimit) {
			mapStationDistance[int64(stationHDConfig.StationId)] = address.StationDistanceEntity{
				HdStationId:  int64(stationHDConfig.StationId),
				DistanceType: station_constant.DistanceTypeDriving,
				Distance:     uint64(drivingDistance),
			}
		}
	}
	// 保存新地址的行驶距离
	saveBuyerStationDistanceList := make([]*address.SaveBuyerDistanceDto, 0)
	saveBuyerStationDistance := s.convertToSaveBuyerDistanceDto(ctx, addressStationInfo, stationDistanceHdConfMap, mapMatrixDistance, time.Now().Unix()) // nolint
	saveBuyerStationDistanceList = append(saveBuyerStationDistanceList, saveBuyerStationDistance)
	// 保存新地址行驶距离数据
	saveErr := s.HomeDeliveryService.SaveBuyerDistance(ctx, saveBuyerStationDistanceList)
	if saveErr != nil {
		logger.CtxLogErrorf(ctx, "save buyer distance error, err=%v", saveErr)
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.SaveBuyerAddressFailure, constant.StatusSuccess, "")
	}

	return mapStationDistance
}

// getAddressListHomeDeliveryStationFallback 兜底逻辑：获取站带你的直线距离
func (s *StationServiceImpl) getAddressListHomeDeliveryStationFallback(ctx context.Context, addressInfo address.HomeDeliveryAddressEntity,
	tempData *address.TempDataWithRWLock) (res address.HomeDeliveryAddressWithStationEntity, err error) {
	var addressCoordinateRes address.CommonCoordinate
	var nearByStationList address.NearbyStation
	var isNearbyReady bool
	var standardAddress string
	func() {
		tempData.Lock.RLock()         // 加读锁
		defer tempData.Lock.RUnlock() // 解锁

		if tempData.BuyerCoordinate != nil {
			addressCoordinateRes = *tempData.BuyerCoordinate
		}
		if tempData.NearbyStation != nil {
			nearByStationList = *tempData.NearbyStation
			isNearbyReady = true
		}
		if tempData.BuyerStandardAddress != nil {
			standardAddress = *tempData.BuyerStandardAddress
		}
	}()

	// 1.直线距离兜底
	_ = monitor.ReportEvent(constant.CatModuleStation, constant.TimeoutFallBack, constant.StatusSuccess, "")
	res.BuyerId = addressInfo.BuyerId
	res.AddressInfo = address.AddressWithStationEntity{
		CommonAddress:    addressInfo.AddressInfo,
		CommonCoordinate: addressCoordinateRes,
		ReturnType:       station_constant.DefaultHubFallBackAccept,
		StationList:      make([]address.StationDistanceEntity, 0),
		ExtraInfo: &address.MatchResultExtraInfoEntity{
			GoogleMatchResult: address.GoogleMatchResult{
				StandardAddress: standardAddress,
				Coordinate:      addressCoordinateRes,
			},
		},
	}

	if isNearbyReady {
		for _, stationCoordinateInfo := range nearByStationList.StationList {
			lineDistance := utils.GetDistanceFloat(addressCoordinateRes.Lng, addressCoordinateRes.Lat, stationCoordinateInfo.Coordinate.Lng, stationCoordinateInfo.Coordinate.Lat)
			res.AddressInfo.StationList = append(res.AddressInfo.StationList, address.StationDistanceEntity{
				HdStationId:  stationCoordinateInfo.StationID,
				DistanceType: station_constant.DistanceTypeLine,
				Distance:     uint64(lineDistance),
			})
		}
	}

	logger.CtxLogInfof(ctx, "getAddressListHomeDeliveryStationFallback finish, result=%+v", res)
	return res, nil
}

func (s *StationServiceImpl) filterStationByHomeDeliveryConfig(ctx context.Context, stationList []address.StationDistanceEntity,
	mapStationRejectReason map[uint64]station_constant.HomeDeliveryStationRejectReason) ([]uint64, error) {
	// 1.获取HomeDelivery配置
	stationIdList := make([]uint64, 0)
	for _, stationDistanceEntity := range stationList {
		stationIdList = append(stationIdList, uint64(stationDistanceEntity.HdStationId))
	}
	hdConfMap, err := s.getStationConfList(ctx, stationIdList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get hd conf map error:%+v", err)
		return nil, err
	}

	// 2.过滤不符合HomeDelivery要求的站点
	stationIdList = make([]uint64, 0) // 清空
	for _, stationDistanceEntity := range stationList {
		hdConfig, found := hdConfMap[uint64(stationDistanceEntity.HdStationId)]
		if !found || hdConfig.HdStatus != station_constant.Available { // 不支持
			mapStationRejectReason[uint64(stationDistanceEntity.HdStationId)] = station_constant.RejectReasonNotSupportHD
			_ = monitor.ReportEvent(constant.CatModuleStation, station_constant.MapRejectReasonWording[station_constant.RejectReasonNotSupportHD], constant.StatusSuccess, "")
			logger.CtxLogInfof(ctx, "station filter by status, station info:%+v, station conf:%+v", stationDistanceEntity, hdConfig)
			continue
		}
		if stationDistanceEntity.DistanceType == station_constant.DistanceTypeDefault {
			stationIdList = append(stationIdList, uint64(stationDistanceEntity.HdStationId)) // L3兜底用的
		} else if stationDistanceEntity.DistanceType == station_constant.DistanceTypeDriving {
			if stationDistanceEntity.Distance <= uint64(hdConfig.HdDistance) {
				stationVolume := s.GetStationVolume(ctx, stationDistanceEntity.HdStationId)
				if stationVolume < hdConfig.HdMaxCapacity || hdConfig.HdMaxCapacity == station_constant.StationCheckoutNoLimit {
					logger.CtxLogInfof(ctx, "[Driving Type]station is support, station info:%+v, station conf:%+v, currentVolume: %v", stationDistanceEntity, hdConfig, stationVolume)
					stationIdList = append(stationIdList, uint64(stationDistanceEntity.HdStationId))
				} else {
					mapStationRejectReason[uint64(stationDistanceEntity.HdStationId)] = station_constant.RejectReasonOverCapacity
					_ = monitor.ReportEvent(constant.CatModuleStation, station_constant.MapRejectReasonWording[station_constant.RejectReasonOverCapacity], constant.StatusSuccess, "")
					logger.CtxLogInfof(ctx, "[Driving Type]station filter by volume, station info:%+v, station conf:%+v, currentVolume: %v", stationDistanceEntity, hdConfig, stationVolume)
				}
			} else {
				mapStationRejectReason[uint64(stationDistanceEntity.HdStationId)] = station_constant.RejectReasonOverServiceDrivingDistance
				_ = monitor.ReportEvent(constant.CatModuleStation, station_constant.MapRejectReasonWording[station_constant.RejectReasonOverServiceDrivingDistance], constant.StatusSuccess, "")
				logger.CtxLogInfof(ctx, "[Driving Type]station filter by distance, station info:%+v, station conf:%+v", stationDistanceEntity, hdConfig)
			}
		} else if stationDistanceEntity.DistanceType == station_constant.DistanceTypeLine {
			if stationDistanceEntity.Distance <= uint64(float64(hdConfig.HdDistance)*config.GetMutableConf(ctx).StationConfig.GetHigherStraightLineDistanceRadius()) {
				stationVolume := s.GetStationVolume(ctx, stationDistanceEntity.HdStationId)
				if stationVolume < hdConfig.HdMaxCapacity || hdConfig.HdMaxCapacity == station_constant.StationCheckoutNoLimit {
					logger.CtxLogInfof(ctx, "[Line Type]station is support, station info:%+v, station conf:%+v, currentVolume: %v", stationDistanceEntity, hdConfig, stationVolume)
					stationIdList = append(stationIdList, uint64(stationDistanceEntity.HdStationId)) // 直线距离兜底
				} else {
					mapStationRejectReason[uint64(stationDistanceEntity.HdStationId)] = station_constant.RejectReasonOverCapacity
					_ = monitor.ReportEvent(constant.CatModuleStation, station_constant.MapRejectReasonWording[station_constant.RejectReasonOverCapacity], constant.StatusSuccess, "")
					logger.CtxLogInfof(ctx, "[Line Type]station filter by volume, station info:%+v, station conf:%+v, currentVolume: %v", stationDistanceEntity, hdConfig, stationVolume)
				}
			} else {
				mapStationRejectReason[uint64(stationDistanceEntity.HdStationId)] = station_constant.RejectReasonOverServiceFallbackLineDistance
				_ = monitor.ReportEvent(constant.CatModuleStation, station_constant.MapRejectReasonWording[station_constant.RejectReasonOverServiceFallbackLineDistance], constant.StatusSuccess, "")
				logger.CtxLogInfof(ctx, "[Line Type]station filter by distance, station info:%+v, station conf:%+v", stationDistanceEntity, hdConfig)
			}
		} else {
			logger.CtxLogErrorf(ctx, "Invalid distance type, station dis: %+v", stationDistanceEntity)
			continue
		}
	}
	logger.CtxLogInfof(ctx, "filter support stationIdList: %v", stationIdList)
	return stationIdList, nil
}

// selectOptimalStation 根据配置和条件选择最优履约HUB
func (s *StationServiceImpl) selectOptimalStation(ctx context.Context, candidateStation []uint64,
	mapStationCandidate map[uint64]address.StationDistanceEntity, mapStationRejectReason map[uint64]station_constant.HomeDeliveryStationRejectReason) uint64 {
	var optimalDrivingStationID, optimalLineStationID, defaultFallbackStationID uint64
	minDrivingDis := uint64(station_constant.MaxDistance)
	minLineDis := uint64(station_constant.MaxDistance)

	sort.SliceStable(candidateStation, func(i, j int) bool { // 对站点ID排一下序，保证输出结果稳定
		return candidateStation[i] < candidateStation[j]
	})
	for _, stationID := range candidateStation {
		// 判断是否更优的站点
		stationDis := mapStationCandidate[stationID]
		if stationDis.DistanceType == station_constant.DistanceTypeDefault {
			defaultFallbackStationID = stationID
		} else if stationDis.DistanceType == station_constant.DistanceTypeDriving { // 找出行驶距离最近
			if stationDis.Distance < minDrivingDis {
				minDrivingDis = stationDis.Distance
				optimalDrivingStationID = stationID
			}
		} else if stationDis.DistanceType == station_constant.DistanceTypeLine { // 找出直线距离最近
			if stationDis.Distance < minLineDis {
				minLineDis = stationDis.Distance
				optimalLineStationID = stationID
			}
		}
	}

	// 优先级选择：行驶距离 > 直线距离 > 默认兜底
	optimalStationID := defaultFallbackStationID
	if optimalDrivingStationID != 0 {
		optimalStationID = optimalDrivingStationID
	} else if optimalLineStationID != 0 {
		optimalStationID = optimalLineStationID
	}

	// 补充非最优站点被过滤的原因
	for _, stationID := range candidateStation {
		if _, found := mapStationRejectReason[stationID]; found {
			continue
		}
		if stationID != optimalStationID {
			mapStationRejectReason[stationID] = station_constant.RejectReasonNotOptimalStation
			_ = monitor.ReportEvent(constant.CatModuleStation, station_constant.MapRejectReasonWording[station_constant.RejectReasonNotOptimalStation], constant.StatusSuccess, "")
		}
	}
	logger.CtxLogInfof(ctx, "get optimal station is: %v, candidateStation:%+v, mapStationCandidate: %+v, mapStationRejectReason: %+v", candidateStation, mapStationCandidate, mapStationRejectReason)

	return optimalStationID
}

// reportGetHomeDeliveryStationResult 统一上报
func (s *StationServiceImpl) reportGetHomeDeliveryStationResult(ctx context.Context, module string, result address.AddressWithStationEntity) {
	if result.ReturnType == station_constant.StationLibAccept {
		_ = monitor.ReportEvent(module, string(station_constant.AcceptFromOfflineDistanceLib), "0", "")
	} else if result.ReturnType == station_constant.DrivingDistanceAccept {
		_ = monitor.ReportEvent(module, string(station_constant.AcceptFromRealtimeDrivingDistance), "0", "")
	} else if result.ReturnType == station_constant.LineDistanceAccept {
		_ = monitor.ReportEvent(module, string(station_constant.AcceptFromRealtimeLineDistance), "0", "")
	} else if result.ReturnType == station_constant.LineDistanceFallbackAccept {
		_ = monitor.ReportEvent(module, string(station_constant.AcceptFromRealtimeTimeoutLineDistance), "0", "")
	} else if result.ReturnType == station_constant.DefaultHubFallBackAccept {
		_ = monitor.ReportEvent(module, string(station_constant.AcceptFromRealtimeTimeoutDefaultHub), "0", "")
	} else {
		_ = monitor.ReportEvent(module, string(station_constant.NoAccept), "0", "")
	}
}

// convertToSaveBuyerDistanceDto 转成待保存的新地址和站点的行驶距离数据
func (s *StationServiceImpl) convertToSaveBuyerDistanceDto(ctx context.Context, stationCoordinate address.NearbyStation, stationTableMap map[uint64]address.HomeDeliveryConf, mapMatrixDistance map[string]float64, nowTime int64) *address.SaveBuyerDistanceDto {
	saveBuyerStationDistance := &address.SaveBuyerDistanceDto{
		BuyerAddressId: address_library_util.GetAddressId(ctx, address.DivisionZipcodeEntity{
			Division: address_library_util.CreateDivision(stationCoordinate.Address.AddressL1, stationCoordinate.Address.AddressL2, "", "", "", 2),
		}, stationCoordinate.Address.Address),
		Address:    stationCoordinate.Address,
		Coordinate: stationCoordinate.CenterPoint,
	}
	for _, stationCoordinateInfo := range stationCoordinate.StationList {
		stationHDConfig := stationTableMap[uint64(stationCoordinateInfo.StationID)]
		inputStr, _ := json.Marshal(address.PointPair{
			StartPoint: stationCoordinateInfo.Coordinate,
			EndPoint:   stationCoordinate.CenterPoint,
		})
		drivingDistance, found := mapMatrixDistance[string(inputStr)]
		// 保存买家地址附近服务范围内的station
		logger.CtxLogDebugf(ctx, "station_id: %v, drivingDistance: %v, stationHdDistance: %v", stationCoordinateInfo.StationID, drivingDistance, stationHDConfig.HdDistance)
		if found && drivingDistance >= 0 && drivingDistance <= float64(stationHDConfig.HdDistance) {
			saveBuyerStationDistance.DistanceList = append(saveBuyerStationDistance.DistanceList, hd_distance_repo.HdBuyerHubDistanceTab{
				BuyerAddrL1:     stationCoordinate.Address.AddressL1,
				BuyerAddrL2:     stationCoordinate.Address.AddressL2,
				BuyerAddr:       stationCoordinate.Address.Address,
				BuyerAddrID:     saveBuyerStationDistance.BuyerAddressId,
				StationID:       uint32(stationCoordinateInfo.StationID),
				DrivingDistance: drivingDistance,
				ExtraInfo: hd_distance_repo.HdBuyerExtraInfo{
					BuyerAddrLat: stationCoordinate.CenterPoint.Lat,
					BuyerAddrLng: stationCoordinate.CenterPoint.Lng,
				},
				DataType: constant.DistanceInfoFromSLS,
			})
		}
	}
	return saveBuyerStationDistance
}

func (s *StationServiceImpl) ListStation(ctx utils.LCOSContext, req *station.ListStationRequest) (*station.ListStationResponse, *lcos_error.LCOSError) {
	condition, _ := utils.Struct2map(req)
	stationList, total, err := s.stationDao.PageListStation(ctx, req.PageNo, req.PageSize, condition)
	if err != nil {
		return nil, err
	}

	return s.genStationListResp(ctx, req, total, stationList)
}

func (s *StationServiceImpl) genStationListResp(ctx utils.LCOSContext, req *station.ListStationRequest, total uint32, list []*station_conf.LogisticStationInfoTab) (*station.ListStationResponse, *lcos_error.LCOSError) {
	var resp = &station.ListStationResponse{
		PageNo:   req.PageNo,
		PageSize: req.PageSize,
		Total:    total,
		List:     make([]*station.ListStationItem, 0, len(list)),
	}
	for _, item := range list {
		stationInfo := &station.ListStationItem{
			StationId:          item.StationId,
			StationName:        item.StationName,
			StationType:        item.StationType,
			LmDeliveryMode:     item.LmDeliveryMode,
			Operator:           item.Operator,
			Mtime:              item.Mtime,
			MaxCapacity:        0,
			CurrentOrderVolume: 0,
		}
		stationInfo.MaxCapacity = item.GetHDMaxCapacity(ctx)
		volumeKey := GenStationRedisVolumeKey(ctx, item.StationId, NotUsePaidDate, item.Region)
		volume, err := s.HDStationCacheManager.GetStationCurrentVolume(ctx, volumeKey)
		if err != nil {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.RedisReadWriteErrorCode, "GetStationCurrentVolume fail, key: %s, err: %s", volumeKey, err.Error())
		}
		stationInfo.CurrentOrderVolume = volume
		resp.List = append(resp.List, stationInfo)
	}

	return resp, nil
}

func (s *StationServiceImpl) GetStation(ctx utils.LCOSContext, req *station.GetStationRequest) (*station.GetStationResponse, *lcos_error.LCOSError) {
	stationTab, err := s.stationDao.GetStationByStationId(ctx, req.StationId, ctx.GetCountry())
	if err != nil {
		return nil, err
	}
	if stationTab == nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.NotFoundStationErr, "station not found, station id: %d", req.StationId)
	}

	return s.genGetStationResp(ctx, stationTab)
}

func (s *StationServiceImpl) genGetStationResp(ctx utils.LCOSContext, stationTab *station_conf.LogisticStationInfoTab) (*station.GetStationResponse, *lcos_error.LCOSError) {
	var resp = &station.GetStationResponse{
		StationInfo: &station.StationBasicInfo{
			StationId:          stationTab.StationId,
			StationName:        stationTab.StationName,
			StationType:        stationTab.StationType,
			Country:            stationTab.Region,
			Operator:           stationTab.Operator,
			LmDeliveryMode:     stationTab.LmDeliveryMode,
			Postcode:           stationTab.Postcode,
			Support4plLineList: stationTab.Support4PlLineList,
		},
		StationGeo: &station.StationGeoInfo{
			DetailAddress:      stationTab.DetailAddress,
			LocationId:         stationTab.LocationId,
			Longitude:          strconv.FormatFloat(stationTab.Longitude, 'f', -1, 64),
			Latitude:           strconv.FormatFloat(stationTab.Latitude, 'f', -1, 64),
			ContactPerson:      stationTab.ContactPerson,
			ContactPersonPhone: stationTab.ContactPersonPhone,
			ContactPersonEmail: stationTab.ContactPersonEmail,
		},
		StationAllocation: &station.StationAllocationInfo{
			DistanceThreshold: strconv.FormatFloat(stationTab.DistanceThreshold, 'f', -1, 64),
			CheckoutStatus:    stationTab.CheckoutStatus,
		},
	}
	checkoutLimit := station.StationCheckoutLimit{}
	_ = copier.Copy(&checkoutLimit, stationTab.CheckoutLimit)
	resp.StationAllocation.CheckoutLimit = &checkoutLimit
	// get current station volume
	volumeKey := GenStationRedisVolumeKey(ctx, stationTab.StationId, NotUsePaidDate, stationTab.Region)
	volume, err := s.HDStationCacheManager.GetStationCurrentVolume(ctx, volumeKey)
	if err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.RedisReadWriteErrorCode, "GetStationCurrentVolume fail, key: %s, err: %s", volumeKey, err.Error())
	}
	resp.StationAllocation.CurrentOrderVolume = volume
	// get address info
	locationInfo, lErr := address_service.LocationServer.GetLocationInfoById(ctx, int(stationTab.LocationId))
	if err != nil {
		logger.LogError(err)
		return nil, lErr
	}
	if locationInfo.State != "" {
		resp.StationGeo.State = locationInfo.State
	}
	if locationInfo.City != "" {
		resp.StationGeo.City = locationInfo.City
	}
	if locationInfo.District != "" {
		resp.StationGeo.District = locationInfo.District
	}
	if locationInfo.Street != "" {
		resp.StationGeo.Street = locationInfo.Street
	}

	return resp, nil
}

func (s *StationServiceImpl) ListAllStationDetail(ctx utils.LCOSContext, req *station.ExportStationRequest) ([]*station.GetStationResponse, *lcos_error.LCOSError) {
	condition, _ := utils.Struct2map(req)
	stationTabList, err := s.stationDao.SearchStations(ctx, condition, ctx.GetCountry())
	if err != nil {
		return nil, err
	}

	var stationInfoList = make([]*station.GetStationResponse, 0, len(stationTabList))
	for _, stationTab := range stationTabList {
		stationInfo, err := s.genGetStationResp(ctx, stationTab)
		if err != nil {
			logger.CtxLogErrorf(ctx, "fill station info fail, station id: %d, err: %s", stationTab.StationId, err.Msg)
			return nil, err
		}
		stationInfoList = append(stationInfoList, stationInfo)
	}

	return stationInfoList, nil
}

func (s *StationServiceImpl) ExportStation(ctx utils.LCOSContext, req *station.ExportStationRequest) (*station.ExportStationResponse, *lcos_error.LCOSError) {
	// get all data
	stationInfoList, lErr := s.ListAllStationDetail(ctx, req)
	if lErr != nil {
		return nil, lErr
	}
	excelDataList := genExcelDataList(stationInfoList)
	// gen excel file
	filePath := fmt.Sprintf("station-export-%d.xlsx", utils.GetTimestampInMillisecond())
	excelFile := xlsx.NewFile()
	var sheetName = station_constant.ExportStationSheetName
	if err := excel.WriteTitleAndStruct(excelFile, sheetName, station_constant.ExportStationExcelTitleList, excelDataList); err != nil {
		logger.CtxLogErrorf(ctx, "Export|failed,err=%v", err)
		return nil, lcos_error.NewLCOSError(lcos_error.ExportStationFail, err.Error())
	}
	// save temp file
	err := excelFile.Save(filePath)
	if err != nil {
		logger.CtxLogErrorf(ctx, "save temp file fail", err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	// upload file to S3
	fileUrl, _, _, lErr := s.s3Service.UploadFileToS3(ctx, config.GetConf(ctx).LCOSBranchServiceS3Config.AccessKeyID, config.GetConf(ctx).LCOSBranchServiceS3Config.BucketKey, filePath, config.GetConf(ctx).LCOSBranchServiceS3Config.TimeOut, config.GetConf(ctx).LCOSBranchServiceS3Config.ExpirationDays, "")
	if lErr != nil {
		logger.CtxLogErrorf(ctx, "upload file to S3 fail, file path:%s, err:%s", filePath, lErr.Msg)
		return nil, lErr
	}
	// del local temp file
	err = os.Remove(filePath)
	if err != nil {
		logger.CtxLogErrorf(ctx, "delete local temp file fail", err.Error())
	}

	return &station.ExportStationResponse{
		FileUrl: fileUrl,
	}, nil
}

// todo 处理-1的特殊情况
func (s *StationServiceImpl) GetStationVolume(ctx context.Context, stationId int64) int64 {
	region := utils.GetRegion(ctx)
	volumeKey := GenStationRedisVolumeKey(ctx, uint64(stationId), NotUsePaidDate, region)
	volume, redisErr := s.HDStationCacheManager.GetStationCurrentVolume(ctx, volumeKey)
	if redisErr != nil {
		_ = monitor.ReportEvent(constant.CatModuleStation, constant.GetStationCurrentVolumeError, constant.StatusSuccess, "")
		logger.CtxLogInfof(ctx, "[stationLib] get station current volume error, err=%v", redisErr)
	}
	return volume
}

func (s *StationServiceImpl) BatchGetDistanceFromCache(ctx utils.LCOSContext, req *station.BatchGetDistanceReq) ([]address.StationToAddressDrivingDistance, *lcos_error.LCOSError) {
	newCtx := ctxhelper.CloneTrace(ctx)
	newCtx = context.WithValue(newCtx, constant.DeployRegionKey, req.Region)
	addressList := make([]address.CommonAddress, len(req.List))
	err := copier.Copy(&addressList, &req.List)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.BatchGetDistanceCacheErr, err.Error())
	}
	result, err := s.HomeDeliveryService.BatchQueryDistanceCache(newCtx, addressList)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.BatchGetDistanceCacheErr, err.Error())
	}
	return result, nil
}

func (s *StationServiceImpl) BatchGetDistanceCacheByAddrId(ctx utils.LCOSContext, req *station.BatchGetDistanceReq) ([]address.DistanceCachePair, *lcos_error.LCOSError) {
	newCtx := ctxhelper.CloneTrace(ctx)
	newCtx = context.WithValue(newCtx, constant.DeployRegionKey, req.Region)
	result, err := s.HomeDeliveryService.BatchQueryDistanceCacheByAddrId(newCtx, req.BuyerAddrIdList)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.BatchGetDistanceCacheErr, err.Error())
	}
	return result, nil
}

func genExcelDataList(list []*station.GetStationResponse) []interface{} {
	var excelDataList = make([]interface{}, 0, len(list))
	for _, item := range list {
		excelDataList = append(excelDataList, &station.ExportStationExcelRawData{
			StationId:          item.StationInfo.StationId,
			StationName:        item.StationInfo.StationName,
			StationType:        station_constant.StationTypeToStr[item.StationInfo.StationType],
			Country:            item.StationInfo.Country,
			LmDeliveryMode:     station_constant.LmDeliveryModeToStr[item.StationInfo.LmDeliveryMode],
			Support4plLineList: strings.Join(item.StationInfo.Support4plLineList, ","),
			Postcode:           item.StationInfo.Postcode,
			State:              item.StationGeo.State,
			City:               item.StationGeo.City,
			District:           item.StationGeo.District,
			Street:             item.StationGeo.Street,
			DetailAddress:      item.StationGeo.DetailAddress,
			Longitude:          item.StationGeo.Longitude,
			Latitude:           item.StationGeo.Latitude,
			LocationId:         strconv.FormatUint(item.StationGeo.LocationId, 10),
			DistanceThreshold:  item.StationAllocation.DistanceThreshold,
			CheckoutStatus:     station_constant.CheckoutStatusToStr[item.StationAllocation.CheckoutStatus],
			SunCheckoutLimit:   strconv.FormatInt(item.StationAllocation.CheckoutLimit.Sun, 10),
			MonCheckoutLimit:   strconv.FormatInt(item.StationAllocation.CheckoutLimit.Mon, 10),
			TueCheckoutLimit:   strconv.FormatInt(item.StationAllocation.CheckoutLimit.Tue, 10),
			WedCheckoutLimit:   strconv.FormatInt(item.StationAllocation.CheckoutLimit.Wed, 10),
			ThuCheckoutLimit:   strconv.FormatInt(item.StationAllocation.CheckoutLimit.Thu, 10),
			FriCheckoutLimit:   strconv.FormatInt(item.StationAllocation.CheckoutLimit.Fri, 10),
			SatCheckoutLimit:   strconv.FormatInt(item.StationAllocation.CheckoutLimit.Sat, 10),
			CurrentOrderVolume: item.StationAllocation.CurrentOrderVolume,
		})
	}

	return excelDataList
}

func (s *StationServiceImpl) SyncDistanceData(ctx utils.LCOSContext, req *station.SyncDistanceDataReq) *lcos_error.LCOSError {
	lcosCtx := utils.NewCommonCtx(ctx)
	goasync.GoAndRecover(func() {
		err := s.HomeDeliveryService.ProcessData(lcosCtx, req.Region, req.PathList)
		if err != nil {
			logger.CtxLogErrorf(lcosCtx, "SyncDistanceData failed, err :%v", err)
		}
	}, lcosCtx)
	return nil
}

func (s *StationServiceImpl) HandleStationBuyerRelation(ctx utils.LCOSContext, req *station.SyncDistanceDataReq) *lcos_error.LCOSError {
	return s.HomeDeliveryService.HandleStationBuyerRelation(ctx, req.Region)
}

func (s *StationServiceImpl) CalculateStationBuyerDistance(ctx utils.LCOSContext, req *station.CalculateStationBuyerDistance) *lcos_error.LCOSError {
	return s.HomeDeliveryService.CalculateStationBuyerDistance(ctx, req.Region)
}

func (s *StationServiceImpl) AddressRevisionHandle(ctx utils.LCOSContext, req *station.HandleAddressRevisionJobReq) *lcos_error.LCOSError {
	return s.HomeDeliveryService.AddressRevisionHandle(ctx, req.Region)
}

func (s *StationServiceImpl) AddressRevisionDataInsert(ctx utils.LCOSContext, req *schema.AddressRevisionDataInsertMsg) *lcos_error.LCOSError {
	return s.HomeDeliveryService.AddressRevisionDataInsert(ctx, req)
}

func (s *StationServiceImpl) RefreshStationInfoInRedis(ctx utils.LCOSContext, req *station.RefreshStationInfoInRedisReq) ([]*station.RefreshStationInfoInRedisItem, *lcos_error.LCOSError) {
	switch req.Type {
	case station_constant.Geo:
		return s.refreshStationGeoInRedis(ctx, req)
	case station_constant.MaxCapacity:
		return s.refreshStationMaxDistance(ctx)
	}
	return nil, lcos_error.NewLCOSErrorf(lcos_error.ParamsError, "invalid val type: %s", req.Type)
}

func (s *StationServiceImpl) refreshStationGeoInRedis(ctx utils.LCOSContext, req *station.RefreshStationInfoInRedisReq) ([]*station.RefreshStationInfoInRedisItem, *lcos_error.LCOSError) {
	condition := map[string]interface{}{
		"station_id in": req.StationIdList,
	}
	stationTabList, err := s.stationDao.SearchStations(ctx, condition, req.Region)
	if err != nil {
		logger.CtxLogErrorf(ctx, "SearchStations fail, cond: %+v, err: %s", condition, err.Msg)
		return nil, err
	}
	var resp = make([]*station.RefreshStationInfoInRedisItem, 0, len(req.StationIdList))
	for _, stationTab := range stationTabList {
		err := redislibv2.GeoAdd(ctx, redislibv2.GetHDStationRedisClient(), service_point_geo.BuildHDServicePointGeoHashCacheKey(ctx), strconv.FormatUint(stationTab.StationId, 10), stationTab.Longitude, stationTab.Latitude)
		if err != nil {
			return resp, lcos_error.NewLCOSErrorf(lcos_error.RedisReadWriteErrorCode, "GeoAdd fail, station id: %d, err: %s", stationTab.StationId, err.Error())
		}
		logger.CtxLogInfof(ctx, "refresh station geo in redis, station_id: %d, longitude: %f, latitude: %f", stationTab.StationId, stationTab.Longitude, stationTab.Latitude)
		resp = append(resp, &station.RefreshStationInfoInRedisItem{
			Region: req.Region,
			Type:   req.Type,
			Key:    strconv.FormatUint(stationTab.StationId, 10),
			Val:    fmt.Sprintf("longitude: %f, latitude: %f", stationTab.Longitude, stationTab.Latitude),
		})
	}

	return resp, nil
}

func (s *StationServiceImpl) GetCacheVal(ctx utils.LCOSContext, req *station.QueryCacheValReq) ([]*station.QueryCacheValRespItem, *lcos_error.LCOSError) {
	var respList = make([]*station.QueryCacheValRespItem, 0, len(req.List))
	var errMsg, cacheVal string
	for _, item := range req.List {
		switch item.CacheType {
		case station_constant.Local:
			switch item.ValType {
			case station_constant.SingleStation:
				stationId, _ := strconv.Atoi(item.CacheKey)
				info, lErr := s.stationDao.GetStationByStationIdFromCache(ctx, uint64(stationId))
				if lErr != nil {
					errMsg = lErr.Msg
				} else {
					cacheVal, _ = jsoniter.MarshalToString(info)
				}
			case station_constant.AllStationId:
				info, lErr := s.stationDao.GetAllStationIdFromCache(ctx)
				if lErr != nil {
					errMsg = lErr.Msg
				} else {
					cacheVal, _ = jsoniter.MarshalToString(info)
				}
			default:
				errMsg = "invalid val type"
			}
		case station_constant.Redis:
			switch item.ValType {
			case station_constant.Geo:
				locs, err := redislibv2.GeoPos(ctx, redislibv2.GetHDStationRedisClient(), service_point_geo.BuildHDServicePointGeoHashCacheKey(ctx), item.CacheKey)
				if err != nil {
					errMsg = err.Error()
				} else {
					cacheVal, _ = jsoniter.MarshalToString(locs)
				}
			case station_constant.MaxCapacity:
				info, err := s.HDStationCacheManager.GetDistanceMaxCache(ctx)
				if err != nil {
					errMsg = err.Error()
				} else {
					cacheVal = strconv.FormatFloat(info, 'f', -1, 64)
				}
			default:
				errMsg = "invalid val type"
			}
		default:
			errMsg = "invalid val cache type"
		}

		if errMsg != "" {
			respList = append(respList, &station.QueryCacheValRespItem{
				CacheKey:  item.CacheKey,
				CacheType: item.CacheType,
				ValType:   item.ValType,
				Retcode:   -1,
				Message:   errMsg,
			})
		} else {
			respList = append(respList, &station.QueryCacheValRespItem{
				CacheKey:  item.CacheKey,
				CacheType: item.CacheType,
				ValType:   item.ValType,
				Value:     cacheVal,
			})
		}
	}

	return respList, nil
}

func (s *StationServiceImpl) GetDistancePartition(ctx utils.LCOSContext, req *station.GetDistancePartitionReq) (*station.GetDistancePartitionRsp, *lcos_error.LCOSError) {
	var partitionId uint64
	var buyerAddrId string
	if req.BuyerAddrId != "" {
		buyerAddrId = req.BuyerAddrId
	} else {
		buyerAddrId = address_library_util.GetAddressId(ctx, address.DivisionZipcodeEntity{Division: address.CreateDivision(req.Address.AddressL1,
			req.Address.AddressL2, "", "", "", 2)}, req.Address.Address)
	}

	partitionId = hd_distance_repo.GetHdBuyerHubDistanceTabPartitionID(buyerAddrId)
	return &station.GetDistancePartitionRsp{
		PartitionId: partitionId,
		BuyerAddrId: buyerAddrId,
	}, nil
}

// SyncAddressRevision 同步接口接受SPX TW地址修正请求
func (s *StationServiceImpl) SyncAddressRevision(ctx utils.LCOSContext, req *pis_protocol.AddressRevisionReq) *lcos_error.LCOSError {
	// 生成task_id 精确到分
	taskId := recorder.Now(ctx).Format("200601021504")
	// 插入address_revision_task_tab
	taskTab := &address_revision_repo.AddressRevisionTaskTab{
		TaskId:     taskId,
		FilePath:   req.FilePath,
		TotalCount: req.TotalCount,
		TaskStatus: constant.RevisionDataInit,
	}
	err := s.AddressRevisionDao.CreateAddressRevisionTask(ctx, req.Region, taskTab)
	if err != nil {
		return err
	}
	// 发送异步任务处理
	msg := schema.AddressRevisionDataInsertMsg{
		TaskId: taskId,
		Region: req.Region,
		LogID:  ctx.GetRequestId(),
	}
	data, _ := jsoniter.Marshal(msg)
	producer, sErr := saturnprovider.GetSaturnProducer(config.GetLCOSSaturn(ctx).DomainName)
	if sErr != nil {
		logger.CtxLogErrorf(ctx, "GetSaturnProducer Failed, err: %v", sErr)
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, sErr.Error())
	}
	sErr = producer.SendMessage(ctx, 1, saturn_constant.AddressRevisionDataInsertTask, data)
	if sErr != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatHomeDeliveryAddressMatch, constant.SendDataInsertTaskFailed, constant.StatusError, sErr.Error())
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, sErr.Error())
	}
	return nil
}
