package scheduled

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"net/url"
	"os"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/goroutine"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/scheduled_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seatalk"
)

type ScheduledService interface {
	// lcos-api功能函数
	// CreateScheduledJob 创建定时任务，isScheduled为false的任务不会被lcos-task自动执行
	CreateScheduledJob(ctx utils.LCOSContext, region string, scheduledModule, scheduledType uint8, fileUrl string, isScheduled bool, scheduledTime uint32, operator string) (*scheduled.Scheduled, *lcos_error.LCOSError)
	// UpdateScheduledJob 更新定时任务，只允许在限制条件内修改scheduled_time或者删除此任务
	UpdateScheduledJob(ctx utils.LCOSContext, req *scheduled_protocol.UpdateScheduledJobRequest) (*scheduled.Scheduled, *lcos_error.LCOSError)
	// ListScheduledJob 查询定时任务列表
	ListScheduledJob(ctx utils.LCOSContext, req *scheduled_protocol.ListScheduledJobRequest, onlyScheduled bool) ([]*scheduled.Scheduled, uint32, *lcos_error.LCOSError)
	// UpdateValidateResult 更新校验结果
	UpdateValidateResult(ctx utils.LCOSContext, job *scheduled.Scheduled, result excel.ParseFileResult) *lcos_error.LCOSError
	// ResetScheduledJobStatus
	ResetScheduledJobStatus(ctx utils.LCOSContext, job *scheduled.Scheduled) *lcos_error.LCOSError

	// lcos-task功能函数
	// ProcessScheduledJob 抢占Scheduled任务，成功则将此任务修改为PROCESSING状态，失败则返回error
	ProcessScheduledJob(ctx utils.LCOSContext, job *scheduled.Scheduled) *lcos_error.LCOSError
	// DoneScheduledJob 声明Scheduled任务执行结束，将任务修改为COMPLETED或者FAILED状态
	DoneScheduledJob(ctx utils.LCOSContext, job *scheduled.Scheduled, result excel.ParseFileResult) *lcos_error.LCOSError
	// GetScheduledJobById
	GetScheduledJobById(ctx utils.LCOSContext, id uint32) (*scheduled.Scheduled, *lcos_error.LCOSError)

	// 工具函数
	// RefreshFileUrl 刷新文件URL，USS限制了URL最长7天后会过期
	RefreshFileUrl(ctx utils.LCOSContext, fileUrl string) string
	// GenerateResultFile 生成并上传结果文件，获取结果文件的URL
	GenerateResultFile(ctx utils.LCOSContext, taskId uint32, fileUrl string, result excel.ParseFileResult) (string, *lcos_error.LCOSError)
	// SeatalkNotify 异步Seatalk通知，内部判断了配置是否开启Seatalk通知
	SeatalkNotify(ctx utils.LCOSContext, message string, ccList []string)
}

var _ ScheduledService = (*scheduledService)(nil)

type scheduledService struct {
	scheduledDao scheduled.ScheduledDao
	s3Service    s3_service.S3Service
}

func NewScheduledService(scheduledDao scheduled.ScheduledDao, s3Service s3_service.S3Service) *scheduledService {
	return &scheduledService{
		scheduledDao: scheduledDao,
		s3Service:    s3Service,
	}
}

func (s *scheduledService) RefreshFileUrl(ctx utils.LCOSContext, fileUrl string) string {
	// 1. 解析文件URL
	fileUrlModel, err := url.Parse(fileUrl)
	if err != nil {
		logger.CtxLogErrorf(ctx, "refresh scheduled job file url error|parse url error|url=%s, message=%s", fileUrl, err.Error())
		return fileUrl
	}
	// 2. 校验URL的host和bucket是否和配置相同，不同则无法刷新
	bucketPrefix := "/" + config.GetConf(ctx).SlsOpsS3Config.BucketKey + "/"
	if fileUrlModel.Host != config.GetConf(ctx).SlsOpsS3Config.Endpoint || !strings.HasPrefix(fileUrlModel.Path, bucketPrefix) {
		logger.CtxLogErrorf(ctx, "refresh scheduled job file url error|host or bucket not match|url=%s, host=%s, bucket=%s", fileUrl, config.GetConf(ctx).SlsOpsS3Config.Endpoint, config.GetConf(ctx).SlsOpsS3Config.BucketKey)
		return fileUrl
	}
	// 3. 获取文件在USS服务器上的路径
	remotePath := strings.TrimPrefix(fileUrlModel.Path, bucketPrefix)
	// 4. 请求重新获取预签名URL
	newFileUrl, lcosErr := s.s3Service.GetFileUrlByFilePathForUss2(ctx, config.GetConf(ctx).SlsOpsS3Config.AccessKeyID, config.GetConf(ctx).SlsOpsS3Config.BucketKey, remotePath, config.GetConf(ctx).SlsOpsS3Config.ExpirationDays) // 最大只有7天
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "refresh scheduled job file url error|get url from uss server error|url=%s, message=%s", fileUrl, lcosErr.Msg)
		return fileUrl
	}
	logger.CtxLogInfof(ctx, "refresh scheduled job file url|origin_file_url=%s, new_file_url=%s", fileUrl, newFileUrl)
	return newFileUrl
}

func (s *scheduledService) GenerateResultFile(ctx utils.LCOSContext, taskId uint32, fileUrl string, result excel.ParseFileResult) (string, *lcos_error.LCOSError) {
	fileUrl = s.RefreshFileUrl(ctx, fileUrl)

	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return "", err
	}
	defer os.Remove(filePath)

	if err := excel.WriteParseFileResult(filePath, "Sheet1", result); err != nil {
		return "", err
	}

	resultFileName := fmt.Sprintf("Scheduled_Upload_Result_TaskID[%d]_%d.xlsx", taskId, utils.GetTimestamp(ctx))

	cfg := config.GetConf(ctx).SlsOpsS3Config
	resultFileUrl, _, _, err := s.s3Service.UploadFileWithFileName(ctx, cfg.AccessKeyID, cfg.BucketKey, filePath, cfg.TimeOut, cfg.ExpirationDays, "serviceable_area_upload_result", resultFileName)
	if err != nil {
		return "", err
	}
	return resultFileUrl, nil
}

func (s *scheduledService) SeatalkNotify(ctx utils.LCOSContext, message string, ccList []string) {
	if config.GetScheduledJobConfig(ctx).SeatalkNotify {
		// 异步推送seatalk消息
		goroutine.Go(func() {
			_ = seatalk.NotifyWithTextMessage(ctx, config.GetScheduledJobConfig(ctx).SeatalkNotifyWebhook, message, ccList, len(ccList) == 0)
		})
	}
}

func (s *scheduledService) checkScheduledTime(ctx context.Context, scheduledTime uint32) *lcos_error.LCOSError {
	nowTime := utils.GetTimestamp(ctx)
	if scheduledTime >= nowTime+config.GetScheduledJobConfig(ctx).GetMinScheduledTimeDelta() && scheduledTime <= nowTime+config.GetScheduledJobConfig(ctx).GetMaxScheduledTimeDelta() {
		return nil
	} else {
		return lcos_error.NewLCOSError(lcos_error.ScheduledJobError, "Scheduled time should be after 5 minutes and within 2 weeks")
	}
}

func (s *scheduledService) CreateScheduledJob(ctx utils.LCOSContext, region string, scheduledModule, scheduledType uint8, fileUrl string, isScheduled bool, scheduledTime uint32, operator string) (*scheduled.Scheduled, *lcos_error.LCOSError) {
	if isScheduled {
		if err := s.checkScheduledTime(ctx, scheduledTime); err != nil {
			return nil, err
		}
	} else {
		scheduledTime = utils.GetTimestamp(ctx)
	}
	job := &scheduled.Scheduled{
		Region:          strings.ToUpper(region),
		FileUrl:         fileUrl,
		ScheduledModule: scheduledModule,
		ScheduledType:   scheduledType,
		ScheduledStatus: scheduled_constant.PENDING, // 初始为pending，校验完成后再更新为scheduled
		IsScheduled:     isScheduled,
		ScheduledTime:   scheduledTime,
		Operator:        operator,
		ValidateStatus:  scheduled_constant.ValidateProcessing,
	}
	if duplicate, err := s.scheduledDao.CreateScheduledJob(ctx, job); err != nil {
		if duplicate {
			return nil, lcos_error.NewLCOSError(lcos_error.ScheduledJobError, "Scheduled time already existed for another task, pls change the time")
		} else {
			return nil, err
		}
	}
	message := fmt.Sprintf(scheduled_constant.CreateJobMessageTemplate,
		job.ID,
		job.Region,
		scheduled_constant.ModuleCode2TextMap[job.ScheduledModule],
		scheduled_constant.TypeCode2TextMap[job.ScheduledType],
		utils.FormatTimestamp(job.ScheduledTime, scheduled_constant.TimeTemplate),
		job.Operator,
		job.FileUrl,
	)
	s.SeatalkNotify(ctx, message, []string{job.Operator})
	return job, nil
}

func (s *scheduledService) checkCanToggleStatus(fromStatus, toStatus uint8) *lcos_error.LCOSError {
	switch fromStatus {
	case scheduled_constant.PENDING:
		if utils.CheckInUint8(toStatus, scheduled_constant.StatusFromPending) {
			return nil
		}
	case scheduled_constant.SCHEDULED:
		if utils.CheckInUint8(toStatus, scheduled_constant.StatusFromScheduled) {
			return nil
		}
	case scheduled_constant.PROCESSING:
		if utils.CheckInUint8(toStatus, scheduled_constant.StatusFromProcessing) {
			return nil
		}
	}
	return lcos_error.NewLCOSError(lcos_error.ScheduledJobError, fmt.Sprintf("Cannot toggle status from %s to %s", scheduled_constant.StatusCode2TextMap[fromStatus], scheduled_constant.StatusCode2TextMap[toStatus]))
}

func (s *scheduledService) UpdateScheduledJob(ctx utils.LCOSContext, req *scheduled_protocol.UpdateScheduledJobRequest) (*scheduled.Scheduled, *lcos_error.LCOSError) {
	if req == nil {
		return nil, nil
	}
	job, err := s.scheduledDao.GetScheduledJobById(ctx, req.Id)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ScheduledJobError, fmt.Sprintf("Get scheduled job version failed|job_id=%d, cause=%s", req.Id, err.Msg))
	}
	nowTime := utils.GetTimestamp(ctx)
	if req.ScheduledStatus != 0 {
		if req.ScheduledStatus != scheduled_constant.DELETED || job.ScheduledTime <= nowTime {
			return nil, lcos_error.NewLCOSError(lcos_error.ScheduledJobError, "You can only delete scheduled job before the scheduled time")
		}
		if err := s.checkCanToggleStatus(job.ScheduledStatus, req.ScheduledStatus); err != nil {
			return nil, err
		}
		job.ScheduledStatus = req.ScheduledStatus
	}
	if req.ScheduledTime != 0 {
		if !job.IsScheduled {
			return nil, lcos_error.NewLCOSError(lcos_error.ScheduledJobError, "Cannot edit scheduled time for instant upload job")
		}
		// check can modify the scheduled time or not
		if job.ScheduledTime < nowTime+config.GetScheduledJobConfig(ctx).GetEditableTimeBeforeScheduled() {
			return nil, lcos_error.NewLCOSError(lcos_error.ScheduledJobError, "You cannot edit scheduled time because this scheduled job is ready to process")
		}
		// check the new scheduled time is valid or not
		if err := s.checkScheduledTime(ctx, req.ScheduledTime); err != nil {
			return nil, err
		}
		job.ScheduledTime = req.ScheduledTime
	}
	duplicate, err := s.scheduledDao.UpdateScheduledJob(ctx, job)
	if duplicate {
		return nil, lcos_error.NewLCOSError(lcos_error.ScheduledJobError, "Scheduled time already existed for another task, pls change the time")
	}
	if err != nil {
		return nil, err
	}
	return job, nil
}

func (s *scheduledService) ResetScheduledJobStatus(ctx utils.LCOSContext, job *scheduled.Scheduled) *lcos_error.LCOSError {
	if job == nil {
		return nil
	}
	job.ValidateStatus = scheduled_constant.ValidateProcessing
	job.ScheduledStatus = scheduled_constant.PENDING
	_, err := s.scheduledDao.UpdateScheduledJob(ctx, job)
	return err
}

func (s *scheduledService) ListScheduledJob(ctx utils.LCOSContext, req *scheduled_protocol.ListScheduledJobRequest, onlyScheduled bool) ([]*scheduled.Scheduled, uint32, *lcos_error.LCOSError) {
	if req == nil {
		return []*scheduled.Scheduled{}, 0, nil
	}
	queryMap := make(map[string]interface{})
	// region + module + type + scheduled_time，四个字段查询放前面以使用索引
	if len(req.Region) != 0 {
		queryMap["region"] = strings.ToUpper(req.Region)
	}
	if req.ScheduledModule != 0 {
		queryMap["scheduled_module"] = req.ScheduledModule
	}
	if req.ScheduledType != 0 {
		queryMap["scheduled_type"] = req.ScheduledType
	}
	if req.ScheduledTimeFrom != 0 {
		queryMap["scheduled_time >="] = req.ScheduledTimeFrom
	}
	if req.ScheduledTimeTo != 0 {
		queryMap["scheduled_time <="] = req.ScheduledTimeTo
	}
	desc := false
	switch req.QueryFlag {
	case scheduled_constant.QueryAll:
		// do nothing
	case scheduled_constant.QueryHistory:
		queryMap["scheduled_status in"] = scheduled_constant.HistoryStatus
		desc = true
	case scheduled_constant.QueryWaiting:
		queryMap["scheduled_status in"] = scheduled_constant.WaitingStatus
	default:
		return nil, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("invalid query flag[%d]", req.QueryFlag))
	}
	if req.ScheduledStartTimeFrom != 0 {
		queryMap["scheduled_start_time >="] = req.ScheduledStartTimeFrom
	}
	if req.ScheduledStartTimeTo != 0 {
		queryMap["scheduled_start_time <="] = req.ScheduledStartTimeTo
	}
	if req.ScheduledEndTimeFrom != 0 {
		queryMap["scheduled_end_time >="] = req.ScheduledEndTimeFrom
	}
	if req.ScheduledEndTimeTo != 0 {
		queryMap["scheduled_end_time <="] = req.ScheduledEndTimeTo
	}
	if req.ScheduledStatus != 0 {
		queryMap["scheduled_status"] = req.ScheduledStatus
	}
	if req.ValidateStatus != 0 {
		queryMap["validate_status"] = req.ValidateStatus
	}
	if onlyScheduled {
		queryMap["is_scheduled"] = true
	}
	return s.scheduledDao.ListScheduledJob(ctx, queryMap, req.PageNo, req.PageSize, desc)
}

func (s *scheduledService) ProcessScheduledJob(ctx utils.LCOSContext, job *scheduled.Scheduled) *lcos_error.LCOSError {
	if job == nil {
		return nil
	}
	originJob := *job
	if err := s.checkCanToggleStatus(job.ScheduledStatus, scheduled_constant.PROCESSING); err != nil {
		return err
	}
	queryMap := map[string]interface{}{
		"region":           job.Region,
		"scheduled_module": job.ScheduledModule,
		"scheduled_type":   job.ScheduledType,
		"scheduled_status": scheduled_constant.PROCESSING,
	}
	_, processing, err := s.scheduledDao.ListScheduledJob(ctx, queryMap, 0, 0, false)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.ScheduledJobError, fmt.Sprintf("Check processing job error|cause=%s", err.Msg))
	}
	if processing != 0 {
		return lcos_error.NewLCOSError(lcos_error.ScheduledJobError, "There is another job in processing under the same module, pls have a check and retry later")
	}
	job.ScheduledStatus = scheduled_constant.PROCESSING
	job.ScheduledStartTime = utils.GetTimestamp(ctx)
	job.LogInfo(job.ScheduledStartTime, job.ScheduledStartTime, fmt.Sprintf("toggle status from %s to %s", scheduled_constant.StatusCode2TextMap[originJob.ScheduledStatus], scheduled_constant.StatusCode2TextMap[job.ScheduledStatus]))
	if _, err := s.scheduledDao.UpdateScheduledJob(ctx, job); err != nil {
		*job = originJob
		return err
	}
	return nil
}

func (s *scheduledService) DoneScheduledJob(ctx utils.LCOSContext, job *scheduled.Scheduled, result excel.ParseFileResult) *lcos_error.LCOSError {
	if job == nil {
		return nil
	}
	originJob := *job

	resultFileUrl, err := s.GenerateResultFile(ctx, job.ID, job.FileUrl, result)
	if err != nil {
		// 异常场景：生成结果文件失败

		// 1. error日志
		logger.CtxLogErrorf(ctx, "generate result file for scheduled job error|task_id=%d, cause=%s, parse_result=%+v", job.ID, err.Msg, result)

		// 2. seatalk通知，将校验结果发送到seatalk
		message := fmt.Sprintf(scheduled_constant.GenerateResultFileErrorMessageTemplate,
			job.ID,
			job.Region,
			err.Msg,
			utils.MarshToStringWithoutError(result),
		)
		s.SeatalkNotify(ctx, message, []string{config.GetScheduledJobConfig(ctx).SeatalkNotifyEmail})

		// 3. 上报到cat
		_ = monitor.AwesomeReportEvent(ctx, constant.CatScheduledUpload, "Generate Result File Error", constant.StatusError, fmt.Sprintf("task_id=%d, cause=%s", job.ID, err.Msg))
	}

	job.AllRowsNum = result.RowsCount
	if result.IsAllRowsError() {
		// 全部失败
		job.FailedRowsNum = result.RowsCount
		job.ValidateStatus = scheduled_constant.ValidateFailed
		job.ScheduledStatus = scheduled_constant.FAILED
	} else if len(result.ParseRowErrMap) != 0 {
		// 部分失败
		job.FailedRowsNum = len(result.ParseRowErrMap)
		job.ValidateStatus = scheduled_constant.ValidatePartialSuccess
		job.ScheduledStatus = scheduled_constant.PARTIALCOMPLETED
	} else {
		job.FailedRowsNum = 0
		job.ValidateStatus = scheduled_constant.ValidateSuccess
		job.ScheduledStatus = scheduled_constant.COMPLETED
	}
	job.ResultFileUrl = resultFileUrl
	job.ScheduledEndTime = utils.GetTimestamp(ctx)
	job.LogInfo(job.ScheduledEndTime, job.ScheduledEndTime, fmt.Sprintf("toggle status from %s to %s", scheduled_constant.StatusCode2TextMap[originJob.ScheduledStatus], scheduled_constant.StatusCode2TextMap[job.ScheduledStatus]))

	if _, err := s.scheduledDao.UpdateScheduledJob(ctx, job); err != nil {
		*job = originJob
		return err
	}
	message := fmt.Sprintf(scheduled_constant.DoneJobMessageTemplate,
		job.ID,
		job.Region,
		scheduled_constant.ModuleCode2TextMap[job.ScheduledModule],
		scheduled_constant.TypeCode2TextMap[job.ScheduledType],
		scheduled_constant.StatusCode2TextMap[job.ScheduledStatus],
		utils.FormatTimestamp(job.ScheduledStartTime, scheduled_constant.TimeTemplate),
		utils.FormatTimestamp(job.ScheduledEndTime, scheduled_constant.TimeTemplate),
		job.Operator,
		s.RefreshFileUrl(ctx, job.FileUrl), // Job Done通知使用临时链接，防止被原链接过期
	)
	s.SeatalkNotify(ctx, message, []string{job.Operator})
	return nil
}

func (s *scheduledService) GetScheduledJobById(ctx utils.LCOSContext, id uint32) (*scheduled.Scheduled, *lcos_error.LCOSError) {
	return s.scheduledDao.GetScheduledJobById(ctx, id)
}

func (s *scheduledService) UpdateValidateResult(ctx utils.LCOSContext, job *scheduled.Scheduled, result excel.ParseFileResult) *lcos_error.LCOSError {
	if job == nil {
		return nil
	}
	originJob := *job

	resultFileUrl, err := s.GenerateResultFile(ctx, job.ID, job.FileUrl, result)
	if err != nil {
		// 异常场景：生成结果文件失败

		// 1. error日志
		logger.CtxLogErrorf(ctx, "generate result file for scheduled job error|task_id=%d, cause=%s, parse_result=%+v", job.ID, err.Msg, result)

		// 2. seatalk通知，将校验结果发送到seatalk
		message := fmt.Sprintf(scheduled_constant.GenerateResultFileErrorMessageTemplate,
			job.ID,
			job.Region,
			err.Msg,
			utils.MarshToStringWithoutError(result),
		)
		s.SeatalkNotify(ctx, message, []string{config.GetScheduledJobConfig(ctx).SeatalkNotifyEmail})

		// 3. 上报到cat
		_ = monitor.AwesomeReportEvent(ctx, constant.CatScheduledUpload, "Generate Result File Error", constant.StatusError, fmt.Sprintf("task_id=%d, cause=%s", job.ID, err.Msg))
	}

	job.AllRowsNum = result.RowsCount

	if result.IsAllRowsError() {
		// 全部失败
		job.FailedRowsNum = result.RowsCount
		job.ValidateStatus = scheduled_constant.ValidateFailed
		job.ScheduledStatus = scheduled_constant.FAILED
	} else if len(result.ParseRowErrMap) != 0 {
		// 部分失败
		job.FailedRowsNum = len(result.ParseRowErrMap)
		job.ValidateStatus = scheduled_constant.ValidatePartialSuccess
		job.ScheduledStatus = scheduled_constant.SCHEDULED
	} else {
		job.ValidateStatus = scheduled_constant.ValidateSuccess
		job.ScheduledStatus = scheduled_constant.SCHEDULED
	}

	job.LogInfo(job.ScheduledStartTime, job.ScheduledStartTime, fmt.Sprintf("toggle status from %s to %s", scheduled_constant.StatusCode2TextMap[originJob.ScheduledStatus], scheduled_constant.StatusCode2TextMap[job.ScheduledStatus]))
	job.ResultFileUrl = resultFileUrl

	if _, err := s.scheduledDao.UpdateScheduledJob(ctx, job); err != nil {
		*job = originJob
		return err
	}
	return nil
}
