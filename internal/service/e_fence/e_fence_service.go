package e_fence

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/geopolygon"
	"git.garena.com/shopee/bg-logistics/logistics/geopolygon/geojson"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/goasync"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcos_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seatalk"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/golang/protobuf/proto"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/saturnprovider"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spx_service"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	commonConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	eFenceConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/e_fence"
	lineToggle "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	locationWhitelist "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	jsoniter "github.com/json-iterator/go"
)

type EFenceServiceInterface interface {
	// 接收电子围栏原始多边形数据
	ReceiveZoneInfo(ctx utils.LCOSContext, request *pb.NotifyZoneInfoRequest) *lcos_error.LCOSError
	RefreshSingleZoneInfo(ctx utils.LCOSContext, request *pb.RefreshZoneInfoRequest) *lcos_error.LCOSError
	// 前端查询多边形信息
	ListZoneInUsageByPage(ctx utils.LCOSContext, region string, zoneId, layerId string, zoneName string, page uint32, count uint32) ([]*polygon.EFencePolygonTab, uint32, *lcos_error.LCOSError)
	ListZoneInUsage(ctx utils.LCOSContext, region string, zoneId string, zoneName string) ([]*polygon.EFencePolygonTab, *lcos_error.LCOSError)
	ExportZoneFile(ctx utils.LCOSContext, region string) (*excelize.File, *lcos_error.LCOSError)
	ListLocationWhiteListByPage(ctx utils.LCOSContext, req *protocol.ListLocationWhitelistReq) (*protocol.ListLocationWhitelistResp, *lcos_error.LCOSError)
	ListAllLocationWhiteList(ctx utils.LCOSContext, queryMap map[string]interface{}) ([]*locationWhitelist.EFenceLocationWhitelistTab, *lcos_error.LCOSError)
	DeleteLocationWhitelistById(ctx utils.LCOSContext, locationId int64, layerId string) *lcos_error.LCOSError
	UploadLocationWhiteList(ctx utils.LCOSContext, fileUrl string) *lcos_error.LCOSError
	ListLineToggle(ctx utils.LCOSContext, req *protocol.GetLineToggleListReq) (*protocol.GetLineToggleListResp, *lcos_error.LCOSError)
	UpdateLineToggle(ctx utils.LCOSContext, req *protocol.UpdateLineToggleReq) *lcos_error.LCOSError
	DeleteLineToggle(ctx utils.LCOSContext, req *protocol.DeleteLineToggleReq) *lcos_error.LCOSError
	GetLineToggleLayer(ctx utils.LCOSContext, req *protocol.GetLineToggleLayerReq) ([]*protocol.GetLineToggleLayerResp, *lcos_error.LCOSError)
	GetLayerNameByLayerId(ctx utils.LCOSContext, layerId string) (string, *lcos_error.LCOSError)

	HandleZoneInfoNotification(ctx utils.LCOSContext, region string, notifyMode int32, newVersionList, oldVersionList []schema.VersionItem, newZoneList []*schema.EFenceZoneInfo) *lcos_error.LCOSError
	CleanExpireMeshData(ctx utils.LCOSContext, region string, deleteLimit, zoneLimit int) *lcos_error.LCOSError
	ReportUnhandledPolygon(ctx utils.LCOSContext, region string) *lcos_error.LCOSError
	ParsePolygonAndMesh2GeoJson(ctx utils.LCOSContext, region, zoneId, version, layerId string, includeMesh bool, includeGeoHash []string) (string, *lcos_error.LCOSError)
	RegenerateMesh(ctx utils.LCOSContext, region, zoneId, version, layerId string, pipMode int, refreshCache bool) *lcos_error.LCOSError
}

type EFenceService struct {
	polygonDao           polygon.EFencePolygonDao
	meshDao              mesh.EFenceMeshDao
	locationWhiteListDao locationWhitelist.EFenceLocationWhitelistDao
	lineToggleDao        lineToggle.EFenceLineToggleDao
}

func NewEFenceService(polygonDao polygon.EFencePolygonDao, meshDao mesh.EFenceMeshDao, locationWhiteListDao locationWhitelist.EFenceLocationWhitelistDao, lineToggleDao lineToggle.EFenceLineToggleDao) *EFenceService {
	return &EFenceService{
		polygonDao:           polygonDao,
		meshDao:              meshDao,
		locationWhiteListDao: locationWhiteListDao,
		lineToggleDao:        lineToggleDao,
	}
}

func (e *EFenceService) ReceiveZoneInfo(ctx utils.LCOSContext, request *pb.NotifyZoneInfoRequest) *lcos_error.LCOSError {
	region := request.GetReqHeader().GetDeployRegion()

	// layer id 上线兼容
	for _, zone := range request.GetZoneList() {
		zone.LayerId = proto.String(GetRealLayerId(region, zone.GetLayerId()))
	}

	// 校验参数，生效和失效不能是同一个版本
	isValid, msg := e.validateParam(ctx, request)
	if !isValid {
		logger.CtxLogErrorf(ctx, msg)
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, msg)
	}

	notifyMode := request.GetMode()
	newPolygonList := make([]*polygon.EFencePolygonTab, 0, len(request.GetZoneList()))

	// 纯粹的刷新缓存命令，则更新缓存版本后直接返回
	if request.GetForceHandle() == eFenceConstant.ForceHandleRefreshCache {
		_, err := localcache.IncrLocalCacheVersion(ctx, commonConstant.EFencePolygonNamespace, commonConstant.EFenceMeshNamespace)
		if err != nil {
			return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
		}
		return nil
	}

	// 过滤掉已有的数据，并区分出有新变更的zone和需要过期的version
	expireVersionList, toPullZoneList, newVersionList := e.filterInvalidZone(ctx, request.GetZoneList(), region)

	// 如果过滤之后没有要处理的数据，直接成功
	if len(toPullZoneList) == 0 && len(newVersionList) == 0 && len(expireVersionList) == 0 {
		logger.CtxLogInfof(ctx, "no data to handle, success")
		return nil
	}

	// 处理请求，handleReq 根据 ForceHandle来决定是在协程中执行还是直接执行
	handleReq := func() {

		// 异步走到这里，context可能会cancel，因此在这里单独生成一份ctx
		asyncCtx := genAsyncCtx(ctx)

		var realZoneList []*pb.ZoneInfo
		var lcosErr *lcos_error.LCOSError

		defer func() {
			if lcosErr != nil {
				_ = monitor.ReportEvent(commonConstant.CatModuleEFencePreHandle, commonConstant.EventNameEfencePreHandleRecallSpx, commonConstant.StatusError, lcosErr.Msg)
			} else {
				_ = monitor.ReportEvent(commonConstant.CatModuleEFencePreHandle, commonConstant.EventNameEfencePreHandleRecallSpx, commonConstant.StatusSuccess, "")
			}
		}()

		// SPX的geometry内容太大了无法一次性发送过来，request.GetZoneList() 只是列表，需要我们每n个向SPX发起一次拉取，最终查到完整的数据，便于一次性处理
		if request.GetForceHandle() == eFenceConstant.ForceHandleDefault {
			realZoneList, lcosErr = e.requestDetailZoneInfo(asyncCtx, region, toPullZoneList)
			if lcosErr != nil {
				logger.CtxLogErrorf(asyncCtx, "query zone detail fail, error:%s", lcosErr.Msg)
				return
			}
		} else { // 如果请求接口时使用了其他 ForceHandle 的值，则跳过向SPX拉取，直接使用入参的值
			realZoneList = toPullZoneList
		}

		// 构造落库结构
		for _, zone := range realZoneList {

			layerId := GetRealLayerId(region, zone.GetLayerId())
			polyTab := polygon.EFencePolygonTab{
				Region:       region,
				ZoneId:       zone.GetZoneId(),
				ZoneName:     zone.GetZoneName(),
				StationId:    zone.GetStationId(),
				StationName:  zone.GetStationName(),
				StationType:  zone.GetStationType(),
				Geometry:     zone.GetGeometry(),
				ZoneArea:     strconv.FormatFloat(zone.GetZoneArea(), 'f', -1, 64), // 转成string来存储，避免丢失精度，没有逻辑，只用于数据量分析
				Operator:     zone.GetOperator(),
				DataVersion:  strconv.Itoa(int(zone.GetVersionId())),
				HandleStatus: eFenceConstant.ZoneHandleStatusInit,

				LayerId:   layerId,
				LayerName: GetRealLayerName(region, zone.GetLayerName(), zone.GetLayerName()),
			}

			newPolygonList = append(newPolygonList, &polyTab) // 这是准备要写入的多边形数据
		}

		// 将剩下的数据落库。落库的时候handleStatus=init
		if len(newPolygonList) > 0 {
			lcosErr = e.polygonDao.BatchInsertOrUpdateEFencePolygon(asyncCtx, region, newPolygonList)
			if lcosErr != nil {
				logger.CtxLogErrorf(asyncCtx, "Error inserting polygon, error:%s", lcosErr.Msg)
				return
			}
		}

		//debug
		//if request.GetForceHandle() != eFenceConstant.ForceHandleDefault {
		//	newZoneList := make([]*schema.EFenceZoneInfo, len(toPullZoneList))
		//	for i, zone := range toPullZoneList {
		//		newZoneList[i] = &schema.EFenceZoneInfo{
		//			ZoneId:      zone.GetZoneId(),
		//			DataVersion: strconv.Itoa(int(zone.GetVersionId())),
		//			LayerId:     zone.GetLayerId(),
		//		}
		//	}
		//	err := e.HandleZoneInfoNotification(ctx, region, notifyMode, newVersionList, expireVersionList, newZoneList)
		//	if err != nil {
		//		return
		//	}
		//	return
		//}

		// 发起异步任务，新旧版本号都要透传过去，当任务执行完成后，同时生效新的版本并过期旧的版本
		// Geometry太大了不能直接传入任务，传 newVersionList 和 expireVersionList ，异步任务中按照 newVersionList 取出所所有zone，对Geometry做处理
		err := e.sendEFenceGenerateMeshTask(asyncCtx, region, notifyMode, toPullZoneList, newVersionList, expireVersionList)
		if err != nil {
			return
		}

	}

	// 如果是默认的模式，需要向SPX分批拉取每个zone的详细顶点信息，请求次数太多有可能会导致SPX超时，因此需要异步拉取并处理
	if request.GetForceHandle() == eFenceConstant.ForceHandleDefault {
		goasync.GoAndRecover(handleReq)
	} else { // 如果请求接口时使用了其他 ForceHandle 的值，说明是用工具脚本推送zone信息，则跳过向SPX拉取，直接使用入参的值，就可以不用异步处理，直接执行
		handleReq()
	}

	return nil
}

func genAsyncCtx(ctx utils.LCOSContext) *utils.CommonContext {
	reqId := utils.GetRequestId(ctx)
	tempCtx := context.Background()
	tempCtx = context.WithValue(tempCtx, commonConstant.RequestIdKey, reqId)
	asyncCtx := utils.NewCommonCtx(tempCtx)
	return asyncCtx
}

func (e *EFenceService) validateParam(ctx utils.LCOSContext, request *pb.NotifyZoneInfoRequest) (bool, string) {
	// 先把所有涉及的version拿出来
	newVersionList := make([]string, 0)
	expireVersionList := make([]string, 0)

	for _, zone := range request.GetZoneList() {
		version := strconv.Itoa(int(zone.GetVersionId()))
		// 根据请求中的不同status，决定如何处理
		if zone.GetZoneStatus() == eFenceConstant.ZoneStatusDisable {
			expireVersionList = append(expireVersionList, version)
		} else { // 对于新版本，只处理不存在的数据
			newVersionList = append(newVersionList, version)
		}
	}

	// 校验生效和失效不能是同一个版本
	if findDuplicatedVersion(expireVersionList, newVersionList) {
		errMsg := "can not Enable and Disable a version at the same time"
		return false, errMsg
	}

	return true, ""
}

func (e *EFenceService) filterInvalidZone(ctx utils.LCOSContext, reqZoneList []*pb.ZoneInfo, region string) ([]schema.VersionItem, []*pb.ZoneInfo, []schema.VersionItem) {
	toPullZoneList := make([]*pb.ZoneInfo, 0) // 由于SPX的数据报文太大，一次推送不完，因此需要我们拿着zone_id+version 去多次拉取回来，再一次性处理
	newVersionList := make([]schema.VersionItem, 0)
	expireVersionList := make([]schema.VersionItem, 0)

	for _, zone := range reqZoneList {
		// 根据请求中的不同status，决定如何处理
		// 对于旧版本，只处理存在的数据
		if zone.GetZoneStatus() == eFenceConstant.ZoneStatusDisable {
			// 先查询是否有指定版本的数据
			version := strconv.Itoa(int(zone.GetVersionId()))

			zoneQuery := make([]*schema.VersionItem, 1)
			zoneQuery = append(zoneQuery, &schema.VersionItem{DataVersion: version})
			zoneListInDB, err := e.polygonDao.GetEFencePolygonsByVersionAndLayer(ctx, region, zoneQuery)

			// 不存在的数据直接跳过
			if err != nil || len(zoneListInDB) == 0 {
				logger.CtxLogInfof(ctx, "ZoneStatusDisable: version data doesn't exist, skip|version[%d]", zone.GetVersionId())
				continue
			}

			expireVersionList = recordDataVersion(expireVersionList, version, zone.GetLayerId(), zone.GetOperator()) // 这是一切处理完成之后，要作废掉的旧版本号
		} else { // 对于新版本，只处理不存在的数据
			// 先尝试查数据是否存在
			zoneInDB, err := e.polygonDao.GetOneEFencePolygonByZoneIdLayerIdAndVersion(ctx, region, zone.GetZoneId(), strconv.Itoa(int(zone.GetVersionId())), zone.GetLayerId())
			// 已经存在的数据，要根据status决定是否跳过
			if err == nil && zoneInDB != nil {
				// 如果已有记录，且已经标记为正在使用，跳过处理这条
				if zoneInDB.HandleStatus == eFenceConstant.ZoneHandleStatusInUsage {
					logger.CtxLogInfof(ctx, "ZoneStatusActive: zone data exists and is in usage, skip|zoneId[%s]|version[%d]|layerId[%s]", zone.GetZoneId(), zone.GetVersionId(), zone.GetLayerId())
					continue
				} else if zoneInDB.MTime >= utils.GetTimestamp(ctx)-eFenceConstant.ZoneHandleTimeMax { // 如果已有记录，并且上次发起处理的时间还没超过40分钟，跳过处理这条
					logger.CtxLogInfof(ctx, "ZoneStatusActive: zone data exists and started to process at %d, skip|zoneId[%s]|version[%d]|layerId[%s]", zoneInDB.MTime, zone.GetZoneId(), zone.GetVersionId(), zone.GetLayerId())
					continue
				}

			}

			toPullZoneList = append(toPullZoneList, zone)
			newVersionList = recordDataVersion(newVersionList, strconv.Itoa(int(zone.GetVersionId())), zone.GetLayerId(), zone.GetOperator()) // 这是准备要处理的多边形涉及的版本号
		}
	}

	return expireVersionList, toPullZoneList, newVersionList
}

func (e *EFenceService) ListZoneInUsageByPage(ctx utils.LCOSContext, region string, zoneId string, zoneName string, layerId string, page uint32, count uint32) ([]*polygon.EFencePolygonTab, uint32, *lcos_error.LCOSError) {
	queryMap := make(map[string]interface{})

	if zoneId != "" {
		queryMap["zone_id"] = zoneId
	}
	if zoneName != "" {
		queryMap["zone_name"] = zoneName
	}

	if layerId != "" {
		queryMap["layer_id"] = layerId
	}

	queryMap["handle_status"] = eFenceConstant.ZoneHandleStatusInUsage

	return e.polygonDao.ListEFencePolygonByPage(ctx, region, queryMap, page, count)
}

func (e *EFenceService) ListZoneInUsage(ctx utils.LCOSContext, region string, zoneId string, zoneName string) ([]*polygon.EFencePolygonTab, *lcos_error.LCOSError) {
	queryMap := make(map[string]interface{})

	if zoneId != "" {
		queryMap["zone_id"] = zoneId
	}
	if zoneName != "" {
		queryMap["zone_name"] = zoneName
	}

	queryMap["handle_status"] = eFenceConstant.ZoneHandleStatusInUsage

	res, err := e.polygonDao.ListAllEFencePolygon(ctx, region, queryMap)
	return res, err
}

func (e *EFenceService) ExportZoneFile(ctx utils.LCOSContext, region string) (*excelize.File, *lcos_error.LCOSError) {
	zoneInfos, lcosErr := e.ListZoneInUsage(ctx, region, "", "")
	if lcosErr != nil || len(zoneInfos) <= 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find valid hub zone")
	}

	templateFileName := path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/e_fence", eFenceConstant.HubZoneDataFileName)
	resultFile, err := excelize.OpenFile(templateFileName)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file: %s|error=%s", templateFileName, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}

	// 开始写入数据
	lineNum := 2 // 第一行是固定的表头，从第二行开始写
	for _, zone := range zoneInfos {
		var errorList []error
		errorList = append(errorList,
			resultFile.SetCellValue("Sheet1", fmt.Sprintf("A%d", lineNum), fmt.Sprintf("%s - %s", zone.LayerId, zone.LayerName)),
			resultFile.SetCellValue("Sheet1", fmt.Sprintf("B%d", lineNum), region),
			resultFile.SetCellValue("Sheet1", fmt.Sprintf("C%d", lineNum), zone.ZoneId),
			resultFile.SetCellValue("Sheet1", fmt.Sprintf("D%d", lineNum), zone.ZoneName),
			resultFile.SetCellValue("Sheet1", fmt.Sprintf("E%d", lineNum), zone.StationId),
			resultFile.SetCellValue("Sheet1", fmt.Sprintf("F%d", lineNum), zone.StationName),
			resultFile.SetCellValue("Sheet1", fmt.Sprintf("G%d", lineNum), zone.Operator),
			resultFile.SetCellValue("Sheet1", fmt.Sprintf("H%d", lineNum), zone.MTime),
		)

		for _, singleError := range errorList {
			if singleError != nil {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot export LM Hub Zone|region[%s]|zone_id[%s]|line:%d", region, zone.ZoneId, lineNum)
			}
		}
		lineNum++
	}

	return resultFile, nil
}

func (e *EFenceService) HandleZoneInfoNotification(ctx utils.LCOSContext, region string, notifyMode int32, newVersionList, oldVersionList []schema.VersionItem, newZoneList []*schema.EFenceZoneInfo) *lcos_error.LCOSError {
	// 根据不同的mode，用不同的条件从Polygon表查询详细的多边形信息
	polygonList, lcosErr := e.getPolygonList(ctx, region, notifyMode, newVersionList, newZoneList)
	if lcosErr != nil {
		// 日志，上报
		logger.CtxLogErrorf(ctx, "get polygon list failed: %s", lcosErr.Msg)
		_ = monitor.ReportEvent(commonConstant.CatModuleEFencePreHandle, commonConstant.EventNameEFencePreHandlePolygon, commonConstant.StatusError, lcosErr.Msg)
		return lcosErr
	}

	// 如果polygonList为空，跳过网格化和 activateNewAndExpireOldPolygon，直接做 expirePolygon
	if len(polygonList) == 0 {
		logger.CtxLogInfof(ctx, "no new polygon list, only expire polygons|region[%s]|notifyMode[%d]|newVersionList[%v]|oldVersionList[%v]|newZoneList[%+v]", region, notifyMode, newVersionList, oldVersionList, newZoneList)
		_ = monitor.ReportEvent(commonConstant.CatModuleEFencePreHandle, commonConstant.EventNameEFencePreHandleOnlyExpire, commonConstant.StatusSuccess, "")
		return e.expirePolygon(ctx, region, oldVersionList)
	}

	// 调用SDK做网格化，并将mesh结果落库，此时这些mesh还不会被读取到
	lcosErr = e.generateAndSaveMesh(ctx, region, polygonList)
	if lcosErr != nil {
		// 日志，上报
		logger.CtxLogErrorf(ctx, "generateAndSaveMesh failed: %s", lcosErr.Msg)
		_ = monitor.ReportEvent(commonConstant.CatModuleEFencePreHandle, commonConstant.EventNameEFencePreHandleMesh, commonConstant.StatusError, lcosErr.Msg)
		return lcosErr
	}

	// 同时根据新版本和旧版本列表，修改zone表数据的status
	lcosErr = e.activateNewAndExpireOldPolygon(ctx, region, notifyMode, newVersionList, oldVersionList, newZoneList)
	if lcosErr != nil {
		// 日志，上报
		logger.CtxLogErrorf(ctx, "activateNewAndExpireOldPolygon failed: %s", lcosErr.Msg)
		_ = monitor.ReportEvent(commonConstant.CatModuleEFencePreHandle, commonConstant.EventNameEFencePreHandleActivate, commonConstant.StatusError, lcosErr.Msg)
		return lcosErr
	}

	// region维度决定是否双写，双写失败不阻塞流程，通过告警发现，人工介入
	if config.GetMutableConf(ctx).EFenceConfig.ForwardToMeshTabV1[strings.ToLower(region)] {
		_ = e.forward2MeshTabV1(ctx, region, polygonList)
	}

	_ = monitor.ReportEvent(commonConstant.CatModuleEFencePreHandle, commonConstant.EventNameEFenceSuccess, commonConstant.StatusSuccess, "")
	logger.CtxLogInfof(ctx, "HandleZoneInfoNotification success|region[%s]|notifyMode[%d]|newVersionList[%v]|oldVersionList[%v]|newZoneList[%+v]", region, notifyMode, newVersionList, oldVersionList, newZoneList)
	return nil

}

func (e *EFenceService) getPolygonList(ctx utils.LCOSContext, region string, notifyMode int32, newVersionList []schema.VersionItem, newZoneList []*schema.EFenceZoneInfo) ([]*polygon.EFencePolygonTab, *lcos_error.LCOSError) {
	var polygonsList []*polygon.EFencePolygonTab
	if len(newVersionList) == 0 && len(newZoneList) == 0 {
		logger.CtxLogInfof(ctx, "no new polygon data")
		return polygonsList, nil
	}

	var lcosErr *lcos_error.LCOSError
	// 如果是增量推送，则使用 newVersionList 去查zone详情，整个version里面的所有zone都是要处理的
	if notifyMode == eFenceConstant.NotifyModeIncrement {
		zoneQuery := schema.TransferVersionItemList(newVersionList)
		polygonsList, lcosErr = e.polygonDao.GetEFencePolygonsByVersionAndLayer(ctx, region, zoneQuery)
	} else { // 如果是全量推送，则使用 newZoneList 去查zone详情，只有指定的zone才是要处理的
		polygonsList, lcosErr = e.polygonDao.GetEFencePolygonsByZoneIdLayerIdAndVersion(ctx, region, newZoneList)
	}

	return polygonsList, lcosErr
}

func (e *EFenceService) constructMeshListForStorage(ctx utils.LCOSContext, poly *polygon.EFencePolygonTab, polyMeshList []*geopolygon.PolygonMeshResult) ([]*mesh.EFenceMeshTab, *lcos_error.LCOSError) {
	storageList := make([]*mesh.EFenceMeshTab, len(polyMeshList))
	for i, polyMesh := range polyMeshList {
		storageList[i] = &mesh.EFenceMeshTab{
			Region:       poly.Region,
			ZoneId:       poly.ZoneId,
			GeoHashCode:  polyMesh.GeoHash,
			FullCoverage: polyMesh.FullCoverage,
			DataVersion:  poly.DataVersion,
			CTime:        utils.GetTimestamp(ctx),

			LayerId: poly.LayerId,
		}
	}

	return storageList, nil
}

// 同时生效新版本并过期旧版本
func (e *EFenceService) activateNewAndExpireOldPolygon(ctx utils.LCOSContext, region string, notifyMode int32, newVersionList, oldVersionList []schema.VersionItem, newZoneList []*schema.EFenceZoneInfo) *lcos_error.LCOSError {
	logger.CtxLogInfof(ctx, "ready to activate polygon, newVersionList[%+v]|oldVersionList[%+v]|mesh size[%d]", newVersionList, oldVersionList)

	// 同一批次更新，理论上如果zone id需要更新新的增量版本，spx在同一批次中会把旧版本也作为expired推送上来，否则会导致新旧版本同时有效
	// 新版本和旧版本要同时切换
	fc := func() *lcos_error.LCOSError {
		// 增量模式下更新整个版本的数据，全量模式则精确更新某个zone
		if notifyMode == eFenceConstant.NotifyModeIncrement {
			// 事务中：newVersionList对应的所有zone全部更新为 ZoneHandleStatusInUsage
			for _, v := range newVersionList {
				lcosErr := e.polygonDao.UpdateEFencePolygonsByVersionAndLayerId(ctx, region, v.DataVersion, eFenceConstant.ZoneHandleStatusInUsage, v.Operator)
				if lcosErr != nil {
					logger.CtxLogErrorf(ctx, "NotifyModeIncrement: activate polygon failed")
					return lcosErr
				}
			}
		} else {
			// 事务中：newZoneList对应的所有zone全部更新为 ZoneHandleStatusInUsage
			for _, v := range newZoneList {
				lcosErr := e.polygonDao.UpdatePolygonStatus(ctx, region, v.ZoneId, v.DataVersion, v.LayerId, eFenceConstant.ZoneHandleStatusInUsage)
				if lcosErr != nil {
					logger.CtxLogErrorf(ctx, "NotifyModeFull: activate polygon failed")
					return lcosErr
				}
			}
		}

		// 事务中：oldVersionList对应的所有zone全部更新为 ZoneHandleStatusExpired
		for _, v := range oldVersionList {
			lcosErr := e.polygonDao.UpdateEFencePolygonsByVersionAndLayerId(ctx, region, v.DataVersion, eFenceConstant.ZoneHandleStatusExpired, v.Operator)
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "expire polygon failed")
				return lcosErr
			}
		}
		return nil
	}

	if err := ctx.Transaction(fc); err != nil {
		logger.CtxLogErrorf(ctx, "activateNewAndExpireOldPolygon failed|err[%s]", err.Msg)
		return err
	}

	// 显式刷新缓存版本，因为缓存名称与表名不一样
	_, err := localcache.IncrLocalCacheVersion(ctx, commonConstant.EFencePolygonNamespace, commonConstant.EFenceMeshNamespace)
	if err != nil {
		logger.CtxLogErrorf(ctx, "refresh cache failed|err[%s]", err.Error())
		_ = monitor.ReportEvent(commonConstant.CatModuleEFencePreHandle, commonConstant.EventNameEFenceCacheIncVersion, commonConstant.StatusError, err.Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Error()) // 缓存刷新失败需要上报，但暂时不做全任务的重试
	}

	logger.CtxLogInfof(ctx, "activate success")
	return nil
}

func (e *EFenceService) expirePolygon(ctx utils.LCOSContext, region string, oldVersionList []schema.VersionItem) *lcos_error.LCOSError {
	fc := func() *lcos_error.LCOSError {
		// 事务中：oldVersionList对应的所有zone全部更新为 ZoneHandleStatusExpired
		for _, v := range oldVersionList {
			err := e.polygonDao.UpdateEFencePolygonsByVersionAndLayerId(ctx, region, v.DataVersion, eFenceConstant.ZoneHandleStatusExpired, v.Operator)
			if err != nil {
				return err
			}
		}
		return nil
	}

	if err := ctx.Transaction(fc); err != nil {
		logger.CtxLogErrorf(ctx, "expirePolygon failed|err[%s]", err.Msg)
		return err
	}

	// 显式刷新缓存版本，因为缓存名称与表名不一样
	_, _ = localcache.IncrLocalCacheVersion(ctx, commonConstant.EFencePolygonNamespace)
	logger.CtxLogInfof(ctx, "expirePolygon success, oldVersionList[%+v]", oldVersionList)

	return nil
}

func (e *EFenceService) ListLocationWhiteListByPage(ctx utils.LCOSContext, req *protocol.ListLocationWhitelistReq) (*protocol.ListLocationWhitelistResp, *lcos_error.LCOSError) {
	queryMap := make(map[string]interface{})
	if req.LocationId != nil {
		queryMap["location_id"] = *req.LocationId
	}
	if req.State != nil {
		queryMap["state"] = *req.State
	}
	if req.City != nil {
		queryMap["city"] = *req.City
	}
	if req.District != nil {
		queryMap["district"] = *req.District
	}
	if req.Street != nil {
		queryMap["street"] = *req.Street
	}

	if req.LayerId != nil {
		queryMap["layer_id"] = *req.LayerId
	}

	region := strings.ToUpper(ctx.GetCountry())

	queryMap["region"] = region
	whitelistList, total, err := e.locationWhiteListDao.ListLocationWhitelistByPage(ctx, req.PageNo, req.Count, queryMap)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get location whitelist fail|err[%s]", err.Msg)
		return nil, err
	}

	var dataList []*protocol.LocationInfo
	for _, whitelist := range whitelistList {
		layerName := eFenceConstant.InActiveLayerName
		zone, err := e.polygonDao.GetLayerNameByLayerId(ctx, region, whitelist.LayerId)
		if err != nil {
			return nil, err
		}

		if zone != nil {
			layerName = zone.LayerName
		}

		data := &protocol.LocationInfo{
			LocationId:     whitelist.LocationId,
			State:          whitelist.State,
			City:           whitelist.City,
			District:       whitelist.District,
			Street:         whitelist.Street,
			LastUpdateTime: whitelist.Mtime,
			Operator:       whitelist.Operator,

			LayerId:   whitelist.LayerId,
			LayerName: layerName,
		}
		dataList = append(dataList, data)
	}
	return &protocol.ListLocationWhitelistResp{Total: total, List: dataList}, nil
}

func (e *EFenceService) DeleteLocationWhitelistById(ctx utils.LCOSContext, locationId int64, layerId string) *lcos_error.LCOSError {
	err := e.locationWhiteListDao.DeleteByLocationId(ctx, &locationWhitelist.WhitelistUniqKey{LocationId: int(locationId), LayerId: layerId})
	if err != nil {
		logger.CtxLogErrorf(ctx, "delete whitelist by location id fail|err[%s]", err.Msg)
		return err
	}
	return nil
}

func (e *EFenceService) ListAllLocationWhiteList(ctx utils.LCOSContext, queryMap map[string]interface{}) ([]*locationWhitelist.EFenceLocationWhitelistTab, *lcos_error.LCOSError) {
	list, err := e.locationWhiteListDao.QueryLocationWhitelist(ctx, queryMap)
	if err != nil {
		logger.CtxLogErrorf(ctx, "query whitelist list fail|err[%s]", err.Msg)
		return nil, err
	}
	return list, nil
}

func (e *EFenceService) UploadLocationWhiteList(ctx utils.LCOSContext, fileUrl string) *lcos_error.LCOSError {
	// 下载文件
	dataList, err := e.downloadAndCheckWhiteListFile(ctx, fileUrl)
	if err != nil {
		logger.CtxLogErrorf(ctx, "parse file fail|err[%s]", err.Msg)
		return err
	}

	// 数据处理
	addList, deleteList, errList := e.processUploadWhitelistList(ctx, dataList)
	if len(errList) != 0 {
		logger.CtxLogErrorf(ctx, "error in data preparation phase")
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}

	// 以事务的方式更新
	fc := func() *lcos_error.LCOSError {
		for _, whitelistUniqKey := range deleteList {
			err := e.locationWhiteListDao.DeleteByLocationId(ctx, whitelistUniqKey)
			if err != nil {
				return err
			}
		}
		for _, location := range addList {
			err := e.locationWhiteListDao.InsertOrUpdateWhitelist(ctx, location)
			if err != nil {
				return err
			}
		}
		return nil
	}

	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}

func (e *EFenceService) processUploadWhitelistList(ctx utils.LCOSContext, dataList []*EFenceWhitelistUpload) ([]*locationWhitelist.EFenceLocationWhitelistTab, []*locationWhitelist.WhitelistUniqKey, []*lcos_error.LCOSError) {
	var errList []*lcos_error.LCOSError
	var addWhitelist []*locationWhitelist.EFenceLocationWhitelistTab
	var deleteWhitelist []*locationWhitelist.WhitelistUniqKey

	for index, data := range dataList {
		// 通过四级地址查到Location id
		locationInfo, err := ops_service.GetLocationInfoByLocationName(ctx, getRequestLocationParam(data))
		if err != nil {
			msg := fmt.Sprintf("query location info fail|line[%d]|err[%s]", index+2, err.Msg)
			logger.CtxLogErrorf(ctx, msg)
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, msg))
			continue
		}
		region := strings.ToUpper(ctx.GetCountry())

		zone, err := e.polygonDao.GetLayerNameByLayerId(ctx, region, data.LayerId)
		if err != nil {
			msg := fmt.Sprintf("query layer name fail|line[%d]|err[%s]", index+2, err.Msg)
			logger.CtxLogErrorf(ctx, msg)
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, msg))
			continue
		}

		if zone == nil {
			msg := fmt.Sprintf("query layer name fail|line[%d]|err[%s]", index+2, "no such active layer id")
			logger.CtxLogErrorf(ctx, msg)
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, msg))
			continue
		}

		switch data.Action {
		case eFenceConstant.WhitelistAdd:
			whitelist := &locationWhitelist.EFenceLocationWhitelistTab{
				LocationId: int64(locationInfo.LocationId),
				Operator:   ctx.GetUserEmail(),
				State:      data.State,
				City:       data.City,
				District:   data.District,
				Street:     data.Street,
				Region:     region,
				Ctime:      recorder.Now(ctx).Unix(),
				Mtime:      recorder.Now(ctx).Unix(),

				LayerId: data.LayerId,
			}
			addWhitelist = append(addWhitelist, whitelist)
		case eFenceConstant.WhitelistDelete:
			deleteWhitelist = append(deleteWhitelist, &locationWhitelist.WhitelistUniqKey{LocationId: locationInfo.LocationId, LayerId: data.LayerId})
		}
	}
	return addWhitelist, deleteWhitelist, errList
}

func getRequestLocationParam(data *EFenceWhitelistUpload) *ops_service.GetLocationInfoByNameRequest {
	var locationNameList []string
	if data.State != "" {
		locationNameList = append(locationNameList, data.State)
	}
	if data.City != "" {
		locationNameList = append(locationNameList, data.City)
	}
	if data.District != "" {
		locationNameList = append(locationNameList, data.District)
	}
	if data.Street != "" {
		locationNameList = append(locationNameList, data.Street)
	}
	resp := &ops_service.GetLocationInfoByNameRequest{
		Country:      data.Country,
		LocationName: locationNameList,
	}
	return resp
}

func (e *EFenceService) downloadAndCheckWhiteListFile(ctx utils.LCOSContext, fileUrl string) ([]*EFenceWhitelistUpload, *lcos_error.LCOSError) {
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return nil, err
	}
	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	lineNum := 0
	var dataList []*EFenceWhitelistUpload
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 表头校验
		if lineNum == 1 {
			if !compareExcelTitle(row, eFenceConstant.EFenceWhitelistUploadFileTitle) {
				return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Please upload the latest template file")
			}
			continue
		}
		if serviceable_util.IsBlankRow(row) {
			continue
		}
		if len(row) < 6 {
			return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("please upload the correct file format|line[%d]", lineNum))
		}
		action, err := strconv.Atoi(strings.TrimSpace(row[6]))
		if err != nil || (action != 0 && action != -1) {
			return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("action can only be 0 or -1，0 for add,-1 for delete|line[%d]", lineNum))
		}
		if len(row[0]) == 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("country must be uploaded|line[%d]", lineNum))
		}
		data := &EFenceWhitelistUpload{
			Country:  strings.TrimSpace(row[0]),
			LayerId:  strings.TrimSpace(row[1]),
			State:    strings.TrimSpace(row[2]),
			City:     strings.TrimSpace(row[3]),
			District: strings.TrimSpace(row[4]),
			Street:   strings.TrimSpace(row[5]),
			Action:   action,
		}
		dataList = append(dataList, data)
	}
	return dataList, nil
}

func (e *EFenceService) ListLineToggle(ctx utils.LCOSContext, req *protocol.GetLineToggleListReq) (*protocol.GetLineToggleListResp, *lcos_error.LCOSError) {
	queryMap := make(map[string]interface{})
	if req.LineId != "" {
		queryMap["line_id"] = req.LineId
	}

	if req.LayerId != "" {
		queryMap["layer_id"] = req.LayerId
	}

	region := strings.ToUpper(ctx.GetCountry())
	queryMap["region"] = region
	dataList, total, err := e.lineToggleDao.ListLineToggle(ctx, queryMap, req.PageNo, req.Count)
	if err != nil {
		logger.CtxLogErrorf(ctx, "query line toggle list fail|err[%s]", err.Msg)
		return nil, err
	}
	var lineList []*protocol.LineToggleInfo
	for _, data := range dataList {

		layerName := eFenceConstant.InActiveLayerName
		zone, err := e.polygonDao.GetLayerNameByLayerId(ctx, region, data.LayerId)
		if err != nil {
			return nil, err
		}

		if zone != nil {
			layerName = zone.LayerName
		}

		line := &protocol.LineToggleInfo{
			LineId:           data.LineId,
			LineName:         data.LineName,
			SupportLmHubZone: data.SupportLmHubZone,
			CheckWith:        data.CheckWith,
			Operator:         data.Operator,
			CreateTime:       data.Ctime,
			ModifyTime:       data.Mtime,

			LayerId:   data.LayerId,
			LayerName: layerName,
		}
		lineList = append(lineList, line)
	}

	return &protocol.GetLineToggleListResp{List: lineList, Total: total}, nil
}

func (e *EFenceService) UpdateLineToggle(ctx utils.LCOSContext, req *protocol.UpdateLineToggleReq) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	// 检查该layerId是否是生效的
	zone, err := e.polygonDao.GetLayerNameByLayerId(ctx, region, req.LayerId)
	if err != nil {
		return err
	}

	if zone == nil {
		return lcos_error.NewLCOSError(lcos_error.DataNotFound, "please check if layer id active")
	}

	data := &lineToggle.EFenceLineToggleTab{
		LineId:           req.LineId,
		LineName:         req.LineName,
		SupportLmHubZone: req.SupportLmHubZone,
		CheckWith:        req.CheckWith,
		Operator:         ctx.GetUserEmail(),
		Region:           region,
		Ctime:            recorder.Now(ctx).Unix(),
		Mtime:            recorder.Now(ctx).Unix(),

		LayerId: req.LayerId,
	}
	err = e.lineToggleDao.InsertOrUpdateLineToggle(ctx, data)
	if err != nil {
		logger.CtxLogErrorf(ctx, "update line toggle switch fail|err[%s]", err.Msg)
		return err
	}
	return nil
}

func (e *EFenceService) DeleteLineToggle(ctx utils.LCOSContext, req *protocol.DeleteLineToggleReq) *lcos_error.LCOSError {
	err := e.lineToggleDao.DeleteLineToggle(ctx, req.LineId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "delete line toggle switch fail|err[%s]", err.Msg)
		return err
	}
	return nil
}

func (e *EFenceService) GetLineToggleLayer(ctx utils.LCOSContext, req *protocol.GetLineToggleLayerReq) ([]*protocol.GetLineToggleLayerResp, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	results, err := e.polygonDao.GetAllLayer(ctx, region)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get line toggle layer fail|err[%s]", err.Msg)
		return nil, err
	}

	layerList := make([]*protocol.GetLineToggleLayerResp, 0)
	for _, r := range results {
		layerList = append(layerList, &protocol.GetLineToggleLayerResp{
			LayerId:   r.LayerId,
			LayerName: r.LayerName,
		})
	}

	return layerList, nil
}

func (e *EFenceService) GetLayerNameByLayerId(ctx utils.LCOSContext, layerId string) (string, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())

	layerName := eFenceConstant.InActiveLayerName
	zone, err := e.polygonDao.GetLayerNameByLayerId(ctx, region, layerId)

	if err != nil {
		return "", err
	}
	if zone != nil {
		layerName = zone.LayerName
	}

	return layerName, nil
}

func (e *EFenceService) sendEFenceGenerateMeshTask(ctx utils.LCOSContext, region string, notifyMode int32, pullZoneList []*pb.ZoneInfo, newVersionList []schema.VersionItem, oldVersionList []schema.VersionItem) error {
	if len(pullZoneList) == 0 && len(newVersionList) == 0 && len(oldVersionList) == 0 {
		logger.CtxLogInfof(ctx, "no new data, skip sending task")
		return nil
	}

	newZoneList := make([]*schema.EFenceZoneInfo, len(pullZoneList))
	for i, zone := range pullZoneList {
		newZoneList[i] = &schema.EFenceZoneInfo{
			ZoneId:      zone.GetZoneId(),
			DataVersion: strconv.Itoa(int(zone.GetVersionId())),
			LayerId:     zone.GetLayerId(),
		}
	}

	msg := schema.EFenceGenerateMeshMessage{
		NotifyMode:     notifyMode,
		Region:         region,
		NewZoneList:    newZoneList,
		NewVersionList: newVersionList,
		OldVersionList: oldVersionList,
	}
	data, err := jsoniter.Marshal(msg)
	if err != nil {
		logger.CtxLogErrorf(ctx, "marshal EFenceGenerateMeshMessage fail|notifyMode[%d]|newVersionList[%v]|oldVersionList[%v]",
			notifyMode, newVersionList, oldVersionList)
		return err
	}

	producer, err := saturnprovider.GetSaturnProducer(config.GetLCOSSaturn(ctx).DomainName)
	if err != nil {
		return err
	}
	return producer.SendMessage(ctx, 1, constant.EFenceGenerateMeshTask, data)
}

func (e *EFenceService) generateAndSaveMesh(ctx utils.LCOSContext, region string, polyList []*polygon.EFencePolygonTab) *lcos_error.LCOSError {
	logger.CtxLogInfof(ctx, "ready to do mesh, polyList size[%d]", len(polyList))

	// 对于统一批过来的所有多边形，逐个做网格化并落库网格数据，落库后的网格数据并不会马上被读到，要在后面的事务中启用zone之后才会生效
	for _, poly := range polyList {
		// 得到单个多边形的网格化结果
		polyMeshList, err := e.generateMesh(ctx, region, poly)
		if err != nil {
			return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
		}

		// 构造落库结构
		saveMeshList, lcosErr := e.constructMeshListForStorage(ctx, poly, polyMeshList)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "constructMeshListForStorage failed|err[%s]|Geometry:%s", lcosErr.Msg, poly.Geometry)
			return lcosErr
		}

		// 不需要放事务中：逐个多边形写mesh数据，此时zone并不是 InUsage 状态，因此这部分数据写进去并不会被用到
		lcosErr = e.meshDao.BatchInsertEFenceMesh(ctx, region, saveMeshList)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "insert mesh failed: %s", lcosErr.Msg)
			return lcosErr
		}
	}

	logger.CtxLogInfof(ctx, "transfer all poly to mesh success")

	return nil
}

func (e *EFenceService) generateMesh(ctx utils.LCOSContext, region string, poly *polygon.EFencePolygonTab) ([]*geopolygon.PolygonMeshResult, error) {
	// 反序列化多边形的顶点信息
	polyDetail, err := geopolygon.UnmarshalPoly([]byte(poly.Geometry))
	if err != nil {
		return nil, err
	}

	// 多边形网格化
	var (
		conf                     = config.GetMutableConf(ctx).EFenceConfig
		encodingMin, encodingMax = conf.GetEncodingSize(region, eFenceConstant.GenerateMesh)
	)
	if conf.GeohashEncodingSwitch {
		// 切换后直接走v2
		return geopolygon.TransferPolygonToMesh(polyDetail, encodingMin, encodingMax, geopolygon.PolyInPolyCheckModeV2)
	}
	if !conf.GeohashEncodingCompare {
		// 未切换并且不开对比，走v1
		return geopolygon.TransferPolygonToMesh(polyDetail, encodingMin, encodingMax, geopolygon.PolyInPolyCheckModeV1)
	}

	// 两版本逻辑对比结果，如果有差异则消息通知
	meshListV1, err := geopolygon.TransferPolygonToMesh(polyDetail, encodingMin, encodingMax, geopolygon.PolyInPolyCheckModeV1)
	if err != nil {
		return nil, err
	}
	meshListV2, err := geopolygon.TransferPolygonToMesh(polyDetail, encodingMin, encodingMax, geopolygon.PolyInPolyCheckModeV2)
	if err != nil {
		// 对比时，如果v2失败，则直接返回v1
		return meshListV1, nil
	}
	var (
		inV1NotInV2      []string
		inV2NotInV1      []string
		notEqualMeshList [][]*geopolygon.PolygonMeshResult
	)
	for _, meshV1 := range meshListV1 {
		var hit bool
		for _, meshV2 := range meshListV2 {
			if meshV1.GeoHash == meshV2.GeoHash {
				hit = true

				// 网格V1和V2都存在，但full coverage不一致
				if meshV1.FullCoverage != meshV2.FullCoverage {
					notEqualMeshList = append(notEqualMeshList, []*geopolygon.PolygonMeshResult{meshV1, meshV2})
				}
				break
			}
		}
		if !hit {
			// 网格在V1存在但V2不存在
			inV1NotInV2 = append(inV1NotInV2, meshV1.GeoHash)
		}
	}
	for _, meshV2 := range meshListV2 {
		var hit bool
		for _, meshV1 := range meshListV1 {
			if meshV2.GeoHash == meshV1.GeoHash {
				hit = true
				break
			}
		}
		if !hit {
			// 网格在V2存在但V1不存在
			inV2NotInV1 = append(inV2NotInV1, meshV2.GeoHash)
		}
	}

	if len(inV1NotInV2) != 0 || len(inV2NotInV1) != 0 || len(notEqualMeshList) != 0 {
		// 对比后不一致则seatalk通知，可以由dev人工介入手动修正
		message := fmt.Sprintf(eFenceConstant.MeshGenerateDiffAlertMessageTemplate,
			utils.GetEnv(ctx),
			region,
			poly.ZoneId,
			poly.DataVersion,
			poly.LayerId,
			strings.Join(inV1NotInV2, ","),
			strings.Join(inV2NotInV1, ","),
			utils.MarshToStringWithoutError(notEqualMeshList),
		)
		_ = seatalk.NotifyWithTextMessage(ctx, conf.NotifyWebhook, message, nil, true)
	}

	// 对比模式返回V1结果
	return meshListV1, nil
}

func (e *EFenceService) requestDetailZoneInfo(ctx utils.LCOSContext, region string, zoneInfoList []*pb.ZoneInfo) ([]*pb.ZoneInfo, *lcos_error.LCOSError) {
	var result []*pb.ZoneInfo
	spxService := spx_service.NewSpxSmrService(ctx, region)

	// 每次向SPX拉取5个 zoneInfo，避免返回报文过大
	const batchSize = 5
	for i := 0; i < len(zoneInfoList); i += batchSize {
		end := i + batchSize
		if end > len(zoneInfoList) {
			end = len(zoneInfoList)
		}
		batch := zoneInfoList[i:end]

		zoneIdList := constructBatchZoneReq(batch, region)

		zoneDetails, err := spxService.GetCustomizedZoneDetail(ctx, zoneIdList)
		if err != nil {
			return nil, err
		}

		result = transferBatchZoneDetails(zoneDetails, result)
	}

	return result, nil
}

func (e *EFenceService) CleanExpireMeshData(ctx utils.LCOSContext, region string, deleteLimit, zoneLimit int) *lcos_error.LCOSError {
	// 一次任务只清理指定个数的zone
	zoneList, err := e.polygonDao.ListAllEFencePolygonByLimit(ctx, region, map[string]interface{}{"handle_status": eFenceConstant.ZoneHandleStatusExpired}, zoneLimit)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get expired zone list fail|err[%s]", err.Msg)
		return err
	}
	if len(zoneList) == 0 {
		logger.CtxLogInfof(ctx, "no mesh data that needs to be deleted")
		return nil
	}

	for _, zone := range zoneList {
		idList, err := e.meshDao.GetMeshIdList(ctx, region, map[string]interface{}{"data_version": zone.DataVersion, "zone_id": zone.ZoneId, "layer_id": zone.LayerId})
		if err != nil {
			logger.CtxLogErrorf(ctx, "get mesh id list by zone info fail|zoneId[%s]|version[%s]|err[%s]", zone.ZoneId, zone.DataVersion, err.Msg)
			return err
		}
		if err := e.CleanMesh(ctx, zone.ZoneId, zone.DataVersion, zone.LayerId, region, idList, deleteLimit); err != nil {
			logger.CtxLogErrorf(ctx, "clean expire mesh fail|zoneId[%s]|version[%s]|err[%s]", zone.ZoneId, zone.DataVersion, err.Msg)
			return err
		}
	}
	logger.CtxLogInfof(ctx, "batch clean expire mesh success|region[%s]", region)
	return nil
}

func (e *EFenceService) ReportUnhandledPolygon(ctx utils.LCOSContext, region string) *lcos_error.LCOSError {
	// 取出来init状态的多边形
	zoneList, err := e.polygonDao.ListAllEFencePolygon(ctx, region, map[string]interface{}{"handle_status": eFenceConstant.ZoneHandleStatusInit})
	if err != nil {
		logger.CtxLogErrorf(ctx, "get init zone list fail|err[%s]", err.Msg)
		return err
	}
	if len(zoneList) == 0 {
		logger.CtxLogInfof(ctx, "no unhandled polygon")
		return nil
	}

	now := utils.GetTimestamp(ctx)
	expiredTime := uint32(60 * 90) // SPX会有兜底1小时全量推一次，如果90分钟还没处理的要上报
	for _, zone := range zoneList {
		if now-zone.CTime < expiredTime {
			continue
		}

		msg := fmt.Sprintf("zone %s is inited but not handled since %d, %d minutes by now", zone.ZoneId, zone.CTime, (now-zone.CTime)/60)
		logger.CtxLogErrorf(ctx, msg)
		_ = monitor.ReportEvent(commonConstant.CatModuleEFenceUnHandled, commonConstant.EventNameEFenceInit, commonConstant.StatusError, msg)
	}
	logger.CtxLogInfof(ctx, "report unhandled polygon success|region[%s]", region)
	return nil
}

func (e *EFenceService) CleanMesh(ctx utils.LCOSContext, zoneId, zoneVersion, layerId, region string, meshIdList []int64, limit int) *lcos_error.LCOSError {
	// 删除mesh,更新ploy,不需要事务做保证
	for start := 0; start < len(meshIdList); start += limit {
		end := start + limit
		if end > len(meshIdList) {
			end = len(meshIdList)
		}
		batchIdList := meshIdList[start:end]
		err := e.meshDao.DeleteMeshById(ctx, region, batchIdList)
		if err != nil {
			logger.CtxLogErrorf(ctx, "batch delete mesh fail|idList[%v]|err[%s]", batchIdList, err.Msg)
			return err
		}
		time.Sleep(100 * time.Millisecond)
	}

	// 更新poly
	err := e.polygonDao.UpdatePolygonStatus(ctx, region, zoneId, zoneVersion, layerId, eFenceConstant.ZoneHandleStatusCleaned)
	if err != nil {
		logger.CtxLogErrorf(ctx, "update polygon status fail|zoneId[%s]|version[%s]|err[%s]", zoneId, zoneVersion, err.Msg)
		return err
	}

	return nil
}

func (e *EFenceService) RefreshSingleZoneInfo(ctx utils.LCOSContext, request *pb.RefreshZoneInfoRequest) *lcos_error.LCOSError {
	// 查询对应的zone和version是否存在，不存在则返回报错
	region := request.GetReqHeader().GetDeployRegion()
	zoneInfo, err := e.polygonDao.GetOneEFencePolygonByZoneIdLayerIdAndVersion(ctx, region, request.GetZoneId(), request.GetOriVersionId(), request.GetLayerId())
	if err != nil {
		return err
	}
	zoneInfo.DataVersion = request.GetVersionId()
	zoneInfo.ID = 0
	// 异步处理
	goasync.GoAndRecover(func() {
		// 异步走到这里，context可能会cancel，因此在这里单独生成一份ctx
		asyncCtx := genAsyncCtx(ctx)
		// 生成新版本的mesh
		lcosErr := e.generateAndSaveMesh(asyncCtx, region, []*polygon.EFencePolygonTab{zoneInfo})
		if lcosErr != nil {
			logger.CtxLogErrorf(asyncCtx, "generateAndSaveMesh failed: %s", lcosErr.Msg)
			return
		}
		// 在事务中将请求的zone + version置为生效，将zone + layer id + old_version置为失效
		if err = e.refreshSingleZoneInfoByVersionId(asyncCtx, region, request.GetOriVersionId(), zoneInfo); err != nil {
			logger.CtxLogErrorf(asyncCtx, "refresh single zone info fail|new version id[%s]|ori version id[%s]|zone id[%s]", request.GetVersionId(), request.GetOriVersionId(), request.GetZoneId())
			return
		}
	})
	return nil
}

func (e *EFenceService) refreshSingleZoneInfoByVersionId(ctx *utils.CommonContext, region, oriVersion string, zoneInfo *polygon.EFencePolygonTab) *lcos_error.LCOSError {
	zoneId := zoneInfo.ZoneId
	layerId := zoneInfo.LayerId

	fc := func() *lcos_error.LCOSError {
		// 插入一条带有新version_id的记录，其他信息完全一致
		lcosErr := e.polygonDao.BatchInsertOrUpdateEFencePolygon(ctx, region, []*polygon.EFencePolygonTab{zoneInfo})
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "Error inserting polygon, error:%s", lcosErr.Msg)
			return lcosErr
		}
		// 将旧version的同一个zone置为失效
		lcosErr = e.polygonDao.UpdatePolygonStatus(ctx, region, zoneId, oriVersion, layerId, eFenceConstant.ZoneHandleStatusExpired)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "disable polygon failed")
			return lcosErr
		}

		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		logger.CtxLogErrorf(ctx, "refreshSingleZoneInfoByVersionId failed|err[%s]", err.Msg)
		return err
	}

	return nil
}

func (e *EFenceService) ParsePolygonAndMesh2GeoJson(ctx utils.LCOSContext, region, zoneId, version, layerId string, includeMesh bool, includeGeoHash []string) (string, *lcos_error.LCOSError) {
	region = strings.ToUpper(region)
	builder := geojson.NewGeoJsonBuilder()

	// 1. 生成zone对应polygon的geojson数据
	polygonList, err := e.polygonDao.GetEFencePolygonsByZoneIdLayerIdAndVersion(ctx, region, []*schema.EFenceZoneInfo{{ZoneId: zoneId, DataVersion: version, LayerId: layerId}})
	if err != nil {
		return "", err
	}
	if len(polygonList) == 0 {
		return "", lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "spx lm hub zone polygon not found")
	}
	polyDetail, parseErr := geopolygon.UnmarshalPoly([]byte(polygonList[0].Geometry))
	if parseErr != nil {
		return "", lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, parseErr.Error())
	}
	builder.AppendPolygon(polyDetail.Coordinates)

	// 2. 生成zone对应mesh结果的geojson数据
	if includeMesh {
		meshList, err := e.meshDao.ListMesh(ctx, region, map[string]interface{}{"zone_id": zoneId, "data_version": version})
		if err != nil {
			return "", err
		}
		for _, mesh := range meshList {
			prop := geojson.RedBoundingBoxStyle
			if mesh.FullCoverage {
				prop = geojson.BlueBoundingBoxStyle
			}
			builder.AppendGeohash(mesh.GeoHashCode, geojson.WithProperties(prop))
		}
	}

	// 3. 生成给定geohash列表对应的geojson数据
	for _, hash := range includeGeoHash {
		builder.AppendGeohash(hash, geojson.WithProperties(geojson.GreenBoundingBoxStyle))
	}
	return builder.ToString(), nil
}

func (e *EFenceService) RegenerateMesh(ctx utils.LCOSContext, region, zoneId, version, layerId string, pipMode int, refreshCache bool) *lcos_error.LCOSError {
	// 1. 获取zone的多边形数据
	polygonList, lcosErr := e.polygonDao.GetEFencePolygonsByZoneIdLayerIdAndVersion(ctx, region, []*schema.EFenceZoneInfo{{ZoneId: zoneId, DataVersion: version, LayerId: layerId}})
	if lcosErr != nil {
		return lcosErr
	}
	if len(polygonList) == 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "spx lm hub zone polygon not found")
	}
	polygon := polygonList[0]

	// 2. 反序列化多边形的坐标信息
	geometry, err := geopolygon.UnmarshalPoly([]byte(polygon.Geometry))
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	// 3. 按照指定的poly in poly校验方式生成网格
	encodingMin, encodingMax := config.GetMutableConf(ctx).EFenceConfig.GetEncodingSize(region, eFenceConstant.GenerateMesh)
	meshList, err := geopolygon.TransferPolygonToMesh(geometry, encodingMin, encodingMax, pipMode)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	saveMeshList, lcosErr := e.constructMeshListForStorage(ctx, polygon, meshList)
	if lcosErr != nil {
		return lcosErr
	}

	// 4. 获取zone在DB中存量的网格
	clearMeshIdList, lcosErr := e.meshDao.GetMeshIdList(ctx, region, map[string]interface{}{"zone_id": zoneId, "data_version": version})
	if lcosErr != nil {
		return lcosErr
	}

	// 5. 清理存量网格，同时写入新的网格。单个zone的网格数据不多，可以开启事务修改
	lcosErr = ctx.Transaction(func() *lcos_error.LCOSError {
		if lcosErr = e.meshDao.DeleteMeshById(ctx, region, clearMeshIdList); lcosErr != nil {
			return lcosErr
		}
		if lcosErr = e.meshDao.BatchInsertEFenceMesh(ctx, region, saveMeshList); lcosErr != nil {
			return lcosErr
		}
		return nil
	})
	if lcosErr != nil {
		return lcosErr
	}

	// 6. （可选）立即刷新网格缓存
	if refreshCache {
		_, _ = localcache.IncrLocalCacheVersion(ctx, commonConstant.EFenceMeshNamespace)
	}
	return nil
}

func (e *EFenceService) forward2MeshTabV1(ctx utils.LCOSContext, region string, polygonList []*polygon.EFencePolygonTab) error {
	lcosService := lcos_service.NewLCOSService(ctx, region)
	for _, p := range polygonList {
		if err := lcosService.Forward2MeshTabV1(ctx, region, p.ZoneId, p.DataVersion, p.LayerId, geopolygon.PolyInPolyCheckModeV1, false); err != nil {
			logger.CtxLogErrorf(ctx, "forward new mesh to tab v1 fail, err[%s], polygon[%+v]", err.Error(), p)
			_ = monitor.ReportEvent(commonConstant.CatModuleEFenceForwardMesh, commonConstant.EventNameEFenceForwardMesh, commonConstant.StatusError, err.Error())
			continue
		}
		logger.CtxLogInfof(ctx, "forward new mesh to tab v1 success, polygon[%+v]", p)
		_ = monitor.ReportEvent(commonConstant.CatModuleEFenceForwardMesh, commonConstant.EventNameEFenceForwardMesh, commonConstant.StatusSuccess, "")
	}

	return nil
}
