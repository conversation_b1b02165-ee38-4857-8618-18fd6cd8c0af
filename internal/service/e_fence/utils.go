package e_fence

import (
	"fmt"
	eFenceConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"git.garena.com/shopee/bg-logistics/spx/sorting/smart-sorting-protobuf/zone_facade_protobuf"
	"strconv"
	"strings"
)

func TransferStationType(stationTypeEnum int32) string {
	return eFenceConstant.StationTypeMap[stationTypeEnum]
}

// 如果list里面没有这个version + layerId，则加入list
func recordDataVersion(list []schema.VersionItem, version, layerId string, operator string) []schema.VersionItem {
	for _, v := range list {
		if v.DataVersion == version && v.LayerId == layerId {
			return list
		}
	}

	list = append(list, schema.VersionItem{
		DataVersion: version,
		Operator:    operator,
		LayerId:     layerId,
	})
	return list
}

func transferBatchZoneDetails(zoneDetails []*zone_facade_protobuf.CustomizedZoneDetail, result []*lcos_protobuf.ZoneInfo) []*lcos_protobuf.ZoneInfo {
	for _, zoneDetail := range zoneDetails {
		zoneInfo := &lcos_protobuf.ZoneInfo{
			VersionId:   utils.NewInt64(zoneDetail.GetVersionId()),
			ZoneStatus:  utils.NewInt32(zoneDetail.GetZoneStatus()),
			Operator:    utils.NewString(zoneDetail.GetOperator()),
			ZoneId:      utils.NewString(zoneDetail.GetZoneId()),
			ZoneName:    utils.NewString(zoneDetail.GetZoneName()),
			StationId:   utils.NewString(fmt.Sprintf("%d", zoneDetail.GetStationId())),
			StationType: utils.NewInt32(zoneDetail.GetStationType()),
			StationName: utils.NewString(zoneDetail.GetStationName()),
			Geometry:    utils.NewString(zoneDetail.GetGeometry()),
			ZoneArea:    utils.NewFloat64(zoneDetail.GetZoneArea()),

			LayerId:   utils.NewString(fmt.Sprintf("%d", zoneDetail.GetLayerId())),
			LayerName: utils.NewString(zoneDetail.GetLayerName()),
		}
		result = append(result, zoneInfo)
	}
	return result
}

func constructBatchZoneReq(batch []*lcos_protobuf.ZoneInfo, region string) []*zone_facade_protobuf.ZoneDetailParamItem {
	zoneIdList := make([]*zone_facade_protobuf.ZoneDetailParamItem, len(batch))

	for j, zoneInfo := range batch {
		layerId := GetRealLayerId(region, zoneInfo.GetLayerId())
		layerIdInt, _ := strconv.ParseInt(layerId, 10, 64)
		zoneIdList[j] = &zone_facade_protobuf.ZoneDetailParamItem{
			VersionId: int64(zoneInfo.GetVersionId()),
			ZoneId:    zoneInfo.GetZoneId(),
			LayerId:   layerIdInt,
		}
	}
	return zoneIdList
}

// 比较上传的文件表头是否和模版相同
func compareExcelTitle(row []string, template []string) bool {
	if len(row) != len(template) {
		return false
	}
	for i := 0; i < len(row); i++ {
		if row[i] != template[i] {
			return false
		}
	}
	return true
}

// 判断两个list是否有重复的version
func findDuplicatedVersion(list1 []string, list2 []string) bool {
	// 创建一个集合用于存储版本
	versionSet := make(map[string]bool)

	// 遍历第一个列表，并将每个版本添加到集合中
	for _, version := range list1 {
		versionSet[version] = true
	}

	// 遍历第二个列表，检查每个版本是否已经存在于集合中
	for _, version := range list2 {
		if versionSet[version] {
			// 存在重复版本
			return true
		}
	}

	// 不存在重复版本
	return false
}

func GetRealLayerId(region string, layerId string) string {
	region = strings.ToLower(region)

	if region == eFenceConstant.RegionTW && (layerId == "" || layerId == eFenceConstant.DefaultLayer) {
		// tw 没有layer id = 0, 1为默认值
		return eFenceConstant.TwDefaultLayer
	}

	// 其他市场默认值为0
	if layerId == "" {
		return eFenceConstant.DefaultLayer
	}

	return layerId
}

func GetRealLayerName(region string, layerId, layerName string) string {
	region = strings.ToLower(region)

	if region == eFenceConstant.RegionTW && (layerId == "" || layerId == eFenceConstant.DefaultLayer) {
		return eFenceConstant.TwDefaultLayerName
	}

	if layerId == "" {
		return eFenceConstant.DefaultLayerName
	}

	return layerName
}
