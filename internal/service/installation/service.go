package installation

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/datetime"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/golang/protobuf/proto"
	"strconv"
	"time"
)

type InstallationDateInterface interface {
	BatchGetInstallationDate(ctx utils.LCOSContext, slsTnInfo *pb.BatchGetInstallationDataRequest) (*pb.InstallationDateResponse, *lcos_error.LCOSError)
}

type InstallationService struct {
	lpsHolidayDao lps_holiday.LPSHolidayDAO
}

func (i InstallationService) BatchGetInstallationDate(ctx utils.LCOSContext, installationInfo *pb.BatchGetInstallationDataRequest) (*pb.InstallationDateResponse, *lcos_error.LCOSError) {
	res := &pb.InstallationDateResponse{}
	dateItems := make([]*pb.DateItem, 0, len(installationInfo.InstallationItems))
	installationAfterDays := config.GetMutableConf(ctx).InstallationConfig.InstallationExtendDays[installationInfo.GetRegion()]
	extendDays := config.GetMutableConf(ctx).InstallationConfig.InstallationQuantityDays[installationInfo.GetRegion()]
	if installationAfterDays == 0 {
		installationAfterDays = constant.DefaultInstallationExtendDays
	}
	if extendDays == 0 {
		extendDays = constant.DefaultInstallationExtendDays
	}
	for _, installationItem := range installationInfo.InstallationItems {
		dateItem := &pb.DateItem{
			SlsTn: installationItem.SlsTn,
			Msg:   proto.String("success"),
		}
		var start time.Time
		holidayDateStrings, WeekendNum, err := i.lpsHolidayDao.GetNonWorkingDaysInfo(ctx, strconv.Itoa(int(installationItem.GetProductId())), 0, installationInfo.GetRegion(), constant.True, false, false)
		if err != nil {
			dateItem.Msg = proto.String(fmt.Sprintf("get non working days for product%v from cache err: %v", installationItem.GetProductId(), err))
			dateItems = append(dateItems, dateItem)
			continue
		}
		holidayTimestampMap := make(map[string]bool, len(holidayDateStrings))
		WeekendsMap := make(map[int32]bool, len(WeekendNum))
		for _, holidayDateString := range holidayDateStrings {
			holidayTimestampMap[holidayDateString] = true
		}
		for _, weekend := range WeekendNum {
			WeekendsMap[weekend] = true
		}
		switch installationItem.GetScenario() {
		case constant.FirstChoose:
			if installationItem.GetEdt() <= 0 {
				dateItem.Msg = proto.String("need edt for scenario 1")
				dateItems = append(dateItems, dateItem)
				continue
			}
			start = utils.ParseTimestampToTime(installationItem.GetEdt(), installationInfo.GetRegion())
		case constant.Change:
			if installationItem.GetEdd() > 0 {
				start = utils.ParseTimestampToTime(installationItem.GetEdd(), installationInfo.GetRegion())
			} else if installationItem.GetEdt() > 0 {
				start = utils.ParseTimestampToTime(installationItem.GetEdt(), installationInfo.GetRegion())
			} else {
				dateItem.Msg = proto.String("need edd or edt for scenario 2")
				dateItems = append(dateItems, dateItem)
				continue
			}
		case constant.Worst:
			if installationItem.GetEdt() <= 0 {
				dateItem.Msg = proto.String("need edt for scenario 3")
				dateItems = append(dateItems, dateItem)
			} else {
				onlyDay := utils.ParseTimestampToTime(installationItem.GetEdt()+installationAfterDays*constant.SecondByDay, installationInfo.GetRegion())
				for idx := 0; idx < 100; idx++ {
					if !holidayTimestampMap[onlyDay.Format(constant.DateFormat)] && !WeekendsMap[int32(onlyDay.Weekday())] {
						break
					}
					onlyDay = onlyDay.AddDate(0, 0, 1)
				}
				dateItem.AvailableDays = append(dateItem.AvailableDays, &pb.InstallDay{
					TimestampDay: proto.Uint32(uint32(datetime.ResetTime(onlyDay).Unix())),
				})
				dateItems = append(dateItems, dateItem)
			}
			continue
		}

		for idx := 1; len(dateItem.AvailableDays) < int(extendDays) && idx <= 200; idx++ {
			thisDay := start.AddDate(0, 0, idx)
			if holidayTimestampMap[thisDay.Format(constant.DateFormat)] || WeekendsMap[int32(thisDay.Weekday())] {
				continue
			}
			dateItem.AvailableDays = append(dateItem.AvailableDays, &pb.InstallDay{
				TimestampDay: proto.Uint32(uint32(datetime.ResetTime(thisDay).Unix())),
			})
		}
		dateItems = append(dateItems, dateItem)
	}
	res.DateItems = dateItems
	return res, nil
}

var _ InstallationDateInterface = (*InstallationService)(nil)

func NewInstallationMethod(lpsHolidayDao lps_holiday.LPSHolidayDAO) *InstallationService {
	return &InstallationService{
		lpsHolidayDao: lpsHolidayDao,
	}
}
