package pickup_window_grpc_open_logistic

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_window_calculator"
	"strconv"
)

const clientType = constant.OPEN

type PickupWindowOpenLogisticService struct {
	pickupGroupDao     pickup_group.PickupGroupDAO
	pickupConfDao      open_logistic_pickup_config.OpenLogisticPickupConfigurationDAO
	serviceableConfDao basic_conf.LineBasicServiceableConfDAO
	validator          common_utils.PickupWindowValidatorInterface
	calculator         pickup_window_calculator.PickupWindowCalculatorInterface
}

func NewPickupWindowOpenLogisticService() *PickupWindowOpenLogisticService {
	return &PickupWindowOpenLogisticService{
		pickupGroupDao:     pickup_group.NewPickupGroupDAO(),
		pickupConfDao:      open_logistic_pickup_config.NewOpenLogisticPickupConfigurationDAO(),
		serviceableConfDao: basic_conf.NewLineBasicServiceableConfDAO(),
		validator:          common_utils.NewPickupWindowValidator(clientType),
		calculator:         pickup_window_calculator.NewPickupWindowCalculator(clientType),
	}
}

// 通过lineID列表获取pickup group，并检查是否满足pickup条件
func (p *PickupWindowOpenLogisticService) getValidPickupGroupByLineIDList(ctx utils.LCOSContext, lineIDList []string) (*pickup_group.PickupGroupTab, *lcos_error.LCOSError) {
	// 获取pickup group
	pickupGroup, lcosErr := p.pickupGroupDao.GetPickupGroupByLineIDListUsingCache(ctx, lineIDList, clientType)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 获取line basic conf用于校验pickup
	lineServiceableConfMap, lcosErr := p.serviceableConfDao.GetBasicServiceableConfModelMapByLineIdsUseCache(ctx, lineIDList)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// pickup 条件校验
	lcosErr = p.validator.GeneralCheck(ctx, lineIDList, pickupGroup, lineServiceableConfMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return pickupGroup, nil
}

func (p *PickupWindowOpenLogisticService) GetArrangedPickupDays(ctx utils.LCOSContext, request interface{}) (*common_utils.PickupDays, *lcos_error.LCOSError) {
	req := request.(*common_utils.OpenLogisticArrangedPickupDaysRequest)
	// 参数校验
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, req.LineIdList)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 获取pickup Conf
	pickupConf, lcosErr := p.pickupConfDao.GetPickupConfigUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion, req.MerchantType, req.AccountGroup)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcosErr
	}

	// 获取startTime
	startDatetime := p.calculator.GetStartShipDateTime(ctx, pickupGroup, pickupConf.PickupCutoffHour)
	// TODO SPLN-18095点线的获取pickup days接口加上了zipcode字段，开放物流暂时不需要，暂时填充为空字符串
	pickupDays, lcosErr := p.calculator.CalculatePickupDays(ctx, pickupGroup, int(pickupConf.PickupDays), startDatetime, req.PickupLocationID, "", constant.ENABLED, pickupConf.PickupDateFormat)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcosErr
	}

	// 计算timeslots
	returnPickupDays, returnTimeslots, lcosErr := p.calculator.GetOpenLogisticTimeSlots(ctx, pickupGroup, pickupDays, req.MerchantType, req.AccountGroup, pickupConf.IsHaveTimeslot, pickupConf.SlotNum)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcosErr
	}

	//在所有计算都结束后，如果获取的pickup days仍为0，则报错
	if len(returnPickupDays) == 0 {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcos_error.NotGetPickupDaysErrorCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcos_error.NewLCOSError(lcos_error.NotGetPickupDaysErrorCode, fmt.Sprintf("No pickup time is available with extend pickup date to escrow| pickup_group_id=%s", pickupGroup.PickupGroupID))
	}

	// 封装返回结果
	actualPickupDays := &common_utils.PickupDays{
		Days:          returnPickupDays,
		Timeslots:     returnTimeslots,
		PickupGroupId: pickupGroup.PickupGroupID,
	}
	_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(0), "pickup_group": pickupGroup.PickupGroupID})
	return actualPickupDays, nil
}

func (p *PickupWindowOpenLogisticService) GetReturnedPickupDays(ctx utils.LCOSContext, request interface{}) (*common_utils.PickupDays, *lcos_error.LCOSError) {
	return nil, nil
}

func (p *PickupWindowOpenLogisticService) CheckPickupTime(ctx utils.LCOSContext, request interface{}) *lcos_error.LCOSError {
	req := request.(*common_utils.CheckOpenLogisitcPickupTimeRequest)

	// 参数校验
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, req.LineIdList)
	if lcosErr != nil {
		return lcosErr
	}

	// 获取配置
	pickupConf, lcosErr := p.pickupConfDao.GetPickupConfigUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion, req.MerchantType, req.AccountGroup)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return lcosErr
	}

	// 校验pickup time是否合法
	// TODO SPLN-18095点线的获取pickup days接口加上了zipcode字段，开放物流暂时不需要，暂时填充为空字符串
	lcosErr = p.calculator.CheckPickupTime(ctx, pickupGroup, req.PickupTime, req.StateLocationId, "", pickupConf.PickupCutoffHour, constant.ENABLED)
	reportCode := 0
	if lcosErr != nil {
		reportCode = int(lcosErr.RetCode)
	}
	_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(reportCode), "pickup_group": pickupGroup.PickupGroupID})

	return lcosErr
}

func (p *PickupWindowOpenLogisticService) GetPickupTimeslots(ctx utils.LCOSContext, request interface{}) ([]*common_utils.TimeslotInfo, *lcos_error.LCOSError) {
	return nil, nil
}

func (p *PickupWindowOpenLogisticService) GetPickupTimeslot(ctx utils.LCOSContext, request interface{}) (*common_utils.SingleTimeslotInfo, *lcos_error.LCOSError) {
	req := request.(*common_utils.GetSingleOpenLogisticPickupTimeslotRequest)

	// 参数校验
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, req.LineIdList)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 获取timeslots
	timeslots, lcosErr := p.calculator.GetPickupTimeslots(ctx, pickupGroup, &req.PickupTimeRangeId, req.MerchantType, req.AccountGroup)

	if lcosErr != nil {
		return nil, lcosErr
	}

	if len(timeslots) < 1 {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundTimeslotErrorCode, fmt.Sprintf("cannot find timeslots|line_ids=%v", req.LineIdList))
	}

	returnTimeslot := &common_utils.SingleTimeslotInfo{
		StartHour:   timeslots[0].StartTimeHour,
		StartMinute: timeslots[0].StartTimeMin,
		StartSecond: timeslots[0].StartTimeSecond,
		EndHour:     timeslots[0].EndTimeHour,
		EndMinute:   timeslots[0].EndTimeMin,
		EndSecond:   timeslots[0].EndTimeSecond,
	}
	// 检查当前的pickup time range是否为0，是的话，需要将timeslot的开始时间设置为当前时间
	if pickup.IsNowTimeslot(timeslots[0]) {
		nowTime := pickup.GetCurrentTime(ctx, pickupGroup.OriginRegion)
		returnTimeslot.StartHour = uint32(nowTime.Hour())
		returnTimeslot.StartMinute = uint32(nowTime.Minute())
		returnTimeslot.StartSecond = uint32(nowTime.Second())
	}

	return returnTimeslot, nil
}

func (p *PickupWindowOpenLogisticService) GetAllPickupGroups(ctx utils.LCOSContext) ([]*common_utils.PickupGroup, *lcos_error.LCOSError) {
	return nil, nil
}

func (p *PickupWindowOpenLogisticService) GetHolidays(ctx utils.LCOSContext, request interface{}) ([]string, *lcos_error.LCOSError) {
	return nil, nil
}

func (p *PickupWindowOpenLogisticService) GetValidPickupConf(ctx utils.LCOSContext, request interface{}) (*common_utils.PickupConf, *lcos_error.LCOSError) {
	return nil, nil
}

func (p *PickupWindowOpenLogisticService) GetPickupHolidays(ctx utils.LCOSContext, request interface{}) (*common_utils.ProductPickupDays, *lcos_error.LCOSError) {
	return nil, nil
}
