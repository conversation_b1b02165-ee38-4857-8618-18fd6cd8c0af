package pickup_window_calculator

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/pickup_window_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
	"golang.org/x/net/context"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/recurring_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/common_utils"
)

const DEFAULT_DAYS int = 30

type PickupWindowCalculatorInterface interface {
	GetStartShipDateTime(ctx context.Context, pickupGroup *pickup_group.PickupGroupTab, pickupCutoffHour uint32) time.Time
	CalculatePickupDays(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, shipDays int, startShipDatetime time.Time, stateLocationID int64, zipcode string, IsNonWorkingDayPickupAllowed uint8, pickupTimeFormat string) ([]*common_utils.PickupDay, *lcos_error.LCOSError)
	CheckPickupTime(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupTimestamp uint32, stateLocationID int64, zipcode string, cutoffHour uint32, isNonWorkingDayPickupAllowed uint8) *lcos_error.LCOSError
	CalculatePickupDaysWithExtends(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, orderTime *common_utils.OrderTime, shipByDatetime, acl2time uint32, stateLocationID int64, zipcode string, buyerTimeslotID uint32, pickupConf *pickup_config.PickupConfTab, IsChangePickupDate, isRapidSla bool) ([]*common_utils.PickupDay, bool, *lcos_error.LCOSError)
	GetSiteLineTimeSlots(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupDays []*common_utils.PickupDay, isHaveTimeslot uint8, isExtend bool, advanceDays uint32, returnDays uint32, returnCutoffHour uint32, returnCutoffHourDays uint32, extendDaysBeforeAcl2 uint32, slotNum uint32, extendSlotNum uint32, buyerTimeslotID uint32, pickupConfId uint64) ([]*common_utils.PickupDay, []*common_utils.TimeSlot, *lcos_error.LCOSError)
	GetOpenLogisticTimeSlots(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupDays []*common_utils.PickupDay, merchantType uint32, accountGroup uint32, isHaveTimeslot uint8, slotNum uint32) ([]*common_utils.PickupDay, []*common_utils.TimeSlot, *lcos_error.LCOSError)
	PickupDaysControlLimit(ctx context.Context, pickupGroup *pickup_group.PickupGroupTab, pickupDays []*common_utils.PickupDay, volumes []*common_utils.Volume, dailyMinDaysExtend uint32, dailyControlStatus uint8, dailyControlBeginTime uint32, dailyControlEndTime uint32, dailyMaxVolume uint32)
	GetPickupTimeslots(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupTimeRangeID *uint32, merchantType uint32, accountGroup uint32) ([]*common_utils.TimeslotInfo, *lcos_error.LCOSError)
	GetHolidays(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, days int, stateLocationID int64, zipcode string) ([]string, *lcos_error.LCOSError)
	GetPickupHolidays(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, stateLocationID int64, zipcode string) (*common_utils.ProductPickupDays, *lcos_error.LCOSError)
}
type PickupWindowCalculator struct {
	clientType              uint32
	holidayDao              holiday.LogisticHolidayDAO
	recurringHolidayDao     recurring_holiday.RecurringHolidayDAO
	siteLineTimeslotDao     timeslot.PickupTimeslotDAO
	openLogisticTimeslotDao open_logistic_timeslot.OpenLogisticPickupTimeslotDAO

	// sbd, end ship date, acl
	shipByDatetime        time.Time
	endShipDatetime       time.Time
	extendCalculationFlag bool // check if under extend calculation, if true, need to cutoff timeslot by sbd, if not, need to cutoff timeslot by acl
	isExtend              bool // check if need to extend, if true, need to cutoff timeslot by acl, else cutoff by sbd
	isExtendByACL2        bool // check if is extend by ACL2,
	isRapidSla            bool // 是否RapidSLA渠道，如果是，SBD和ACL2需要精细到小时维度。不可返回超过SBD或ACL2的timeslot
	acl2Time              uint32
}

var _ PickupWindowCalculatorInterface = (*PickupWindowCalculator)(nil)

func NewPickupWindowCalculator(clientType uint32) *PickupWindowCalculator {
	return &PickupWindowCalculator{
		clientType:              clientType,
		holidayDao:              holiday.NewLogisticHolidayDAO(),
		recurringHolidayDao:     recurring_holiday.NewRecurringHolidayDAO(),
		siteLineTimeslotDao:     timeslot.NewPickupTimeslotDAO(),
		openLogisticTimeslotDao: open_logistic_timeslot.NewOpenLogisticPickupTimeslotDAO(),
	}
}

func (p *PickupWindowCalculator) CalculatePickupDays(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, shipDays int, startShipDatetime time.Time, stateLocationID int64, zipcode string, isNonWorkingDayPickupAllowed uint8, pickupTimeFormat string) ([]*common_utils.PickupDay, *lcos_error.LCOSError) {
	pickupDays := make([]*common_utils.PickupDay, 0, 10)
	timeLocation := pickup.GetTimeLocation(pickupGroup.OriginRegion)
	for i := 0; i < shipDays; i++ {
		shipDatetime := pickup.AddDays(startShipDatetime, i)
		timeDate := time.Date(shipDatetime.Year(), shipDatetime.Month(), shipDatetime.Day(), pickup.DefaultHour, pickup.DefaultMinute, pickup.DefaultSecond, pickup.DefaultNSecond, timeLocation)
		timeValue := uint32(timeDate.Unix())
		pickupDay := &common_utils.PickupDay{
			Date:  shipDatetime.Format(pickupTimeFormat),
			Value: timeValue,
		}
		// 检查是否为holiday或者weekend
		if isNonWorkingDayPickupAllowed != constant.ENABLED {
			isHoliday, llsErr := p.holidayDao.CheckIsHolidayUsingCache(ctx, shipDatetime, stateLocationID, pickupGroup)
			if llsErr != nil {
				return nil, llsErr
			}
			if isHoliday {
				continue
			}
			isWeekend, llsErr := p.recurringHolidayDao.CheckIsWeekendUsingCache(ctx, shipDatetime, stateLocationID, zipcode, pickupGroup)
			if llsErr != nil {
				return nil, llsErr
			}
			if isWeekend {
				continue
			}
		}
		pickupDays = append(pickupDays, pickupDay)
	}
	logger.LogInfof("getPickupDays, pickupGroupID=%v,shippingDays=%v,startShippingDate=%v,stateLocationID=%d,pickupDays=%v",
		pickupGroup.PickupGroupID, shipDays, startShipDatetime, stateLocationID, pickupDays)
	return pickupDays, nil
}

func (p *PickupWindowCalculator) GetStartShipDateTime(ctx context.Context, pickupGroup *pickup_group.PickupGroupTab, pickupCutoffHour uint32) time.Time {
	now := pickup.GetCurrentTime(ctx, pickupGroup.OriginRegion)
	hour := now.Hour()
	addDay := 1
	if pickupGroup.PickupType == constant.SAMEDAY {
		addDay = 0
	}
	if pickupCutoffHour > 0 && hour >= int(pickupCutoffHour) {
		addDay += 1
	}
	return pickup.AddDays(now, addDay)
}

func (p *PickupWindowCalculator) getPickupDaysControlLimit(ctx context.Context, pickupGroup *pickup_group.PickupGroupTab, volumeMap map[string]uint32, dayValue uint32, dailyControlStatus uint8, dailyControlBeginTime uint32, dailyControlEndTime uint32, dailyMaxVolume uint32) int {
	if dailyControlStatus != constant.ENABLED {
		return pickup.VolumeControlUnLimit
	}
	// 开启了容量限制，但是没有设置开始时间和结束时间
	if dailyControlBeginTime == 0 || dailyControlEndTime == 0 {
		logger.LogErrorf("pickup daily control is enabled, but not set control begin time or end time|pickup_group_id=%s, destination_region=%s", pickupGroup.PickupGroupID, pickupGroup.DestinationRegion)
		return pickup.VolumeControlUnLimit
	}
	// 检查当前时间是否在容量限制的时间
	beginTime := pickup.TransferTimeStampToTime(dailyControlBeginTime, pickupGroup.OriginRegion)
	endTime := pickup.TransferTimeStampToTime(dailyControlEndTime, pickupGroup.OriginRegion)
	// 将beginTime和endTime的小时和分钟抹去，容量限制只考虑天
	beginTime = time.Date(beginTime.Year(), beginTime.Month(), beginTime.Day(), 0, 0, 0, 0, beginTime.Location())
	endTime = time.Date(endTime.Year(), endTime.Month(), endTime.Day()+1, 0, 0, 0, 0, endTime.Location())

	nowTime := pickup.GetCurrentTime(ctx, pickupGroup.OriginRegion)
	if nowTime.Before(beginTime) || nowTime.After(endTime) {
		logger.LogInfof("now time:[%v] not between volume control begin time:[%v] and end time:[%v]", nowTime, beginTime, endTime)
		return pickup.VolumeControlUnLimit
	}
	pickupDate := time.Unix(int64(dayValue), 0).Format(pickup.DateFormat)
	if _, ok := volumeMap[pickupDate]; !ok {
		return pickup.VolumeControlUnLimit
	}
	if volumeMap[pickupDate] >= dailyMaxVolume {
		return pickup.VolumeControlLimit
	}
	return pickup.VolumeControlUnLimit
}

func (p *PickupWindowCalculator) CheckPickupTime(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupTimestamp uint32, stateLocationID int64, zipcode string, cutoffHour uint32, isNonWorkingDayPickupAllowed uint8) *lcos_error.LCOSError {
	pickupDatetime := pickup.TransferTimeStampToTime(pickupTimestamp, pickupGroup.OriginRegion)
	cutoffDatetime := common_utils.CalculateCutoffTime(pickupDatetime, cutoffHour, pickupGroup)
	nowDatetime := pickup.GetCurrentTime(ctx, pickupGroup.OriginRegion)
	logger.LogInfof("nowTime:%v, cutoffTime:%v, pickupGroup_id=%s, destination_region=%s", nowDatetime, cutoffDatetime, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion)
	if nowDatetime.After(cutoffDatetime) {
		return lcos_error.NewLCOSError(lcos_error.CurrentTimeOverCutoffTimeErrorCode, fmt.Sprintf("now_time:[%v] over cutoff_time:[%v]|pickup_group_id=%s, destination_region=%s", nowDatetime.Format("2006-01-02 15:04"), cutoffDatetime.Format("2006-01-02 15:04"), pickupGroup.PickupGroupID, pickupGroup.DestinationRegion))
	}
	// 校验pickup time不能是holiday，只有在不允许节假日揽收时才有效
	if isNonWorkingDayPickupAllowed == constant.DISABLED {
		isHoliday, _ := p.holidayDao.CheckIsHolidayUsingCache(ctx, pickupDatetime, stateLocationID, pickupGroup)
		if isHoliday {
			return lcos_error.NewLCOSError(lcos_error.PickupDayIsHolidayErrorCode, fmt.Sprintf("pickup time:[%v] is holiday|pickup_group_id=%s", pickupDatetime, pickupGroup.PickupGroupID))
		}
		isWeekend, _ := p.recurringHolidayDao.CheckIsWeekendUsingCache(ctx, pickupDatetime, stateLocationID, zipcode, pickupGroup)
		if isWeekend {
			return lcos_error.NewLCOSError(lcos_error.PickupDayIsWeekendErrorCode, fmt.Sprintf("pickup time:[%v] is weekend|pickup_group_id=%s", pickupDatetime, pickupGroup.PickupGroupID))
		}
	}
	return nil
}

// 通过pickupConf获取实际的间隔时间
func (p *PickupWindowCalculator) getActualShipDays(shipDays int, advanceDays uint32) int {
	if advanceDays > 0 && shipDays > int(advanceDays) {
		shipDays = int(advanceDays)
	}
	return shipDays
}

func (p *PickupWindowCalculator) CalculatePickupDaysWithExtends(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, orderTime *common_utils.OrderTime, shipByDatetime, acl2time uint32, stateLocationID int64, zipcode string, buyerTimeslotID uint32, pickupConf *pickup_config.PickupConfTab, IsChangePickupDate, isRapidSla bool) ([]*common_utils.PickupDay, bool, *lcos_error.LCOSError) {
	startShipDatetime := p.GetStartShipDateTime(ctx, pickupGroup, pickupConf.PickupCutoffHour)
	endShipDatetime := p.getEndShipDateByShipDate(shipByDatetime, pickupGroup.OriginRegion)

	if acl2time == 0 {
		// ACL2空缺异常上报
		_ = monitor.AwesomeReportEvent(ctx, constant.CatPickupWindowExtendError, "EmptyACL2", constant.StatusError, "")
		acl2time = uint32(endShipDatetime.AddDate(0, 0, int(pickupConf.ExtendDays)).Unix()) // ACL2没传则使用旧的extend x days逻辑兜底，设置ACL2 = SBD + x days
	}

	// update sdb and end ship date
	p.SetRapidSla(isRapidSla, acl2time) // 在calculator中设置Rapid SLA的上下文信息
	p.SetShipByDatetime(endShipDatetime)
	p.SetEndShipDate(endShipDatetime)

	shipDays := pickup.GetIntervalDays(startShipDatetime, endShipDatetime)
	// IsChangePickupDate为true，是lfs改变截止时间下单的场景，此时不走advanceDays逻辑
	if !IsChangePickupDate {
		shipDays = p.getActualShipDays(shipDays, pickupConf.AdvanceDays)
	}
	pickupDays, lcosErr := p.CalculatePickupDays(ctx, pickupGroup, shipDays, startShipDatetime, stateLocationID, zipcode, pickupConf.IsNonWorkingDayPickupAllowed, pickupConf.PickupDateFormat)
	if lcosErr != nil {
		return nil, false, lcosErr
	}
	isExtend := false
	// 计算extend的逻辑。满足以下其中一个条件即可触发extend逻辑
	// 1. 在SBD前无法计算出可选的pickup day
	if len(pickupDays) == 0 {
		var (
			extendEndShipDatetime time.Time
			isExtendFlag          bool
		)

		// 新旧extend逻辑切换，用于上线兼容。上下游全部发布后开启开关切换到ACL2 extend逻辑
		if config.IsPickupWindowExtendByACL2(ctx) && acl2time != 0 {
			extendEndShipDatetime, isExtendFlag = p.getExtendEndShipDatetimeWithACL2(ctx, endShipDatetime, acl2time, pickupGroup, IsChangePickupDate)
			p.SetIsExtendByACL2Flag(isExtendFlag) // 设置上下文，是否使用ACL2 extend pickup window
			_ = monitor.AwesomeReportEvent(ctx, constant.CatPickupWindowExtend, "ACL2", constant.StatusSuccess, "")
		} else {
			extendEndShipDatetime, isExtendFlag = p.getExtendEndShipDatetime(ctx, endShipDatetime, orderTime, pickupConf.UseReleaseTime, pickupConf.IsExtend, pickupConf.ExtendDays, pickupGroup, IsChangePickupDate)
			p.SetIsExtendByACL2Flag(false)
			_ = monitor.AwesomeReportEvent(ctx, constant.CatPickupWindowExtend, "ExtendDays", constant.StatusSuccess, "")
		}

		// update end ship date
		p.SetEndShipDate(extendEndShipDatetime)
		p.SetIsExtendFlag(isExtendFlag)

		// 需要根据新的endShipDatetime重新计算
		if isExtendFlag {
			extendShipDays := pickup.GetIntervalDays(startShipDatetime, extendEndShipDatetime)
			if !p.GetIsExtendByACL2Flag() {
				// advance_days只作用于以下场景：
				// 1. SBD之前的pickup window计算
				// 2. SBD之后pickup window的旧extend逻辑计算（也就是此分支）
				// SBD之后pickup window的新extend逻辑计算由extend_days_before_acl2控制。在pickup days计算后生效（同return_days逻辑）
				extendShipDays = p.getActualShipDays(extendShipDays, pickupConf.AdvanceDays)
			}
			shipDays = extendShipDays
			pickupDays, lcosErr = p.CalculatePickupDays(ctx, pickupGroup, shipDays, startShipDatetime, stateLocationID, zipcode, pickupConf.IsNonWorkingDayPickupAllowed, pickupConf.PickupDateFormat)
			isExtend = isExtendFlag
			if lcosErr != nil {
				return nil, isExtend, lcosErr
			}
		}
	}
	return pickupDays, isExtend, nil
}

func (p *PickupWindowCalculator) getEndShipDateByShipDate(shipByDateTime uint32, region string) time.Time {
	endShipDate := pickup.TransferTimeStampToTime(shipByDateTime, region)
	return endShipDate
}

func (p *PickupWindowCalculator) getExtendEndShipDatetime(ctx utils.LCOSContext, shipByDate time.Time, orderTime *common_utils.OrderTime, useReleaseTime uint8, isExtend uint8, extendDays uint32, pickupGroup *pickup_group.PickupGroupTab, IsChangePickupDate bool) (time.Time, bool) {
	// IsChangePickupDate为true，是lfs更换时间尝试下单，此场景不使用extend逻辑
	if isExtend != constant.ENABLED || IsChangePickupDate {
		return shipByDate, false
	}
	// 逆向运单不能使用 release time
	if orderTime.IsReturnOrder {
		useReleaseTime = constant.DISABLED
	}
	endShipDatetime := shipByDate
	if extendDays > 0 {
		endShipDatetime = pickup.AddDays(shipByDate, int(extendDays))
	} else if useReleaseTime == constant.ENABLED {
		if orderTime.ReleaseTime > 0 {
			endShipDatetime = pickup.TransferTimeStampToTime(orderTime.ReleaseTime, pickupGroup.OriginRegion)
		} else {
			endShipDatetime = pickup.TransferTimeStampToTime(orderTime.PayTime, pickupGroup.OriginRegion)
			endShipDatetime = pickup.AddDays(endShipDatetime, orderTime.DaysToShip)
		}
		endShipDatetime = pickup.AddDays(endShipDatetime, pickup.DaysBeforeEscrowToExtendPickup)
	}
	return endShipDatetime, true
}

func (p *PickupWindowCalculator) getExtendEndShipDatetimeWithACL2(ctx utils.LCOSContext, shipByDate time.Time, acl2Time uint32, pickupGroup *pickup_group.PickupGroupTab, isChangePickupDate bool) (time.Time, bool) {
	// 以下场景不使用extend逻辑：
	// 1. SPLN-19855：isChangePickupDate为true，为LFS修复卡单重新获取pickup time的场景，不触发extend逻辑
	if isChangePickupDate {
		return shipByDate, false
	}

	return pickup.TransferTimeStampToTime(acl2Time, pickupGroup.OriginRegion), true
}

func (p *PickupWindowCalculator) getReturnPickupDays(ctx context.Context, pickupDays []*common_utils.PickupDay, advanceDays uint32, returnDays uint32, returnCutoffHour uint32, returnCutoffHourDays uint32, extendDaysBeforeAcl2 uint32, pickupGroup *pickup_group.PickupGroupTab) []*common_utils.PickupDay {
	if len(pickupDays) == 0 {
		return pickupDays
	}
	if p.GetIsExtendByACL2Flag() {
		// 如果使用ACL2 extend SBD计算pickup window，那么使用extend_days_before_acl2控制返回的天数
		// extend_days_before_acl2为0表示不限返回天数
		if extendDaysBeforeAcl2 > 0 && len(pickupDays) > int(extendDaysBeforeAcl2) {
			pickupDays = pickupDays[:extendDaysBeforeAcl2]
		}
		return pickupDays
	}
	// 非extend流向以及旧extend逻辑流向使用return_days控制返回的天数

	todayHour := pickup.GetCurrentTime(ctx, pickupGroup.OriginRegion).Hour()
	firstPickupValue := pickupDays[0].Value
	// 检查第一个pickup day是否为今天，且是否有return_cutoff_hour配置
	if pickup.IsToday(ctx, firstPickupValue, pickupGroup.OriginRegion) && returnCutoffHour > 0 && todayHour >= int(returnCutoffHour) && returnCutoffHourDays > 0 {
		returnDays = returnCutoffHourDays
	}
	// 如果没有AdvanceDays，且returnDays不为0
	if advanceDays == 0 && returnDays > 0 && len(pickupDays) > int(returnDays) {
		pickupDays = pickupDays[:returnDays]
	}
	return pickupDays
}

func (p *PickupWindowCalculator) getOpenLogisticTimeslots(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupDays []*common_utils.PickupDay, isHaveTimeslot uint8, merchantType uint32, accountGroup uint32) ([]*common_utils.PickupDay, []*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, *lcos_error.LCOSError) {
	var lcosErr *lcos_error.LCOSError
	pickupTimeSlots := make([]*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, 0, 10)
	if len(pickupDays) == 0 {
		return pickupDays, pickupTimeSlots, nil
	}
	if isHaveTimeslot != constant.ENABLED {
		return pickupDays, pickupTimeSlots, nil
	}
	timeslots, lcosErr := p.openLogisticTimeslotDao.GetTimeslotsUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion, merchantType, accountGroup)
	if lcosErr != nil {
		return nil, nil, lcosErr
	}
	if len(timeslots) == 0 {
		return nil, nil, lcos_error.NewLCOSError(lcos_error.PickupConfErrorCode, fmt.Sprintf("pickup group was configged to have timeslot, but no timeslots were found|pickup_group_id=%s, destination_region=%s", pickupGroup.PickupGroupID, pickupGroup.DestinationRegion))
	}
	// 检查pickupDays中的第一项是否为今天
	firstDay := pickupDays[0].Value
	if pickup.IsToday(ctx, firstDay, pickupGroup.OriginRegion) {
		// 如果今天已经过了所有timeslot的cutoff hour，则需要截断今天
		if !pickup.CheckTodaySlot(ctx, timeslots, pickupGroup.OriginRegion) {
			pickupDays = pickupDays[1:]
		}
	}
	return pickupDays, timeslots, nil
}

func (p *PickupWindowCalculator) getSiteLineTimeslots(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupDays []*common_utils.PickupDay, isHaveTimeslot uint8, advanceDays uint32, returnDays uint32, returnCutoffHour uint32, returnCutoffHourDays uint32, extendDaysBeforeAcl2 uint32, buyerTimeslotID uint32, pickupConfId uint64) ([]*common_utils.PickupDay, []*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, *lcos_error.LCOSError) {
	var lcosErr *lcos_error.LCOSError
	pickupTimeSlots := make([]*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, 0, 10)
	if len(pickupDays) == 0 {
		return pickupDays, pickupTimeSlots, nil
	}
	if isHaveTimeslot != constant.ENABLED {
		returnPickupDays := p.getReturnPickupDays(ctx, pickupDays, advanceDays, returnDays, returnCutoffHour, returnCutoffHourDays, extendDaysBeforeAcl2, pickupGroup)
		return returnPickupDays, pickupTimeSlots, nil
	}

	timeslots, lcosErr := p.siteLineTimeslotDao.GetOpenLogisticPickupTimeslotsUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion)
	if lcosErr != nil {
		return nil, nil, lcosErr
	}
	if len(timeslots) == 0 {
		return nil, nil, lcos_error.NewLCOSError(lcos_error.PickupConfErrorCode, fmt.Sprintf("pickup group was configged to have timeslot, but no timeslots were found|pickup_group_id=%s, destination_region=%s", pickupGroup.PickupGroupID, pickupGroup.DestinationRegion))
	}

	returnedTimeslots := make([]*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, 0, len(timeslots))
	for _, timeslot := range timeslots {
		if timeslot.PickupConfId != pickupConfId {
			continue
		}
		returnedTimeslots = append(returnedTimeslots, timeslot)
	}
	// 检查pickupDays中的第一项是否为今天
	firstDay := pickupDays[0].Value
	if pickup.IsToday(ctx, firstDay, pickupGroup.OriginRegion) {
		// 如果今天已经过了所有timeslot的cutoff hour，则需要截断今天
		if !pickup.CheckTodaySlot(ctx, returnedTimeslots, pickupGroup.OriginRegion) {
			pickupDays = pickupDays[1:]
		}
	}
	return pickupDays, returnedTimeslots, nil
}

/*
将数据库获取的timeslot转为需要返回的结构体，当buyerTimeslotID不为0时，为uparcel的timeslot特殊逻辑
*/
func (p *PickupWindowCalculator) packReturnSlots(ctx context.Context, pickupDays []*common_utils.PickupDay, timeslots []*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, pickupGroup *pickup_group.PickupGroupTab, slotNum uint32, buyerTimeslotID uint32) ([]*common_utils.PickupDay, []*common_utils.TimeSlot) {
	var countNum uint32 = 0
	actualPickupDays := make([]*common_utils.PickupDay, 0, 10)
	pickupTimeSlots := make([]*common_utils.TimeSlot, 0, 10)
	for _, pickupDay := range pickupDays {
		isToday := pickup.IsToday(ctx, pickupDay.Value, pickupGroup.OriginRegion)

		var slots []*common_utils.PickupSlot
		var slotCountNum uint32
		// SPLN-17910, 渠道id为10014的特殊逻辑
		// SPLN-24112 delete special logic for product 10014
		if pickupGroup.PickupGroupID == "10016" && buyerTimeslotID != 0 { // uparcel的特殊逻辑
			// SPLN-17910的特殊逻辑，目前的情况是可选的timeslot需要和buyer_timeslot_id相同
			var uparcelTimeslots []*open_logistic_timeslot.OpenLogisticPickupTimeslotTab
			for _, _timeslot := range timeslots {
				if _timeslot.TimeslotValue == buyerTimeslotID {
					uparcelTimeslots = append(uparcelTimeslots, _timeslot)
				}
			}
			slots, slotCountNum = p.getSlots(ctx, countNum, slotNum, isToday, uparcelTimeslots, pickupGroup, pickupDay)
		} else {
			slots, slotCountNum = p.getSlots(ctx, countNum, slotNum, isToday, timeslots, pickupGroup, pickupDay)
		}

		if len(slots) == 0 {
			continue
		}
		pickupTimeslot := &common_utils.TimeSlot{
			Date:        pickupDay.Date,
			Value:       pickupDay.Value,
			PickupSlots: slots,
		}
		// 总的slot数不能超过slotNum
		if slotNum > 0 && slotCountNum > slotNum {
			break
		}
		pickupTimeSlots = append(pickupTimeSlots, pickupTimeslot)
		actualPickupDays = append(actualPickupDays, pickupDay)
		countNum = slotCountNum
	}
	return actualPickupDays, pickupTimeSlots
}

func (p *PickupWindowCalculator) GetSiteLineTimeSlots(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupDays []*common_utils.PickupDay, isHaveTimeslot uint8, isExtend bool, advanceDays uint32, returnDays uint32, returnCutoffHour uint32, returnCutoffHourDays uint32, extendDaysBeforeAcl2 uint32, slotNum uint32, extendSlotNum uint32, buyerTimeslotID uint32, pickupConfId uint64) ([]*common_utils.PickupDay, []*common_utils.TimeSlot, *lcos_error.LCOSError) {
	_pickupDays, timeslots, lcosErr := p.getSiteLineTimeslots(ctx, pickupGroup, pickupDays, isHaveTimeslot, advanceDays, returnDays, returnCutoffHour, returnCutoffHourDays, extendDaysBeforeAcl2, buyerTimeslotID, pickupConfId)
	if lcosErr != nil {
		return nil, nil, lcosErr
	}
	if isExtend && extendSlotNum != 0 {
		slotNum = extendSlotNum
	}
	if isHaveTimeslot != constant.ENABLED {
		return _pickupDays, make([]*common_utils.TimeSlot, 0), nil
	}

	_pickupDays = p.getReturnPickupDays(ctx, _pickupDays, advanceDays, returnDays, returnCutoffHour, returnCutoffHourDays, extendDaysBeforeAcl2, pickupGroup)

	actualPickupDays, actualTimeslots := p.packReturnSlots(ctx, _pickupDays, timeslots, pickupGroup, slotNum, buyerTimeslotID)
	return actualPickupDays, actualTimeslots, nil
}

func (p *PickupWindowCalculator) GetOpenLogisticTimeSlots(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupDays []*common_utils.PickupDay, merchantType uint32, accountGroup uint32, isHaveTimeslot uint8, slotNum uint32) ([]*common_utils.PickupDay, []*common_utils.TimeSlot, *lcos_error.LCOSError) {
	_pickupDays, timeslots, lcosErr := p.getOpenLogisticTimeslots(ctx, pickupGroup, pickupDays, isHaveTimeslot, merchantType, accountGroup)
	if lcosErr != nil {
		return nil, nil, lcosErr
	}
	if isHaveTimeslot != constant.ENABLED {
		return _pickupDays, make([]*common_utils.TimeSlot, 0), nil
	}
	actualPickupDays, actualTimeslots := p.packReturnSlots(ctx, _pickupDays, timeslots, pickupGroup, slotNum, 0)
	return actualPickupDays, actualTimeslots, nil
}

func (p *PickupWindowCalculator) PickupDaysControlLimit(ctx context.Context, pickupGroup *pickup_group.PickupGroupTab, pickupDays []*common_utils.PickupDay, volumes []*common_utils.Volume, dailyMinDaysExtend uint32, dailyControlStatus uint8, dailyControlBeginTime uint32, dailyControlEndTime uint32, dailyMaxVolume uint32) {
	// 将volume转为date->volume的map，方便查询
	volumeMap := map[string]uint32{}
	controlDays := 0
	timeLocation := pickup.GetTimeLocation(pickupGroup.OriginRegion)
	for _, volume := range volumes {
		realDatetimeString := ""
		// 做一步容错处理。日期格式可能是2006-01-06，也可能是********
		dateTime, err := time.ParseInLocation("2006-01-02", volume.Date, timeLocation)
		if err == nil {
			realDatetimeString = dateTime.Format("2006-01-02")
		} else {
			dateTime, err = time.ParseInLocation("20060102", volume.Date, timeLocation)
			if err == nil {
				realDatetimeString = dateTime.Format("2006-01-02")
			}
		}
		// 获取不到dateString则跳过
		if realDatetimeString == "" {
			logger.LogErrorf("[control_limit]date is not valid:[%s] for pickup_group:[%s]", volume.Date, pickupGroup.PickupGroupID)
			continue
		}
		volumeMap[realDatetimeString] = volume.Volume
	}
	for index, pickupDay := range pickupDays {
		controlLimit := p.getPickupDaysControlLimit(ctx, pickupGroup, volumeMap, pickupDay.Value, dailyControlStatus, dailyControlBeginTime, dailyControlEndTime, dailyMaxVolume)
		if controlLimit == pickup.VolumeControlLimit {
			controlDays += 1
		}
		pickupDays[index].VolumeLimit = controlLimit
	}
	// 如果所有的day都被容量限制
	if dailyMinDaysExtend > 0 && controlDays == len(pickupDays) {
		rangeBegin := 0
		rangeEnd := len(pickupDays)
		if int(dailyMinDaysExtend) < rangeEnd {
			rangeBegin = rangeEnd - int(dailyMinDaysExtend)
		}
		for index := range pickupDays[rangeBegin:rangeEnd] {
			pickupDays[rangeBegin+index].VolumeLimit = pickup.VolumeControlUnLimit
		}
	}
}

func (p *PickupWindowCalculator) getSlots(ctx context.Context, countNum uint32, slotNum uint32, isToday bool, timeSlots []*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, pickupGroup *pickup_group.PickupGroupTab, pickUpDay *common_utils.PickupDay) ([]*common_utils.PickupSlot, uint32) {
	var slots []*common_utils.PickupSlot
	for _, timeSlot := range timeSlots {
		if isToday && pickup.IsExceedSlotCutoffTime(ctx, timeSlot, pickupGroup.OriginRegion) {
			continue
		}
		if !isToday && pickup.IsNowTimeslot(timeSlot) {
			continue
		}
		timeLocation := pickup.GetTimeLocation(pickupGroup.OriginRegion)
		daytime := time.Unix(int64(pickUpDay.Value), 0).In(timeLocation)
		var startTime int64
		if pickup.IsNowTimeslot(timeSlot) {
			startTime = pickup.GetCurrentTime(ctx, pickupGroup.OriginRegion).Unix()
		} else {
			startTime = time.Date(daytime.Year(), daytime.Month(), daytime.Day(), int(timeSlot.StartTimeHour), int(timeSlot.StartTimeMin), int(timeSlot.StartTimeSecond), 0, timeLocation).Unix()
		}

		// check whether timeslot fits day
		intWeekDay := int(daytime.Weekday())
		if intWeekDay == 0 { // weekday return 0 as sunday, need to set to 7
			intWeekDay = 7
		}

		if !timeSlot.IsValidTimeslotForWeekday(intWeekDay) {
			continue
		}

		// SPLN-30142 check whether acl over current cutoff hour
		if p.checkIfNeedToCutoffTimeslots(ctx, pickupGroup.PickupGroupID, timeSlot, daytime, timeLocation) {
			continue
		}

		endTime := time.Date(daytime.Year(), daytime.Month(), daytime.Day(), int(timeSlot.EndTimeHour), int(timeSlot.EndTimeMin), int(timeSlot.EndTimeSecond), 0, timeLocation).Unix()
		s := &common_utils.PickupSlot{
			SlotTime:         timeSlot.TimeslotRemark,
			Value:            timeSlot.TimeslotValue,
			StartTime:        uint32(startTime),
			EndTime:          uint32(endTime),
			SlotStartHour:    timeSlot.SlotStartHour,
			SlotCutoffHour:   uint32(timeSlot.SlotCutoffHour),
			SlotCutoffMinute: timeSlot.SlotCutoffMin,
		}
		slots = append(slots, s)
		countNum++
		if slotNum > 0 && countNum >= slotNum {
			break
		}
	}
	return slots, countNum
}

func (p *PickupWindowCalculator) GetPickupTimeslots(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupTimeRangeID *uint32, merchantType uint32, accountGroup uint32) ([]*common_utils.TimeslotInfo, *lcos_error.LCOSError) {
	var timeslots []*open_logistic_timeslot.OpenLogisticPickupTimeslotTab
	var lcosErr *lcos_error.LCOSError
	switch p.clientType {
	case constant.LFS:
		timeslots, lcosErr = p.siteLineTimeslotDao.GetOpenLogisticPickupTimeslotsUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion)
	case constant.OPEN:
		timeslots, lcosErr = p.openLogisticTimeslotDao.GetTimeslotsUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion, merchantType, accountGroup)
	}
	if lcosErr != nil {
		return nil, lcosErr
	}

	if pickupTimeRangeID != nil && len(timeslots) < 1 {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAnyTimeslotErrorCode, fmt.Sprintf("cannot find any timeslots|pickup_group=%v", pickupGroup))
	}

	returnResults := make([]*common_utils.TimeslotInfo, 0, 10)
	for _, _timeslot := range timeslots {
		returnedResult := &common_utils.TimeslotInfo{
			OpenLogisticPickupTimeslotTab: *_timeslot,
			OriginRegion:                  pickupGroup.OriginRegion,
		}
		// 如果未传入pickup time range id，或者传入了，且其值和当前value相同
		if pickupTimeRangeID == nil || *pickupTimeRangeID == _timeslot.TimeslotValue {
			returnResults = append(returnResults, returnedResult)
		}
	}
	return returnResults, nil
}

/*
返回未来days的所有假期，如果days为0，则取days=DEFAULT_DAYS
*/
func (p *PickupWindowCalculator) GetHolidays(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, days int, stateLocationID int64, zipcode string) ([]string, *lcos_error.LCOSError) {
	if days == 0 {
		days = DEFAULT_DAYS
	}
	// 按照pickupGroup获取当前的时间
	now := pickup.GetCurrentTime(ctx, pickupGroup.OriginRegion)

	// 获取当前所有的假期，并将假期转为datetimeString-bool的map以供查询
	holidaysList, lcosErr := p.holidayDao.GetAllHolidaysUsingCache(ctx, pickupGroup, stateLocationID)
	if lcosErr != nil {
		holidaysList = make([]*holiday.LogisticHolidayTab, 0)
	}
	holidayMap := map[string]bool{}
	for _, _holiday := range holidaysList {
		holidayMap[_holiday.DateString] = true
	}

	// 获取当前的weekends，并将其转为map
	weekendsList, lcosErr := p.recurringHolidayDao.GetAllWeekendsUsingCache(ctx, stateLocationID, zipcode, pickupGroup)
	if lcosErr != nil {
		weekendsList = make([]*recurring_holiday.RecurringHolidayTab, 0)
	}
	weekendsMap := map[int]bool{}
	for _, _weekend := range weekendsList {
		weekendsMap[_weekend.WeekDay] = true
	}

	returnHolidays := make([]string, 0, DEFAULT_DAYS)
	// 遍历从现在到days的日期里面的所有时间，返回所有的假期
	for i := 0; i <= days; i++ {
		currentDay := pickup.AddDays(now, i)
		currentDayString := currentDay.Format("2006-01-02")
		if _, ok := holidayMap[currentDayString]; ok {
			returnHolidays = append(returnHolidays, currentDayString)
			continue
		}
		// 周日时间存的是0，但是数据库用的是7
		weekDay := int(currentDay.Weekday())
		if weekDay == 0 {
			weekDay = 7
		}
		if _, ok := weekendsMap[weekDay]; ok {
			returnHolidays = append(returnHolidays, currentDayString)
			continue
		}
	}
	return returnHolidays, nil
}

/*
 */
func (p *PickupWindowCalculator) GetPickupHolidays(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, stateLocationID int64, zipcode string) (*common_utils.ProductPickupDays, *lcos_error.LCOSError) {

	// 获取当前所有的假期，并将假期转为datetimeString-bool的map以供查询
	holidaysList, lcosErr := p.holidayDao.GetAllHolidaysUsingCache(ctx, pickupGroup, stateLocationID)
	if lcosErr != nil {
		holidaysList = make([]*holiday.LogisticHolidayTab, 0)
	}
	holidays := make([]string, 0)
	for _, _holiday := range holidaysList {
		holidays = append(holidays, _holiday.DateString)
	}
	// 获取当前的weekends，并将其转为map
	weekendsList, lcosErr := p.recurringHolidayDao.GetAllWeekendsUsingCache(ctx, stateLocationID, zipcode, pickupGroup)

	if lcosErr != nil {
		weekendsList = make([]*recurring_holiday.RecurringHolidayTab, 0)
	}
	weekends := make([]uint32, 0)
	for _, _weekend := range weekendsList {
		weekends = append(weekends, uint32(_weekend.WeekDay))
	}

	return &common_utils.ProductPickupDays{
		Status:                  0,
		PickupHolidays:          holidays,
		PickupRecurringHolidays: weekends,
	}, nil
}

// Deprecated
// SPLN-30142 & SPLN-36499 Rapid SLA逻辑
// Rapid SLA渠道的SBD为小时维度，触发extend逻辑前不允许返回end time晚于SBD的timeslot，触发extend逻辑后不允许返回end time晚于ACL2的timeslot
// checkCanExtendByRapidSlaTimeslots 判断Rapid SLA渠道使用SBD过滤timeslot后是否仍有可用的pickup day和timeslot，如果没有则需要触发extend逻辑
func (p *PickupWindowCalculator) checkCanExtendByRapidSlaTimeslots(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, pickupDays []*common_utils.PickupDay, pickupConf *pickup_config.PickupConfTab, buyerTimeslotID uint32) bool {
	extendFlag := false // default not to extend

	p.SetExtendCalculationFlag(true)
	defer func() {
		p.SetExtendCalculationFlag(false) // when finish extend calculation, need to reset extend calculation flag
	}()

	// pickup时间不支持timeslot维度，则不触发Rapid SLA逻辑
	if pickupConf.IsHaveTimeSlot == constant.FALSE {
		return false
	}
	// 非Rapid SLA渠道，不触发Rapid SLA逻辑
	if !p.IsRapidSla(ctx, pickupGroup.PickupGroupID) {
		return false
	}

	// 基于当前在SBD前的pickup day计算timeslot，同时以SBD时间戳作为截止时间过滤timeslot，判断是否仍有可用pickup day和timeslot
	var slotPickupConfId uint64
	if pickupConf.UseTimeslotType == pickup_window_constant.PickupConfTimeslot {
		// 使用pickup conf维度的timeslot
		slotPickupConfId = pickupConf.ID
	}
	returnedPickupDays, timeslots, lcosErr := p.GetSiteLineTimeSlots(ctx, pickupGroup, pickupDays, pickupConf.IsHaveTimeSlot, false, pickupConf.AdvanceDays, pickupConf.ReturnDays, pickupConf.ReturnCutoffHour, pickupConf.ReturnCutoffHourDays, pickupConf.ExtendDaysBeforeAcl2, pickupConf.SlotNum, pickupConf.ExtendSlotNum, buyerTimeslotID, slotPickupConfId)
	if lcosErr != nil {
		logger.CtxLogInfof(ctx, "cannot check can extend by timeslots, error=[%s]", lcosErr.Msg)
	} else if len(returnedPickupDays) == 0 && len(timeslots) == 0 {
		logger.CtxLogInfof(ctx, "need to extend pickup days because timeslots return empty|pickup_group_id=[%s], pickup_group_name=[%s]", pickupGroup.PickupGroupID, pickupGroup.PickupGroupName)
		extendFlag = true
	}
	return extendFlag
}

// SPLN-30142 & SPLN-36499
// checkIfNeedToCutoffTimeslots 检查是否需要截断timeslot。目前能截断timeslot的逻辑只有Rapid SLA
func (p *PickupWindowCalculator) checkIfNeedToCutoffTimeslots(ctx context.Context, pickupGroupID string, timeSlot *open_logistic_timeslot.OpenLogisticPickupTimeslotTab, daytime time.Time, timeLocation *time.Location) bool {
	if cutoffTime, isRapidSla := p.getRapidSlaTimeslotCutoffTime(ctx, pickupGroupID); isRapidSla {
		var slotEndTime time.Time
		if pickup.IsNowTimeslot(timeSlot) {
			slotEndTime = recorder.Now(ctx).Add(time.Hour) // Now的slot end time视为请求时间的一小时后。即如果当前时间在ACL2的一小时前，now仍然可选
		} else {
			slotEndTime = time.Date(daytime.Year(), daytime.Month(), daytime.Day(), int(timeSlot.EndTimeHour), int(timeSlot.EndTimeMin), int(timeSlot.EndTimeSecond), 0, timeLocation) // 实际上只有时和分，线上秒全部为0
		}
		if !cutoffTime.IsZero() && slotEndTime.After(cutoffTime) {
			return true
		}
	}
	return false
}

func (p *PickupWindowCalculator) SetShipByDatetime(shipByDate time.Time) {
	p.shipByDatetime = shipByDate
}

func (p *PickupWindowCalculator) SetEndShipDate(endShipDate time.Time) {
	p.endShipDatetime = endShipDate
}

func (p *PickupWindowCalculator) GetShipByDatetime() time.Time {
	return p.shipByDatetime
}

func (p *PickupWindowCalculator) GetEndShipDate() time.Time {
	return p.endShipDatetime
}

func (p *PickupWindowCalculator) GetExtendCalculationFlag() bool {
	return p.extendCalculationFlag
}

func (p *PickupWindowCalculator) SetExtendCalculationFlag(extendCalculationFlag bool) {
	p.extendCalculationFlag = extendCalculationFlag
}

func (p *PickupWindowCalculator) GetIsExtendFlag() bool {
	return p.isExtend
}

func (p *PickupWindowCalculator) SetIsExtendFlag(isExtend bool) {
	p.isExtend = isExtend
}

func (p *PickupWindowCalculator) GetIsExtendByACL2Flag() bool {
	return p.isExtendByACL2
}

func (p *PickupWindowCalculator) SetIsExtendByACL2Flag(isExtendByACL2 bool) {
	p.isExtendByACL2 = isExtendByACL2
}

func (p *PickupWindowCalculator) SetRapidSla(isRapidSla bool, acl2Time uint32) {
	p.isRapidSla = isRapidSla
	p.acl2Time = acl2Time
}

// IsRapidSla 是否为Rapid SLA渠道
func (p *PickupWindowCalculator) IsRapidSla(ctx context.Context, pickupGroupId string) bool {
	if !config.IsPickupWindowUseRapidSLA(ctx, pickupGroupId) {
		return false
	}

	// SPLN-30142：支持Rapid SLA的初版逻辑。是否为Rapid SLA为后端apollo控制，按pickup group维度配置ACL2与SBD的小时差，ACL2 = SBD + x hours
	// SPLN-36499：支持Rapid SLA的第二版逻辑。是否为Rapid SLA由LPS产品属性控制，Rapid SLA标识和ACL2通过LFS透传给LCOS
	_, ok := config.GetHoursAfterShipByDatetime(pickupGroupId)
	return ok || p.IsRapidSlaUseACL2()
}

func (p *PickupWindowCalculator) IsRapidSlaUseACL2() bool {
	return p.isRapidSla && p.acl2Time != 0
}

func (p *PickupWindowCalculator) GetAcl2DateTime() time.Time {
	return time.Unix(int64(p.acl2Time), 0)
}

// SPLN-30142 & SPLN-36499 Rapid SLA逻辑
// getRapidSlaTimeslotCutoffTime 获取Rapid SLA的timeslot截止时间，返回的timeslot end time不允许晚于这个时间
func (p *PickupWindowCalculator) getRapidSlaTimeslotCutoffTime(ctx context.Context, pickupGroupID string) (time.Time, bool) {
	if !p.IsRapidSla(ctx, pickupGroupID) {
		return time.Time{}, false
	}

	var cutoffTime time.Time
	if p.IsRapidSlaUseACL2() {
		cutoffTime = p.GetAcl2DateTime()
	} else {
		acl2Hour, _ := config.GetHoursAfterShipByDatetime(pickupGroupID)
		cutoffTime = p.GetShipByDatetime().Add(time.Duration(acl2Hour) * time.Hour)
	}
	return cutoffTime, true
}
