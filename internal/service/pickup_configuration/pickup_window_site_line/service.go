package pickup_window_site_line

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/pickup_window_constant"
	"golang.org/x/net/context"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_window_calculator"
)

const clientType = constant.LFS

// 点线pickup window的grpc service
type PickupWindowSiteLineService struct {
	pickupGroupDao     pickup_group.PickupGroupDAO
	pickupConfDao      pickup_config.PickupConfigurationDAO
	serviceableConfDao basic_conf.LineBasicServiceableConfDAO
	validator          common_utils.PickupWindowValidatorInterface
	calculator         pickup_window_calculator.PickupWindowCalculatorInterface
}

func NewPickupWindowSiteLineService() *PickupWindowSiteLineService {
	return &PickupWindowSiteLineService{
		pickupGroupDao:     pickup_group.NewPickupGroupDAO(),
		pickupConfDao:      pickup_config.NewPickupConfigurationDAO(),
		serviceableConfDao: basic_conf.NewLineBasicServiceableConfDAO(),
		validator:          common_utils.NewPickupWindowValidator(clientType),
		calculator:         pickup_window_calculator.NewPickupWindowCalculator(clientType),
	}
}

// 获取特殊的pickupType和cutoff hour，目前仅按照shop的维度来判断
func (p *PickupWindowSiteLineService) getSpecialPickupTypeAndCutoffHour(specialSettings []*pickup_config.PickupConfSpecialSettingsTab, clientGroupIDList []string) (uint8, uint8, uint32, uint8) {
	for _, setting := range specialSettings {
		switch setting.SpecialPickupCriteria {
		case constant.CriteriaByShop: // 目前默认全部是by shop
			for _, clientGroupID := range clientGroupIDList {
				if clientGroupID == setting.SpecialShopGroup {
					return setting.SpecialPickupType, setting.ApplyForDayType, setting.SpecialCutoffHour, setting.ApplyForCutoffHour
				}
			}
		}
	}
	return 0, 0, 0, 0
}

// 通过lineID列表获取pickup group，并检查是否满足pickup条件
func (p *PickupWindowSiteLineService) getValidPickupGroupByLineIDList(ctx utils.LCOSContext, lineIDList []string) (*pickup_group.PickupGroupTab, *lcos_error.LCOSError) {
	// 获取pickup group
	pickupGroup, lcosErr := p.pickupGroupDao.GetPickupGroupByLineIDListUsingCache(ctx, lineIDList, clientType)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 获取line basic conf用于校验pickup
	lineServiceableConfMap, lcosErr := p.serviceableConfDao.GetBasicServiceableConfModelMapByLineIdsUseCache(ctx, lineIDList)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// pickup 条件校验
	lcosErr = p.validator.GeneralCheck(ctx, lineIDList, pickupGroup, lineServiceableConfMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return pickupGroup, nil
}

func (p *PickupWindowSiteLineService) GetArrangedPickupDays(ctx utils.LCOSContext, request interface{}) (*common_utils.PickupDays, *lcos_error.LCOSError) {
	req := request.(*common_utils.ArrangedPickupDaysRequest)

	lineIDList := req.LineIdList
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, lineIDList)
	if lcosErr != nil {
		// SPLN-23044 仓库单走兜底逻辑
		if req.IsB2C {
			return p.GetPickUpTimeslotForB2CWithoutPickUpGroup(ctx, req.Region)
		}
		return nil, lcosErr
	}

	// 获取pickup window配置
	// 切换开关
	// - 切换前：需要读取special setting和special cutoff hour中的shop group特殊数据
	// - 切换后：直接读取shop group维度的pickup window配置
	useShopGroupPickupWindow := config.IsUseShopGroupPickupConf(ctx, pickupGroup.DestinationRegion, pickupGroup.PickupGroupID)

	var shopGroupList []string
	if useShopGroupPickupWindow {
		shopGroupList = req.ClientGroupIDList
	}
	pickupConf, lcosErr := p.pickupConfDao.GetPickupConfigUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion, shopGroupList)
	if lcosErr != nil {
		// SPLN-23044 仓库单走兜底逻辑
		if req.IsB2C {
			return p.GetPickUpTimeslotForB2CWithoutPickUpGroup(ctx, req.Region)
		}
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcosErr
	}

	if !useShopGroupPickupWindow {
		// 检查是否存在特殊配置，如果有的话，需要更新pickupGroup.PickupType和pickupConf.PickupCutoffHour
		specialSettings, _ := p.pickupConfDao.GetPickupSpecialSettingsByPickupConfIDUsingCache(ctx, pickupConf.ID)
		if len(specialSettings) > 0 {
			specialPickupType, applyForDayType, specialCutoffHour, applyForCutoffHour := p.getSpecialPickupTypeAndCutoffHour(specialSettings, req.ClientGroupIDList)
			if applyForDayType == constant.TRUE {
				pickupGroup.PickupType = specialPickupType // pickupGroup和pickupConf已经经过deepcopy，不会修改内存中存储的数据
			}
			if applyForCutoffHour == constant.TRUE {
				pickupConf.PickupCutoffHour = specialCutoffHour
			}
		}

		// SPLN-32920
		specialCutoffHour, ok := config.GetPickupSpecialCutoffHour(ctx, pickupGroup.PickupGroupID, req.ClientGroupIDList)
		if ok {
			pickupConf.PickupCutoffHour = specialCutoffHour
		}
	}

	logger.CtxLogInfof(ctx, "using pickup_type:%d, cutoff hour:%d for pickup group id:[%s]", pickupGroup.PickupType, pickupConf.PickupCutoffHour, pickupGroup.PickupGroupID)

	// 获取pickup days
	orderTime := &common_utils.OrderTime{
		ReleaseTime:   req.ReleaseTime,
		PayTime:       req.PayTime,
		DaysToShip:    int(req.DaysToShip),
		IsReturnOrder: false,
	}
	pickupDays, isExtendFlag, lcosErr := p.calculator.CalculatePickupDaysWithExtends(ctx, pickupGroup, orderTime, req.ShipByDatetime, req.Acl2Time, int64(req.StateLocationId), req.Zipcode, req.BuyerTimeslotID, pickupConf, req.IsChangePickupDate, req.IsRapidSla)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcosErr
	}

	// 计算timeslots
	var slotPickupConfId uint64
	if pickupConf.UseTimeslotType == pickup_window_constant.PickupConfTimeslot {
		// 使用pickup conf维度的timeslot
		slotPickupConfId = pickupConf.ID
	}
	returnPickupDays, returnTimeslots, lcosErr := p.calculator.GetSiteLineTimeSlots(ctx, pickupGroup, pickupDays, pickupConf.IsHaveTimeSlot, isExtendFlag, pickupConf.AdvanceDays, pickupConf.ReturnDays, pickupConf.ReturnCutoffHour, pickupConf.ReturnCutoffHourDays, pickupConf.ExtendDaysBeforeAcl2, pickupConf.SlotNum, pickupConf.ExtendSlotNum, req.BuyerTimeslotID, slotPickupConfId)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcosErr
	}

	//在所有计算都结束后，如果获取的pickup days仍为0，则报错
	if len(returnPickupDays) == 0 {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcos_error.NotGetPickupDaysErrorCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcos_error.NewLCOSError(lcos_error.NotGetPickupDaysErrorCode, fmt.Sprintf("No pickup time is available with extend pickup date to escrow| pickup_group_id=%s", pickupGroup.PickupGroupID))
	}

	// 容量限制
	p.calculator.PickupDaysControlLimit(ctx, pickupGroup, returnPickupDays, req.Volumes, pickupConf.DailyMinDaysExtend, pickupConf.DailyControlStatus, pickupConf.DailyControlBeginTime, pickupConf.DailyControlEndTime, pickupConf.DailyMaxVolume)

	// SPLN-23044 仓库单只返回第一个有效的timeslot
	if req.IsB2C {
		returnPickupDays, returnTimeslots = p.getFirstTimeSlotForB2C(returnPickupDays, returnTimeslots)
	}

	// 封装返回结果
	actualPickupDays := &common_utils.PickupDays{
		Days:             returnPickupDays,
		Timeslots:        returnTimeslots,
		PickupGroupId:    pickupGroup.PickupGroupID,
		PickupCutoffHour: pickupConf.PickupCutoffHour,
	}

	_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(0), "pickup_group": pickupGroup.PickupGroupID})
	return actualPickupDays, nil
}

func (p *PickupWindowSiteLineService) GetPickUpTimeslotForB2CWithoutPickUpGroup(ctx context.Context, region string) (*common_utils.PickupDays, *lcos_error.LCOSError) {
	timeFormat := "2006-01-02" // 日期格式
	var pickup_hour int = 15
	if conf_pickup, ok := config.GetMutableConf(ctx).B2CPickupTime[strings.ToUpper(region)]; ok {
		pickup_hour = conf_pickup
	}
	cutoff_hour := pickup_hour - 2
	now := pickup.GetCurrentTime(ctx, region)
	location := pickup.GetTimeLocation(region)
	var timeDate time.Time
	if now.Hour() < cutoff_hour {
		timeDate = time.Date(now.Year(), now.Month(), now.Day(), pickup.DefaultHour, pickup.DefaultMinute, pickup.DefaultSecond, pickup.DefaultNSecond, location)
	} else {
		t := now.Add(time.Hour * 24)
		timeDate = time.Date(t.Year(), t.Month(), t.Day(), pickup.DefaultHour, pickup.DefaultMinute, pickup.DefaultSecond, pickup.DefaultNSecond, location)
	}

	slotTime := fmt.Sprintf("%d:00-%d:00", pickup_hour, pickup_hour+1)
	startTime := time.Date(timeDate.Year(), timeDate.Month(), timeDate.Day(), pickup_hour, 0, 0, 0, location).Unix()
	endTime := time.Date(timeDate.Year(), timeDate.Month(), timeDate.Day(), pickup_hour+1, 0, 0, 0, location).Unix()

	day := common_utils.PickupDay{
		Date:        timeDate.Format(timeFormat),
		Value:       uint32(timeDate.Unix()),
		VolumeLimit: 0,
	}

	s := common_utils.PickupSlot{
		Value:     1,
		SlotTime:  slotTime,
		StartTime: uint32(startTime),
		EndTime:   uint32(endTime),
	}

	slot := common_utils.TimeSlot{
		Date:        day.Date,
		Value:       day.Value,
		PickupSlots: []*common_utils.PickupSlot{&s},
	}

	result := &common_utils.PickupDays{
		Days:          []*common_utils.PickupDay{&day},
		Timeslots:     []*common_utils.TimeSlot{&slot},
		PickupGroupId: "B2C default",
	}

	return result, nil

}

func (p *PickupWindowSiteLineService) getFirstTimeSlotForB2C(days []*common_utils.PickupDay, slots []*common_utils.TimeSlot) ([]*common_utils.PickupDay, []*common_utils.TimeSlot) {
	var d []*common_utils.PickupDay = nil
	var s []*common_utils.TimeSlot = nil
	if len(days) >= 1 {
		d = days[:1]
	}
	if len(slots) >= 1 {
		s = slots[:1]
		if len(s[0].PickupSlots) >= 1 {
			s[0].PickupSlots = s[0].PickupSlots[:1]
		}
	}

	if s == nil && d != nil {
		// 没配置timeslot，补充一个
		pday := d[0]
		ps := &common_utils.PickupSlot{
			Value:     1,
			SlotTime:  "default",
			StartTime: pday.Value,
			EndTime:   pday.Value + 3600,
		}
		ts := &common_utils.TimeSlot{
			Date:        pday.Date,
			Value:       pday.Value,
			PickupSlots: []*common_utils.PickupSlot{ps},
		}
		s = []*common_utils.TimeSlot{ts}
	}
	return d, s
}

func (p *PickupWindowSiteLineService) GetReturnedPickupDays(ctx utils.LCOSContext, request interface{}) (*common_utils.PickupDays, *lcos_error.LCOSError) {
	req := request.(*common_utils.ReturnPickupDaysRequest)
	lineIDList := req.LineIdList

	// 获取pickup group
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, lineIDList)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 获取pickup Conf
	pickupConf, lcosErr := p.pickupConfDao.GetPickupConfigUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion, nil)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcosErr
	}

	// 封装order time
	orderTime := &common_utils.OrderTime{
		IsReturnOrder: true,
	}

	// 计算ship_by_date
	now := pickup.GetCurrentTime(ctx, pickupGroup.OriginRegion)
	shipByDate := pickup.AddDays(now, int(req.ShipByDays))
	shipByDatetime := uint32(shipByDate.Unix())
	// RR场景，ACL2 = Now + 2*shipByDays
	acl2Date := pickup.AddDays(shipByDate, int(req.ShipByDays))
	acl2Datetime := uint32(acl2Date.Unix())

	// 获取pickup days
	pickupDays, isExtendFlag, lcosErr := p.calculator.CalculatePickupDaysWithExtends(ctx, pickupGroup, orderTime, shipByDatetime, acl2Datetime, int64(req.StateLocationId), req.Zipcode, 0, pickupConf, false, false)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcosErr
	}

	// 计算timeslots
	var slotPickupConfId uint64
	if pickupConf.UseTimeslotType == pickup_window_constant.PickupConfTimeslot {
		// 使用pickup conf维度的timeslot
		slotPickupConfId = pickupConf.ID
	}
	returnPickupDays, returnTimeslots, lcosErr := p.calculator.GetSiteLineTimeSlots(ctx, pickupGroup, pickupDays, pickupConf.IsHaveTimeSlot, isExtendFlag, pickupConf.AdvanceDays, pickupConf.ReturnDays, pickupConf.ReturnCutoffHour, pickupConf.ReturnCutoffHourDays, pickupConf.ExtendDaysBeforeAcl2, pickupConf.SlotNum, pickupConf.ExtendSlotNum, 0, slotPickupConfId)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcosErr
	}

	//在所有计算都结束后，如果获取的pickup days仍为0，则报错
	if len(returnPickupDays) == 0 {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcos_error.NotGetPickupDaysErrorCode)), "pickup_group": pickupGroup.PickupGroupID})
		return nil, lcos_error.NewLCOSError(lcos_error.NotGetPickupDaysErrorCode, fmt.Sprintf("No pickup time is available with extend pickup date to escrow| pickup_group_id=%s", pickupGroup.PickupGroupID))
	}

	actualPickupDays := &common_utils.PickupDays{
		Days:             returnPickupDays,
		Timeslots:        returnTimeslots,
		PickupGroupId:    pickupGroup.PickupGroupID,
		PickupCutoffHour: pickupConf.PickupCutoffHour,
	}
	_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(0), "pickup_group": pickupGroup.PickupGroupID})
	return actualPickupDays, nil
}

func (p *PickupWindowSiteLineService) CheckPickupTime(ctx utils.LCOSContext, request interface{}) *lcos_error.LCOSError {
	req := request.(*common_utils.CheckSiteLinePickupTimeRequest)
	lineIDList := req.LineIdList

	// 获取pickup group
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, lineIDList)
	if lcosErr != nil {
		return lcosErr
	}

	// 获取配置
	// 切换开关
	// - 切换前：需要读取special setting和special cutoff hour中的shop group特殊数据
	// - 切换后：直接读取shop group维度的pickup window配置
	useShopGroupPickupWindow := config.IsUseShopGroupPickupConf(ctx, pickupGroup.DestinationRegion, pickupGroup.PickupGroupID)

	var shopGroupList []string
	if useShopGroupPickupWindow {
		shopGroupList = req.ClientGroupIDList
	}
	pickupConf, lcosErr := p.pickupConfDao.GetPickupConfigUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion, shopGroupList)
	if lcosErr != nil {
		_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "pickup_group": pickupGroup.PickupGroupID})
		return lcosErr
	}

	if !useShopGroupPickupWindow {
		// 检查是否存在特殊配置，如果有的话，需要更新pickupGroup.PickupType和pickupConf.PickupCutoffHour
		specialSettings, _ := p.pickupConfDao.GetPickupSpecialSettingsByPickupConfIDUsingCache(ctx, pickupConf.ID)
		if len(specialSettings) > 0 {
			specialPickupType, applyForDayType, specialCutoffHour, applyForCutoffHour := p.getSpecialPickupTypeAndCutoffHour(specialSettings, req.ClientGroupIDList)
			if applyForDayType == constant.TRUE {
				pickupGroup.PickupType = specialPickupType // pickupGroup和pickupConf已经经过deepcopy，不会修改内存中存储的数据
			}
			if applyForCutoffHour == constant.TRUE {
				pickupConf.PickupCutoffHour = specialCutoffHour
			}
		}

		// SPLN-32920
		specialCutoffHour, ok := config.GetPickupSpecialCutoffHour(ctx, pickupGroup.PickupGroupID, req.ClientGroupIDList)
		if ok {
			pickupConf.PickupCutoffHour = specialCutoffHour
		}
	}

	logger.CtxLogInfof(ctx, "using pickup_type:%d, cutoff hour:%d for pickup group id:[%s]", pickupGroup.PickupType, pickupConf.PickupCutoffHour, pickupGroup.PickupGroupID)

	// 校验pickup time是否合法
	lcosErr = p.calculator.CheckPickupTime(ctx, pickupGroup, req.PickupTime, req.StateLocationId, req.Zipcode, pickupConf.PickupCutoffHour, pickupConf.IsNonWorkingDayPickupAllowed)
	reportCode := 0
	if lcosErr != nil {
		reportCode = int(lcosErr.RetCode)
	}
	_ = metrics.CounterIncr(constant.MetricPickupGroupStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(reportCode), "pickup_group": pickupGroup.PickupGroupID})

	return lcosErr
}

func (p *PickupWindowSiteLineService) GetPickupTimeslots(ctx utils.LCOSContext, request interface{}) ([]*common_utils.TimeslotInfo, *lcos_error.LCOSError) {
	req := request.(*common_utils.GetSiteLinePickupTimeslotsRequest)

	// 参数校验
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, req.LineIdList)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 获取timeslots
	timeslots, lcosErr := p.calculator.GetPickupTimeslots(ctx, pickupGroup, req.PickupTimeRangeId, 0, 0)

	if lcosErr != nil {
		return nil, lcosErr
	}
	return timeslots, nil
}

func (p *PickupWindowSiteLineService) GetPickupTimeslot(ctx utils.LCOSContext, request interface{}) (*common_utils.SingleTimeslotInfo, *lcos_error.LCOSError) {
	req := request.(*common_utils.GetSingleSiteLinePickupTimeslotsRequest)

	// 参数校验
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, req.LineIdList)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 获取timeslots
	timeslots, lcosErr := p.calculator.GetPickupTimeslots(ctx, pickupGroup, &req.PickupTimeRangeId, 0, 0)

	if lcosErr != nil {
		return nil, lcosErr
	}

	if len(timeslots) < 1 {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundTimeslotErrorCode, fmt.Sprintf("cannot find timeslots|line_ids=%v", req.LineIdList))
	}

	returnTimeslot := &common_utils.SingleTimeslotInfo{
		StartHour:   timeslots[0].StartTimeHour,
		StartMinute: timeslots[0].StartTimeMin,
		StartSecond: timeslots[0].StartTimeSecond,
		EndHour:     timeslots[0].EndTimeHour,
		EndMinute:   timeslots[0].EndTimeMin,
		EndSecond:   timeslots[0].EndTimeSecond,
	}
	// 检查当前的pickup time range是否为0，是的话，需要将timeslot的开始时间设置为当前时间
	if pickup.IsNowTimeslot(timeslots[0]) {
		nowTime := pickup.GetCurrentTime(ctx, pickupGroup.OriginRegion)
		returnTimeslot.StartHour = uint32(nowTime.Hour())
		returnTimeslot.StartMinute = uint32(nowTime.Minute())
		returnTimeslot.StartSecond = uint32(nowTime.Second())
	}

	return returnTimeslot, nil
}

func (p *PickupWindowSiteLineService) GetAllPickupGroups(ctx utils.LCOSContext) ([]*common_utils.PickupGroup, *lcos_error.LCOSError) {
	groups, lcosErr := p.pickupGroupDao.GetPickupGroupByClientTypeUsingCache(ctx, clientType)
	if lcosErr != nil {
		return nil, lcosErr
	}
	sort.Slice(groups, func(i, j int) bool {
		return groups[i].ID < groups[j].ID
	})

	lineIDMap, lcosErr := p.pickupGroupDao.GetLineIDsByGroupsUsingCache(ctx, groups)
	if lcosErr != nil {
		return nil, lcosErr
	}

	returnedPickupGroups := make([]*common_utils.PickupGroup, 0, len(groups))
	for _, item := range groups {
		lineIDs, ok := lineIDMap[item]
		if !ok {
			continue
		}
		// 重排 lineIds 顺序，避免流量回放差异
		sort.Strings(lineIDs)
		pickupType := uint32(item.PickupType)
		returnedPickupGroups = append(returnedPickupGroups, &common_utils.PickupGroup{
			PickupGroupId:     item.PickupGroupID,
			PickupGroupName:   item.PickupGroupName,
			OriginRegion:      item.OriginRegion,
			DestinationRegion: item.DestinationRegion,
			PickupType:        pickupType,
			LineIdList:        lineIDs,
			Ctime:             item.CTime,
			Mtime:             item.MTime,
		})
	}
	return returnedPickupGroups, nil
}

func (p *PickupWindowSiteLineService) GetHolidays(ctx utils.LCOSContext, request interface{}) ([]string, *lcos_error.LCOSError) {
	req := request.(*common_utils.GetHolidaysRequest)
	emptyResult := make([]string, 0)
	// 参数校验
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, req.LineIdList)
	if lcosErr != nil {
		return emptyResult, nil
	}

	// 计算假期
	holidays, lcosErr := p.calculator.GetHolidays(ctx, pickupGroup, req.Days, req.StateLocationID, req.Zipcode)
	if lcosErr != nil {
		return emptyResult, lcosErr
	}

	return holidays, nil

}

func (p *PickupWindowSiteLineService) GetValidPickupConf(ctx utils.LCOSContext, request interface{}) (*common_utils.PickupConf, *lcos_error.LCOSError) {
	req := request.(*common_utils.GetValidPickupConfRequest)
	pickupGroup, lcosErr := p.getValidPickupGroupByLineIDList(ctx, req.LineIdList)
	if lcosErr != nil {
		return nil, lcosErr
	}
	pickupConf, lcosErr := p.pickupConfDao.GetPickupConfigUsingCache(ctx, pickupGroup.PickupGroupID, pickupGroup.DestinationRegion, nil)
	if lcosErr != nil || pickupConf == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("cannot find pickup group|pickup_group_id=%s, destination_region=%s", pickupGroup.PickupGroupID, pickupGroup.DestinationRegion))
	}
	return &common_utils.PickupConf{
		PickupConfTab: *pickupConf,
	}, nil
}

func (p *PickupWindowSiteLineService) GetPickupHolidays(ctx utils.LCOSContext, request interface{}) (*common_utils.ProductPickupDays, *lcos_error.LCOSError) {
	req := request.(*common_utils.GetPickupHolidaysRequest)
	// 过滤下能够pickup的line
	lineServiceableConfMap, lcosErr := p.serviceableConfDao.GetBasicServiceableConfModelMapByLineIdsUseCache(ctx, req.LineIdList)
	if len(lineServiceableConfMap) == 0 || lcosErr != nil {
		return &common_utils.ProductPickupDays{
			Status:                  0,
			PickupHolidays:          nil,
			PickupRecurringHolidays: nil,
		}, nil
	}
	pickupLines := make([]string, 0)
	for _, lineID := range req.LineIdList {
		// 检查入参的line需要支持pickup，对于grpc和admin接口来说，访问的路径不同
		var serviceableConfTab *basic_conf.LineBasicServiceableConfTab
		if _, ok := lineServiceableConfMap[lineID]; ok {
			serviceableConfTab = lineServiceableConfMap[lineID]

			if serviceableConfTab != nil && (serviceableConfTab.CollectDeliverAbility&uint32(constant.PICKUP) != 0) {
				pickupLines = append(pickupLines, lineID)
			}
		}
	}
	if len(pickupLines) == 0 {
		return &common_utils.ProductPickupDays{
			Status:                  0,
			PickupHolidays:          nil,
			PickupRecurringHolidays: nil,
		}, nil
	}
	IDs, groupIDs, lcosError := p.pickupGroupDao.GetPickupGroupIDsByLineIDListUsingCache(ctx, pickupLines, clientType)
	if lcosError != nil {
		return nil, lcosError
	}
	// 表示数据一个没有
	if len(IDs) == 0 {
		return &common_utils.ProductPickupDays{
			Status:                  0,
			PickupHolidays:          nil,
			PickupRecurringHolidays: nil,
		}, nil
	}
	// 只映射一个groupid
	if len(IDs) == len(pickupLines) && len(groupIDs) == 1 {
		return p.calculator.GetPickupHolidays(ctx, groupIDs[0], req.StateLocationID, req.Zipcode)
	} else {
		// 返回特定码给lps lps调用自身的逻辑
		return &common_utils.ProductPickupDays{
			Status:                  1,
			PickupHolidays:          nil,
			PickupRecurringHolidays: nil,
		}, nil
	}
}
