package area_route_serviceable

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/jinzhu/copier"
)

/*
* @Author: yajun.han
* @Date: 2020/8/5 12:12 上午
* @Name：area_route_serviceable
* @Description:
 */

type LineServiceableAreaServiceInterface interface {
	Create(ctx utils.LCOSContext, request *area_route_serviceable.CreateAreaRequest) (*fullAreaResponse, *lcos_error.LCOSError)
	Delete(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	GetByID(ctx utils.LCOSContext, request *common_protocol.IDRequest) (*fullAreaResponse, *lcos_error.LCOSError)
	List(ctx utils.LCOSContext, request *area_route_serviceable.SearchAreaRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAll(ctx utils.LCOSContext, request *area_route_serviceable.GetAllAreaRequest) ([]*model.LogisticLineServiceableAreaTab, *lcos_error.LCOSError)
	ListAreaRef(ctx utils.LCOSContext, request *area_route_serviceable.SearchAreaLocationRefRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllAreaRef(ctx utils.LCOSContext, request *area_route_serviceable.GetAllAreaLocationRefRequest) ([]*area_location_ref.LogisticLineServiceableAreaLocationRefTab, *lcos_error.LCOSError)
	DeleteAreaRef(ctx utils.LCOSContext, request *area_route_serviceable.DeleteAreaLocationRef) *lcos_error.LCOSError
	UploadAreaRef(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
}

type LineServiceableAreaService struct {
	lineServiceableAreaDAO model.LogisticLineServiceableAreaTabDAO
	areaLocationDao        area_location_ref.LogisticLineServiceableAreaLocationRefTabDAO
}

func NewLineServiceableAreaService(lineServiceableAreaDAO model.LogisticLineServiceableAreaTabDAO, areaLocationDao area_location_ref.LogisticLineServiceableAreaLocationRefTabDAO) *LineServiceableAreaService {
	return &LineServiceableAreaService{
		lineServiceableAreaDAO: lineServiceableAreaDAO,
		areaLocationDao:        areaLocationDao,
	}
}

func (s *LineServiceableAreaService) Create(ctx utils.LCOSContext, request *area_route_serviceable.CreateAreaRequest) (*fullAreaResponse, *lcos_error.LCOSError) {
	areaModel := new(model.LogisticLineServiceableAreaTab)
	refModels := make([]*area_location_ref.LogisticLineServiceableAreaLocationRefTab, len(request.LocationIds))
	_ = copier.Copy(areaModel, request)
	fc := func() *lcos_error.LCOSError {
		if _, err := s.lineServiceableAreaDAO.CreateLogisticLineServiceableAreaTab(ctx, areaModel); err != nil {
			return err
		}
		if err := fillAreaLocationRefModel(ctx, refModels, request, areaModel.ID); err != nil {
			return err
		}
		for _, refModel := range refModels {
			if _, err := s.areaLocationDao.CreateLogisticLineServiceableAreaLocationRefTab(ctx, refModel); err != nil {
				return err
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return &fullAreaResponse{
		LogisticLineServiceableAreaTab: areaModel,
		Locations:                      refModels,
	}, nil
}

func (s *LineServiceableAreaService) Delete(c utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	fc := func() *lcos_error.LCOSError {
		if err := s.lineServiceableAreaDAO.DeleteLogisticLineServiceableAreaTabsById(c, request.ID); err != nil {
			return err
		}
		if err := s.areaLocationDao.DeleteLogisticLineServiceableAreaLocationRefTabsByAreaId(c, request.ID); err != nil {
			return err
		}
		return nil
	}
	if err := c.Transaction(fc); err != nil {
		return err
	}
	return nil
}

func (s *LineServiceableAreaService) GetByID(c utils.LCOSContext, request *common_protocol.IDRequest) (*fullAreaResponse, *lcos_error.LCOSError) {
	areaModel, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(c, request.ID)
	if err != nil {
		return nil, err
	}
	if areaModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundAreaErrorCode)
	}
	refModels, err := s.areaLocationDao.GetLogisticLineServiceableAreaLocationRefTabByAreaId(c, request.ID)
	if err != nil {
		return nil, err
	}
	return &fullAreaResponse{
		LogisticLineServiceableAreaTab: areaModel,
		Locations:                      refModels,
	}, nil
}

func (s *LineServiceableAreaService) List(c utils.LCOSContext, request *area_route_serviceable.SearchAreaRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	paramMap, _ := utils.Struct2map(request)
	list, total, lcosError := s.lineServiceableAreaDAO.SearchLogisticLineServiceableAreaTab(c, pageNo, count, paramMap)
	return &common.PageModel{
		PageNO: pageNo,
		Count:  count,
		Total:  total,
		List:   list,
	}, lcosError
}

func (s *LineServiceableAreaService) GetAll(c utils.LCOSContext, request *area_route_serviceable.GetAllAreaRequest) ([]*model.LogisticLineServiceableAreaTab, *lcos_error.LCOSError) {
	return s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabByRegion(c, request.Region)
}

func (s *LineServiceableAreaService) ListAreaRef(c utils.LCOSContext, request *area_route_serviceable.SearchAreaLocationRefRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	paramMap, _ := utils.Struct2map(request)
	list, total, lcosError := s.areaLocationDao.SearchLogisticLineServiceableAreaLocationRefTab(c, pageNo, count, paramMap)
	if lcosError != nil {
		return nil, lcosError
	}

	return &common.PageModel{
		PageNO: pageNo,
		Count:  count,
		Total:  total,
		List:   list,
	}, nil
}

func (s *LineServiceableAreaService) GetAllAreaRef(c utils.LCOSContext, request *area_route_serviceable.GetAllAreaLocationRefRequest) ([]*area_location_ref.LogisticLineServiceableAreaLocationRefTab, *lcos_error.LCOSError) {
	paramMap, _ := utils.Struct2map(request)
	list, lcosError := s.areaLocationDao.SearchAllLogisticLineServiceableAreaLocationRefTab(c, paramMap)
	if lcosError != nil {
		return nil, lcosError
	}

	return list, nil
}

func (s *LineServiceableAreaService) DeleteAreaRef(c utils.LCOSContext, request *area_route_serviceable.DeleteAreaLocationRef) *lcos_error.LCOSError {
	fc := func() *lcos_error.LCOSError {
		if err := s.areaLocationDao.DeleteLogisticLineServiceableAreaLocationRefTabById(c, request.ID); err != nil {
			return err
		}
		models, err := s.areaLocationDao.GetLogisticLineServiceableAreaLocationRefTabByAreaId(c, request.AreaId)
		if err != nil {
			return err
		}
		if len(models) == 0 {
			if err := s.lineServiceableAreaDAO.DeleteLogisticLineServiceableAreaTabsById(c, request.AreaId); err != nil {
				return err
			}
		}
		return nil
	}
	if err := c.Transaction(fc); err != nil {
		return err
	}
	return nil
}

func (s *LineServiceableAreaService) UploadAreaRef(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_area.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	wg := sync.WaitGroup{}
	var errList []*lcos_error.LCOSError
	var addAreaLocationRefModels []*area_location_ref.LogisticLineServiceableAreaLocationRefTab
	var deleteAreaLocationRefModels []*area_location_ref.LogisticLineServiceableAreaLocationRefTab

	areaNameList := []string{}
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		if err := serviceable_util.CheckAreaRefData(row, lineNum); err != nil {
			return err
		}

		areaNameList = append(areaNameList, row[0])

		wg.Add(1)
		go func(rowData []string) {
			locationInfo, err := ops_service.GetLocationInfoByLocationName(ctx, serviceable_util.GetLocationRequestByAreaFileRow(row, request.Region))
			if err != nil {
				errList = append(errList, err)
			}

			AreaLocationRefModel := &area_location_ref.LogisticLineServiceableAreaLocationRefTab{
				AreaName:   rowData[0],
				Region:     request.Region,
				LocationID: uint64(locationInfo.LocationId),
				State:      rowData[1],
				City:       rowData[2],
				District:   rowData[3],
			}

			actionCode, _ := strconv.Atoi(rowData[4])
			if actionCode != -1 {
				addAreaLocationRefModels = append(addAreaLocationRefModels, AreaLocationRefModel)
			} else {
				deleteAreaLocationRefModels = append(deleteAreaLocationRefModels, AreaLocationRefModel)
			}
			wg.Done()
		}(row)
	}
	wg.Wait()

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}
	fc := func() *lcos_error.LCOSError {
		for _, addAreaLocationRefModel := range addAreaLocationRefModels {
			dbModel, err := s.areaLocationDao.GetLogisticLineServiceableAreaLocationRefTabByAreaNameAndLocationId(ctx, addAreaLocationRefModel.AreaName, addAreaLocationRefModel.LocationID)
			if err != nil {
				return err
			}
			if dbModel != nil {
				if err := s.areaLocationDao.DeleteLogisticLineServiceableAreaLocationRefTabById(ctx, dbModel.ID); err != nil {
					return err
				}
			}
			areaModel, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabByName(ctx, addAreaLocationRefModel.AreaName)
			if err != nil {
				return err
			}
			if areaModel == nil {
				areaModel, err = s.lineServiceableAreaDAO.CreateLogisticLineServiceableAreaTab(ctx, &model.LogisticLineServiceableAreaTab{
					Region:   request.Region,
					AreaName: addAreaLocationRefModel.AreaName,
				})
				if err != nil {
					return err
				}
			}
			addAreaLocationRefModel.AreaID = areaModel.ID
			if _, err := s.areaLocationDao.CreateLogisticLineServiceableAreaLocationRefTab(ctx, addAreaLocationRefModel); err != nil {
				return err
			}
		}
		for _, deleteAreaLocationRefModel := range deleteAreaLocationRefModels {
			dbModel, err := s.areaLocationDao.GetLogisticLineServiceableAreaLocationRefTabByAreaNameAndLocationId(ctx, deleteAreaLocationRefModel.AreaName, deleteAreaLocationRefModel.LocationID)
			if err != nil {
				return err
			}
			if dbModel != nil {
				if err := s.areaLocationDao.DeleteLogisticLineServiceableAreaLocationRefTabById(ctx, dbModel.ID); err != nil {
					return err
				}
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(areaNameList, ","))
	return nil
}

func fillAreaLocationRefModel(ctx context.Context, areaLocationModels []*area_location_ref.LogisticLineServiceableAreaLocationRefTab, request *area_route_serviceable.CreateAreaRequest, areaId uint64) *lcos_error.LCOSError {
	for i := range request.LocationIds {
		areaLocationModels[i] = &area_location_ref.LogisticLineServiceableAreaLocationRefTab{
			Region:     request.Region,
			LocationID: request.LocationIds[i],
			AreaName:   request.AreaName,
			AreaID:     areaId,
		}
	}

	var errList []*lcos_error.LCOSError
	// 是否需要流控
	wg := sync.WaitGroup{}
	wg.Add(len(areaLocationModels))
	for _, areaLocationModel := range areaLocationModels {
		go func(refModel *area_location_ref.LogisticLineServiceableAreaLocationRefTab) {
			locationInfo, err := ops_service.GetUpLocationInfo(ctx, &ops_service.GetLocationInfoByIdRequest{
				Country:    refModel.Region,
				LocationId: refModel.LocationID,
			})

			if err != nil {
				errList = append(errList, err)
			} else {
				if locationInfo.State != nil {
					refModel.State = *locationInfo.State
				}
				if locationInfo.City != nil {
					refModel.City = *locationInfo.City
				}
				if locationInfo.District != nil {
					refModel.District = *locationInfo.District
				}
			}
			wg.Done()
		}(areaLocationModel)
	}
	wg.Wait()

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.CreateAreaLocationErrorCode, lcos_error.GetMessageFromList(errList))
	}

	return nil
}
