package shop_serviceable_zone

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/zone_serviceable_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"strconv"
)

type ShopServiceableZoneService interface {
	UpdateShopServiceableZone(ctx utils.LCOSContext, req *zone_serviceable_protocol.UpdateShopServiceableZoneRequest) (*zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse, *lcos_error.LCOSError)
	ListShopServiceableZone(ctx utils.LCOSContext, shopId, productId int) ([]string, *lcos_error.LCOSError)

	// QuerySellerServiceableZoneInfo 提供给seller侧的集成接口：
	// 1. 匹配卖家地址所属的zone
	// 2. 查询渠道支持的zone列表
	// 3. 查询shop开启的zone列表
	QuerySellerServiceableZoneInfo(ctx utils.LCOSContext, req *zone_serviceable_protocol.QuerySellerServiceableZoneInfoRequest) (*zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse, *lcos_error.LCOSError)
}

func NewShopServiceableZoneService(productZoneDao product_serviceable_zone.ProductServiceableZoneDao, shopZoneDao shop_serviceable_zone.ShopServiceableZoneDao) *shopServiceableZoneService {
	return &shopServiceableZoneService{
		productZoneDao: productZoneDao,
		shopZoneDao:    shopZoneDao,
	}
}

type shopServiceableZoneService struct {
	productZoneDao product_serviceable_zone.ProductServiceableZoneDao
	shopZoneDao    shop_serviceable_zone.ShopServiceableZoneDao
}

func (s *shopServiceableZoneService) UpdateShopServiceableZone(ctx utils.LCOSContext, req *zone_serviceable_protocol.UpdateShopServiceableZoneRequest) (*zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse, *lcos_error.LCOSError) {
	var (
		shopId          = req.ShopId
		productId       = req.ProductId
		enabledZoneList = req.EnabledZoneList
		postcode        = req.SellerAddress.Postcode
	)

	// 1. 获取渠道支持的所有区域
	productZoneList, err := s.productZoneDao.ListLogisticProductServiceableZone(ctx, map[string]interface{}{"product_id": productId})
	if err != nil {
		return nil, err
	}

	// 2. 获取渠道下业务上传的所有cep group并按照zone分组
	cepGroupList, err := s.productZoneDao.ListLogisticProductServiceableCepGroup(ctx, map[string]interface{}{"product_id": productId})
	if err != nil {
		return nil, err
	}
	cepGroupMap := make(map[string][]*product_serviceable_zone.LogisticProductServiceableCepGroupTab)
	for _, cepGroup := range cepGroupList {
		cepGroupMap[cepGroup.ZoneName] = append(cepGroupMap[cepGroup.ZoneName], cepGroup)
	}

	// 3. 匹配卖家地址所属的city和zone
	var (
		cep                 int
		originZone          string
		cityName            string
		productZoneMap      = make(map[string]*product_serviceable_zone.LogisticProductServiceableZoneTab, len(productZoneList))
		destinationZoneMap  = make(map[string][]string)
		destinationZoneList []string
	)
	if postcode != "" {
		var paramErr error
		cep, paramErr = strconv.Atoi(utils.ValidPostcode(postcode))
		if paramErr != nil {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid postcode|postcode=%s, cause=%s", postcode, paramErr.Error())
		}
	} else {
		cityName = config.GetMutableConf(ctx).DirectDeliveryDefaultCity // 上线兼容，没传卖家地址默认为São Paulo
	}
	for _, zone := range productZoneList {
		productZoneMap[zone.ZoneName] = zone

		cepGroup, ok := cepGroupMap[zone.ZoneName]
		if !ok {
			continue
		}

		if originZone == "" {
			// 没有匹配到卖家地址所属zone，则需要根据cep group进行匹配
			for _, cepRange := range cepGroup {
				if cep >= cepRange.CepInitial && cep <= cepRange.CepFinal {
					originZone = zone.ZoneName
					cityName = zone.CityName
					break
				}
			}
		}

		// 将zone按照city进行分组
		destinationZoneMap[zone.CityName] = append(destinationZoneMap[zone.CityName], zone.ZoneName)
	}
	// 仅返回卖家city下的zone
	destinationZoneList = destinationZoneMap[cityName]

	// 4. 校验卖家开启的区域，是否被渠道支持且跟卖家在同一city，并且是否已上传cep group数据
	shopZoneList := make([]*shop_serviceable_zone.LogisticShopServiceableZoneTab, 0, len(enabledZoneList))
	for _, enabledZone := range enabledZoneList {
		destZone, ok := productZoneMap[enabledZone]
		if !ok {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "enabled zone not found|product_id=%d, zone_name=%s", productId, enabledZone)
		}
		if destZone.CityName != cityName {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "enabled zone not belong to the same city|product_id=%d, zone_name=%s", productId, enabledZone)
		}
		if _, ok = cepGroupMap[enabledZone]; !ok {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "enabled zone doesn't support cep group check|product_id=%d, zone_name=%s", productId, enabledZone)
		}

		shopZoneList = append(shopZoneList, &shop_serviceable_zone.LogisticShopServiceableZoneTab{
			ShopId:    shopId,
			ProductId: productId,
			ZoneName:  enabledZone,
		})
	}

	// 5. 更新shop服务范围数据
	err = ctx.Transaction(func() *lcos_error.LCOSError {
		if err = s.shopZoneDao.DeleteLogisticShopServiceableZone(ctx, map[string]interface{}{"shop_id": shopId, "product_id": productId}); err != nil {
			return err
		}
		if err = s.shopZoneDao.BatchCreateLogisticShopServiceableZone(ctx, shopZoneList); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse{
		OriginCepZone:          originZone,
		CityName:               cityName,
		DestinationCepZoneList: destinationZoneList,
		AvailableCepZoneList:   enabledZoneList,
	}, nil
}

func (s *shopServiceableZoneService) ListShopServiceableZone(ctx utils.LCOSContext, shopId, productId int) ([]string, *lcos_error.LCOSError) {
	queryMap := make(map[string]interface{})
	if shopId != 0 {
		queryMap["shop_id"] = shopId
	}
	if productId != 0 {
		queryMap["product_id"] = productId
	}
	shopEnabledZoneList, err := s.shopZoneDao.ListLogisticShopServiceableZone(ctx, queryMap)
	if err != nil {
		return nil, err
	}
	enabledZoneList := make([]string, 0, len(shopEnabledZoneList))
	for _, zone := range shopEnabledZoneList {
		enabledZoneList = append(enabledZoneList, zone.ZoneName)
	}
	return enabledZoneList, nil
}

// QuerySellerServiceableZoneInfo 提供给seller侧的集成接口：
// 1. 匹配卖家地址所属的zone
// 2. 查询渠道支持的zone列表
// 3. 查询shop开启的zone列表
func (s *shopServiceableZoneService) QuerySellerServiceableZoneInfo(ctx utils.LCOSContext, req *zone_serviceable_protocol.QuerySellerServiceableZoneInfoRequest) (*zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse, *lcos_error.LCOSError) {
	// 1. 获取渠道开启的所有电子围栏区域
	productZoneList, lcosErr := s.productZoneDao.ListLogisticProductServiceableZone(ctx, map[string]interface{}{"product_id": req.ProductId})
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 2. 根据渠道和zone name获取对应的cep group信息，并基于zone name分组
	cepGroupList, lcosErr := s.productZoneDao.ListLogisticProductServiceableCepGroup(ctx, map[string]interface{}{"product_id": req.ProductId})
	if lcosErr != nil {
		return nil, lcosErr
	}
	cepGroupMap := make(map[string][]*product_serviceable_zone.LogisticProductServiceableCepGroupTab)
	for _, cepGroup := range cepGroupList {
		cepGroupMap[cepGroup.ZoneName] = append(cepGroupMap[cepGroup.ZoneName], cepGroup)
	}

	// 3. 处理卖家地址postcode参数，转换为int类型用于cep range匹配
	postcode := utils.ValidPostcode(req.PostalCode)
	cep, err := strconv.Atoi(postcode)
	if err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid address, %s", err.Error())
	}

	// 4. 匹配卖家地址所属的zone（cep group）并返回所有的destination zone
	var (
		originZoneName      string
		cityName            string
		destinationZoneMap  = make(map[string][]string)
		destinationZoneList []string
	)
	for _, zone := range productZoneList {
		cepGroup, ok := cepGroupMap[zone.ZoneName]
		if !ok {
			// 没有上传cep group的zone，不对卖家返回
			continue
		}

		if originZoneName == "" {
			// 没有匹配到卖家地址所属zone，则需要根据cep group进行匹配
			for _, cepRange := range cepGroup {
				if cep >= cepRange.CepInitial && cep <= cepRange.CepFinal {
					originZoneName = zone.ZoneName
					cityName = zone.CityName
					break
				}
			}
		}

		// 将zone按照city进行分组
		destinationZoneMap[zone.CityName] = append(destinationZoneMap[zone.CityName], zone.ZoneName)
	}
	// 仅返回卖家city下的zone
	destinationZoneList = destinationZoneMap[cityName]

	// 5. 获取shop开启的所有zone
	availableZoneList, lcosErr := s.ListShopServiceableZone(ctx, req.ShopId, req.ProductId)
	if lcosErr != nil {
		return nil, lcosErr
	}

	return &zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse{
		OriginCepZone:          originZoneName,
		CityName:               cityName,
		DestinationCepZoneList: destinationZoneList,
		AvailableCepZoneList:   availableZoneList,
	}, nil
}
