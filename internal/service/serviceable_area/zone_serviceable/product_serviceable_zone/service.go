package product_serviceable_zone

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	jsoniter "github.com/json-iterator/go"
	"io/ioutil"
	"sort"
)

type ProductSerivceableZoneService interface {
	ImportProductServiceableZone(ctx utils.LCOSContext, productId int, cityName, fileUrl string) *lcos_error.LCOSError

	// for Seller Fulfillment
	// ListProductServiceableZone 提供给Seller Fulfillment用于在卖家e-map上渲染电子围栏数据
	ListProductServiceableZone(ctx utils.LCOSContext, productId int, cityName string) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError)
	// ListProductServiceableZoneWithCepGroup 仅返回上传过cep group的电子围栏区域
	ListProductServiceableZoneWithCepGroup(ctx utils.LCOSContext, productId int, cityName string) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError)

	// for SLS OPS
	ImportProductServiceableCepGroup(ctx utils.LCOSContext, fileUrl string) *lcos_error.LCOSError
	ListProductServiceableCepGroup(ctx utils.LCOSContext, productId int) ([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab, *lcos_error.LCOSError)
}

func NewProductServiceableZoneService(dao product_serviceable_zone.ProductServiceableZoneDao) *productServiceableZoneService {
	return &productServiceableZoneService{
		dao: dao,
	}
}

type productServiceableZoneService struct {
	dao product_serviceable_zone.ProductServiceableZoneDao
}

func (p *productServiceableZoneService) ImportProductServiceableZone(ctx utils.LCOSContext, productId int, cityName, fileUrl string) *lcos_error.LCOSError {
	filePath, lcosErr := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if lcosErr != nil {
		return lcosErr
	}
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "read file error, %s", err.Error())
	}

	var cityList []CityServiceableZone
	if err = jsoniter.Unmarshal(data, &cityList); err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "read file error, %s", err.Error())
	}
	var zoneList []*product_serviceable_zone.LogisticProductServiceableZoneTab
	for _, city := range cityList {
		if city.Name != cityName {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "city name not match|expected_city=%s, actual_city=%s", cityName, city.Name)
		}

		for _, zone := range city.Zones {
			zoneList = append(zoneList, &product_serviceable_zone.LogisticProductServiceableZoneTab{
				ProductId:     productId,
				CityName:      city.Name,
				ZoneName:      zone.Name,
				Polygon:       zone.Polygon,
				Neighborhoods: zone.Neighborhoods,
			})
		}
	}

	return ctx.Transaction(func() *lcos_error.LCOSError {
		if lcosErr = p.dao.DeleteLogisticProductServiceableZone(ctx, map[string]interface{}{"product_id": productId, "city_name": cityName}); lcosErr != nil {
			return lcosErr
		}
		if lcosErr = p.dao.BatchCreateLogisticProductServiceableZone(ctx, zoneList); lcosErr != nil {
			return lcosErr
		}
		return nil
	})
}

func (p *productServiceableZoneService) ListProductServiceableZone(ctx utils.LCOSContext, productId int, cityName string) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError) {
	zoneQueryMap := make(map[string]interface{})
	if productId != 0 {
		zoneQueryMap["product_id"] = productId
	}
	if cityName != "" {
		zoneQueryMap["city_name"] = cityName
	}
	zoneList, err := p.dao.ListLogisticProductServiceableZone(ctx, zoneQueryMap)
	if err != nil {
		return nil, err
	}
	return zoneList, nil
}

func (p *productServiceableZoneService) ListProductServiceableZoneWithCepGroup(ctx utils.LCOSContext, productId int, cityName string) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError) {
	// 1. 筛选所有的zone
	productZoneList, err := p.ListProductServiceableZone(ctx, productId, cityName)
	if err != nil {
		return nil, err
	}

	// 2. 获取所有上传过cep group的zone列表
	cepGroupList, err := p.ListProductServiceableCepGroup(ctx, productId)
	if err != nil {
		return nil, err
	}
	cepGroupMap := make(map[string]struct{})
	for _, cepGroup := range cepGroupList {
		cepGroupMap[cepGroup.ZoneName] = struct{}{}
	}

	// 3. 筛选并只返回上传了cep group的zone列表
	productZoneWithCepGroupList := make([]*product_serviceable_zone.LogisticProductServiceableZoneTab, 0, len(productZoneList))
	for _, zone := range productZoneList {
		if _, ok := cepGroupMap[zone.ZoneName]; ok {
			productZoneWithCepGroupList = append(productZoneWithCepGroupList, zone)
		}
	}
	return productZoneWithCepGroupList, nil
}

func (p *productServiceableZoneService) ImportProductServiceableCepGroup(ctx utils.LCOSContext, fileUrl string) *lcos_error.LCOSError {
	header := serviceable_util.ProductServiceableCepGroupHeader

	filePath, lcosErr := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if lcosErr != nil {
		return lcosErr
	}

	// Excel文件数据解析
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "read file error, %s", err.Error())
	}
	rows, err := file.GetRows(file.GetSheetName(0))
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "read file sheet error, %s", err.Error())
	}
	var dataList []*product_serviceable_zone.LogisticProductServiceableCepGroupTab
	var productList []int
	productMap := make(map[int]struct{})
	rowDataMap := make(map[int][]serviceable_util.ProductServiceableCepGroupRowdata)
	for i, row := range rows {
		rowId := i + 1
		if rowId == 1 {
			continue
		}
		if excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}

		var rowData serviceable_util.ProductServiceableCepGroupRowdata
		if lcosErr = excel.ParseRowDataWithHeader(rowId, row, header, &rowData); lcosErr != nil {
			return lcosErr
		}
		if rowData.CepInitial > rowData.CepFinal {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Initial is larger than CEP Final | row=%d", rowData.RowId)
		}

		rowDataMap[rowData.ChannelId] = append(rowDataMap[rowData.ChannelId], rowData)
		model := &product_serviceable_zone.LogisticProductServiceableCepGroupTab{
			ProductId:  rowData.ChannelId,
			ZoneName:   rowData.GroupName,
			CepInitial: rowData.CepInitial,
			CepFinal:   rowData.CepFinal,
		}
		dataList = append(dataList, model)
		if _, ok := productMap[model.ProductId]; !ok {
			productList = append(productList, model.ProductId)
			productMap[model.ProductId] = struct{}{}
		}
	}

	// CEP Group合法性校验
	zoneList, lcosErr := p.dao.ListLogisticProductServiceableZone(ctx, map[string]interface{}{"product_id in": productList})
	if lcosErr != nil {
		return lcosErr
	}
	productZoneMap := make(map[int]map[string]struct{})
	for _, zone := range zoneList {
		zoneMap, ok := productZoneMap[zone.ProductId]
		if !ok {
			zoneMap = make(map[string]struct{})
		}
		zoneMap[zone.ZoneName] = struct{}{}
		productZoneMap[zone.ProductId] = zoneMap
	}
	for _, rowDataList := range rowDataMap {
		for _, rowData := range rowDataList {
			zoneMap, ok := productZoneMap[rowData.ChannelId]
			if !ok {
				return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Group '%s' is invalid | row=%d", rowData.GroupName, rowData.RowId)
			}
			_, ok = zoneMap[rowData.GroupName]
			if !ok {
				return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Group '%s' is invalid | row=%d", rowData.GroupName, rowData.RowId)
			}
		}
	}

	// CEP Range Overlap Check
	for _, rowDataList := range rowDataMap {
		// 排序前，不重叠的情况只有[i, i] [j, j]和[j, j] [i, i]
		sort.SliceStable(rowDataList, func(i, j int) bool {
			return rowDataList[i].CepInitial < rowDataList[j].CepInitial
		})
		// 排序后，由于i.Left < j.Left，所以不重叠的情况只剩[i, i] [j, j]，因此所有i.Right >= j.Left的均有重叠
		for i := 0; i < len(rowDataList)-1; i++ {
			if rowDataList[i].CepFinal >= rowDataList[i+1].CepInitial {
				msg := fmt.Sprintf("CEP range has overlap between row %d and row %d.", rowDataList[i].RowId, rowDataList[i+1].RowId)
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, msg)
			}
		}
	}

	return ctx.Transaction(func() *lcos_error.LCOSError {
		if lcosErr = p.dao.DeleteLogisticProductServiceableCepGroup(ctx, map[string]interface{}{"product_id in": productList}); lcosErr != nil {
			return lcosErr
		}
		if lcosErr = p.dao.BatchCreateLogisticProductServiceableCepGroup(ctx, dataList); lcosErr != nil {
			return lcosErr
		}
		return nil
	})
}

func (p *productServiceableZoneService) ListProductServiceableCepGroup(ctx utils.LCOSContext, productId int) ([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab, *lcos_error.LCOSError) {
	queryMap := make(map[string]interface{})
	if productId != 0 {
		queryMap["product_id"] = productId
	}
	return p.dao.ListLogisticProductServiceableCepGroup(ctx, queryMap)
}
