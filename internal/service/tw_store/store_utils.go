package tw_store

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tw_store"
	"strconv"
)

// 解析单行为结构体
func parseSingleStoreRow(row []string, lineNum int, storeType uint32) (*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError) {
	if len(row) < 11 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("length of row is less than 11|lineNum=%v", lineNum))
	}
	// status校验 0表示not available，1表示availa
	var status uint8
	if row[10] == "1" {
		status = 1
	}
	var isAllConsistency uint8
	if row[5] == "1" {
		isAllConsistency = 1
	}
	districtID, err := strconv.Atoi(row[4])
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("district is not valid|lineNum=%v", lineNum))
	}
	return &tw_store.LogisticThirdPartyConvenienceStoreTab{
		StoreID:          row[0],
		NewStoreID:       row[1],
		StoreName:        row[2],
		Address:          row[3],
		StoreType:        storeType,
		CloseDate:        row[6],
		EndDate:          row[7],
		EnableStatus:     status,
		DistrictID:       districtID,
		IsAllConsistency: isAllConsistency,
		Phone:            row[8],
		PostalCode:       row[9],
		IsVirtual:        0,
	}, nil
}

func isSameStore(oldStore, newStore *tw_store.LogisticThirdPartyConvenienceStoreTab) bool {
	return oldStore.StoreName == newStore.StoreName &&
		oldStore.Address == newStore.Address &&
		oldStore.CloseDate == newStore.CloseDate &&
		oldStore.EndDate == newStore.EndDate &&
		oldStore.Phone == newStore.Phone &&
		oldStore.PostalCode == newStore.PostalCode &&
		oldStore.EnableStatus == newStore.EnableStatus &&
		oldStore.NewStoreID == newStore.NewStoreID &&
		oldStore.IsAllConsistency == newStore.IsAllConsistency &&
		oldStore.District == newStore.District &&
		oldStore.City == newStore.City &&
		oldStore.Area == newStore.Area &&
		oldStore.DistrictID == newStore.DistrictID &&
		oldStore.IsVirtual == newStore.IsVirtual &&
		oldStore.DcroNo == newStore.DcroNo &&
		oldStore.OkArea == newStore.OkArea &&
		oldStore.RsNo == newStore.RsNo &&
		oldStore.ShipType == newStore.ShipType &&
		oldStore.PathNo == newStore.PathNo &&
		oldStore.AisleNo == newStore.AisleNo &&
		oldStore.GridNo == newStore.GridNo &&
		oldStore.MidType == newStore.MidType
}

// 比较新老店铺信息
func compareStores(ctx utils.LCOSContext, oldStores, newStores []*tw_store.LogisticThirdPartyConvenienceStoreTab) ([]*tw_store.LogisticThirdPartyConvenienceStoreTab, []*tw_store.LogisticThirdPartyConvenienceStoreTab, []uint64) {
	if len(oldStores) == 0 || len(newStores) == 0 {
		logger.CtxLogInfof(ctx, "warning stores is empty|length(db_stores)=v, length(latest_stores)=v", len(oldStores), len(newStores))
	}

	var deleteIDs []uint64
	var addedStores []*tw_store.LogisticThirdPartyConvenienceStoreTab
	var updatedStores []*tw_store.LogisticThirdPartyConvenienceStoreTab

	// 将列表按照store id聚合为map
	oldStoreMap := map[string]*tw_store.LogisticThirdPartyConvenienceStoreTab{}

	for _, store := range oldStores {
		oldStoreMap[store.StoreID] = store
	}

	for _, store := range newStores {
		// 检查是否存在old store中，不存在，则表示为新增，存在但是不相同，则表示更新
		if oldStore, ok := oldStoreMap[store.StoreID]; !ok {
			addedStores = append(addedStores, store)
		} else {
			if !isSameStore(oldStore, store) {
				store.ID = oldStore.ID
				updatedStores = append(updatedStores, store)
			}
			// 存在的话，需要删除old store里面的key
			delete(oldStoreMap, store.StoreID)
		}
	}

	// 将old store中剩余的key加入到删除的id中
	for _, store := range oldStoreMap {
		// if stores needed to be deleted are already disabled, will not need to disable
		if store.EnableStatus != constant.DISABLED {
			deleteIDs = append(deleteIDs, store.ID)
		}
	}
	return addedStores, updatedStores, deleteIDs
}
