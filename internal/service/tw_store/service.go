package tw_store

import (
	"fmt"
	"io/ioutil"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tw_store"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	jsoniter "github.com/json-iterator/go"
)

type TWStoreService interface {
	// ParseAndImportStores 解析文件url，导入数据库
	ParseAndImportStores(ctx utils.LCOSContext, fileUrl string, storeType uint32) *lcos_error.LCOSError
	GetStoreByStoreIDAndType(ctx utils.LCOSContext, storeID string, storeType uint32, status uint8) (*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError)
	GetStoreByStoreIdAndTypeWithoutStatus(ctx utils.LCOSContext, storeId string, storeType uint32) (*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError)
	// SPLN-25715: LCOS TW store info支持基于new store id进行查询
	GetStoreByNewStoreIdAndType(ctx utils.LCOSContext, newStoreID string, storeType uint32) (*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError)
	GetStoreByAddressID(ctx utils.LCOSContext, addressID uint64) (*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError)
}

type twStoreService struct {
	s3Service                     s3_service.S3Service
	thirdPartyConvenienceStoreDao tw_store.ThirdPartyConvenienceStoreDAO
}

func NewTWStoreService(thirdPartyConvenienceStoreDao tw_store.ThirdPartyConvenienceStoreDAO, s3Service s3_service.S3Service) *twStoreService {
	return &twStoreService{
		s3Service:                     s3Service,
		thirdPartyConvenienceStoreDao: thirdPartyConvenienceStoreDao,
	}
}

func (t *twStoreService) GetStoreByStoreIDAndType(ctx utils.LCOSContext, storeID string, storeType uint32, status uint8) (*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError) {
	// 先通过store id和store type找到map，然后通过new csv id获取到新的store信息

	var ref *tw_store.LogisticOldStoreIdxMapTab
	var lcosErr *lcos_error.LCOSError

	// SPLN-22402  check store type one of LOCAL_FAMILYMART
	if utils.InUint32Slice(storeType, []uint32{constant.TwFamilyMartBuyer, constant.TwFamilyMartSeller}) {
		// if store id starts with F, remove if and try if can get store map
		if strings.HasPrefix(storeID, "F") {
			fStoreID := strings.TrimPrefix(storeID, "F")
			ref, lcosErr = t.thirdPartyConvenienceStoreDao.GetStoreMapByStoreIDAndStoreTypeUsingCache(ctx, fStoreID, storeType)
		}
		if ref == nil || lcosErr != nil {
			ref, lcosErr = t.thirdPartyConvenienceStoreDao.GetStoreMapByStoreIDAndStoreTypeUsingCache(ctx, storeID, storeType)
		}
	} else {
		ref, lcosErr = t.thirdPartyConvenienceStoreDao.GetStoreMapByStoreIDAndStoreTypeUsingCache(ctx, storeID, storeType)
	}
	if lcosErr != nil {
		return nil, lcosErr
	}

	store, lcosErr := t.thirdPartyConvenienceStoreDao.GetStoreByIDUsingCache(ctx, ref.NewCVSID)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if store.EnableStatus != status {
		return nil, lcos_error.NewLCOSError(lcos_error.StoreNotFoundError, fmt.Sprintf("cannot find store|store_id=%v, store_type=%v, status=%v", storeID, storeType, status))
	}

	// 将id填充为老的store id
	store.ID = ref.OldCVSID
	store.StoreType = ref.OldStoreType
	return store, nil
}

func (t *twStoreService) GetStoreByStoreIdAndTypeWithoutStatus(ctx utils.LCOSContext, storeID string, storeType uint32) (*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError) {
	// 1. 通过store id和type获取store映射，从而得到new cvs id
	var mapping *tw_store.LogisticOldStoreIdxMapTab
	var lcosErr *lcos_error.LCOSError
	if utils.InUint32Slice(storeType, []uint32{constant.TwFamilyMartBuyer, constant.TwFamilyMartSeller}) {
		if strings.HasPrefix(storeID, "F") {
			fStoreID := strings.TrimPrefix(storeID, "F")
			mapping, lcosErr = t.thirdPartyConvenienceStoreDao.GetStoreMapByStoreIDAndStoreTypeUsingCache(ctx, fStoreID, storeType)
		}
		if mapping == nil || lcosErr != nil {
			mapping, lcosErr = t.thirdPartyConvenienceStoreDao.GetStoreMapByStoreIDAndStoreTypeUsingCache(ctx, storeID, storeType)
		}
	} else {
		mapping, lcosErr = t.thirdPartyConvenienceStoreDao.GetStoreMapByStoreIDAndStoreTypeUsingCache(ctx, storeID, storeType)
	}
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 2. 通过new cvs id查询store信息
	store, lcosErr := t.thirdPartyConvenienceStoreDao.GetStoreByIDUsingCache(ctx, mapping.NewCVSID)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 将id填充为老的store id
	store.ID = mapping.OldCVSID
	store.StoreType = mapping.OldStoreType
	return store, nil
}

// SPLN-25715: LCOS TW store info支持基于new store id进行查询
func (t *twStoreService) GetStoreByNewStoreIdAndType(ctx utils.LCOSContext, newStoreID string, storeType uint32) (*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError) {
	// get new_store_type by map
	newStoreType, ok := constant.TWCategroyToStoreTypeMap[storeType]
	if !ok {
		msg := fmt.Sprintf("invalid old_store_type=%d", storeType)
		logger.CtxLogInfof(ctx, msg)
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, msg)
	}
	// get store info by new_store_id and new_store_type
	store, err := t.thirdPartyConvenienceStoreDao.GetStoreByNewStoreIdAndTypeUsingCache(ctx, newStoreID, newStoreType)
	if err != nil {
		logger.CtxLogInfof(ctx, err.Msg)
		return nil, err
	}
	// get old_cvs_id by new_cvs_id and old_store_type
	storeMapList, err := t.thirdPartyConvenienceStoreDao.GetStoreMapByNewCvsIdAndTypeUsingCache(ctx, store.ID, storeType)
	if err != nil {
		logger.CtxLogInfof(ctx, err.Msg)
		return nil, err
	}
	if len(storeMapList) == 0 {
		msg := fmt.Sprintf("cannot find store map list from cache by new_cvs_id=%d and type=%d", store.ID, storeType)
		logger.CtxLogInfof(ctx, msg)
		return nil, lcos_error.NewLCOSError(lcos_error.StoreNotFoundError, msg)
	}
	// notice: 通过new_cvs_id和type可能查到多个old_cvs_id，此处只取查到的第一个值
	store.ID = storeMapList[0].OldCVSID // 此处的old_cvs_id不一定准确，但调用方tracking并不使用此字段，因此不影响业务
	store.StoreType = storeType
	return store, nil
}

func (t *twStoreService) GetStoreByAddressID(ctx utils.LCOSContext, addressID uint64) (*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError) {
	// 先通过store id和store type找到map，然后通过new csv id获取到新的store信息
	ref, lcosErr := t.thirdPartyConvenienceStoreDao.GetStoreMapByAddressIDUsingCache(ctx, addressID)
	if lcosErr != nil {
		return nil, lcosErr
	}

	store, lcosErr := t.thirdPartyConvenienceStoreDao.GetStoreByIDUsingCache(ctx, ref.NewCVSID)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 将id填充为老的store id
	store.ID = ref.OldCVSID
	store.StoreType = ref.OldStoreType
	return store, nil
}

// getCategoryByStoreType
// get both buyer and seller category for store type to add store map
func getCategoryByStoreType(storeType uint32) []uint32 {
	results := make([]uint32, 0)
	for key, value := range constant.TWCategroyToStoreTypeMap {
		if value == storeType {
			results = append(results, key)
		}
	}
	return results
}

func (t *twStoreService) ParseAndImportStores(ctx utils.LCOSContext, fileUrl string, storeType uint32) *lcos_error.LCOSError {
	// 为了兼容，传入的store type为convenience_store_tab的category字段
	oldStoreType := storeType
	if _, ok := constant.TWCategroyToStoreTypeMap[oldStoreType]; !ok {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("store type is not valid|store_type=%v", storeType))
	}
	newsStoreType := constant.TWCategroyToStoreTypeMap[oldStoreType]
	// parse result
	newStores, lcosErr := t.parseStores(ctx, fileUrl, newsStoreType)
	if lcosErr != nil {
		return lcosErr
	}

	// get old store from db, virtual store no need to update
	oldStores, lcosErr := t.thirdPartyConvenienceStoreDao.SearchPartyConvenienceStores(ctx, map[string]interface{}{"store_type": newsStoreType, "is_virtual": constant.FALSE})
	if lcosErr != nil {
		return lcosErr
	}

	// diff
	addStores, updateStores, deleteStoreIDs := compareStores(ctx, oldStores, newStores)
	logger.CtxLogInfof(ctx, "added stores are:[%s]", utils.MarshToStringWithoutError(addStores))
	logger.CtxLogInfof(ctx, "updated stores are:[%s]", utils.MarshToStringWithoutError(updateStores))
	logger.CtxLogInfof(ctx, "deleted id list are:[%v]", deleteStoreIDs)

	// 开启事务，执行数据库操作
	fc := func() *lcos_error.LCOSError {
		// 将需要删除的门店的updatedate更新为当前的日期，status置为0
		updateDate := pickup.GetCurrentTime(ctx, "TW").Format("2006-01-02")
		lcosErr := t.thirdPartyConvenienceStoreDao.UpdatePartyConvenienceStores(ctx, map[string]interface{}{"id in": deleteStoreIDs}, map[string]interface{}{"enable_status": constant.DISABLED, "update_date": updateDate})
		if lcosErr != nil {
			return lcosErr
		}

		lcosErr = t.thirdPartyConvenienceStoreDao.UpdatePartyConvenienceStoresOnConflict(ctx, updateStores)
		if lcosErr != nil {
			return lcosErr
		}

		// 创建门店信息
		lcosErr = t.thirdPartyConvenienceStoreDao.BatchCreatePartyConvenienceStores(ctx, addStores)
		if lcosErr != nil {
			return lcosErr
		}

		// 查找当前最大的old_cvs_id，防止冲突
		maxLogisticOldStoreIdxMapTab := &tw_store.LogisticOldStoreIdxMapTab{}
		db := ctx.ReadDB().Model(&tw_store.LogisticOldStoreIdxMapTab{}).Order("old_cvs_id DESC").Limit(1).Take(maxLogisticOldStoreIdxMapTab)
		if db.GetError() != nil {
			return lcos_error.NewLCOSError(lcos_error.DBWriteErrorCode, db.GetError().Error())
		}
		maxOldCVSID := maxLogisticOldStoreIdxMapTab.OldCVSID + 1

		// 创建门店映射信息
		buyerAndSellerCategories := getCategoryByStoreType(newsStoreType)
		var models []*tw_store.LogisticOldStoreIdxMapTab
		for _, addStore := range addStores {
			// need to add map for both buyer and seller
			for _, singleCategory := range buyerAndSellerCategories {
				models = append(models, &tw_store.LogisticOldStoreIdxMapTab{
					OldCVSID:     maxOldCVSID,
					NewCVSID:     addStore.ID,
					OldStoreType: singleCategory,
					StoreID:      addStore.StoreID,
				})
				maxOldCVSID++
			}
		}

		return t.thirdPartyConvenienceStoreDao.BatchCreateStoreMaps(ctx, models)
	}
	lcosErr = ctx.Transaction(fc)
	if lcosErr != nil {
		return lcosErr
	}
	// 手动刷新缓存
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LogisticOldStoreIdxMapNamespace)
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.ThirdPartyConvenienceStoreNamespace)
	return nil
}

func (t *twStoreService) parseStores(ctx utils.LCOSContext, fileUrl string, storeType uint32) ([]*tw_store.LogisticThirdPartyConvenienceStoreTab, *lcos_error.LCOSError) {
	// 下载解析文件
	// 下载文件到本地
	reader, err := t.s3Service.Download(ctx, config.GetConf(ctx).LCSS3Config.AccessKeyID, config.GetConf(ctx).LCSS3Config.BucketKey, fileUrl, time.Second*10)
	if err != nil {
		logger.CtxLogErrorf(ctx, "S3 download fail, file url:%s, err:%v", fileUrl, err)
		return nil, err
	}

	data, e := ioutil.ReadAll(reader)
	if e != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, e.Error())
	}

	var storeDatas []*tw_store.LogisticThirdPartyConvenienceStoreTab
	var returnStoreDatas []*tw_store.LogisticThirdPartyConvenienceStoreTab
	nonDuplicateStoreDatasMap := map[string]*tw_store.LogisticThirdPartyConvenienceStoreTab{}
	if e := jsoniter.Unmarshal(data, &storeDatas); e != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, e.Error())
	}
	// 填充store type
	for _, store := range storeDatas {
		store.StoreType = storeType
		nonDuplicateStoreDatasMap[store.StoreID] = store
	}

	// 去重
	for _, singleStore := range nonDuplicateStoreDatasMap {
		returnStoreDatas = append(returnStoreDatas, singleStore)
	}

	return returnStoreDatas, nil
}

var _ TWStoreService = (*twStoreService)(nil)
