package local_formula

import (
	jsoniter "github.com/json-iterator/go"

	schema "git.garena.com/peng.li/sls-package-calculate/formula/service/calculate_formula"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/line_package_limit"
	model2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/product_package_limit"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

func ConvertInputToCalculateInput(valumetericFactor *uint32, orderInfo *pb.OrderInfo, skuInfos []*pb.SkuInfo, conditionParams []*pb.ConditionParams, parcelLimits *pb.SizeLimitInfo) *schema.CalculateFormulaInputParam {
	return &schema.CalculateFormulaInputParam{
		VolumetricFactor: valumetericFactor,
		OrderParam:       convertOrderInfo(orderInfo),
		SkuParam:         ConvertSkuInfo(skuInfos),
		ConditionParam:   ConvertConditionInfo(conditionParams),
		ParcelLimits:     ConvertParcelLimitsInfo(parcelLimits),
	}
}

func convertOrderInfo(orderInfo *pb.OrderInfo) *schema.CalculateFormulaOrderParam {
	if orderInfo == nil {
		return nil
	}
	var defaultValue float64
	orderParam := &schema.CalculateFormulaOrderParam{}
	if orderInfo.OrderActualWeight == nil {
		orderParam.OrderActualWeight = &defaultValue
	} else {
		actualWeight := *orderInfo.OrderActualWeight
		orderParam.OrderActualWeight = &actualWeight
	}

	if orderInfo.OrderVolumetricWeight == nil {
		orderParam.OrderVolumetricWeight = &defaultValue
	} else {
		volumetricWeight := *orderInfo.OrderVolumetricWeight
		orderParam.OrderVolumetricWeight = &volumetricWeight
	}

	if orderInfo.OrderLength == nil {
		orderParam.OrderLength = &defaultValue
	} else {
		length := *orderInfo.OrderLength
		orderParam.OrderLength = &length
	}

	if orderInfo.OrderWidth == nil {
		orderParam.OrderWidth = &defaultValue
	} else {
		width := *orderInfo.OrderWidth
		orderParam.OrderWidth = &width
	}

	if orderInfo.OrderHeight == nil {
		orderParam.OrderHeight = &defaultValue
	} else {
		height := *orderInfo.OrderHeight
		orderParam.OrderHeight = &height
	}
	return orderParam
}

func ConvertSkuInfo(skuInfos []*pb.SkuInfo) []*schema.CalculateFormulaSkuParam {
	var defaultFloat float64
	var defaultUint uint32

	skuParams := make([]*schema.CalculateFormulaSkuParam, len(skuInfos))
	for i, requestSkuInfo := range skuInfos {
		singleSkuParam := &schema.CalculateFormulaSkuParam{}
		if requestSkuInfo.Weight == nil {
			singleSkuParam.Weight = &defaultFloat
		} else {
			weight := *requestSkuInfo.Weight
			singleSkuParam.Weight = &weight
		}

		if requestSkuInfo.Quantity == nil {
			singleSkuParam.Quantity = &defaultUint
		} else {
			singleSkuParam.Quantity = requestSkuInfo.Quantity
		}

		if requestSkuInfo.Length == nil {
			singleSkuParam.Length = &defaultFloat
		} else {
			length := *requestSkuInfo.Length
			singleSkuParam.Length = &length
		}

		if requestSkuInfo.Width == nil {
			singleSkuParam.Width = &defaultFloat
		} else {
			width := *requestSkuInfo.Width
			singleSkuParam.Width = &width
		}

		if requestSkuInfo.Height == nil {
			singleSkuParam.Height = &defaultFloat
		} else {
			height := *requestSkuInfo.Height
			singleSkuParam.Height = &height
		}
		skuParams[i] = singleSkuParam
	}
	return skuParams
}

func ConvertConditionInfo(conditionParams []*pb.ConditionParams) []*schema.CalculateFormulaConditionParam {
	var conditionInfos []*schema.CalculateFormulaConditionParam
	for _, requestCondition := range conditionParams {
		singleCondition := &schema.CalculateFormulaConditionParam{}
		if requestCondition.KeyParam == nil {
			singleCondition.KeyParam = nil
		} else {
			keyParam := *requestCondition.KeyParam
			singleCondition.KeyParam = &keyParam
		}

		if requestCondition.KeySymbol == nil {
			singleCondition.KeySymbol = nil
		} else {
			keySymbol := uint8(*requestCondition.KeySymbol)
			singleCondition.KeySymbol = &keySymbol
		}

		conditionInfos = append(conditionInfos, singleCondition)
	}
	return conditionInfos
}

func ConvertParcelLimitsInfo(limitInfo *pb.SizeLimitInfo) *schema.Size {
	if limitInfo != nil {
		return &schema.Size{
			Length: limitInfo.GetMaxLength(),
			Width:  limitInfo.GetMaxWidth(),
			Height: limitInfo.GetMaxHeight(),
		}
	}

	return nil // nil为不限制
}

func NewCalculateFormulaOrderParam(orderInfo *pb.OrderInfo) *schema.CalculateFormulaOrderParam {
	if orderInfo == nil {
		return &schema.CalculateFormulaOrderParam{}
	}
	return &schema.CalculateFormulaOrderParam{
		OrderLength:           orderInfo.OrderLength,
		OrderWidth:            orderInfo.OrderWidth,
		OrderHeight:           orderInfo.OrderHeight,
		OrderActualWeight:     orderInfo.OrderActualWeight,
		OrderVolumetricWeight: orderInfo.OrderVolumetricWeight,
	}
}

func ConvertLineDbDataToCalculateInput(orderInfo *pb.OrderInfo, skuInfos []*pb.SkuInfo, limitData *model.LinePackageLimitTab, sideLimits *pb.SizeLimitInfo) *schema.CalculateFormulaInputParam {
	var conditionParams []*schema.CalculateFormulaConditionParam
	if len(limitData.ConditionFormulaParams) == 0 {
		conditionParams = nil
	} else {
		err := jsoniter.Unmarshal([]byte(limitData.ConditionFormulaParams), &conditionParams)
		if err != nil {
			logger.LogErrorf("convert db data failed, unmarshal error: %s", err.Error())
		}
	}

	if orderInfo == nil {
		orderInfo = &pb.OrderInfo{}
	}

	return &schema.CalculateFormulaInputParam{
		VolumetricFactor: &limitData.VolumetricFactor,
		OrderParam:       NewCalculateFormulaOrderParam(orderInfo),
		SkuParam:         ConvertSkuInfo(skuInfos),
		ConditionParam:   conditionParams,
		ParcelLimits:     ConvertParcelLimitsInfo(sideLimits),
	}
}

func ConvertProductDbDataToCalculateInput(skuInfos []*pb.SkuInfo, orderInfo *pb.OrderInfo, limitData *model2.ProductPackageLimitTab, sideLimits *pb.SizeLimitInfo) *schema.CalculateFormulaInputParam {
	var conditionParams []*schema.CalculateFormulaConditionParam
	if len(limitData.ConditionFormulaParams) == 0 {
		conditionParams = nil
	} else {
		err := jsoniter.Unmarshal([]byte(limitData.ConditionFormulaParams), &conditionParams)
		if err != nil {
			logger.LogErrorf("convert db data failed, unmarshal error: %s", err.Error())
		}
	}
	return &schema.CalculateFormulaInputParam{
		VolumetricFactor: &limitData.VolumetricFactor,
		OrderParam:       NewCalculateFormulaOrderParam(orderInfo),
		SkuParam:         ConvertSkuInfo(skuInfos),
		ConditionParam:   conditionParams,
		ParcelLimits:     ConvertParcelLimitsInfo(sideLimits),
	}
}

func GetSizeLimitFromProductRules(rules []*model2.ProductPackageLimitTab) *pb.SizeLimitInfo {
	var zero float64 = 0
	result := &pb.SizeLimitInfo{
		MaxLength: &zero,
		MinLength: &zero,
		MaxWidth:  &zero,
		MinWidth:  &zero,
		MaxHeight: &zero,
		MinHeight: &zero,
	}

	for _, rule := range rules {
		if rule.LimitFlag == constant.ENABLED {
			if rule.RuleType == constant.MaxLengthExtremumRule {
				tmp1 := rule.MaxLength
				tmp2 := rule.MinLength
				result.MaxLength = &tmp1
				result.MinLength = &tmp2
			}
			if rule.RuleType == constant.MaxWidthExtremumRule {
				tmp1 := rule.MaxWidth
				tmp2 := rule.MinWidth
				result.MaxWidth = &tmp1
				result.MinWidth = &tmp2
			}
			if rule.RuleType == constant.HeightSumExtremumRule {
				tmp1 := rule.MaxHeight
				tmp2 := rule.MinHeight
				result.MaxHeight = &tmp1
				result.MinHeight = &tmp2
			}
		}
	}
	return result
}

func GetSizeLimitFromLineRules(rules []*model.LinePackageLimitTab) *pb.SizeLimitInfo {
	var zero float64 = 0
	result := &pb.SizeLimitInfo{
		MaxLength: &zero,
		MinLength: &zero,
		MaxWidth:  &zero,
		MinWidth:  &zero,
		MaxHeight: &zero,
		MinHeight: &zero,
	}

	for _, rule := range rules {
		if rule.LimitFlag == constant.ENABLED {
			if rule.RuleType == constant.MaxLengthExtremumRule {
				tmp1 := rule.MaxLength
				tmp2 := rule.MinLength
				result.MaxLength = &tmp1
				result.MinLength = &tmp2
			}
			if rule.RuleType == constant.MaxWidthExtremumRule {
				tmp1 := rule.MaxWidth
				tmp2 := rule.MinWidth
				result.MaxWidth = &tmp1
				result.MinWidth = &tmp2
			}
			if rule.RuleType == constant.HeightSumExtremumRule {
				tmp1 := rule.MaxHeight
				tmp2 := rule.MinHeight
				result.MaxHeight = &tmp1
				result.MinHeight = &tmp2
			}
		}
	}
	return result
}
