package local_formula

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"

func CompareWith(symbol uint8, value float64, compareValue float64) bool {
	if symbol == constant.GreatThan {
		return value > compareValue
	}
	if symbol == constant.GreatThanOrEqual {
		return value >= compareValue
	}
	if symbol == constant.LessThan {
		return value < compareValue
	}
	if symbol == constant.LessThanOrEqual {
		return value <= compareValue
	}
	return false
}
