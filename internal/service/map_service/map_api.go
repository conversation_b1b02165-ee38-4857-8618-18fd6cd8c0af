package map_service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	json "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/station_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/schema/address"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/limiter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/monitor/cat_client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/geo/map_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/geo/schema/geo_map"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/geo/schema/matrix"
)

type MapServiceApi interface {
	// GetAddressCoordinate 获取地址经纬度（跳过缓存会增加成本，请先评估）
	GetAddressCoordinate(ctx context.Context, addressList []address.Address) (coord []address.GoogleCoordinateOutput, err error)
	// GetDrivingDistanceFromGoogle 获取路网距离（跳过缓存会增加成本，请先评估）
	GetDrivingDistanceFromGoogle(ctx context.Context, origins []address.CommonCoordinate, destinations []address.CommonCoordinate) (result []address.CoordDistance, err error)
	// SaveGoogleResult 保存google结果
	SaveGoogleResult(ctx context.Context, message address.GoogleResultMessage) error
}

type mapService struct {
	googleResultDao google_result_repo.GoogleResultDao
}

func NewMapService(googleResultDao google_result_repo.GoogleResultDao) *mapService {
	return &mapService{
		googleResultDao: googleResultDao,
	}
}

func (m *mapService) GetAddressCoordinate(ctx context.Context, addressList []address.Address) (coord []address.GoogleCoordinateOutput, err error) {
	_, catEndFunc := cat_client.TransactionStart(ctx)
	defer func() {
		catEndFunc(constant.CatHomeDeliveryAddressMatch, constant.MatchAddressFromGoogle, err)
	}()
	// 结果集
	mapAddrResult := map[address.Address]address.GoogleCoordinateOutput{}
	// 从数据库查询
	// 上层已经用缓存去兜底了，SPX Smart Sorting这里并没有去查数据库
	//noCacheList, err := g.getGeoCodingFromCache(ctx, addressList, skipCache, mapAddrResult)
	//if err != nil {
	//	return nil, err
	//}
	//
	//if len(noCacheList) == 0 {
	//	return g.buildCoordinateResult(ctx, addressList, mapAddrResult), nil
	//}

	if !config.GetMutableConf(ctx).GoogleCallFlowLimitSwitch {
		flowLimiter, err := limiter.GetFlowLimiter(station_constant.GoogleCallFlowLimiter)
		if err == nil && !flowLimiter.Allow() {
			logger.CtxLogErrorf(ctx, "GetAddressCoordinate flow limit")
			return nil, fmt.Errorf("GetAddressCoordinate flow limit")
		}
	}

	err = m.getGeoCodingFromGoogle(ctx, addressList, mapAddrResult)
	if err != nil {
		return nil, err
	}

	return m.buildCoordinateResult(ctx, addressList, mapAddrResult), nil
}

func (m *mapService) GetDrivingDistanceFromGoogle(ctx context.Context, origins []address.CommonCoordinate, destinations []address.CommonCoordinate) (result []address.CoordDistance, err error) {
	_, catEndFunc := cat_client.TransactionStart(ctx)
	defer func() {
		catEndFunc(constant.CatHomeDeliveryAddressMatch, constant.GetDistanceMatrixFromGoogle, err)
	}()

	mapPairResult := map[address.CoordDistancePair]address.CoordDistance{}

	// 从缓存读
	//noCachePairList := m.getDrivingDistanceFromCache(ctx, origins, destinations, skipCache, mapPairResult)
	//if len(noCachePairList) == 0 {
	//	return g.buildDistanceMatrixResult(ctx, origins, destinations, mapPairResult), nil
	//}

	pairList := []address.CoordDistancePair{}
	for _, origin := range origins {
		for _, destination := range destinations {
			pairList = append(pairList, address.CoordDistancePair{
				Origin:      origin,
				Destination: destination,
			})
		}
	}
	// 实时查询
	err = m.getDrivingDistanceFromGoogle(ctx, pairList, mapPairResult)
	if err != nil {
		return nil, err
	}

	return m.buildDistanceMatrixResult(ctx, origins, destinations, mapPairResult), nil
}

func (m *mapService) SaveGoogleResult(ctx context.Context, message address.GoogleResultMessage) error {
	for _, row := range message.ResultList {
		mdl := &google_result_repo.GeneralGoogleResultTab{
			APIPath:      row.APIPath,
			APICode:      row.APICode,
			APISource:    row.APISource,
			InputData:    row.InputData,
			InputDataMD5: m.buildInputMd5(row.InputData),
			OutputData:   row.OutputData,
			Ctime:        uint32(recorder.Now(ctx).Unix()),
			Mtime:        uint32(recorder.Now(ctx).Unix()),
		}
		logger.CtxLogInfof(ctx, "Save google result: %+v", mdl)
		err := m.googleResultDao.SaveGoogleResult(ctx, mdl)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Save google result failed, result=%+v, err=%v", mdl, err)
		}
	}
	return nil
}

func (m *mapService) getGeoCodingFromGoogle(ctx context.Context, noCacheList []address.Address, mapAddrResult map[address.Address]address.GoogleCoordinateOutput) error {
	if ctxhelper.IsShadow(ctx) {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatHomeDeliveryAddressMatch, constant.ShadowGetBuyerAddressCoordinate, constant.StatusSuccess, "")
		return fmt.Errorf("shadow flow is limit")
	}
	param := geo_map.AddressMatchQueryV1{
		Meta: geo_map.Meta{
			UseCase:    "BE.SPX.smart_zoning",
			Components: fmt.Sprintf("country:%s", utils.GetRegion(ctx)),
		},
	}
	for _, addr := range noCacheList {
		param.AddressList = append(param.AddressList, geo_map.AddressDetail{
			AddressL1: addr.AddressL1,
			AddressL2: addr.AddressL2,
			AddressL3: addr.AddressL3,
			AddressL4: addr.AddressL4,
			Address:   addr.Address,
			Zipcode:   addr.Zipcode,
		})
	}
	paramStr, _ := json.Marshal(param)
	logger.CtxLogInfof(ctx, "match address from google, request: %+v", paramStr)

	geoClient := geo.NewGeoClient()
	mapRsp, err := geoClient.MatchAddress(ctx, param)
	if err != nil {
		return err
	}
	if mapRsp.Error == 0 {
		for i := 0; i < len(noCacheList); i++ {
			if int(mapRsp.ResultList[i].Geometry.Location.Lng*1000000) == 0 &&
				int(mapRsp.ResultList[i].Geometry.Location.Lat*1000000) == 0 {
				continue
			}
			resOutput := address.GoogleCoordinateOutput{
				CommonCoordinate: address.CommonCoordinate{
					Lng: mapRsp.ResultList[i].Geometry.Location.Lng,
					Lat: mapRsp.ResultList[i].Geometry.Location.Lat,
				},
				StandardAddress: mapRsp.ResultList[i].OutputAddress,
			}
			mapAddrResult[noCacheList[i]] = resOutput
		}
	}
	return nil
}

func (m *mapService) buildCoordinateResult(ctx context.Context, addressList []address.Address, mapAddrResult map[address.Address]address.GoogleCoordinateOutput) []address.GoogleCoordinateOutput {
	locationList := []address.GoogleCoordinateOutput{}
	logger.CtxLogInfof(ctx, "match address from map, response: %+v", locationList)
	for i := 0; i < len(addressList); i++ {
		if res, found := mapAddrResult[addressList[i]]; found {
			locationList = append(locationList, res)
		} else {
			locationList = append(locationList, address.GoogleCoordinateOutput{})
		}
	}
	logger.CtxLogInfof(ctx, "match address from map, response: %+v", locationList)
	return locationList
}

func (m *mapService) getDrivingDistanceFromGoogle(ctx context.Context, noCachePairList []address.CoordDistancePair,
	mapPairResult map[address.CoordDistancePair]address.CoordDistance) error {
	if ctxhelper.IsShadow(ctx) {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatHomeDeliveryAddressMatch, constant.ShadowGetDrivingDistanceMatrix, constant.StatusSuccess, "")
		return fmt.Errorf("shadow flow is limit")
	}
	mapOrigin, mapDestination := map[address.CommonCoordinate]interface{}{}, map[address.CommonCoordinate]interface{}{}
	for _, pair := range noCachePairList {
		mapOrigin[pair.Origin] = nil
		mapDestination[pair.Destination] = nil
	}

	param := map_config.GetMapMatrixDefaultReq(ctx)
	param.Source = "google"
	param.Region = utils.GetRegion(ctx)
	for origin := range mapOrigin {
		param.Origins = append(param.Origins, matrix.Location{
			Lon: origin.Lng,
			Lat: origin.Lat,
		})
	}
	for destination := range mapDestination {
		param.Destinations = append(param.Destinations, matrix.Location{
			Lon: destination.Lng,
			Lat: destination.Lat,
		})
	}

	var geoClient = geo.GeoClient{}
	logger.CtxLogInfof(ctx, "Get distance matrix from map, request: %+v", param)
	rsp, err := geoClient.Matrix(ctx, param)
	if err != nil || rsp.Status != "SUCCESS" {
		logger.CtxLogInfof(ctx, "Get matrix distance from map failed, rsp=%v, err=%v", rsp, err)
		return fmt.Errorf("Matrix response failed")
	}

	if len(rsp.Distances) != len(mapOrigin) {
		logger.CtxLogErrorf(ctx, "Invalid response")
		return fmt.Errorf("Invalid response")
	}

	for i, origin := range param.Origins {
		oneOrigin := rsp.Distances[i]
		if len(oneOrigin) != len(param.Destinations) {
			logger.CtxLogErrorf(ctx, "Invalid response")
			return fmt.Errorf("Invalid response")
		}
		for j, dest := range param.Destinations {
			pair := address.CoordDistancePair{
				Origin: address.CommonCoordinate{
					Lng: origin.Lon,
					Lat: origin.Lat,
				},
				Destination: address.CommonCoordinate{
					Lng: dest.Lon,
					Lat: dest.Lat,
				},
			}
			mapPairResult[pair] = address.CoordDistance{
				CoordDistancePair: pair,
				IsNoResult:        false,
				Distance:          float64(oneOrigin[j]),
			}
			//inputStr, _ := json.Marshal(pair)
			//outputStr, _ := json.Marshal(address.MatrixDistance{
			//	Distance: float64(oneOrigin[j]),
			//})
			//resultModels = append(resultModels, &model.GeneralGoogleResultTab{
			//	APIPath:    config.GetMapMatrixUrl(ctx),
			//	APICode:    string(constant.GoogleApiCodeDistanceMatrix),
			//	APISource:  constant.GoogleApiSourceMap,
			//	InputData:  string(inputStr),
			//	OutputData: string(outputStr),
			//})
		}
	}

	//if len(resultModels) > 0 {
	//	goasync.GoAndRecover(func() {
	//		newCtx := goasync.NewChassisCtxWithCtxGlobalId(ctx)
	//		_ = g.sendSaveResultMessage(newCtx, resultModels)
	//	}, ctx)
	//
	//}

	return nil
}

func (m *mapService) buildDistanceMatrixResult(ctx context.Context, origins, destinations []address.CommonCoordinate,
	mapPairResult map[address.CoordDistancePair]address.CoordDistance) (result []address.CoordDistance) {
	for _, org := range origins {
		for _, dst := range destinations {
			pair := address.CoordDistancePair{
				Origin:      org,
				Destination: dst,
			}
			if res, found := mapPairResult[pair]; found {
				result = append(result, address.CoordDistance{
					CoordDistancePair: pair,
					IsNoResult:        false,
					Distance:          res.Distance,
					GetFromCache:      res.GetFromCache,
				})
			} else {
				result = append(result, address.CoordDistance{
					CoordDistancePair: pair,
					IsNoResult:        true,
				})
			}
		}
	}

	logger.CtxLogInfof(ctx, "distance matrix from google, response: %+v", result)
	return result
}

func (m *mapService) buildInputMd5(input string) string {
	hash := md5.Sum([]byte(input))
	return hex.EncodeToString(hash[:])
}
