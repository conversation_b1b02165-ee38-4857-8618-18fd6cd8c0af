package edd_pushing

import (
	"context"
	"fmt"
	"math/rand"
	"os"
	"path"
	"testing"

	edd_auto_update "git.garena.com/shopee/bg-logistics/algo/sls/edd-auto-update"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_delay_queue"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lts_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/waybill_center_service"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
	"git.garena.com/shopee/platform/service-governance/viewercontext/attr"
	uuid "github.com/satori/go.uuid"
)

/*
SPLN-28409

prerequisite:
product: 20020
update event  :   SOC Inbound
update rule   :   All Tracking

test cases
1. Has been pushed Pickup Done EDD, and the threshold_min is 0, threshold_max is 2, edd difference is 0, below threshold min ,not allowed to push
2. Has been pushed Pickup Done EDD, and the threshold_min is 0, threshold_max is 2, edd difference is 3, over threshold max ,not allowed to push
3. Has been pushed Pickup Done EDD, and the threshold_min is 0, threshold_max is 2, edd difference is 1. Max times is 1, will not allowed to push
4. Has been pushed Pickup Done EDD, and the threshold_min is 0, threshold_max is 2, edd difference is 1 and later than old edd. Max times is 2, but trending is deduction, not allowed to push
5. Has been pushed Pickup Done EDD, and the threshold_min is 0, threshold_max is 2, edd difference is 1 and later than old edd. Max times is 2, trending is extension, allowed to push
6. Has been pushed Pickup Done EDD, and the threshold_min is 0, threshold_max is 2, edd difference is 1 and earlier than old edd. Max times is 2, trending is extension, not allowed to push
7. Has been pushed Pickup Done EDD, and the threshold_min is 0, threshold_max is 2, edd difference is 1 and earlier than old edd. Max times is 2, trending is deduction, allowed to push
*/

func Test_Init(t *testing.T) {
	ctx := utils.NewCommonCtx(context.Background())
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("CID", "ID")
	_ = os.Setenv("PFB_NAME", "PFB_NAME=pfb-dms-dev-spln-33284")

	startup.InitSSCEnv()
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/cdttask"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}
	_, err = cf.InitMutableConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}
	cf.InitChassisConfig()

	if err = startup.InitLogger(c); err != nil {
		t.Fatalf("InitLogger Error:%v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}

	if err := cache.InitRefreshCache(c); err != nil {
		t.Fatalf("init refresh cache %v", err)
	}

	if err := startup.InitRedisCache(c); err != nil {
		t.Fatalf("init redis cache error: %s", err.Error())
	}

	if err := startup.InitLayeredCache(c); err != nil {
		t.Fatalf("init layered cache error:%v", err)
	}

	if err := startup.InitAbtest(c); err != nil {
		t.Fatalf("init abtest error:%v", err)
	}

}

func test_NewAlgoEdd(t *testing.T, ctx utils.LCOSContext, slsTN string, trackingCode string, eddUpdateType edd_auto_update.EddUpdateType, preemptiveUpdateEvent uint8, lineList []*cdt_calculation.LineInfo, isReturnOrder bool) *EDDJob {
	var forderId uint64 = 1715224986
	orderInfo := &lfs_service.LogisticOrderData{
		ForderId:          forderId,
		OrderSN:           "rg-80023-LID44-01131412332643",
		LmTrackingNumber:  "JP0870504991",
		LogisticProductId: "80023",
		ResourceId:        "LID24",
		DeliverCountry:    "ID",
		SellerAddress: &lfs_service.LogisticOrderAddressInfo{
			StateLocationId:    7361,
			CityLocationId:     7540,
			DistrictLocationId: 7549,
			PostCode:           "10110",
		},
		BuyerAddress: &lfs_service.LogisticOrderAddressInfo{
			StateLocationId:    326,
			CityLocationId:     408,
			DistrictLocationId: 409,
			PostCode:           "",
		},
	}
	addedTrackingList := []*schema.Tracking{
		{
			ResourceTn:        "",
			ResourceId:        "LID44",
			TrackingCode:      trackingCode,
			TrackingName:      trackingCode,
			Description:       "Paket sedang dalam proses transit  ke kota tujuan.",
			ResourceStatus:    "100",
			ResourceSubStatus: "",
			ActualTime:        1715091003,
		},
	}
	historyTrackingList := []*lts_service.TrackingData{}

	if eddUpdateType == edd_auto_update.UpdateOnDeadline {
		addedTrackingList = []*schema.Tracking{}
	}

	eddService, lcosErr := NewEDDJobByAlgo(ctx, addedTrackingList, historyTrackingList, orderInfo, slsTN, eddUpdateType, preemptiveUpdateEvent, lineList, isReturnOrder)
	if lcosErr != nil {
		t.Errorf(lcosErr.Msg)
	}
	return eddService
}

func test_OldEddJob(t *testing.T, ctx utils.LCOSContext, slsTN string, trackingCode string, eddUpdateType edd_auto_update.EddUpdateType, preemptiveUpdateEvent uint8, productAutoUpdateConf *edd_auto_update_rule.EddAutoUpdateRule) *EDDJob {
	var forderId uint64 = 1715224986
	orderInfo := &lfs_service.LogisticOrderData{
		ForderId:          forderId,
		OrderSN:           "rg-80023-LID44-01131412332643",
		LmTrackingNumber:  "JP0870504991",
		LogisticProductId: "80023",
		ResourceId:        "LID24",
		DeliverCountry:    "ID",
		SellerAddress: &lfs_service.LogisticOrderAddressInfo{
			StateLocationId:    7361,
			CityLocationId:     7540,
			DistrictLocationId: 7549,
			PostCode:           "10110",
		},
		BuyerAddress: &lfs_service.LogisticOrderAddressInfo{
			StateLocationId:    326,
			CityLocationId:     408,
			DistrictLocationId: 409,
			PostCode:           "",
		},
	}
	addedTrackingList := []*schema.Tracking{
		{
			ResourceTn:        "",
			ResourceId:        "LID44",
			TrackingCode:      trackingCode,
			TrackingName:      trackingCode,
			Description:       "Paket sedang dalam proses transit  ke kota tujuan.",
			ResourceStatus:    "100",
			ResourceSubStatus: "",
			ActualTime:        1715091003,
		},
	}
	historyTrackingList := []*lts_service.TrackingData{}
	eddService, lcosErr := NewEDDJob(ctx, addedTrackingList, historyTrackingList, orderInfo, productAutoUpdateConf, slsTN, eddUpdateType, preemptiveUpdateEvent)
	if lcosErr != nil {
		t.Errorf(lcosErr.Msg)
	}
	return eddService
}

func testNewContext() utils.LCOSContext {
	ctxWithPfb, _ := viewercontext.Start(context.Background(), attr.WithPFB("pfb-display-edd"))
	ctxWithPfb = context.WithValue(ctxWithPfb, constant.RequestIdKey, uuid.NewV4().String())
	return utils.NewCommonCtx(ctxWithPfb)
}

func testNewContextWithRetryTimes(retryTimes int) utils.LCOSContext {
	ctxWithPfb, _ := viewercontext.Start(context.Background(), attr.WithPFB("pfb-dms-qa-spln-33284"))
	ctxWithPfb = context.WithValue(ctxWithPfb, constant.EDDRetryTimesKey, retryTimes)
	return utils.NewCommonCtx(ctxWithPfb)
}

func testGetEDDHistory(ctx utils.LCOSContext, slsTN string) []*edd_history.EddHistoryTab {
	e := edd_history.NewEddHistoryDao()
	got, _ := e.GetEDDHistoryBySlsTn(ctx, slsTN, edd_history.TableName(ctx, ""), false)
	return got
}

func TestCanPushing(t *testing.T) {
	Test_Init(t)
	var (
		ctx          = testNewContext()
		slsTN        = "ID244519166677LT"
		region       = "ID"
		trackingCode = "F100"
		lineList     = []*cdt_calculation.LineInfo{
			{LineID: "LID44", SubType: 256},
		}
	)
	eddService := test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, false)
	now := int64(utils.GetTimestamp(ctx))
	algoEddMin := now + 600
	algoEddMax := now + 600

	eddMin, eddMax, updateType, err := eddService.CheckAndGetEddForPushing(ctx, algoEddMin, algoEddMax, region, 0)
	if err != nil {
		t.Errorf("push error： %s", utils.MarshToStringWithoutError(err))
	}
	fmt.Println(eddMin, eddMax, updateType)
	if updateType&edd_constant.UpdateEddMax != edd_constant.UpdateEddMax {
		t.Errorf("update type error： %s", utils.MarshToStringWithoutError(err))
	}

	eddService.eddHistories = []*edd_history.EddHistoryTab{
		{
			Edd:        algoEddMax,
			EddMin:     algoEddMin,
			UpdateType: 3,
		},
	}
	eddMin, eddMax, updateType, err = eddService.CheckAndGetEddForPushing(ctx, algoEddMin, algoEddMax, region, 0)
	if err == nil || err.RetCode != lcos_error.OnlyCanUpdateDDLError {
		t.Errorf("push error： %s", utils.MarshToStringWithoutError(err))
	}
	fmt.Println(eddMin, eddMax, updateType)
	if updateType != 0 {
		t.Errorf("update type error")
	}

	eddService.GetEDDAutoUpdateConf().MaxTimes = 2
	eddService.eddHistories = []*edd_history.EddHistoryTab{
		{
			Edd:        algoEddMax,
			EddMin:     0,
			UpdateType: 3,
		},
	}
	eddMin, eddMax, updateType, err = eddService.CheckAndGetEddForPushing(ctx, algoEddMax, algoEddMax+86400, region, 0)
	if err != nil {
		t.Errorf("push error： %s", utils.MarshToStringWithoutError(err))
	}
	fmt.Println(eddMin, eddMax, updateType)

	eddService.GetEDDAutoUpdateConf().MaxTimes = 1
	eddMin, eddMax, updateType, err = eddService.CheckAndGetEddForPushing(ctx, algoEddMax, algoEddMax+86400, region, 0)
	if err == nil || err.RetCode != lcos_error.SchemaParamsErrorCode {
		t.Errorf("push error： %s", utils.MarshToStringWithoutError(err))
	}
	fmt.Println(eddMin, eddMax, updateType)
	if updateType != 0 {
		t.Errorf("update type error")
	}
}
func TestAlgoModelCache(t *testing.T) {
	Test_Init(t)

	var (
		ctx          = testNewContext()
		slsTN        = "ID244519166673LT"
		trackingCode = "F100"
		region       = "ID"
		lineList     = []*cdt_calculation.LineInfo{
			{LineID: "LID44", SubType: 256},
		}
	)
	eddService := test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, false)
	if !eddService.IsAlgoModelEffective(ctx, region) {
		t.Errorf("model not effecitve")
	}
	fmt.Println("model effective")
	if !eddService.IsAlgoModelEffective(ctx, region) {
		t.Errorf("model not effecitve")
	}
	fmt.Println("model effective")
}

func TestOmsReturn(t *testing.T) {
	Test_Init(t)

	var (
		ctx   = testNewContext()
		slsTN = "SG2400471592411T"
	)

	wbcService := waybill_center_service.NewWaybillCenterService(ctx, "SG")
	resultMap, lcosErr := wbcService.BatchGetLaneCodeInfo(ctx, []string{slsTN})

	fmt.Println(utils.MarshToStringWithoutError(resultMap[slsTN]), lcosErr, 1111)
}

func TestReverseEDD(t *testing.T) {
	Test_Init(t)

	var (
		ctx                 = testNewContext()
		slsTN               = fmt.Sprintf("ID2445%08dLT", rand.Intn(8))
		trackingCode        = "F100"
		region              = "ID"
		buyerId      uint64 = 19030034 // 101291039
		actualTime   int64  = 1715091003
		lineList            = []*cdt_calculation.LineInfo{
			{LineID: "LID44", SubType: 256},
		}
	)
	found := false
	ts, _ := cf.GetReverseEventTrackingCodeList(ctx, "Shipped Out-Reverse")
	fmt.Println(ts)
	for _, tc := range ts {
		if tc == "F048" {
			found = true
		}
	}
	if !found {
		t.Errorf("not found reverse tracking code")
	}

	fmt.Println(cf.GetMutableConf(ctx).ReverseEDDConfig.ReverseProductSwitchMap)

	if !cf.GetReverseEddSwitchFlag(ctx, "80092") {
		t.Errorf("not found reverse switch %s", "80092")
	}
	if !cf.GetReverseEddSwitchFlag(ctx, "80014") {
		t.Errorf("not found reverse switch %s", "80014")
	}

	fmt.Println(222, cf.GetMutableConf(ctx).EDDConfig.AlgoConfig, cf.GetMutableConf(ctx).ReverseEDDConfig.ReverseEventTrackingCodeMap, cf.GetMutableConf(ctx).ReverseEDDConfig.ReverseProductSwitchMap)
	eddService := test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, true)
	lcosErr := eddService.CanSwitchAlgoEDD(ctx, region, buyerId)

	if lcosErr != nil {
		fmt.Println(lcosErr)
	}

	fmt.Println(eddService.updateEvent)

	// 第一条记录是没有algo，强制走algo
	if !eddService.GetFirstAlgoFlag() {
		edd, _ := eddService.GetEDDInfoByAlgo(ctx, region, "", buyerId, lineList, actualTime)
		eddRecord, lcosErr := eddService.GenerateEDDHistory(ctx, edd.EddMin, edd.EddMax, edd.EddProcess, uint8(eddService.getEDDUpdateType()), nil, buyerId, false, constant.FALSE)
		if lcosErr != nil {
			t.Errorf("gen history error %s", utils.MarshToStringWithoutError(lcosErr))
		}
		fmt.Println(utils.MarshToStringWithoutError(eddRecord))
		lcosErr = eddService.StoreEDDInfo(ctx, eddRecord)
		if lcosErr != nil {
			t.Errorf("store history error %s", utils.MarshToStringWithoutError(lcosErr))
		}
		fmt.Println("gen history ok")
	} else if lcosErr != nil {
		t.Errorf("switch algo error %s", utils.MarshToStringWithoutError(lcosErr))
	}

	// 同一个单第二次进来，允许走 algo
	eddService = test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, false)
	lcosErr = eddService.CanSwitchAlgoEDD(ctx, region, buyerId)
	if lcosErr != nil {
		t.Errorf("double check algo error %s", utils.MarshToStringWithoutError(lcosErr))
	}
	fmt.Println("same edd ok")
}

func TestEDDSameAlgo(t *testing.T) {
	Test_Init(t)

	var (
		ctx                 = testNewContext()
		slsTN               = "ID244519166673LT"
		trackingCode        = "F100"
		region              = "ID"
		buyerId      uint64 = 19030034 // 101291039
		actualTime   int64  = 1715091003
		lineList            = []*cdt_calculation.LineInfo{
			{LineID: "LID44", SubType: 256},
		}
	)
	eddService := test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, false)
	lcosErr := eddService.CanSwitchAlgoEDD(ctx, region, buyerId)

	// 第一条记录是没有algo，强制走algo
	if !eddService.GetFirstAlgoFlag() {
		edd, _ := eddService.GetEDDInfoByAlgo(ctx, region, "", buyerId, lineList, actualTime)
		eddRecord, lcosErr := eddService.GenerateEDDHistory(ctx, edd.EddMin, edd.EddMax, edd.EddProcess, uint8(eddService.getEDDUpdateType()), nil, buyerId, false, constant.FALSE)
		if lcosErr != nil {
			t.Errorf("gen history error %s", utils.MarshToStringWithoutError(lcosErr))
		}
		lcosErr = eddService.StoreEDDInfo(ctx, eddRecord)
		if lcosErr != nil {
			t.Errorf("store history error %s", utils.MarshToStringWithoutError(lcosErr))
		}
		fmt.Println("gen history ok")
	} else if lcosErr != nil {
		t.Errorf("switch algo error %s", utils.MarshToStringWithoutError(lcosErr))
	}

	// 同一个单第二次进来，允许走 algo
	eddService = test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, false)
	lcosErr = eddService.CanSwitchAlgoEDD(ctx, region, buyerId)
	if lcosErr != nil {
		t.Errorf("double check algo error %s", utils.MarshToStringWithoutError(lcosErr))
	}
	fmt.Println("same edd ok")
}

func TestEDDAlgoDDLJob(t *testing.T) {
	Test_Init(t)

	var (
		ctx                 = testNewContext()
		slsTN               = "ID240400887040GT"
		trackingCode        = "F100"
		region              = "ID"
		buyerId      uint64 = 19030034 // 101291039
		lineList            = []*cdt_calculation.LineInfo{
			{LineID: "LID44", SubType: 256},
		}
	)
	eddService := test_NewAlgoEdd(t, ctx, slsTN, trackingCode, edd_auto_update.UpdateOnDeadline, edd_constant.OutForDelivery, lineList, false)
	edd, lcosErr := eddService.SwitchGetEDDInfoByAlgo(ctx, region, "", buyerId, lineList)
	if lcosErr != nil {
		t.Errorf("SwitchGetEDDInfoByAlgo error %s", utils.MarshToStringWithoutError(lcosErr))
	}
	fmt.Println(utils.MarshToStringWithoutError(edd))

	eddMin, eddMax, updateType, err := eddService.CheckAndGetEddForPushing(ctx, edd.GetEddMin(), edd.GetEddMax(), region, 0)
	if err != nil {
		t.Errorf("check get error %s", utils.MarshToStringWithoutError(err))
	}
	fmt.Println(eddMin, eddMax, updateType, err)
	eddWaybillDelayQueue := edd_delay_queue.NewEddWaybillDelayQueue()

	if edd.DDL < int64(utils.GetTimestamp(ctx)) {
		edd.DDL = int64(utils.GetTimestamp(ctx) + 600)
	}

	waitingWaybill := eddService.HandleWaybillDDL(ctx, edd.DDLCdt, edd.DDL, region, eddWaybillDelayQueue)
	if waitingWaybill == nil {
		t.Error("waybill empty")
	}
	fmt.Println(utils.MarshToStringWithoutError(waitingWaybill))

	eddRecord, lcosErr := eddService.GenerateEDDHistory(ctx, eddMin, eddMax, edd.GetEddProcess(), updateType, waitingWaybill, buyerId, false, constant.FALSE)
	if lcosErr != nil {
		t.Errorf("GenerateEDDHistory error %s", utils.MarshToStringWithoutError(lcosErr))
	}
	fmt.Println(utils.MarshToStringWithoutError(eddRecord))
	if lcosErr = eddService.StoreEDDInfo(ctx, eddRecord); lcosErr != nil {
		t.Errorf("StoreEDDInfo error %s", utils.MarshToStringWithoutError(lcosErr))
	}
}

func TestEDDAlgoDDL(t *testing.T) {
	Test_Init(t)

	var (
		ctx                 = testNewContext()
		slsTN               = "ID244519166675LT"
		trackingCode        = "F100"
		region              = "ID"
		buyerId      uint64 = 19030034 // 101291039
		lineList            = []*cdt_calculation.LineInfo{
			{LineID: "LID44", SubType: 256},
		}
	)
	eddService := test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, false)
	edd, lcosErr := eddService.SwitchGetEDDInfoByAlgo(ctx, region, "", buyerId, lineList)
	if lcosErr != nil {
		t.Errorf("algo switch error %s", utils.MarshToStringWithoutError(lcosErr))
	}
	fmt.Println("get edd", edd.EddMax, edd.DDL, edd.DDLCdt)
	if (edd.EddMax - edd.DDL) != 86400 {
		t.Errorf("ddl error: %d %d %f", edd.EddMax, edd.DDL, edd.DDLCdt)
	}
	edd.DDL = 1715342400 // 2024-05-10 20:00
	_, lcosErr = eddService.GetEventTime(ctx)
	if lcosErr != nil {
		t.Errorf("actual time error %s", utils.MarshToStringWithoutError(lcosErr))
	}
	lcosErr = eddService.CheckCanPushEDD(ctx)
	if lcosErr != nil {
		t.Errorf("push error %s", utils.MarshToStringWithoutError(lcosErr))
	}
	eddMin, eddMax, updateType, err := eddService.CheckAndGetEddForPushing(ctx, edd.GetEddMin(), edd.GetEddMax(), region, 0)
	if err != nil {
		t.Errorf("check get error %s", utils.MarshToStringWithoutError(err))
	}
	if eddMin != edd.EddMin || eddMax != edd.EddMax {
		t.Errorf("edd min error %d != %d", eddMin, edd.EddMin)
	}
	if updateType != edd_constant.UpdateAll {
		t.Errorf("updateType error %d", updateType)
	}
}

func TestRetryTimes(t *testing.T) {
	Test_Init(t)

	var (
		ctx                 = testNewContextWithRetryTimes(0)
		ctx2                = testNewContextWithRetryTimes(2)
		ctx3                = testNewContextWithRetryTimes(1)
		slsTN               = "ID244519166673LT"
		trackingCode        = "F100"
		region              = "ID"
		buyerId      uint64 = 19030034 // 101291039
		lineList            = []*cdt_calculation.LineInfo{
			{LineID: "LID44", SubType: 256},
		}
	)
	eddService := test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, false)
	lcosErr := eddService.CanSwitchAlgoEDD(ctx, region, buyerId)
	if lcosErr.Msg == "exceed max retry times" {
		t.Errorf("invalid max times %s", utils.MarshToStringWithoutError(lcosErr))
	}

	eddService = test_NewAlgoEdd(t, ctx2, slsTN, trackingCode, 0, 0, lineList, false)
	lcosErr = eddService.CanSwitchAlgoEDD(ctx2, region, buyerId)
	if lcosErr.Msg != "exceed max retry times" {
		t.Errorf("invalid max times %s", utils.MarshToStringWithoutError(lcosErr))
	}

	eddService = test_NewAlgoEdd(t, ctx3, slsTN, trackingCode, 0, 0, lineList, false)
	lcosErr = eddService.CanSwitchAlgoEDD(ctx3, region, buyerId)
	if lcosErr.Msg == "exceed max retry times" {
		t.Errorf("invalid max times %s", utils.MarshToStringWithoutError(lcosErr))
	}

}

func TestProductSwitch(t *testing.T) {
	Test_Init(t)

	var (
		ctx          = testNewContext()
		slsTN        = "ID244519166671LT"
		region       = "ID"
		trackingCode = "F100"
		lineList     = []*cdt_calculation.LineInfo{
			{LineID: "LID44", SubType: 256},
		}
	)

	eddService := test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, false)
	eddService.orderInfo.LogisticProductId = "89999"
	fmt.Println(eddService.IsAlgoModelEffective(ctx, region))

	eddService.orderInfo.LogisticProductId = "80012"
	fmt.Println(eddService.IsAlgoModelEffective(ctx, region))
}

func TestCanSwitchAlgoEDD(t *testing.T) {
	Test_Init(t)

	var (
		ctx                 = testNewContext()
		slsTN               = "ID244519166671LT"
		region              = "ID"
		buyerId      uint64 = 19030034 // 101291039
		actualTime   int64  = 1715091003
		trackingCode        = "F100"
		lineList            = []*cdt_calculation.LineInfo{
			{LineID: "LID44", SubType: 256},
		}
	)

	eddService := test_NewAlgoEdd(t, ctx, slsTN, trackingCode, 0, 0, lineList, false)
	eventStr, _ := edd_constant.GetUpdateEventString(eddService.updateEvent)
	fmt.Println(eventStr, eddService.updateRule, eddService.eddUpdateType)

	fmt.Println("222")
	ok := eddService.CanSwitchAlgoEDD(ctx, region, buyerId)
	t.Logf("ok=%s abdata=%v", utils.MarshToStringWithoutError(ok), eddService.ABtestGroup)
	fmt.Println(ok, eddService.ABtestGroup)

	edd, lcosErr := eddService.GetEDDInfoByAlgo(ctx, region, "", buyerId, lineList, actualTime)
	fmt.Println(111, utils.MarshToStringWithoutError(edd), utils.MarshToStringWithoutError(eddService.AlgoResult), utils.MarshToStringWithoutError(lcosErr))

	edd, lcosErr = eddService.SwitchGetEDDInfoByAlgo(ctx, region, "", buyerId, lineList)
	fmt.Println(111, utils.MarshToStringWithoutError(edd), utils.MarshToStringWithoutError(eddService.AlgoResult), utils.MarshToStringWithoutError(lcosErr))

	eventStr, _ = edd_constant.GetUpdateEventString(eddService.updateEvent)
	fmt.Println(eventStr, eddService.updateRule, eddService.eddUpdateType)

	eddService = test_NewAlgoEdd(t, ctx, slsTN, trackingCode, edd_auto_update.UpdateOnDeadline, edd_constant.OutForDelivery, lineList, false)
	edd, lcosErr = eddService.SwitchGetEDDInfoByAlgo(ctx, region, "", buyerId, lineList)
	fmt.Println(222, utils.MarshToStringWithoutError(edd), utils.MarshToStringWithoutError(eddService.AlgoResult), utils.MarshToStringWithoutError(lcosErr))

}

func TestEDDJob_ValidateEDD(t *testing.T) {
	// GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn
	// first time push pickup done event
	eddHistories := []*edd_history.EddHistoryTab{
		{
			ForderID:              111,
			ProductID:             "20020",
			IsLM:                  0,
			IsFirstTime:           1,
			EventTime:             1677636000, // 2023-03-01 10:00:00 +0800
			ResourceID:            "LMY1",
			SlsTN:                 "**********",
			LmTN:                  "MYTEST111",
			Edd:                   1677808800, // 2023-03-03 14:30:00 +0800
			EddRecalculationCount: 0,
			UpdateEvent:           edd_constant.ShippedOut,
			UpdateType:            edd_constant.UpdateEddMax,
		},
	}

	type fields struct {
		productAutoUpdateConf *edd_auto_update_rule.EddAutoUpdateRule
		updateEvent           uint8
		updateRule            uint8
		updateTrending        uint8
		eventTime             int64
		slsTN                 string
		eddHistories          []*edd_history.EddHistoryTab
	}
	type args struct {
		ctx utils.LCOSContext
		edd int64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		{
			name: "EDD Difference Below Threshold",
			fields: fields{
				productAutoUpdateConf: &edd_auto_update_rule.EddAutoUpdateRule{
					Region:      "MY",
					ProductId:   "20020",
					ProductName: "test",
					IsCB:        0,
					UpdateEvents: []edd_auto_update_rule.UpdateEvent{{
						Event:          edd_constant.SOCInbound,
						Rule:           edd_constant.AllTracking,
						EddMaxTrending: edd_constant.FreeTrending,
					}},
					MaxTimes:           1,
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 2,
					EnableStatus:       1,
					Operator:           "<EMAIL>",
				},
				updateEvent:    edd_constant.SOCInbound,
				updateRule:     edd_constant.AllTracking,
				updateTrending: edd_constant.FreeTrending,
				eventTime:      int64(utils.GetTimestamp(context.Background())),
				eddHistories:   eddHistories,
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				edd: 1677825000, // 2023-03-03 14:30:00 +0800
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, ""),
		},
		{
			name: "EDD Difference Over Threshold",
			fields: fields{
				productAutoUpdateConf: &edd_auto_update_rule.EddAutoUpdateRule{
					Region:      "MY",
					ProductId:   "20020",
					ProductName: "test",
					IsCB:        0,
					UpdateEvents: []edd_auto_update_rule.UpdateEvent{{
						Event:          edd_constant.SOCInbound,
						Rule:           edd_constant.AllTracking,
						EddMaxTrending: edd_constant.FreeTrending,
					}},
					MaxTimes:           1,
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 2,
					EnableStatus:       1,
					Operator:           "<EMAIL>",
				},
				updateEvent:    edd_constant.SOCInbound,
				updateRule:     edd_constant.AllTracking,
				updateTrending: edd_constant.FreeTrending,
				eventTime:      int64(utils.GetTimestamp(context.Background())),
				eddHistories:   eddHistories,
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				edd: 1678032000, // 2023-03-06 00:00:00 +0800
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, ""),
		},
		{
			name: "EDD Difference Between Threshold, but reach max times",
			fields: fields{
				productAutoUpdateConf: &edd_auto_update_rule.EddAutoUpdateRule{
					Region:      "MY",
					ProductId:   "20020",
					ProductName: "test",
					IsCB:        0,
					UpdateEvents: []edd_auto_update_rule.UpdateEvent{{
						Event:          edd_constant.SOCInbound,
						Rule:           edd_constant.AllTracking,
						EddMaxTrending: edd_constant.FreeTrending,
					}},
					MaxTimes:           1,
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 2,
					EnableStatus:       1,
					Operator:           "<EMAIL>",
				},
				updateEvent:    edd_constant.SOCInbound,
				updateRule:     edd_constant.AllTracking,
				updateTrending: edd_constant.FreeTrending,
				eventTime:      int64(utils.GetTimestamp(context.Background())),
				eddHistories:   eddHistories,
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				edd: 1677859200, // 2023-03-04 00:00:00 +0800
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, ""),
		},
		{
			name: "EDD Difference Between Threshold, not reach max times, but later than old edd while trending is deduction",
			fields: fields{
				productAutoUpdateConf: &edd_auto_update_rule.EddAutoUpdateRule{
					Region:      "MY",
					ProductId:   "20020",
					ProductName: "test",
					IsCB:        0,
					UpdateEvents: []edd_auto_update_rule.UpdateEvent{{
						Event:          edd_constant.SOCInbound,
						Rule:           edd_constant.AllTracking,
						EddMaxTrending: edd_constant.DeductTrending,
					}},
					MaxTimes:           2,
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 2,
					EnableStatus:       1,
					Operator:           "<EMAIL>",
				},
				updateEvent:    edd_constant.SOCInbound,
				updateRule:     edd_constant.AllTracking,
				updateTrending: edd_constant.DeductTrending,
				eventTime:      int64(utils.GetTimestamp(context.Background())),
				eddHistories:   eddHistories,
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				edd: 1677859200, // 2023-03-04 00:00:00 +0800
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, ""),
		},
		{
			name: "EDD Difference Between Threshold, not reach max times, later than old edd and trending is extension",
			fields: fields{
				productAutoUpdateConf: &edd_auto_update_rule.EddAutoUpdateRule{
					Region:      "MY",
					ProductId:   "20020",
					ProductName: "test",
					IsCB:        0,
					UpdateEvents: []edd_auto_update_rule.UpdateEvent{{
						Event:          edd_constant.SOCInbound,
						Rule:           edd_constant.AllTracking,
						EddMaxTrending: edd_constant.ExtendTrending,
					}},
					MaxTimes:           2,
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 2,
					EnableStatus:       1,
					Operator:           "<EMAIL>",
				},
				updateEvent:    edd_constant.SOCInbound,
				updateRule:     edd_constant.AllTracking,
				updateTrending: edd_constant.ExtendTrending,
				eventTime:      int64(utils.GetTimestamp(context.Background())),
				eddHistories:   eddHistories,
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				edd: 1677859200, // 2023-03-04 00:00:00 +0800
			},
			want: nil,
		},
		{
			name: "EDD Difference Between Threshold, not reach max times, but earlier than old edd while trending is extension",
			fields: fields{
				productAutoUpdateConf: &edd_auto_update_rule.EddAutoUpdateRule{
					Region:      "MY",
					ProductId:   "20020",
					ProductName: "test",
					IsCB:        0,
					UpdateEvents: []edd_auto_update_rule.UpdateEvent{{
						Event:          edd_constant.SOCInbound,
						Rule:           edd_constant.AllTracking,
						EddMaxTrending: edd_constant.ExtendTrending,
					}},
					MaxTimes:           2,
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 2,
					EnableStatus:       1,
					Operator:           "<EMAIL>",
				},
				updateEvent:    edd_constant.SOCInbound,
				updateRule:     edd_constant.AllTracking,
				updateTrending: edd_constant.ExtendTrending,
				eventTime:      int64(utils.GetTimestamp(context.Background())),
				eddHistories:   eddHistories,
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				edd: 1677686400, // 2023-03-02 00:00:00 +0800
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, ""),
		},
		{
			name: "EDD Difference Between Threshold, not reach max times, earlier than old edd and trending is deduction",
			fields: fields{
				productAutoUpdateConf: &edd_auto_update_rule.EddAutoUpdateRule{
					Region:      "MY",
					ProductId:   "20020",
					ProductName: "test",
					IsCB:        0,
					UpdateEvents: []edd_auto_update_rule.UpdateEvent{{
						Event:          edd_constant.SOCInbound,
						Rule:           edd_constant.AllTracking,
						EddMaxTrending: edd_constant.DeductTrending,
					}},
					MaxTimes:           2,
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 2,
					EnableStatus:       1,
					Operator:           "<EMAIL>",
				},
				updateEvent:    edd_constant.SOCInbound,
				updateRule:     edd_constant.AllTracking,
				updateTrending: edd_constant.DeductTrending,
				eventTime:      int64(utils.GetTimestamp(context.Background())),
				eddHistories:   eddHistories,
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				edd: 1677686400, // 2023-03-02 00:00:00 +0800
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			E := &EDDJob{
				productAutoUpdateConf: tt.fields.productAutoUpdateConf,
				updateEvent:           tt.fields.updateEvent,
				updateRule:            tt.fields.updateRule,
				eddMaxUpdateTrending:  tt.fields.updateTrending,
				eventTime:             tt.fields.eventTime,
				slsTN:                 tt.fields.slsTN,
				eddHistories:          tt.fields.eddHistories,
			}
			if _, got := E.CheckCanUpdateEDD(tt.args.ctx, 0, tt.args.edd); !((got == nil) == (tt.want == nil)) {
				t.Errorf("CheckCanUpdateEDD() = %v, want %v", got, tt.want)
			} else if got != nil {
				t.Log(got.Msg)
			}
		})
	}
}
