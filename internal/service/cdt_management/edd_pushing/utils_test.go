package edd_pushing

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"testing"
)

func Test_dealWithLaneCode(t *testing.T) {
	type args struct {
		laneCode    string
		updateEvent uint8
		cbFlag      bool
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test cb product lane code",
			args: args{
				laneCode:    "A|B",
				updateEvent: edd_constant.DestinationInbound,
				cbFlag:      true,
			},
			want: "B",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := DealWithLaneCode(tt.args.laneCode, tt.args.updateEvent, tt.args.cbFlag); got != tt.want {
				t.Errorf("dealWithLaneCode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generateDiffString(t *testing.T) {
	type args struct {
		diff int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test 0 day",
			args: args{
				diff: 0,
			},
			want: "0",
		},
		{
			name: "test 1 day",
			args: args{
				diff: 24*3600 - 1,
			},
			want: "1",
		},
		{
			name: "test 2 day",
			args: args{
				diff: 24 * 3600,
			},
			want: "2",
		},
		{
			name: "test 5 day",
			args: args{
				diff: 5 * 24 * 3600,
			},
			want: "5",
		},
		{
			name: "test 5 more day",
			args: args{
				diff: 1000*5*24*3600 + 10,
			},
			want: "5",
		},
		{
			name: "test -5 more day",
			args: args{
				diff: -5 * 24 * 3600,
			},
			want: "-5",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateDiffString(tt.args.diff); got != tt.want {
				t.Errorf("generateDiffString() = %v, want %v", got, tt.want)
			}
		})
	}
}
