package edd_pushing

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"time"

	edd_auto_update "git.garena.com/shopee/bg-logistics/algo/sls/edd-auto-update"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	abtest_edd "git.garena.com/shopee/bg-logistics/logistics/logistics-abtest/api/edd"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/redislibv2"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/datetime"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/edd_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/math"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/algo_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/tools"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_delay_queue"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/abtest_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/algo_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcos_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lpop_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lts_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/waybill_center_service"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	orderPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"
	"github.com/gogo/protobuf/proto"
)

type EDDJob struct {
	addedTrackingList     []*schema.Tracking
	historyTrackingList   []*lts_service.TrackingData
	orderInfo             *lfs_service.LogisticOrderData
	productAutoUpdateConf *edd_auto_update_rule.EddAutoUpdateRule
	updateEvent           uint8
	updateRule            uint8
	eddMaxUpdateTrending  uint8
	eddMinUpdateTrending  uint8
	eventTime             int64
	slsTN                 string
	eventTrackingCodeList []string
	eddHistories          []*edd_history.EddHistoryTab
	eddDao                edd_history.EddHistoryDAO
	eddModelConfDao       algo_model_conf.AlgoModelConfTabDAO
	spexService           spex_service.SpexService

	eddMinAvailable bool // SPLN-27376

	nextPreemptiveEvent *edd_auto_update_rule.UpdateEvent // SPLN-29072 0 means cannot add ddl to next event
	eddUpdateType       edd_auto_update.EddUpdateType

	// SPLN-31383
	ddlOnlyHistories []*edd_history.EddHistoryTab // store this info to get total times of ddl

	// SPLN-33284
	AlgoResult  *edd_history.AlgoResult
	ABtestGroup *edd_history.ABTestGroup

	AlgoFlag bool

	// SPLN-33587 仅algo model用
	algoReverseFlag bool

	// algo edd修正标记。无业务逻辑，仅用于保存edd history做数据分析
	// 0-算法返回的原始edd
	// 1-被range limit修正后的edd
	AlgoEddModifyFlag edd_constant.AlgoEddModifyFlag

	// SPLN-34825 是否已迟到EDD更新。0-非迟到EDD更新，1-已迟到EDD更新
	IsLateEddUpdate uint8
}

func NewEDDJob(ctx utils.LCOSContext,
	addedTrackingList []*schema.Tracking,
	historyTrackingList []*lts_service.TrackingData,
	orderInfo *lfs_service.LogisticOrderData,
	productAutoUpdateConf *edd_auto_update_rule.EddAutoUpdateRule,
	slsTN string,
	eddUpdateType edd_auto_update.EddUpdateType,
	preemptiveUpdateEvent uint8,
) (*EDDJob, *lcos_error.LCOSError) {
	eddJob := &EDDJob{
		eddDao:                edd_history.NewEddHistoryDao(),
		eddModelConfDao:       algo_model_conf.NewAlgoModelConfigDao(),
		spexService:           spex_service.NewSpexService(),
		addedTrackingList:     addedTrackingList,
		historyTrackingList:   historyTrackingList,
		orderInfo:             orderInfo,
		productAutoUpdateConf: productAutoUpdateConf,
		slsTN:                 slsTN,
		eddUpdateType:         eddUpdateType,
	}
	if eddJob.IsPreemptiveUpdate() {
		eddJob.updateEvent = preemptiveUpdateEvent // SPLN-29072 for preemptive update, need to set update event as preemptive update event
	}
	return eddJob, eddJob.Initiate(ctx)
}

func (E *EDDJob) Initiate(ctx utils.LCOSContext) *lcos_error.LCOSError {

	// SPLN-29072, for preemptive update, no need to init update rule or trending, but need to set next update event to itself
	if E.IsPreemptiveUpdate() {
		if E.GetEDDAutoUpdateConf() == nil {
			return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot update when edd auto update rule is disabled|sls_tn=[%s], product_id=[%s], update_event=[%s]", E.GetSlsTN(), E.orderInfo.LogisticProductId, E.GetUpdateEventString())
		}
		if updateRule, lcosErr := E.GetEDDAutoUpdateConf().FindEvent(E.GetUpdateEvent()); lcosErr == nil {
			if updateRule.PreemptLateParcels == constant.FALSE {
				return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "preemptive late parcels is not enabled for update event in edd auto update rule|sls_tn=[%s], product_id=[%s], update_event=[%s]", E.GetSlsTN(), E.orderInfo.LogisticProductId, E.GetUpdateEventString())
			}
			E.nextPreemptiveEvent = updateRule
			return nil
		} else {
			return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "update event is disabled in edd auto update rule|sls_tn=[%s], product_id=[%s], update_event=[%s]", E.GetSlsTN(), E.orderInfo.LogisticProductId, E.GetUpdateEventString())
		}
	}

	// init update event
	for _, singleTracking := range E.addedTrackingList {
		if E.GetEDDAutoUpdateConf() != nil {
			// algo 流程需要区分 reverse event
			if E.GetAlgoReverseFlag() {
				if updateEvent, codeList, lcosErr := getReverseEventFromTrackingCode(ctx, singleTracking.TrackingCode, E.productAutoUpdateConf.UpdateEvents); lcosErr == nil {
					E.updateEvent = updateEvent.Event
					E.eventTrackingCodeList = codeList
					E.updateRule = edd_constant.EarliestAndSequentialTracking // Reverse 默认 sequential
					return nil
				}
				continue
			}
			if updateEvent, codeList, lcosErr := getUpdateEventFromTrackingCode(ctx, singleTracking.TrackingCode, E.productAutoUpdateConf.UpdateEvents); lcosErr == nil {
				E.updateEvent = updateEvent.Event
				E.updateRule = updateEvent.Rule
				E.eddMaxUpdateTrending = updateEvent.EddMaxTrending
				E.eddMinUpdateTrending = updateEvent.EddMinTrending
				E.eventTrackingCodeList = codeList

				// SPLN-29072 find next update event
				nextEvent, err := E.productAutoUpdateConf.FindNextPreemptiveEvent(E.GetUpdateEvent())
				if err != nil {
					logger.CtxLogInfof(ctx, err.Error())
				}
				E.nextPreemptiveEvent = nextEvent
				return nil
			}
		}
	}
	// algo reverse 流程，如果没有匹配到 event，则直接报错不需要计算 edd
	if E.GetAlgoReverseFlag() {
		// reverse 不在轨迹码列表，则直接报错不需要计算edd
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "[is_return=true]%s can not parse event from tracking code", E.GetSlsTN())
	}

	// cannot find edd auto update rule, need to set as shipped out
	E.updateEvent = edd_constant.ShippedOut
	E.updateRule = edd_constant.EarliestAndSequentialTracking
	E.eddMaxUpdateTrending = edd_constant.FreeTrending
	E.eddMinUpdateTrending = edd_constant.FreeTrending
	E.eventTrackingCodeList = config.GetShippedOutTrackingCodeList(ctx)

	// SPLN-31665 如果上面没有匹配到UpdateEvent，则在这里对ShippedOut系列的event做匹配，匹配不到则直接用ShippedOut
	for _, singleTracking := range E.addedTrackingList {
		if updateEventEnum, codeList, lcosErr := getUpdateEventFromTrackingCodeByEnum(ctx, singleTracking.TrackingCode, edd_constant.AllShippedOutEventEnumList); lcosErr == nil {
			E.updateEvent = updateEventEnum
			E.eventTrackingCodeList = codeList
			// 这一组event都属于ShippedOut系列，eddMaxUpdateTrending和eddMinUpdateTrending 都按照ShippedOut的来
		}
	}

	// SPLN-29072 find next update event
	if E.GetEDDAutoUpdateConf() != nil {
		nextEvent, err := E.GetEDDAutoUpdateConf().FindNextPreemptiveEvent(E.GetUpdateEvent())
		if err != nil {
			logger.CtxLogInfof(ctx, err.Error())
		}
		E.nextPreemptiveEvent = nextEvent
	}
	return nil
}

// LoadEDDHistories 加载并过滤订单的EDD历史推送记录，EDD history获取逻辑需要收口到这个函数
func (E *EDDJob) LoadEDDHistories(ctx utils.LCOSContext) *lcos_error.LCOSError {
	// 初始化 edd history
	eddHistories, lcosErr := E.eddDao.GetEDDHistoryBySlsTn(ctx, E.slsTN, edd_history.TableName(ctx, ""), true)
	if lcosErr != nil {
		return lcosErr
	}
	// 过滤edd更新历史记录
	E.eddHistories, E.ddlOnlyHistories = FilterEddHistories(eddHistories)
	return nil
}

func (E *EDDJob) GetEventTime(ctx utils.LCOSContext) (int64, *lcos_error.LCOSError) {

	if E.IsPreemptiveUpdate() {
		nowTime := int64(utils.GetTimestamp(ctx))
		E.eventTime = nowTime
		// for update on ddl, event time is now
		return nowTime, nil
	}

	var actualTime int64

	// for rule, EarliestTracking and EarliestAndSequentialTracking, the event time is first event time,
	// for rule, AllTracking, then event time is current event time
	if E.updateRule != edd_constant.AllTracking {
		for _, singleTracking := range E.historyTrackingList {
			if utils.InStringSlice(singleTracking.TrackingCode, E.eventTrackingCodeList) && (actualTime == 0 || int64(singleTracking.ActualTime) < actualTime) {
				actualTime = int64(singleTracking.ActualTime)
			}
		}
	}

	for _, singleTracking := range E.addedTrackingList {
		if utils.InStringSlice(singleTracking.TrackingCode, E.eventTrackingCodeList) && (actualTime == 0 || singleTracking.ActualTime < actualTime) {
			actualTime = singleTracking.ActualTime
		}
	}

	logger.CtxLogInfof(ctx, "get event time:%d", actualTime)
	E.eventTime = actualTime
	if actualTime == 0 {
		return 0, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "actual time is not valid|event=[%s]", E.GetUpdateEventString())
	}
	return actualTime, nil
}

func (E *EDDJob) cleanWaitingWaybill(ctx utils.LCOSContext, region string, delayQueue edd_delay_queue.EddWaybillDelayQueue) *lcos_error.LCOSError {
	waitingWaybill, err := getEDDWaybillFromRedis(ctx, E.GetSlsTN())
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	lcosErr := delayQueue.RemoveEddWaybill(ctx, region, waitingWaybill)
	if lcosErr != nil {
		return lcosErr
	}
	err = deleteEDDWaybillFromRedis(ctx, E.GetSlsTN())
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return nil
}

func (E *EDDJob) CheckCanHandleWaitingWaybill(ctx utils.LCOSContext, info *waybill_center_service.LaneCodeInfo, region string, delayQueue edd_delay_queue.EddWaybillDelayQueue) *lcos_error.LCOSError {
	// for edd preemptive update, when order reach first delivery attempt, terminal, rts status, need to delete current waiting waybill from codis and delay queue
	if E.IsPreemptiveUpdate() {
		fdaStatus := config.GetFirstDeliveryAttemptTrackingCodeList(ctx)
		terminalStatus := config.GetTerminalTrackingCodeList(ctx)
		rtsStatus := config.GetReturnToSellerTrackingCodeList(ctx)
		if errMsg, ok := info.CheckValid(fdaStatus, rtsStatus, terminalStatus); !ok {
			logger.CtxLogInfof(ctx, "need to clean waiting waybill, message=[%s]", errMsg)
			lcosErr := E.cleanWaitingWaybill(ctx, region, delayQueue)
			if lcosErr != nil {
				logger.CtxLogInfof(ctx, lcosErr.Msg)
			}
			// finish process
			return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot handle waiting waybill|sls_tn=[%s], reason=[%s]", E.GetSlsTN(), errMsg)
		}
	}
	return nil
}

func (E *EDDJob) CheckCanPushEDD(ctx utils.LCOSContext) *lcos_error.LCOSError {

	// check location grey
	var greyFlag bool
	if E.IsPreemptiveUpdate() {
		greyFlag = E.CheckCityLocationListAllowedToDoPreemptiveUpdate(ctx)
	} else {
		var locationErr error
		greyFlag, locationErr = E.CheckCityLocationListAllowed(ctx)
		if locationErr != nil {
			// will not stop the whole process once error,
			logger.CtxLogInfof(ctx, locationErr.Error())
		}
	}

	if !greyFlag {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("buyer location is not allowed to push edd|product_id=[%s], buyer_city_location_id=[%d]", E.orderInfo.LogisticProductId, E.orderInfo.BuyerAddress.CityLocationId))
	}

	// for rule EarliestAndSequentialTracking and update on deadline, need to check whether later tracking pushed yet
	// get history edd first
	if E.updateRule == edd_constant.EarliestAndSequentialTracking || E.IsPreemptiveUpdate() {
		if lcosErr := E.LoadEDDHistories(ctx); lcosErr != nil {
			lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode // need to retry when get from hbase wrong
			return lcosErr
		}

		message, flag := tools.ContainsLatterAutoUpdateEvent(ctx, E.eddHistories, E.GetUpdateEvent())
		if flag {
			return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "%s|sls_tn=[%s]", message, E.GetSlsTN())
		}
	}
	logger.CtxLogInfof(ctx, "[%s]auto update edd has not been pushed, ready to push|sls_tn=%s", E.GetUpdateEventString(), E.slsTN)
	return nil
}

func (E *EDDJob) GenerateEDDHistory(ctx utils.LCOSContext, eddMin, eddMax int64, eddProcess *edd_history.EddProcess, updateType uint8, eddWaybill *edd_delay_queue.EddWaybill, deliverBuyerID uint64, isDDLOnly bool, isFallbackEddFlag uint8) (*edd_history.EddHistoryTab, *lcos_error.LCOSError) {

	if isDDLOnly && eddWaybill == nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "edd waybill ddl info cannot be empty when only update ddl")
	}
	lastEddMin, lastEddMax := E.GetLastEdd()
	history := &edd_history.EddHistoryTab{
		ForderID:    E.orderInfo.ForderId,
		ProductID:   E.orderInfo.LogisticProductId,
		OrderSN:     E.orderInfo.OrderSN,
		IsLM:        constant.FALSE,
		IsFirstTime: constant.TRUE,
		EventTime:   E.eventTime,
		SlsTN:       E.slsTN,
		LmTN:        E.orderInfo.LmTrackingNumber,
		Edd:         eddMax,
		LastEDD:     lastEddMax,
		EddProcess:  "",
		BuyerAddress: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(E.orderInfo.BuyerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(E.orderInfo.BuyerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(E.orderInfo.BuyerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(E.orderInfo.BuyerAddress.PostCode),
		},
		SellerAddress: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(E.orderInfo.SellerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(E.orderInfo.SellerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(E.orderInfo.SellerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(E.orderInfo.SellerAddress.PostCode),
		},
		EddRecalculationCount: 0,
		UpdateEvent:           E.updateEvent,
		UpdateRule:            E.updateRule,
		// SPLN-27376 新增字段
		EddMin:             eddMin,
		LastEddMin:         lastEddMin,
		NewEddProcess:      eddProcess,
		UpdateType:         updateType,
		IsPreemptive:       E.GetIsPreemptiveUpdateUint8(),
		EDDAutoUpdateRule:  E.GetEDDAutoUpdateConf(),
		WaitingWaybillInfo: eddWaybill,

		DeliverBuyerID: deliverBuyerID,
		TestGroupTag:   eddProcess.GetTestGroupTag(),

		AlgoEddModifyFlag: uint8(E.AlgoEddModifyFlag),
		IsFallbackEddFlag: isFallbackEddFlag,
		IsLateEddUpdate:   E.IsLateEddUpdate,
	}
	// SPLN-33284 新增 ABTest 流控字段
	history.ABTestGroup = E.ABtestGroup
	history.AlgoResult = E.AlgoResult

	if isDDLOnly {
		history.DataType = edd_constant.DdlOnlyUpdateEdd
	} else if E.IsPreemptiveUpdate() {
		history.DataType = edd_constant.PreemptiveUpdateEdd
	} else {
		history.DataType = edd_constant.AutoUpdateEdd
	}
	return history, nil
}

/*
SPLN-28409
check how many times auto edd have been pushed
*/
func (E *EDDJob) checkOverMaxTimes(ctx utils.LCOSContext) uint8 {
	updateType := edd_constant.UpdateEddMin // 更新次数不限制edd min，默认可以更新
	var pushedTimes int
	for _, history := range E.eddHistories {
		// 只有推送edd max才消耗更新次数
		if history.IsConsumeAutoUpdateTimeMaxTimes() && edd_util.CheckCanUpdateEddMax(history.UpdateType) {
			pushedTimes++
		}
	}
	if uint32(pushedTimes) < E.GetEDDAutoUpdateConf().MaxTimes {
		updateType |= edd_constant.UpdateEddMax
	} else {
		logger.CtxLogInfof(ctx, "edd pushing valid, update times of edd max over max times: pushing_times=%d, max_times=%d, product_id=%s", pushedTimes, E.GetEDDAutoUpdateConf().MaxTimes, E.GetProductID())
	}
	logger.CtxLogInfof(ctx, "edd pushing validate, check update times: pushing_times=%d, max_times=%d, product_id=%s", pushedTimes, E.GetEDDAutoUpdateConf().MaxTimes, E.GetProductID())
	return updateType
}

/*
SPLN-28409
check if edd over threshold
*/
func (E *EDDJob) checkOverThreshold(ctx utils.LCOSContext, newEddMin, newEddMax int64) uint8 {
	var updateType uint8

	latestHistory := E.eddHistories[len(E.eddHistories)-1]
	oldEddMin, oldEddMax := latestHistory.EddMin, latestHistory.Edd

	absDays := uint32(math.Abs(int64(datetime.GetDiffDaysFromTimestamp(oldEddMax, newEddMax, E.GetEDDAutoUpdateConf().Region))))
	if absDays > E.GetEDDAutoUpdateConf().EddMaxThresholdMin && absDays < E.GetEDDAutoUpdateConf().EddMaxThresholdMax {
		updateType |= edd_constant.UpdateEddMax
		logger.CtxLogInfof(ctx, "edd pushing validate, check edd max threshold: diff_days=%d, edd_max_threshold_min=%d, edd_max_threshold_max=%d, product_id=%s|old_edd_max=[%d], new_edd_max=[%d]", absDays, E.GetEDDAutoUpdateConf().EddMaxThresholdMin, E.GetEDDAutoUpdateConf().EddMaxThresholdMax, E.GetEDDAutoUpdateConf().ProductId, oldEddMax, newEddMax)
	} else {
		logger.CtxLogInfof(ctx, "edd pushing validate, diff days of current_edd_max-old_edd_max over threshold: diff_days=%d, edd_max_threshold_min=%d, edd_max_threshold_max=%d, product_id=%s|old_edd_max=[%d], new_edd_max=[%d]", absDays, E.GetEDDAutoUpdateConf().EddMaxThresholdMin, E.GetEDDAutoUpdateConf().EddMaxThresholdMax, E.GetEDDAutoUpdateConf().ProductId, oldEddMax, newEddMax)
	}

	if E.EddMinAvailable() {
		if oldEddMin != 0 && newEddMin != 0 {
			absDays = uint32(math.Abs(int64(datetime.GetDiffDaysFromTimestamp(oldEddMin, newEddMin, E.productAutoUpdateConf.Region))))
			if absDays > E.GetEDDAutoUpdateConf().EddMinThresholdMin && absDays < E.GetEDDAutoUpdateConf().EddMinThresholdMax {
				updateType |= edd_constant.UpdateEddMin
				logger.CtxLogInfof(ctx, "edd pushing validate, check edd min threshold: diff_days=%d, edd_min_threshold_min=%d, edd_min_threshold_max=%d, product_id=%s|old_edd_min=[%d], new_edd_min=[%d]", absDays, E.GetEDDAutoUpdateConf().EddMinThresholdMin, E.GetEDDAutoUpdateConf().EddMinThresholdMax, E.GetEDDAutoUpdateConf().ProductId, oldEddMin, newEddMin)
			} else {
				logger.CtxLogInfof(ctx, "edd pushing validate, diff days of current_edd_min-old_edd_min over threshold: diff_days=%d, edd_min_threshold_min=%d, edd_min_threshold_max=%d, product_id=%s|old_edd_min=[%d], new_edd_min=[%d]", absDays, E.GetEDDAutoUpdateConf().EddMinThresholdMin, E.GetEDDAutoUpdateConf().EddMinThresholdMax, E.GetEDDAutoUpdateConf().ProductId, oldEddMin, newEddMin)
			}
		} else {
			// allow update edd min if last edd min is empty
			updateType |= edd_constant.UpdateEddMin
			logger.CtxLogInfof(ctx, "edd pushing validate, check edd min threshold: last edd min is empty")
		}
	}
	return updateType
}

/*
SPLN-28409
check if trending is allowed
*/
func (E *EDDJob) checkTrending(ctx utils.LCOSContext, newEddMin, newEddMax int64) uint8 {
	var lastEddMin, lastEddMax int64
	// find the latest auto update edd
	if len(E.eddHistories) > 0 {
		lastEddMin = E.eddHistories[len(E.eddHistories)-1].EddMin
		lastEddMax = E.eddHistories[len(E.eddHistories)-1].Edd
	}

	// not push any auto update edd yet, do not need to check trending
	if lastEddMax == 0 {
		return edd_constant.UpdateAll
	}

	var updateType uint8
	if E.EddMinAvailable() {
		if lastEddMin != 0 && newEddMin != 0 {
			if E.eddMinUpdateTrending == edd_constant.ExtendTrending && newEddMin > lastEddMin {
				updateType |= edd_constant.UpdateEddMin
			}
			if E.eddMinUpdateTrending == edd_constant.DeductTrending && newEddMin < lastEddMin {
				updateType |= edd_constant.UpdateEddMin
			}
			if E.eddMinUpdateTrending == edd_constant.FreeTrending {
				updateType |= edd_constant.UpdateEddMin
			}
		} else {
			// allow push edd min if last edd min or new edd min is empty
			updateType |= edd_constant.UpdateEddMin
		}
	}

	if E.eddMaxUpdateTrending == edd_constant.ExtendTrending && newEddMax > lastEddMax {
		updateType |= edd_constant.UpdateEddMax
	}
	if E.eddMaxUpdateTrending == edd_constant.DeductTrending && newEddMax < lastEddMax {
		updateType |= edd_constant.UpdateEddMax
	}
	if E.eddMaxUpdateTrending == edd_constant.FreeTrending {
		updateType |= edd_constant.UpdateEddMax
	}

	if updateType&edd_constant.UpdateEddMax > 0 {
		logger.CtxLogInfof(ctx, "edd pushing validate, check edd max trending: edd_max_update_trending=[%s], new_edd_max=[%d], last_edd_max=[%d]", edd_constant.GetTrendingText(E.eddMaxUpdateTrending), newEddMax, lastEddMax)
	} else {
		logger.CtxLogInfof(ctx, "edd pushing validate, the trending of edd max is wrong: edd_max_update_trending=[%s], new_edd_max=[%d], last_edd_max=[%d]", edd_constant.GetTrendingText(E.eddMaxUpdateTrending), newEddMax, lastEddMax)
	}
	if updateType&edd_constant.UpdateEddMin > 0 {
		logger.CtxLogInfof(ctx, "edd pushing validate, check edd min trending: edd_min_update_trending=[%s], new_edd_min=[%d], last_edd_min=[%d]", edd_constant.GetTrendingText(E.eddMinUpdateTrending), newEddMin, lastEddMin)
	} else {
		logger.CtxLogInfof(ctx, "edd pushing validate, the trending of edd min is wrong: edd_min_update_trending=[%s], new_edd_min=[%d], last_edd_min=[%d]", edd_constant.GetTrendingText(E.eddMinUpdateTrending), newEddMin, lastEddMin)
	}

	return updateType
}

// CheckCanUpdateEDD check can update edd min & edd max
// 1. check edd update times
// 2. check edd update threshold
// 3. check edd update trending
func (E *EDDJob) CheckCanUpdateEDD(ctx utils.LCOSContext, eddMin, eddMax int64) (uint8, *lcos_error.LCOSError) {
	// update type enum:
	// 0: unable to update, return error
	// 1: update edd max
	// 2: update edd max
	// 3: update all
	updateType := E.getDefaultUpdateType()

	if len(E.eddHistories) == 0 {
		if lcosErr := E.LoadEDDHistories(ctx); lcosErr != nil {
			return edd_constant.UnableToUpdate, lcosErr
		}
	}
	// skip validation if is the first time to push edd
	if len(E.eddHistories) == 0 {
		return updateType, nil
	}
	sort.SliceStable(E.eddHistories, func(i, j int) bool {
		return E.eddHistories[i].Ctime < E.eddHistories[j].Ctime
	})
	// check if edd can be updated with edd auto update rule
	if E.GetEDDAutoUpdateConf() != nil {
		// check edd update threshold
		updateType &= E.checkOverThreshold(ctx, eddMin, eddMax)
		if updateType == edd_constant.UnableToUpdate {
			return updateType, lcos_error.NewLCOSError(lcos_error.OnlyCanUpdateDDLError, "check edd update threshold error")
		}
		// check edd update times
		updateType &= E.checkOverMaxTimes(ctx)
		if updateType == edd_constant.UnableToUpdate {
			return updateType, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check edd update times error")
		}
		// check edd update trending
		if !E.IsPreemptiveUpdate() { // SPLN-29072, for edd preemptive update, no need to check trending
			updateType &= E.checkTrending(ctx, eddMin, eddMax)
			if updateType == edd_constant.UnableToUpdate {
				return updateType, lcos_error.NewLCOSError(lcos_error.OnlyCanUpdateDDLError, "check edd trending error")
			}
		}
	}
	logger.CtxLogInfof(ctx, "edd:[%d] successfully passes check, will push|sls_tn=[%s], update_event=[%s]", eddMax, E.slsTN, E.GetUpdateEventString())
	return updateType, nil
}

func (E *EDDJob) GetLastEdd() (int64, int64) {
	if len(E.eddHistories) == 0 {
		return 0, 0
	}
	history := E.eddHistories[len(E.eddHistories)-1]
	return history.EddMin, history.Edd
}

func (E *EDDJob) IsFirstEdd() bool {
	/*
		判断是否是第一次推送EDD记录
	*/
	return len(E.eddHistories) == 0
}

func (E *EDDJob) GetLastAlgoFlag() bool {
	/*
		判断第一个EDD记录是否有 Algo 标记
	*/
	if len(E.eddHistories) > 0 {
		return E.eddHistories[len(E.eddHistories)-1].HasAlgoFlag()
	}
	return false
}

func (E *EDDJob) GetFirstAlgoFlag() bool {
	/*
		判断第一个EDD记录是否有 Algo 标记
	*/
	if len(E.eddHistories) > 0 {
		return E.eddHistories[0].HasAlgoFlag()
	}
	return false
}

func (E *EDDJob) EddFirstPushLimit(ctx utils.LCOSContext, region string, eddInfo *lcos_service.EddInfo) *lcos_error.LCOSError {
	if !E.IsFirstEdd() || eddInfo == nil {
		return nil
	}
	// 只用于自动更新
	if E.getEDDUpdateType() != edd_auto_update.UpdateOnEvent {
		return nil
	}
	if E.orderInfo == nil {
		return nil
	}
	ok, limit := config.GetEddPushLimitConfig(ctx, E.orderInfo.LogisticProductId)
	if !ok {
		return nil
	}
	// get edt from wbc
	wbcClient := waybill_center_service.NewWayBillCenterStaticService(region, config.GetConf(ctx).WBCStaticService.MaxRetryTimes)
	createOrderReq, err := wbcClient.BatchQueryStaticData(ctx, E.orderInfo.ForderId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get createOrderReq from wbc fail, err:%v", err)
		return err
	}

	if createOrderReq == nil {
		logger.CtxLogErrorf(ctx, "get createOrderReq from wbc fail, createOrderReq is nil")
		return nil
	}

	// exp: "edt_info":{"edt_max_dt":"2025-03-20","edt_min_dt":"2025-03-20"}
	edtMaxDate := createOrderReq.GetCreateOrderInfo().GetOrderExtInfo().GetEdtMaxDate()
	if edtMaxDate == "" {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "get edt max dt from wbc is empty")
	}
	edtMax, pErr := utils.ParseTimeInRegion(edtMaxDate, constant.DateFormat, region)
	if pErr != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "invalid edt max: %s", edtMaxDate)
	}
	nowTime := utils.GetTimestamp(ctx)
	if edtMax < int64(nowTime) {
		return nil
	}
	edtMaxDateTime := utils.ParseTimestampToTime(uint32(edtMax), region)
	edtMaxMinLimitDate := edtMaxDateTime.AddDate(0, 0, -limit)
	eddMax := eddInfo.EddMax
	eddMaxDate := utils.ParseTimestampToTime(uint32(eddMax), region)
	dealEddMaxDate := time.Date(eddMaxDate.Year(), eddMaxDate.Month(), eddMaxDate.Day(), 0, 0, 0, 0, eddMaxDate.Location())
	// 第一次推送edd需要处理成edt - limit<= edd <= edt
	var limitAvailable bool
	if dealEddMaxDate.Sub(edtMaxMinLimitDate) < 0 {
		eddInfo.EddMax = time.Date(edtMaxMinLimitDate.Year(), edtMaxMinLimitDate.Month(), edtMaxMinLimitDate.Day(), 23, 59, 59, 0, edtMaxMinLimitDate.Location()).Unix()
		limitAvailable = true
	}

	if edtMaxDateTime.Sub(dealEddMaxDate) < 0 {
		eddInfo.EddMax = time.Date(edtMaxDateTime.Year(), edtMaxDateTime.Month(), edtMaxDateTime.Day(), 23, 59, 59, 0, edtMaxDateTime.Location()).Unix()
		limitAvailable = true
	}

	// 对于algo需要重新计算ddl，统计暂时不考虑
	if limitAvailable && E.GetAlgoFlag() {
		algoDDLCdtSeconds := config.GetAlgoDDLCDTSeconds(ctx, E.GetProductID())
		eddInfo.DDL = E.GetAlgoEventDDL(eddInfo.GetEddMax(), region, time.Duration(algoDDLCdtSeconds)*time.Second)
	}
	if eddInfo.GetEddMin() > eddInfo.GetEddMax() {
		eddInfo.EddMin = eddInfo.EddMax
	}

	return nil
}

// CheckAndGetEddForPushing 校验edd，并返回最终推送的edd。
// 校验步骤：
// 1. 校验edd是否可用；
// 2. 校验edd时间是否合法；
// 3. 校验edd是否可以更新。
func (E *EDDJob) CheckAndGetEddForPushing(ctx utils.LCOSContext, eddMin, eddMax int64, region string, deliverBuyerId uint64, productId string) (int64, int64, uint8, *lcos_error.LCOSError) {
	// 1. 校验edd min和edd max是否可用
	if eddMax == 0 {
		return 0, 0, 0, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "edd max unavailable")
	}
	E.CheckAndSetEddMinAvailableFlag(eddMin)

	// if channel support hour level ，need to adjust edd min = edd max and edd value to hour level
	hourLevelEDDChannelMap := config.GetHourLevelEDDChannelList(ctx)
	if _, ok := hourLevelEDDChannelMap[productId]; ok {
		eddTime := time.Unix(eddMax, 0).In(utils.GetTimezoneLocation(region))
		eddMax = GetNextHourDateTime(eddTime, region).Unix()
		eddMin = eddMax
	}

	// 2. 校验edd min和edd max的合法性
	nowTime := recorder.Now(ctx).Unix()
	if E.EddMinAvailable() {
		if eddMin < nowTime {
			eddMin = nowTime
		}
		if datetime.DateAfter(eddMin, eddMax, region) {
			logger.CtxLogInfof(ctx, "set edd max to edd min value because edd min[%d] is later than edd max[%d]", eddMin, eddMax)
			// 如果edd min日期在edd max之后，则需要将edd max替换为edd min的值
			eddMax = eddMin
		}
		if datetime.IsSameDay(eddMin, eddMax, region) {
			logger.CtxLogInfof(ctx, "set edd min to unavailable because edd min[%d] is the same day as edd max[%d]", eddMin, eddMax)
			// 如果edd min和edd max在同一天，则需要将edd min置为0不推送
			eddMin = 0
			E.CheckAndSetEddMinAvailableFlag(eddMin) // 设置edd min不可用
		}
	}
	if eddMax <= nowTime {
		return 0, 0, 0, lcos_error.NewLCOSError(lcos_error.OnlyCanUpdateDDLError, fmt.Sprintf("edd_max:[%d] is earlier than now time:[%d]", eddMax, nowTime))
	}

	// 3. 校验edd min和edd max是否可以更新
	// UpdateType枚举
	// 0: 都不更新，这种情况终止流程，不推送
	// 1：仅更新edd min
	// 2：仅更新edd max
	// 3：同时更新edd min和edd max
	updateType, err := E.CheckCanUpdateEDD(ctx, eddMin, eddMax)
	if err != nil {
		return 0, 0, 0, err
	}
	if updateType == edd_constant.UnableToUpdate {
		return 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "unable to update edd due to validation")
	}

	// 4. 返回即将推送的edd min和edd max
	var hasReplacedToLastEdd bool
	lastEddMin, lastEddMax := E.GetLastEdd()
	if E.EddMinAvailable() && !edd_util.CheckCanUpdateEddMin(updateType) {
		// 如果此次计算edd min可用，但校验无法更新，则推送上次的edd min
		eddMin = lastEddMin
		hasReplacedToLastEdd = true
	}
	if !edd_util.CheckCanUpdateEddMax(updateType) {
		// 如果edd max校验无法更新，则推送上次的edd max
		eddMax = lastEddMax
		hasReplacedToLastEdd = true
	}
	if hasReplacedToLastEdd {
		// edd min或edd max存在替换，则需要进行二次校验
		if datetime.DateAfter(eddMin, eddMax, region) {
			// SPLN-31062 如果替换last edd导致edd min日期在edd max之后，则直接终止推送
			return 0, 0, 0, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "edd min:[%d] is later than edd max:[%d] after update times/threshold/trending check", eddMin, eddMax)
		}
		if datetime.IsSameDay(eddMin, eddMax, region) {
			logger.CtxLogInfof(ctx, "set edd min to unavailable because edd min[%d] is the same day as edd max[%d] after replaced to last edd", eddMin, eddMax)
			// 替换last edd后edd min和edd max在同一天，则将edd min置0不推送
			eddMin = 0
			E.CheckAndSetEddMinAvailableFlag(eddMin) // 设置edd min不可用
		}
	}

	// 5. check edd max is less than now
	if E.IsLastEddMaxLessThanNow(lastEddMax, nowTime) {
		// Last EDD（当前展示在ODP页面上的EDD）已经迟到
		if !config.AllowLateEddUpdate(ctx, E.GetProductID(), deliverBuyerId) {
			return 0, 0, 0, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "last edd max:[%d] is less than now:[%d]", lastEddMax, nowTime)
		}
		E.IsLateEddUpdate = constant.TRUE
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "LateEddUpdate", constant.StatusSuccess, "")
	}

	// 6. 针对 algo，判断最终 eddMin 和 eddMax，如果 min/max 跟上次都属于同一天，则不更新
	if E.GetAlgoFlag() {
		// 判断edd区间长度是否超过渠道限制，如果超过则需要根据limit值重新计算edd min
		if rangeLimit := config.GetAlgoEddRangeLimit(ctx, region, E.GetProductID()); rangeLimit > 0 {
			limitedEddMin := datetime.CalculateEDDMinByEDDMax(eddMax, eddMin, true, rangeLimit, region)
			if eddMin < limitedEddMin {
				// algo edd range超过限制，需要延长edd min
				logger.CtxLogInfof(ctx, "algo edd min modified due to range limit|algo_edd_min=%d, limited_edd_min=%d, region=%s, product_id=%s, range_limit=%d", eddMin, limitedEddMin, region, E.GetProductID(), rangeLimit)

				eddMin = limitedEddMin
				E.AlgoEddModifyFlag = edd_constant.RangeLimitedAlgoEdd // edd min被range limit修正
			}
		}

		// 判断最终 eddMin 和 eddMax，如果 min/max 跟上次都属于同一天，则不更新
		if datetime.IsSameDay(eddMin, lastEddMin, region) && datetime.IsSameDay(eddMax, lastEddMax, region) {
			return 0, 0, 0, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "edd min[%d]/max[%d] is same day as last edd min[%d]/max[%d]", eddMin, eddMax, lastEddMin, lastEddMax)
		}
	}

	return eddMin, eddMax, updateType, nil
}

func (E *EDDJob) StoreEDDInfo(ctx utils.LCOSContext, edd *edd_history.EddHistoryTab) *lcos_error.LCOSError {
	// TODO add redis lock
	return E.eddDao.CreateEDDHistoryBySlsTn(ctx, edd, edd_history.TableName(ctx, ""))
}

func (E *EDDJob) GetUpdateEventString() string {
	event, _ := edd_constant.GetUpdateEventString(E.updateEvent)
	return event
}

func (E *EDDJob) GetUpdateEvent() uint8 {
	return E.updateEvent
}

// CheckCityLocationListAllowed
// SPLN-28067 check whether product id and buyer city location is in grey list
func (E *EDDJob) CheckCityLocationListAllowed(ctx context.Context) (bool, error) {
	// only check non pickup done event
	if E.GetUpdateEvent() != edd_constant.ShippedOut {
		productID := E.orderInfo.LogisticProductId
		locationList, locationErr := config.GetEDDAutoUpdateGreyConfig(ctx, productID)
		if locationErr != nil {
			return false, locationErr
		}
		if len(locationList) > 0 && !utils.CheckInInt32(E.orderInfo.BuyerAddress.CityLocationId, locationList) {
			return false, nil
		}
	}
	return true, nil
}

// CheckCityLocationListAllowedToDoPreemptiveUpdate
// SPLN-29072 check whether product id and buyer city location is in grey list for preemptive update
// greyFlag=DisableGrey: will not allow do any preemptive update
// greyFlag=FullyEnable: will do any preemptive update no matter what the location is
// greyFlag=EnableGrey: will do any preemptive update when location is in grey location list
func (E *EDDJob) CheckCityLocationListAllowedToDoPreemptiveUpdate(ctx context.Context) bool {
	productID := E.orderInfo.LogisticProductId
	buyerCityLocationID := E.orderInfo.BuyerAddress.CityLocationId
	return CheckCityLocationListAllowedToDoPreemptiveUpdate(ctx, productID, buyerCityLocationID)
}

// CheckAndSetEddMinAvailableFlag 校验edd min是否可用。edd min不可用有两种情况：
// 1. 没有配置leadtime min数据，grpc接口计算edd min为0
// 2. edd min灰度开关关闭，开关配置在product+lane+buyer_city维度（灰度逻辑在GetEddInfo接口调用里）
func (E *EDDJob) CheckAndSetEddMinAvailableFlag(eddMin int64) {
	E.eddMinAvailable = eddMin > 0
}

func (E *EDDJob) EddMinAvailable() bool {
	return E.eddMinAvailable
}

func (E *EDDJob) getDefaultUpdateType() uint8 {
	if E.eddMinAvailable {
		return edd_constant.UpdateAll
	} else {
		return edd_constant.UpdateEddMax
	}
}

func (E *EDDJob) GetNextEventRule() *edd_auto_update_rule.UpdateEvent {
	return E.nextPreemptiveEvent
}

func (E *EDDJob) GetNextEvent() uint8 {
	if E.GetNextEventRule() == nil {
		return 0 // 0 as dummy
	}
	return E.GetNextEventRule().Event
}

func (E *EDDJob) GetDeadlineMethod() uint8 {
	// default to backward
	defaultMethod := edd_constant.Backward
	if E.GetNextEventRule() != nil {
		defaultMethod = E.GetNextEventRule().DeadlineMethod
	}
	return defaultMethod
}

func (E *EDDJob) getEDDUpdateType() edd_auto_update.EddUpdateType {
	return E.eddUpdateType
}

func (E *EDDJob) IsPreemptiveUpdate() bool {
	return E.getEDDUpdateType() == edd_auto_update.UpdateOnDeadline
}

func (E *EDDJob) GetIsPreemptiveUpdateUint8() uint8 {
	if E.IsPreemptiveUpdate() {
		return constant.TRUE
	}
	return constant.FALSE
}

func (E *EDDJob) GetEDDAutoUpdateConf() *edd_auto_update_rule.EddAutoUpdateRule {
	return E.productAutoUpdateConf
}

func (E *EDDJob) GetSlsTN() string {
	return E.slsTN
}

func (E *EDDJob) GetProductID() string {
	return E.orderInfo.LogisticProductId
}

func (E *EDDJob) GetNonDDLOnlyEDDHistories() []*edd_history.EddHistoryTab {
	return E.eddHistories
}

func (E *EDDJob) GetDDLOnlyEDDHistories() []*edd_history.EddHistoryTab {
	return E.ddlOnlyHistories
}

func (E *EDDJob) GetCheckpointFrequency() uint32 {
	var defaultFrequency uint32
	if E.productAutoUpdateConf != nil {
		defaultFrequency = E.productAutoUpdateConf.CheckpointFrequency
	}
	return defaultFrequency
}

func (E *EDDJob) GetDDLCDT(response *lcos_protobuf.GetEddCalculationInfoResponse) float64 {
	var ddlCDT float64
	if E.GetNextEventRule() != nil {
		ddlCDT = GetDDLCDTFromEDDResponse(response, E.GetNextEventRule().DeadlineMethod)
	}
	return ddlCDT
}

func (E *EDDJob) GetMinValue() float64 {
	var minValue float64
	if E.GetNextEventRule() != nil {
		minValue = float64(E.GetNextEventRule().MinValue)
	}
	return minValue
}

func (E *EDDJob) GetBuyerCityLocationID() int32 {
	return E.orderInfo.BuyerAddress.CityLocationId
}

// return empty when current event is pickup done
// return edd max, edd min, error
func (E *EDDJob) GetCurrentEDDMaxAndMin() (int64, int64) {
	if E.GetUpdateEvent() == edd_constant.ShippedOut || len(E.eddHistories) <= 0 {
		return 0, 0
	}
	// get latest edd
	latestHistory := E.eddHistories[len(E.eddHistories)-1]

	return latestHistory.Edd, latestHistory.EddMin
}

func (E *EDDJob) CallDataSDK(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, lcosResponse *lcos_protobuf.GetEddCalculationInfoResponse, eventTime int64, region string, isDDLOnly bool) (*lcos_service.EddInfo, *lcos_error.LCOSError) {
	currentEddMax, currentEddMin := E.GetCurrentEDDMaxAndMin()
	deadlineMethod := E.GetDeadlineMethod()
	checkpointFrequency := E.GetCheckpointFrequency()
	holidays := lcosResponse.GetEddInfo().GetHoliday()
	weekends := lcosResponse.GetEddInfo().GetWeekend()
	cdtMin := lcosResponse.GetEddInfo().GetCdtMin()
	cdtMax := lcosResponse.GetEddInfo().GetCdtMax()

	ddlCDT := E.GetDDLCDT(lcosResponse)
	minValue := E.GetMinValue()

	eddUpdateType := E.getEDDUpdateType()
	if isDDLOnly {
		eddUpdateType = edd_auto_update.UpdateDeadlineOnly
	}

	eddMaxInt64, eddMinInt64, ddlInt64, maxNWDList, minNWDList, err := edd_util.CallDataSDK(ctx, eventTime, currentEddMax, currentEddMin, deadlineMethod, eddUpdateType, checkpointFrequency, holidays, weekends, cdtMax, cdtMin, ddlCDT, minValue, region)
	if err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SaturnNeedToRetryErrorCode, "calculate edd by data sdk failed, error=%s", err.Error())
	}

	eddMinInt64 = datetime.CalculateEDDMinByEDDMax(eddMaxInt64, eddMinInt64, lcosResponse.GetEddInfo().GetEddMinAvailable(), lcosResponse.GetEddInfo().GetEddRangeLimit(), region)
	eddInfo := lcos_service.HandleEDDProcessAndEDDMin(ctx, productInfo, lcosResponse.GetEddProcess(), eddMaxInt64, eddMinInt64, ddlInt64, ddlCDT)

	// transfer nwd from time to string list
	eddInfo.EddProcess.NonWorkingDaysProcess.EddMaxNwdList = maxNWDList
	if eddMinInt64 > 0 {
		eddInfo.EddProcess.NonWorkingDaysProcess.EddMinNwdList = minNWDList
	}

	// SPLN-31383 add ddl process
	eddInfo.EddProcess.DdlCdtProcess = &edd_history.DdlCdtProcess{
		DdlCdt: ddlCDT,
	}

	eddInfo.CdtProductInfo = productInfo
	eddInfo.EddCalculationInfo = lcosResponse
	return eddInfo, nil
}

func (E *EDDJob) HandleWaybillDDL(ctx utils.LCOSContext, ddlCdt float64, ddl int64, region string, delayQueue edd_delay_queue.EddWaybillDelayQueue) *edd_delay_queue.EddWaybill {

	// get update times of ddl
	ddlUpdateTimes := edd_history.GetDDLUpdateTimes(E.GetNonDDLOnlyEDDHistories()) + edd_history.GetDDLUpdateTimes(E.GetDDLOnlyEDDHistories())
	return HandleWaybillDDL(ctx, E.GetProductID(), E.GetSlsTN(), E.GetBuyerCityLocationID(), ddlCdt, ddl, ddlUpdateTimes, E.GetUpdateEvent(), E.GetNextEvent(), region, delayQueue)
}

// SPLN-31154 check whether last edd max less than now
func (E *EDDJob) IsLastEddMaxLessThanNow(lastEddMax, nowTime int64) bool {
	return lastEddMax > 0 && lastEddMax <= nowTime
}

// SPLN-33284 支持 algo 走特殊流程初始化，不需要 auto rule，兜底走旧流程
func NewEDDJobByAlgo(ctx utils.LCOSContext,
	addedTrackingList []*schema.Tracking,
	historyTrackingList []*lts_service.TrackingData,
	orderInfo *lfs_service.LogisticOrderData,
	slsTN string,
	eddUpdateType edd_auto_update.EddUpdateType,
	preemptiveUpdateEvent uint8,
	lineList []*cdt_calculation.LineInfo,
	isReturnOrder bool,
	eddAutoUpdateRule *edd_auto_update_rule.EddAutoUpdateRule,
) (*EDDJob, *lcos_error.LCOSError) {
	/*
		初始化 Algo 的 EDDJob
		1. 支持所有 event 映射
		2. update max times 为 10
		3. 会调用 ALgo 计算  EDD，如果失败，需要兜底走旧流程
	*/

	// 检查update event配置是否是从ops取
	var (
		maxTimes           uint32
		eddMinThresholdMin = uint32(0)
		eddMinThresholdMax = uint32(constant.ExtendsDays)
		eddMaxThresholdMin = uint32(0)
		eddMaxThresholdMax = uint32(constant.ExtendsDays)
		product            string
	)
	// 获取max times
	if eddAutoUpdateRule == nil {
		// 兜底走apollo
		maxTimes = config.GetEddDefaultMaxTimes(ctx)
		//上报cat
		if orderInfo != nil {
			product = orderInfo.LogisticProductId
		}
		_ = monitor.AwesomeReportEvent(ctx, "EDDJobForAlgoFromApollo", product, constant.StatusSuccess, "rdd algo rule from apollo")
	} else {
		maxTimes = eddAutoUpdateRule.MaxTimes
		eddMinThresholdMin = eddAutoUpdateRule.EddMinThresholdMin
		eddMinThresholdMax = eddAutoUpdateRule.EddMinThresholdMax
		eddMaxThresholdMin = eddAutoUpdateRule.EddMaxThresholdMin
		eddMaxThresholdMax = eddAutoUpdateRule.EddMaxThresholdMax
	}

	// 1. 更新 update rule, 如果是 spx ，则为 all tracking
	lineIdList := make([]string, 0, len(lineList))
	for _, lineInfo := range lineList {
		lineIdList = append(lineIdList, lineInfo.LineID)
	}
	supplierInfoMap, lcosErr := lpop_service.NewLpopService().GetSupplierInfoByLineId(ctx, lineIdList)
	if lcosErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SaturnNeedToRetryErrorCode, lcosErr.Msg)
	}
	// 获取每个 supplier 对应的 update rule，动态配置
	var supplierId string
	for _, lineId := range lineIdList {
		if supplierInfo, ok := supplierInfoMap[lineId]; ok {
			supplierId = strconv.Itoa(int(supplierInfo.GetSupplierId()))
			break
		}
	}

	// 2. 默认支持全部 event
	eventList := edd_constant.GetUpdateEventList(orderInfo.IsCBOrder()) // nolint

	// SPLN-33587 支持 algo Reverse 渠道
	if isReturnOrder {
		eventList = edd_constant.GetReverseEventList()
	}

	updateEvents := make(edd_auto_update_rule.UpdateEventSlice, 0, len(eventList))

	for _, event := range eventList {
		// 获取event rule配置
		eddMaxTrending, eddMinTrending, updateRule, eventErr := GetEventRule(ctx, supplierId, orderInfo, event, eddAutoUpdateRule)
		if eventErr != nil {
			continue
		}

		if !isReturnOrder && event == edd_constant.OutForDelivery {
			// 仅支持 OutForDelivery 做 ddl
			updateEvents = append(updateEvents, edd_auto_update_rule.UpdateEvent{
				Event:              event,
				PreemptLateParcels: constant.TRUE,
				DeadlineMethod:     edd_constant.Forward,
				EddMaxTrending:     eddMaxTrending,
				EddMinTrending:     eddMinTrending,
				Rule:               updateRule,
			})
		} else {
			updateEvents = append(updateEvents, edd_auto_update_rule.UpdateEvent{
				Event:              event,
				PreemptLateParcels: constant.FALSE,
				EddMaxTrending:     eddMaxTrending,
				EddMinTrending:     eddMinTrending,
				Rule:               updateRule,
			})
		}

	}

	updateEvents.SortByEventSequence(ctx)
	eddJob := &EDDJob{
		eddDao:              edd_history.NewEddHistoryDao(),
		eddModelConfDao:     algo_model_conf.NewAlgoModelConfigDao(),
		spexService:         spex_service.NewSpexService(),
		addedTrackingList:   addedTrackingList,
		historyTrackingList: historyTrackingList,
		orderInfo:           orderInfo,
		productAutoUpdateConf: &edd_auto_update_rule.EddAutoUpdateRule{
			UpdateEvents:       updateEvents,
			MaxTimes:           maxTimes,
			EddMaxThresholdMin: eddMaxThresholdMin,
			EddMinThresholdMin: eddMinThresholdMin,
			EddMaxThresholdMax: eddMaxThresholdMax,
			EddMinThresholdMax: eddMinThresholdMax,
		},
		slsTN:         slsTN,
		eddUpdateType: eddUpdateType,
		updateRule:    edd_constant.EarliestAndSequentialTracking,
		AlgoFlag:      true, // 记录 algo 流程标记

		algoReverseFlag: isReturnOrder, // 记录是否 Reverse 渠道
	}
	if eddJob.IsPreemptiveUpdate() {
		eddJob.updateEvent = preemptiveUpdateEvent
	}
	return eddJob, eddJob.Initiate(ctx)
}

// 从ops或者Apollo获取EventRule，未来都是在ops配置中获取
func GetEventRule(ctx utils.LCOSContext,
	supplierId string,
	orderInfo *lfs_service.LogisticOrderData,
	event uint8,
	eddAutoUpdateRule *edd_auto_update_rule.EddAutoUpdateRule,
) (uint8, uint8, uint8, error) {
	var (
		eddMaxTrending uint8
		eddMinTrending uint8
		updateRule     uint8
	)

	// from ops
	// 当 ops不存在的时候用 apollo配置兜底
	if eddAutoUpdateRule != nil {
		//  查找相应的event
		eventRule, eventErr := eddAutoUpdateRule.FindEvent(event)
		//  检查对应的 event是否可以更新
		if eventErr != nil {
			return eddMaxTrending, eddMinTrending, updateRule, eventErr
		}
		eddMaxTrending = eventRule.EddMaxTrending
		eddMinTrending = eventRule.EddMinTrending
		updateRule = eventRule.Rule

		return eddMaxTrending, eddMinTrending, updateRule, nil
	}
	// from Apollo
	// SPLN-34544 检查对应的 event是否可以更新
	if !checkCanEventUpdateEDDForAlgo(ctx, supplierId, event, orderInfo.IsCBOrder()) {
		return eddMaxTrending, eddMinTrending, updateRule, fmt.Errorf("check can event updateEDDForAlgo fail")
	}

	eventRule := config.GetEddUpdateEventRuleByUpdateEvent(ctx, supplierId, event)

	eddMaxTrending = eventRule.EddMaxTrending
	eddMinTrending = eventRule.EddMinTrending
	updateRule = eventRule.UpdateRule
	return eddMaxTrending, eddMinTrending, updateRule, nil
}

func (E *EDDJob) GetAlgoFlag() bool {
	return E.AlgoFlag
}

func (E *EDDJob) GetAlgoReverseFlag() bool {
	return E.algoReverseFlag
}

func (E *EDDJob) IsAlgoModelEffective(ctx utils.LCOSContext, region string) bool {
	/*
		判断 Algo 模型是否生效
	*/
	modelConf, _ := E.eddModelConfDao.GetAlgoModelConf(ctx, E.GetProductID(), edd_constant.EDDModelType)
	if !modelConf.IsEffective(ctx) {
		return false
	}

	// 需要额外判断 AlGO 仿真任务是否部署成功，读取 algo model 是否开启，使用 redis 缓存
	rdb := redislibv2.GetEddRedisClient()
	cacheKey := fmt.Sprintf(edd_constant.CacheKeyAlgoModelEffectiveStatus, E.GetProductID())
	isEffective, err := rdb.Get(ctx, cacheKey).Bool()
	if err != nil {
		// 缓存失效，请求 algo 接口查询
		productIdInt, _ := strconv.Atoi(E.GetProductID())
		algoResp, _ := algo_service.QueryEffectiveAlgoSimulation(ctx, region, productIdInt, edd_constant.EDDModelType)

		// 缓存结果 5 分钟
		isEffective = algoResp.IsEffective()
		_ = rdb.Set(ctx, cacheKey, isEffective, time.Second*edd_constant.CacheTTLAlgoModelEffectiveStatus).Val()
	}
	return isEffective
}

func (E *EDDJob) GetEddRetryTimes(ctx utils.LCOSContext) int {
	// SPLN-24246 algo 重试次数超过一定次数，直接走旧流程
	obj := ctx.Value(constant.EDDRetryTimesKey)
	if obj == nil {
		return 0
	}
	return obj.(int)
}

func (E *EDDJob) CanSwitchAlgoEDD(ctx utils.LCOSContext, region string, buyerId uint64) *lcos_error.LCOSError {
	/*
		判断是否可以切换到算法模型预测 EDD ~ SPLN-33284

		1. 全局开关控制
		2. 订单始终尽量保持一种模型（Data or Algo）
		3. ABTest 流量控制
	*/
	// SPLN-33587 如果是 Reverse 订单，则走 Reverse 开关控制逻辑
	if E.GetAlgoReverseFlag() {
		return E.CanSwitchAlgoReverseEDD(ctx, region, buyerId)
	}

	// 1. 全局开关
	if !config.GetAlgoEDDSwitchFlag(ctx) {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "global switch off")
	}

	retryTimes := E.GetEddRetryTimes(ctx)
	// 上报重试次数
	_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDAlgo, "RetryTimes", strconv.Itoa(retryTimes), "")

	// SPLN-24246 algo 重试次数超过一定次数，直接走旧流程
	if retryTimes > config.GetAlgoEDDMaxRetryTimes(ctx) {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "exceed max retry times")
	}

	// 2. 检查订单历史标记
	lcosErr := E.LoadEDDHistories(ctx)
	if lcosErr != nil {
		return lcos_error.NewLCOSError(lcos_error.SaturnNeedToRetryErrorCode, "load edd history failed")
	}

	// 如果是 DDL 计算，判断最后一次记录是 algo，则允许更新
	if E.IsPreemptiveUpdate() {
		if E.GetLastAlgoFlag() {
			return nil
		}
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "last edd not algo")
	}

	// 当前订单已经计算过EDD且无algo标记，直接走旧流程
	if len(E.eddHistories) > 0 && !E.GetFirstAlgoFlag() {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "first edd not algo")
	}

	// 3. OPS 控制产品粒度的开关
	if !E.IsAlgoModelEffective(ctx, region) {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "algo model not effective")
	}

	// 4. Algo 模型开启，且第一次 edd 记录有 Algo 标记，则继续走 Algo
	if len(E.eddHistories) > 0 && E.GetFirstAlgoFlag() {
		E.ABtestGroup = E.eddHistories[0].ABTestGroup
		return nil
	}

	// 5. 第一次计算 EDD，使用 ABTest 分流
	abtestGroup, lcosErr := abtest_service.GetEDDAbtestGroup(ctx, region, strconv.Itoa(int(buyerId)), E.GetProductID(), E.GetSlsTN())
	if lcosErr != nil {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, lcosErr.Msg)
	}
	// 更新 abtest 分组信息
	E.ABtestGroup = &edd_history.ABTestGroup{
		GroupID:   abtestGroup.GroupID,
		GroupName: abtestGroup.GroupName,
		GroupType: abtestGroup.GroupType,
	}
	if abtestGroup.GroupType != abtest_edd.EddGroupType_AI {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "abtest not ai group")
	}
	return nil
}

func (E *EDDJob) CanSwitchAlgoReverseEDD(ctx utils.LCOSContext, region string, buyerId uint64) *lcos_error.LCOSError {
	/*
		判断当前是否计算 Reverse EDD
	*/
	// 1. 针对指定 product 开启 reverse edd 计算
	if !config.GetReverseEddSwitchFlag(ctx, E.GetProductID()) {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "[reverse]edd switch off")
	}

	// 2. 只需要推送一次 reverse edd
	lcosErr := E.LoadEDDHistories(ctx)
	if lcosErr != nil {
		return lcos_error.NewLCOSError(lcos_error.SaturnNeedToRetryErrorCode, "[reverse]load edd history failed")
	}
	if len(E.eddHistories) > 0 {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "[reverse]edd already exists")
	}

	// 3. OPS 控制产品粒度的开关
	if !E.IsAlgoModelEffective(ctx, region) {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "[reverse]algo model not effective")
	}

	// 4. 使用 ABTest 分流
	abtestGroup, lcosErr := abtest_service.GetEDDAbtestGroup(ctx, region, strconv.Itoa(int(buyerId)), E.GetProductID(), E.GetSlsTN())
	if lcosErr != nil {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, fmt.Sprintf("[reverse]%s", lcosErr.Msg))
	}
	// 更新 abtest 分组信息
	E.ABtestGroup = &edd_history.ABTestGroup{
		GroupID:   abtestGroup.GroupID,
		GroupName: abtestGroup.GroupName,
		GroupType: abtestGroup.GroupType,
	}
	if abtestGroup.GroupType != abtest_edd.EddGroupType_AI {
		return lcos_error.NewLCOSError(lcos_error.AlgoCannotSwitchAlgo, "[reverse]abtest not ai group")
	}
	return nil
}

func (E *EDDJob) SwitchGetEDDInfoByAlgo(ctx utils.LCOSContext, region string, laneCode string, deliverBuyerID uint64, lineList []*cdt_calculation.LineInfo) (*lcos_service.EddInfo, *lcos_error.LCOSError) {
	/*
		SPLN-33284 检查是否开启 algo，并调用algo完成edd计算

		返回错误则不走algo，否则返回algo结果
	*/
	// 1. 判断切换 Algo 模型预测 EDD
	lcosErr := E.CanSwitchAlgoEDD(ctx, region, deliverBuyerID)
	if lcosErr != nil {
		logger.CtxLogInfof(ctx, "[is_return=%t]CanSwitchAlgoEDD can not switch algo: %s", E.GetAlgoReverseFlag(), utils.MarshToStringWithoutError(lcosErr))
		return nil, lcosErr
	}
	logger.CtxLogInfof(ctx, "[is_return=%t]switch to algo edd", E.GetAlgoReverseFlag())

	// 2. 计算 actualTime
	actualTime, lcosErr := E.GetEventTime(ctx)
	if lcosErr != nil {
		return nil, lcosErr
	}
	// 3. 处理 lanecode
	laneCode = DealWithLaneCode(laneCode, E.GetUpdateEvent(), E.orderInfo.IsCBOrder())
	// 4. 调用 algo
	return E.GetEDDInfoByAlgo(ctx, region, laneCode, deliverBuyerID, lineList, actualTime)
}

func (E *EDDJob) GetTrackingCodeForAlgo() string {
	eventTrackingCode := ""
	for _, singleTracking := range E.addedTrackingList {
		if utils.ContainsString(E.eventTrackingCodeList, singleTracking.TrackingCode) {
			eventTrackingCode = singleTracking.TrackingCode
			break
		}
	}
	return eventTrackingCode
}

func (E *EDDJob) GetEDDInfoByAlgo(ctx utils.LCOSContext, region string, laneCode string, deliverBuyerID uint64, lineList []*cdt_calculation.LineInfo, actualTime int64) (*lcos_service.EddInfo, *lcos_error.LCOSError) {
	/*
		调用 algo 完成edd计算
	*/

	orderInfo := E.orderInfo
	productInfo := &cdt_calculation.CdtProductInfo{
		QueryID:    "1",
		ProductID:  orderInfo.LogisticProductId,
		IsCB:       uint8(orderInfo.CbFlag),
		IsSiteLine: constant.TRUE, // only for line site system
		Region:     region,
		SellerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.SellerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.SellerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.SellerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.SellerAddress.PostCode),
		},
		BuyerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.BuyerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.BuyerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.BuyerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.BuyerAddress.PostCode),
		},
		LaneCode: laneCode,
		LineList: lineList,

		UpdateEvent: E.GetUpdateEvent(),
		BuyerId:     deliverBuyerID,
	}

	var (
		algoDDLCdtSeconds = config.GetAlgoDDLCDTSeconds(ctx, E.GetProductID())
		algoDDLCdtDays    = transferSecondsToDays(algoDDLCdtSeconds)
	)

	// 如果是 DDL 计算，直接取最新的 edd 时间累加 1 天返回
	if E.IsPreemptiveUpdate() {
		// TODO: 评估在 LoadEDDHistories 加载 edd history 时就先排序
		sort.SliceStable(E.eddHistories, func(i, j int) bool {
			return E.eddHistories[i].Ctime < E.eddHistories[j].Ctime
		})
		var (
			eddMin, eddMax = E.GetLastEdd()
			extendSeconds  = int64(config.GetEddDDLExtendDays(ctx) * 24 * 3600)
		)
		E.AlgoResult = &edd_history.AlgoResult{
			AlgoFlag: constant.TRUE,
		}

		return &lcos_service.EddInfo{
			EddMax:         eddMax + extendSeconds,
			EddMin:         eddMin + extendSeconds,
			DDL:            E.GetAlgoEventDDL(eddMax+extendSeconds, region, time.Duration(algoDDLCdtSeconds)*time.Second),
			DDLCdt:         algoDDLCdtDays,
			CdtProductInfo: productInfo,
		}, nil
	}

	eventTrackingCode := ""
	for _, singleTracking := range E.addedTrackingList {
		if utils.ContainsString(E.eventTrackingCodeList, singleTracking.TrackingCode) {
			eventTrackingCode = singleTracking.TrackingCode
			break
		}
	}

	// 正常 EDD 走 ALgo 计算
	lcosService := lcos_service.NewLCOSService(ctx, region)
	lcosResponse, lcosErr := lcosService.GetEddInfoByAlgo(ctx, productInfo, E.GetSlsTN(), uint32(actualTime), eventTrackingCode)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "[is_return=%t]GetEddInfoByAlgo error, sls_tn=%s, code=%d, msg=%s", E.GetAlgoReverseFlag(), E.GetSlsTN(), lcosErr.RetCode, lcosErr.Msg)

		// 上报重试次数，调用后走兜底
		retryTimes := E.GetEddRetryTimes(ctx)
		if E.GetAlgoReverseFlag() {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDAlgo, "ReverseRetryTimes", strconv.Itoa(retryTimes), "")
		}

		// SPLN-33587 特殊逻辑，如果是 Reverse EDD，调用 algo 计算失败，则返回 now + default x days
		// SPLN-35491 Reverse edd 重试次数超过一定次数，直接走旧流程
		if (retryTimes >= config.GetAlgoEDDMaxRetryTimes(ctx) || lcosErr.RetCode != lcos_error.SaturnNeedToRetryErrorCode) && E.GetAlgoReverseFlag() {
			defaultEdd := int64(utils.GetDateBeginTimestamp(ctx, region) + config.GetReverseEDDDefaultDays(ctx)*24*3600 - 1)

			_ = monitor.AwesomeReportEvent(ctx, constant.CatReverseEDDDefault, eventTrackingCode, strconv.Itoa(int(lcosErr.RetCode)), lcosErr.Msg)
			logger.CtxLogErrorf(ctx, "[is_return=%t]calc for default reverse edd=%d, retryTimes=%d", E.GetAlgoReverseFlag(), defaultEdd, retryTimes)

			return &lcos_service.EddInfo{
				EddMax:         defaultEdd,
				EddMin:         defaultEdd,
				CdtProductInfo: productInfo,
			}, nil
		}
		return nil, lcosErr
	}

	if E.GetAlgoReverseFlag() {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatReverseEDDAlgo, eventTrackingCode, constant.StatusSuccess, "")
	}

	E.AlgoResult = &edd_history.AlgoResult{
		AlgoFlag:  constant.TRUE,
		EventType: lcosResponse.GetAlgoResult().GetEventType(),
		EventTime: lcosResponse.GetAlgoResult().GetEventTime(),
		EddMin:    lcosResponse.GetAlgoResult().GetEddMin(),
		EddMax:    lcosResponse.GetAlgoResult().GetEddMax(),
	}

	return &lcos_service.EddInfo{
		EddMax:         lcosResponse.GetEddMax(),
		EddMin:         lcosResponse.GetEddMin(),
		EddProcess:     lcos_service.ConvertEddProcess(ctx, lcosResponse.GetEddProcess()),
		DDL:            E.GetAlgoEventDDL(lcosResponse.GetEddMax(), region, time.Duration(algoDDLCdtSeconds)*time.Second),
		DDLCdt:         algoDDLCdtDays,
		CdtProductInfo: productInfo,
	}, nil
}

func (E *EDDJob) GetAlgoEventDDL(eddMax int64, region string, ddlCdt time.Duration) int64 {
	/*
		DDL = Current EDD max - ddl cdt
	*/
	// SPLN-33587 如果是 Reverse EDD 不需要 DDL
	if E.GetAlgoReverseFlag() {
		return 0
	}

	ddlDateTime := pickup.TransferTimeStampToTime(uint32(eddMax), region).Add(-ddlCdt)
	return ddlDateTime.Unix()
}

func (E *EDDJob) SwitchGetEDDInfoByDataSDK(ctx utils.LCOSContext, region string, laneCode string, actualTime int64, deliverBuyerID uint64, lineList []*cdt_calculation.LineInfo) (*lcos_service.EddInfo, *lcos_error.LCOSError) {
	productInfo := &cdt_calculation.CdtProductInfo{
		QueryID:    "1",
		ProductID:  E.orderInfo.LogisticProductId,
		IsCB:       uint8(E.orderInfo.CbFlag),
		IsSiteLine: constant.TRUE, // only for line site system
		Region:     region,
		SellerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(E.orderInfo.SellerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(E.orderInfo.SellerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(E.orderInfo.SellerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(E.orderInfo.SellerAddress.PostCode),
		},
		BuyerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(E.orderInfo.BuyerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(E.orderInfo.BuyerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(E.orderInfo.BuyerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(E.orderInfo.BuyerAddress.PostCode),
		},
		LaneCode: laneCode,
		LineList: lineList,

		UpdateEvent: E.GetUpdateEvent(),
		NextEvent:   E.GetNextEvent(),
		BuyerId:     deliverBuyerID,
	}

	var newEdd *lcos_service.EddInfo
	var newErr *lcos_error.LCOSError

	var oldEdd *lcos_service.EddInfo
	var oldErr *lcos_error.LCOSError

	var returnedEddInfoParsed *lcos_service.EddInfo
	var returnedLcosErr *lcos_error.LCOSError

	switchFlag, doubleCallFlag, percent := config.GetDataSDKSwitchConfig(E.orderInfo.LogisticProductId)
	doubleCall := doubleCallFlag && utils.CheckPercent(ctx, percent)

	if switchFlag || doubleCall {
		newEdd, newErr = E.GetEDDInfoByDataSDK(ctx, productInfo, region, actualTime)
	}
	if (!switchFlag) || doubleCall {
		lcosService := lcos_service.NewLCOSService(ctx, region)
		if E.IsPreemptiveUpdate() { // when disable data sdk, cannot do preemptive update
			return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot do preemptive update when data sdk is disabled|product_id=[%s], sls_tn=[%s]", E.orderInfo.LogisticProductId, E.GetSlsTN())
		}
		oldEdd, oldErr = lcosService.GetEDDInfo(ctx, productInfo, E.orderInfo.ForderId, uint32(actualTime))
		if oldErr != nil {
			oldErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
		}
	}

	if switchFlag {
		logger.CtxLogInfof(ctx, "calculate edd by data sdk")
		returnedEddInfoParsed = newEdd
		returnedLcosErr = newErr
	} else {
		logger.CtxLogInfof(ctx, "calculate edd by get edd info")
		returnedEddInfoParsed = oldEdd
		returnedLcosErr = oldErr
	}

	// if double call enabled, do diff
	if doubleCall {
		diffOldAndNewEddInfo(ctx, E.orderInfo.LogisticProductId, E.GetUpdateEventString(), E.GetSlsTN(), oldEdd, newEdd, oldErr, newErr)
	}

	if returnedLcosErr != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "AutoUpdateStatus", constant.StatusEddCalculateError, returnedLcosErr.Msg)
	}
	return returnedEddInfoParsed, returnedLcosErr
}

func (E *EDDJob) GetEDDInfoByDataSDK(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, region string, actualTime int64) (*lcos_service.EddInfo, *lcos_error.LCOSError) {
	lcosService := lcos_service.NewLCOSService(ctx, region)
	eddInfo, lcosErr := lcosService.GetEddCalculationInfo(ctx, productInfo, E.orderInfo.ForderId, uint32(actualTime))
	if lcosErr != nil {
		lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
		return nil, lcosErr
	}
	if eddInfo.GetRespHeader().GetRetcode() != 0 {
		errMsg := fmt.Sprintf("cannot find cdt info for product:[%s]", productInfo.ProductID)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	// gather all information, call data sdk
	return E.CallDataSDK(ctx, productInfo, eddInfo, actualTime, region, false)
}

func (E *EDDJob) GetEDDInfoForDirectDelivery(ctx utils.LCOSContext, region, orderSN string) (int64, int64, *lcos_error.LCOSError) {
	// 1. 调用order获取订单的EDT快照和SBD信息
	req := &orderPb.GetOrderListBySnListRequest{
		OrderSns:      []string{orderSN},
		QueryRespType: utils.NewUint32(2), // 参考lps这里请求参数是2
	}
	resp, spexErr := E.spexService.GetOrderListByOrderSN(ctx, req, region)
	if spexErr != nil {
		return 0, 0, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "spex return error: %s", spexErr.Msg)
	}
	if resp == nil || len(resp.GetOrders()) == 0 {
		return 0, 0, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "order response empty")
	}
	var orderExtInfo orderPb.OrderExtInfo
	if err := proto.Unmarshal(resp.GetOrders()[0].GetExtinfo(), &orderExtInfo); err != nil {
		return 0, 0, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "unmarshal order extra info error: %s", err.Error())
	}
	logger.CtxLogInfof(ctx, "get order extra info from order|region=%s, order_sn=%s, order_ext_info=%s", region, orderSN, utils.MarshToStringWithoutError(orderExtInfo))
	// 2. EDD=max(EDT, SBD)
	edtMaxDt := orderExtInfo.GetLogisticsInfo().GetEdtMaxDt()
	if edtMaxDt == "" {
		return 0, 0, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "get edt max dt from order is empty")
	}
	edtMax, err := utils.ParseTimeInRegion(edtMaxDt, constant.DateFormat, region)
	if err != nil {
		return 0, 0, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "invalid edt max: %s", edtMaxDt)
	}
	shipByDate := int64(orderExtInfo.GetShipByDate())
	eddMax := utils.ParseTimestampToTime(uint32(math.MaxInt64(edtMax, shipByDate)), region) // EDD=max(EDT, SBD)，同时转换为time.Time
	// 3. 令返回EDD的时间为当天晚上23:59:59
	returnEddMax := time.Date(eddMax.Year(), eddMax.Month(), eddMax.Day(), 23, 59, 59, 0, eddMax.Location())
	return 0, returnEddMax.Unix(), nil
}
