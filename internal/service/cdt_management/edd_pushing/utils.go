package edd_pushing

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/waybill_center_service"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/redislibv2"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/math"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/saturnprovider"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_delay_queue"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcos_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lts_service"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	jsoniter "github.com/json-iterator/go"
)

var eddRedisClient *redis.Client

// VerifyTrackingList
// check whether tracking list contains error code
func VerifyTrackingList(ctx utils.LCOSContext, trackingList []*schema.Tracking, errCodeList []string) *lcos_error.LCOSError {
	for _, singleTrackingCode := range trackingList {
		if utils.InStringSlice(singleTrackingCode.TrackingCode, errCodeList) {
			errMsg := fmt.Sprintf("tracking contains error code, not allow to push edd|tracking_code=%s, error_code=[%s]", singleTrackingCode.TrackingCode, strings.Join(errCodeList, ","))
			return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
		}
	}
	logger.CtxLogInfof(ctx, "new added tracking does not contain error code")
	return nil
}

// VerifyHistoryTracking
// check whether history tracking list contains error code
func VerifyHistoryTracking(ctx utils.LCOSContext, historyTrackingList []*lts_service.TrackingData, errorCodeList []string) *lcos_error.LCOSError {
	// check whether history tracking contains error
	for _, singleTrackingCode := range historyTrackingList {
		if utils.InStringSlice(singleTrackingCode.TrackingCode, errorCodeList) {
			errMsg := fmt.Sprintf("history trackings contains error code, not allow to push lm edd|history_tracking_code=%s, error_code=[%s]", singleTrackingCode.TrackingCode, strings.Join(errorCodeList, ","))
			return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
		}
	}
	logger.CtxLogInfof(ctx, "history tracking does not contain error code")
	return nil
}

func IsCurrentTrackingContainsEvent(trackingList []*schema.Tracking, eventList []string) bool {
	for _, singleTrackingCode := range trackingList {
		if utils.InStringSlice(singleTrackingCode.TrackingCode, eventList) {
			return true
		}
	}
	return false
}

// CheckIsEddTrackingEvent 检查轨迹是否可以触发EDD计算，校验fcode是否能触发EDD更新的逻辑收口到这里
func CheckIsEddTrackingEvent(ctx utils.LCOSContext, trackings []*schema.Tracking) *lcos_error.LCOSError {
	// 可以触发EDD计算的轨迹分为两部分
	// 1. 对于没有开启自动更新的渠道，如果fcode属于edd_config.shipped_out_tracking_code_list，那么允许使用shipped out event触发EDD计算
	shipOutList := config.GetShippedOutTrackingCodeList(ctx)
	logger.CtxLogInfof(ctx, "configured pickup done status list is: [%s]", strings.Join(shipOutList, ","))
	isEDDEventFlag := IsCurrentTrackingContainsEvent(trackings, shipOutList)
	// 2. 对于开启自动更新的渠道，如果fcode属于自动更新事件中的任何一个event，那么允许使用对应event触发EDD计算
	allTrackingCodeList, trackingErr := config.GetAllAutoUpdateTrackingCodeList(ctx)
	if trackingErr != nil {
		return lcos_error.NewLCOSError(lcos_error.SaturnNeedToRetryErrorCode, trackingErr.Error()) // when parse is unsuccessful, need to retry
	}
	isAutoUpdateFlag := IsCurrentTrackingContainsEvent(trackings, allTrackingCodeList)
	// 3. TODO 新增reverse EDD后，其实触发EDD计算的轨迹新增了一部分，对应reverse_edd_config.reverse_event_tracking_code_map
	// 但目前RR所有能触发EDD的轨迹与正向完全一致，所以逆向轨迹不会被拦截。可考虑是否额外增加一个reverse EDD轨迹的校验，否则如果新增reverse EDD event，需要检查是否能被上面的校验兼容
	if !isEDDEventFlag && !isAutoUpdateFlag {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "new added tracking is not edd event")
	}
	return nil
}

// SPLN-28122
// get order info with switch to use new or old api
func FetchLfsOrderInfo(ctx utils.LCOSContext, region, slsTn string) (*lfs_service.LogisticOrderData, *lcos_error.LCOSError) {
	lfsApiService := lfs_service.NewLFSService(ctx, region)
	if config.UseNewEDDLFSApi(ctx) {
		eddOrderInfo, err := lfsApiService.GetLogisticOrderDataBySloTnForEDD(ctx, slsTn)
		if err != nil {
			return nil, err
		}
		return lfs_service.TransferEDDOrderInfoToOrderInfo(eddOrderInfo), nil
	}
	return lfsApiService.GetLogisticOrderDataBySloTn(ctx, slsTn)
}

func FetchWaybillLaneInfo(ctx utils.LCOSContext, region, slsTn string) (*waybill_center_service.LaneCodeInfo, *lcos_error.LCOSError) {
	wbcService := waybill_center_service.NewWaybillCenterService(ctx, region)
	resultMap, err := wbcService.BatchGetLaneCodeInfo(ctx, []string{slsTn})
	if err != nil {
		return nil, err
	}
	laneInfo, ok := resultMap[slsTn]
	if !ok {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SaturnNeedToRetryErrorCode, fmt.Sprintf("cannot find waybill:[%s] in waybill center", slsTn))
	}
	if !laneInfo.IsSuccess() {
		return nil, lcos_error.NewLCOSError(lcos_error.SaturnNeedToRetryErrorCode, fmt.Sprintf("get lane code error:[%s] for sls_tn:[%s]", laneInfo.Message, slsTn))
	}
	return laneInfo, nil
}

func generateDiffString(diff int64) string {
	// will generate string to report
	//
	if diff == 0 {
		return "0"
	}
	diffInt := math.Abs(diff)/24/3600 + 1
	if diffInt > 5 { // report max to 5
		diffInt = 5
	}
	if diff < 0 {
		diffInt = -1 * diffInt
	}
	return strconv.Itoa(int(diffInt))
}

func getEddInfoFromDataSDK(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, orderInfo *lfs_service.LogisticOrderData, region string, actualTime int64, eddService *EDDJob) (*lcos_service.EddInfo, *lcos_error.LCOSError) {
	lcosService := lcos_service.NewLCOSService(ctx, region)
	eddInfo, lcosErr := lcosService.GetEddCalculationInfo(ctx, productInfo, orderInfo.ForderId, uint32(actualTime))
	if lcosErr != nil {
		lcosErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
		return nil, lcosErr
	}
	if eddInfo.GetRespHeader().GetRetcode() != 0 { // cdt not config
		errMsg := fmt.Sprintf("cannot find cdt info for product:[%s]", productInfo.ProductID)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	// gather all information, call data sdk
	return eddService.CallDataSDK(ctx, productInfo, eddInfo, actualTime, region, false)
}

func diffOldAndNewEddInfo(ctx utils.LCOSContext, productID, eventString, slsTN string, oldEdd, newEdd *lcos_service.EddInfo, oldErr, newErr *lcos_error.LCOSError) {
	// only diff data when old process reports no error
	dataSDKError := "0"
	eddMinDiffFlag := "0"
	eddMaxDiffFlag := "0"
	oldEddError := "0"
	logger.CtxLogInfof(ctx, "ready to do diff")
	if oldErr == nil {
		if newErr != nil {
			if newErr.RetCode == lcos_error.SaturnNeedToRetryErrorCode { // ignore error no need to retry
				dataSDKError = "1"
				logger.CtxLogInfof(ctx, "data sdk report error when old process not|product_id=[%s], update_string=[%s], sls_tn=[%s]", productID, eventString, slsTN)
			}
		} else {
			if oldEdd != nil && newEdd != nil {
				eddMinDiff := oldEdd.GetEddMin() - newEdd.GetEddMin()
				eddMaxDiff := oldEdd.GetEddMax() - newEdd.GetEddMax()
				if eddMinDiff != 0 {
					eddMinDiffFlag = generateDiffString(eddMinDiff)
					logger.CtxLogInfof(ctx, "edd min diff, old_edd_min=[%d], new_edd_min=[%d], diff=[%d]|product_id=[%s], update_string=[%s], sls_tn=[%s]", oldEdd.GetEddMin(), newEdd.GetEddMin(), eddMinDiff, productID, eventString, slsTN)
				}
				if eddMaxDiff != 0 {
					eddMaxDiffFlag = generateDiffString(eddMaxDiff)
					logger.CtxLogInfof(ctx, "edd max diff, old_edd_max=[%d], new_edd_max=[%d], diff=[%d]|product_id=[%s], update_string=[%s], sls_tn=[%s]", oldEdd.GetEddMax(), newEdd.GetEddMax(), eddMaxDiff, productID, eventString, slsTN)
				}
			}
		}
	} else {
		oldEddError = "1"
	}

	_ = metrics.CounterIncr(constant.MetricsEDDDiffReport, map[string]string{"product_id": productID, "update_event": eventString, "data_sdk_error": dataSDKError, "edd_min_diff": eddMinDiffFlag, "edd_max_diff": eddMaxDiffFlag, "old_edd_error": oldEddError})

}

// SPLN-30768
// get edd info with switch
func GetEDDAndDDLInfo(ctx utils.LCOSContext, orderInfo *lfs_service.LogisticOrderData, region string, laneCode string, actualTime int64, deliverBuyerID uint64, lineList []*cdt_calculation.LineInfo, eddService *EDDJob) (*lcos_service.EddInfo, *lcos_error.LCOSError) {
	productInfo := &cdt_calculation.CdtProductInfo{
		QueryID:    "1",
		ProductID:  orderInfo.LogisticProductId,
		IsCB:       uint8(orderInfo.CbFlag),
		IsSiteLine: constant.TRUE, // only for line site system
		Region:     region,
		SellerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.SellerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.SellerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.SellerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.SellerAddress.PostCode),
		},
		BuyerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.BuyerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.BuyerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.BuyerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.BuyerAddress.PostCode),
		},
		LaneCode: laneCode,
		LineList: lineList,

		UpdateEvent: eddService.GetUpdateEvent(),
		NextEvent:   eddService.GetNextEvent(),
		BuyerId:     deliverBuyerID,
	}

	var newEdd *lcos_service.EddInfo
	var newErr *lcos_error.LCOSError

	var oldEdd *lcos_service.EddInfo
	var oldErr *lcos_error.LCOSError

	var returnedEddInfoParsed *lcos_service.EddInfo
	var returnedLcosErr *lcos_error.LCOSError

	switchFlag, doubleCallFlag, percent := config.GetDataSDKSwitchConfig(orderInfo.LogisticProductId)
	doubleCall := doubleCallFlag && utils.CheckPercent(ctx, percent)

	if switchFlag || doubleCall {
		newEdd, newErr = getEddInfoFromDataSDK(ctx, productInfo, orderInfo, region, actualTime, eddService)
	}
	if (!switchFlag) || doubleCall {
		lcosService := lcos_service.NewLCOSService(ctx, region)
		if eddService.IsPreemptiveUpdate() { // when disable data sdk, cannot do preemptive update
			return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot do preemptive update when data sdk is disabled|product_id=[%s], sls_tn=[%s]", orderInfo.LogisticProductId, eddService.GetSlsTN())
		}
		oldEdd, oldErr = lcosService.GetEDDInfo(ctx, productInfo, orderInfo.ForderId, uint32(actualTime))
		if oldErr != nil {
			oldErr.RetCode = lcos_error.SaturnNeedToRetryErrorCode
		}
	}

	if switchFlag {
		logger.CtxLogInfof(ctx, "calculate edd by data sdk")
		returnedEddInfoParsed = newEdd
		returnedLcosErr = newErr
	} else {
		logger.CtxLogInfof(ctx, "calculate edd by get edd info")
		returnedEddInfoParsed = oldEdd
		returnedLcosErr = oldErr
	}

	// if double call enabled, do diff
	if doubleCall {
		diffOldAndNewEddInfo(ctx, orderInfo.LogisticProductId, eddService.GetUpdateEventString(), eddService.GetSlsTN(), oldEdd, newEdd, oldErr, newErr)
	}

	if returnedLcosErr != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDReport, "AutoUpdateStatus", constant.StatusEddCalculateError, returnedLcosErr.Msg)
	}
	return returnedEddInfoParsed, returnedLcosErr
}

func getUpdateEventFromTrackingCode(ctx context.Context, trackingCode string, eventRuleList edd_auto_update_rule.UpdateEventSlice) (*edd_auto_update_rule.UpdateEvent, []string, *lcos_error.LCOSError) {
	for _, rule := range eventRuleList {
		eventString, err1 := edd_constant.GetUpdateEventString(rule.Event)
		if err1 == nil {
			codeList, _ := config.GetEventTrackingCodeList(ctx, eventString)
			if utils.CheckInString(trackingCode, codeList) {
				return &rule, codeList, nil
			}
		}
	}
	return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find update event for tracking code:[%s]", trackingCode))
}

func getReverseEventFromTrackingCode(ctx context.Context, trackingCode string, eventRuleList edd_auto_update_rule.UpdateEventSlice) (*edd_auto_update_rule.UpdateEvent, []string, *lcos_error.LCOSError) {
	for _, rule := range eventRuleList {
		eventString, err1 := edd_constant.GetUpdateEventString(rule.Event)
		if err1 == nil {
			codeList, _ := config.GetReverseEventTrackingCodeList(ctx, eventString)
			if utils.CheckInString(trackingCode, codeList) {
				return &rule, codeList, nil
			}
		}
	}
	return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find update event for tracking code:[%s]", trackingCode))
}

func getUpdateEventFromTrackingCodeByEnum(ctx context.Context, trackingCode string, eventList []uint8) (uint8, []string, *lcos_error.LCOSError) {
	for _, event := range eventList {
		eventString, err1 := edd_constant.GetUpdateEventString(event)
		if err1 == nil {
			codeList, _ := config.GetEventTrackingCodeList(ctx, eventString)
			if utils.CheckInString(trackingCode, codeList) {
				return event, codeList, nil
			}
		}
	}
	return edd_constant.ShippedOut, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find update event for tracking code:[%s]", trackingCode))
}

// SPLN-28999
// for CB Product, with Destination Inbound / LM Hub inbound / Out for Delivery event, only need the LM lane code
func DealWithLaneCode(laneCode string, updateEvent uint8, cbFlag bool) string {
	if cbFlag && cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
		// find the last mile lane code
		laneCodeList := strings.Split(laneCode, "|")
		if len(laneCodeList) > 0 {
			return laneCodeList[len(laneCodeList)-1]
		}
	}
	return laneCode
}

const waitingWaybillPrefix = "waiting_waybill"

func generateWaitingSLSKey(slsTN string) string {
	return utils.GenKey(":", waitingWaybillPrefix, slsTN)
}

func getEDDWaybillFromRedis(ctx utils.LCOSContext, slsTN string) (*edd_delay_queue.EddWaybill, error) {
	if eddRedisClient == nil {
		eddRedisClient = redislibv2.GetEddRedisClient()
	}
	result, err := eddRedisClient.Get(ctx, generateWaitingSLSKey(slsTN)).Result()
	if err != nil {
		return nil, err
	}
	returnResult := &edd_delay_queue.EddWaybill{}
	err = jsoniter.UnmarshalFromString(result, returnResult)
	if err != nil {
		return nil, err
	}
	return returnResult, nil
}

func setEDDWaybillToRedis(ctx utils.LCOSContext, eddWaybill *edd_delay_queue.EddWaybill) error {
	if eddRedisClient == nil {
		eddRedisClient = redislibv2.GetEddRedisClient()
	}
	// set expiredDuration to two days after ddl
	nowTime := utils.GetTimestamp(ctx)
	expiredSecond := eddWaybill.DDL - nowTime + 2*24*3600
	if expiredSecond <= 0 {
		return fmt.Errorf("failed to set edd waybill to codis|sls_tn=[%s], update_event=[%d], ddl=[%d]", eddWaybill.SlsTN, eddWaybill.UpdateEvent, eddWaybill.DDL)
	}
	// be aware that for one sls tn, only one ddl check exists
	return eddRedisClient.Set(ctx, generateWaitingSLSKey(eddWaybill.SlsTN), utils.MarshToStringWithoutError(eddWaybill), time.Duration(expiredSecond)*time.Second).Err()
}

func deleteEDDWaybillFromRedis(ctx utils.LCOSContext, slsTN string) error {
	if eddRedisClient == nil {
		eddRedisClient = redislibv2.GetEddRedisClient()
	}
	return eddRedisClient.Del(ctx, generateWaitingSLSKey(slsTN)).Err()
}

func IsDDLOnlyError(err *lcos_error.LCOSError) bool {
	return err != nil && err.RetCode == lcos_error.OnlyCanUpdateDDLError
}

func GetDDLCDTFromEDDResponse(response *lcos_protobuf.GetEddCalculationInfoResponse, deadlineMethod uint8) float64 {
	var ddlCDT float64
	if deadlineMethod == edd_constant.Forward {
		ddlCDT = response.GetDdlInfo().GetForwardCdt()
	} else {
		ddlCDT = response.GetDdlInfo().GetNextEventBackwardCdt()
	}
	return ddlCDT
}

func checkDDLUpdateTimes(productID string, updateTimes uint32) *lcos_error.LCOSError {
	// get max times of product
	maxTimes := config.GetMaxDDLUpdateTimes(productID)

	if updateTimes >= maxTimes {
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "ddl update times:[%d] over max times:[%d]|product_id=[%s]", updateTimes, maxTimes, productID)
	}
	return nil
}

func CheckCityLocationListAllowedToDoPreemptiveUpdate(ctx context.Context, productID string, cityLocationID int32) bool {
	greyFlag, locationList := config.GetEDDPreemptiveGreyCityLocationList(ctx, productID)
	if greyFlag == edd_constant.DisableGrey {
		return false
	} else if greyFlag == edd_constant.FullyEnable {
		return true
	} else if greyFlag == edd_constant.EnableGrey {
		return utils.CheckInInt32(cityLocationID, locationList)
	}
	return false
}

func HandleWaybillDDL(ctx utils.LCOSContext, productID string, slsTN string, buyerCityLocationID int32, ddlCDT float64, ddl int64, ddlUpdateTimes uint32, updateEvent, nextEvent uint8, region string, delayQueue edd_delay_queue.EddWaybillDelayQueue) *edd_delay_queue.EddWaybill {
	// if current location not in grey list, skip push to delay queue
	greyFlag := CheckCityLocationListAllowedToDoPreemptiveUpdate(ctx, productID, buyerCityLocationID)
	if !greyFlag {
		logger.CtxLogInfof(ctx, "current product not enable preemptive update|product_id=[%s], buyer_city_location_id=[%d]", productID, buyerCityLocationID)
		return nil
	}

	nowTime := utils.GetTimestamp(ctx)
	if uint32(ddl) < nowTime {
		logger.CtxLogInfof(ctx, "ddl:[%d] is earlier than now, cannot wait", ddl)
		return nil
	}

	if nextEvent == 0 {
		// no next event to wait, do nothing
		logger.CtxLogInfof(ctx, "there are no waiting event, will send nothing to delay queue")
		return nil
	}

	updateEventStr, _ := edd_constant.GetUpdateEventString(updateEvent)
	nextEventStr, _ := edd_constant.GetUpdateEventString(nextEvent)

	// for ddl_cdt less than 0, need to skip waiting for event
	if ddlCDT <= 0 {
		logger.CtxLogInfof(ctx, "cannot find ddl cdt|product_id=[%s], update_event=[%s], next_event=[%s]", productID, updateEventStr, nextEventStr)
		return nil
	}

	// SPLN-31383 add max ddl update times check
	if updateTimesErr := checkDDLUpdateTimes(productID, ddlUpdateTimes); updateTimesErr != nil {
		logger.CtxLogInfof(ctx, "%s, sls_tn=[%s], update_event=[%s], next_event=[%s]", updateTimesErr.Msg, slsTN, updateEventStr, nextEventStr)
		return nil
	}

	// get current ddl
	currentEDDWaybill, err := getEDDWaybillFromRedis(ctx, slsTN)
	if err != nil {
		logger.CtxLogInfof(ctx, "current waybill is not waiting any event|sls_tn=[%s]", slsTN)
	} else {
		// if next event is earlier than current EDD waybill waiting event, do nothing
		if nextEvent != currentEDDWaybill.UpdateEvent && common_utils.IsUpdateEvent1BeforeOrEqualUpdateEvent2(ctx, nextEvent, currentEDDWaybill.UpdateEvent) {
			eddWaybillEventStr, _ := edd_constant.GetUpdateEventString(currentEDDWaybill.UpdateEvent)
			logger.CtxLogInfof(ctx, "next event:[%s] is earlier than current edd waybill waiting event:[%s], will not add next event to delay queue|sls_tn=[%s], product_id=[%s]", nextEventStr, eddWaybillEventStr, slsTN, productID)
			return nil
		}

		// SPLN-31383 if next event is equal to the current edd waybill waiting event, will do nothing
		if nextEvent == currentEDDWaybill.UpdateEvent && uint32(ddl) == currentEDDWaybill.DDL {
			logger.CtxLogInfof(ctx, "the new event:[%s] and the new ddl:[%d] are the same as old one, will do nothing|sls_tn=[%s], product_id=[%s]", updateEventStr, ddl, slsTN, productID)
			return nil
		}

		// next event is latter than or equal to current edd waybill waiting event, delete current edd waybill from delay queue
		lcosErr := delayQueue.RemoveEddWaybill(ctx, region, currentEDDWaybill)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "cannot remove current edd waybill from delay queue, edd_waybill=[%s]|error=[%s]", utils.MarshToStringWithoutError(currentEDDWaybill), lcosErr.Msg)
		} else {
			logger.CtxLogInfof(ctx, "successfully remove current edd waybill from delay queue, edd_waybill=[%s]", utils.MarshToStringWithoutError(currentEDDWaybill))
		}
	}

	// handle edd waybill
	eddWaybill := &edd_delay_queue.EddWaybill{
		SlsTN:       slsTN,
		UpdateEvent: nextEvent,
		DDL:         uint32(ddl),
	}
	eddWaybillStr := utils.MarshToStringWithoutError(eddWaybill)
	lcosErr := delayQueue.PushEddWaybill(ctx, region, eddWaybill)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "cannot push edd waybill to delay queue, cannot do preemptive update, edd_waybill=[%s]|error=[%s]", eddWaybillStr, lcosErr.Msg)
	} else {
		logger.CtxLogInfof(ctx, "successfully push edd waybill to delay queue, edd_waybill=[%s]", eddWaybillStr)
		err = setEDDWaybillToRedis(ctx, eddWaybill)
		if err != nil {
			logger.CtxLogErrorf(ctx, "cannot set edd waybill to redis, edd_waybill=[%s]|error=[%s]", eddWaybillStr, err.Error())
		} else {
			logger.CtxLogInfof(ctx, "successfully set edd waybill to redis, edd_waybill=[%s]", eddWaybillStr)
		}
	}
	return eddWaybill
}

func HandleDataSync(ctx utils.LCOSContext, eddRecord *edd_history.EddHistoryTab) *lcos_error.LCOSError {
	if config.GetDataSyncEnabledConfig(ctx) && utils.CheckPercent(ctx, int(config.GetDataSyncPercent(ctx))) {
		kafkaProducer, kafkaErr := saturnprovider.GetKafkaProducer(edd_constant.DataSyncClusterName)
		if kafkaErr != nil {
			logger.CtxLogErrorf(ctx, "cannot sync edd to data kafka|error=[%s]", kafkaErr.Error())
		} else {
			messageErr := kafkaProducer.SendMessage(ctx, config.GetDataSyncTopic(ctx), []byte(utils.MarshToStringWithoutError(eddRecord)))
			if messageErr != nil {
				logger.CtxLogErrorf(ctx, "sync edd to data kafka error|error=[%s]", messageErr.Error())
			} else {
				logger.CtxLogInfof(ctx, "sync edd to data kafka successfully")
			}
		}
	}
	return nil
}

func FilterEddHistories(eddHistories []*edd_history.EddHistoryTab) ([]*edd_history.EddHistoryTab, []*edd_history.EddHistoryTab) {
	ddlOnlyHistories := make([]*edd_history.EddHistoryTab, 0, len(eddHistories))
	nonDDLOnlyHistories := make([]*edd_history.EddHistoryTab, 0, len(eddHistories))
	for _, singleHistory := range eddHistories {
		if singleHistory.IsFallbackEddFlag == constant.TRUE {
			// 忽略fallback edd的推送记录，不影响主流程
			continue
		}

		if singleHistory.IsDDLOnlyEDD() {
			ddlOnlyHistories = append(ddlOnlyHistories, singleHistory)
		} else {
			nonDDLOnlyHistories = append(nonDDLOnlyHistories, singleHistory)
		}
	}
	return nonDDLOnlyHistories, ddlOnlyHistories
}

func transferSecondsToDays(seconds int) float64 {
	return float64(seconds) / float64(24*3600)
}

func transferUpdateEventStringListToUpdateEventList(ctx utils.LCOSContext, supplierId string, isCB bool) []uint8 {
	updateEventStringList := config.GetEddUpdateEventListBySupplierID(ctx, supplierId)
	if len(updateEventStringList) <= 0 {
		return []uint8{}
	}

	updateEventList := make([]uint8, 0, len(updateEventStringList))
	for _, updateEventString := range updateEventStringList {
		updateEvent, err := edd_constant.GetUpdateEvent(updateEventString, isCB)
		if err == nil {
			updateEventList = append(updateEventList, updateEvent)
		}
	}
	return updateEventList
}

func checkCanEventUpdateEDDForAlgo(ctx utils.LCOSContext, supplierId string, event uint8, isCB bool) bool {
	// 先检查对应的supplier id是否配置了可更新的event 列表，如果配置了，则检查event是否在列表中，如果没配置，则不限制，全部的event都可以更新
	updateEventList := transferUpdateEventStringListToUpdateEventList(ctx, supplierId, isCB)

	if len(updateEventList) <= 0 {
		return true
	}

	// 检查对应的event是否在配置项中
	for _, updateEvent := range updateEventList {
		if updateEvent == event {
			return true
		}
	}
	return false
}

func IsDirectDeliveryProduct(ctx utils.LCOSContext, productId string) bool {
	directDeliveryMap := config.GetDirectDeliveryEddMap(ctx)
	if len(directDeliveryMap) == 0 {
		return false
	}
	return directDeliveryMap[productId]
}

func GetNextHourDateTime(current time.Time, region string) time.Time {
	if current.Minute() == 0 && current.Second() == 0 && current.Nanosecond() == 0 { // 对应整点，此时不需要加1小时
		return current
	}
	return time.Date(current.Year(), current.Month(), current.Day(), current.Hour()+1, 0, 0, 0, utils.GetTimezoneLocation(region))
}
