package cdt_ab_test

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	aggregate_masked_channel_cdt3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"strings"
	"time"
)

type CdtAbTestService interface {
	// for admin
	CreateCdtAbTestRule(ctx utils.LCOSContext, ruleName, productId string, objectType uint8, testGroupList cdt_ab_test.TestGroupList) *lcos_error.LCOSError
	UpdateCdtAbTestRule(ctx utils.LCOSContext, ruleId uint64, ruleName, productId string, testGroupList cdt_ab_test.TestGroupList) *lcos_error.LCOSError
	DeleteCdtAbTestRule(ctx utils.LCOSContext, ruleId uint64) *lcos_error.LCOSError
	EnableCdtAbTestRule(ctx utils.LCOSContext, ruleId uint64) *lcos_error.LCOSError
	DeployCdtAbTestRule(ctx utils.LCOSContext, ruleId uint64, deployGroupTag uint8) *lcos_error.LCOSError
	ListCdtAbTestRuleByParamsWithPaging(ctx utils.LCOSContext, ruleId uint64, productId string, pageNo, pageSize uint32) ([]*cdt_ab_test.CdtAbTestRule, uint32, *lcos_error.LCOSError)
	GetActiveAbTestRuleByProductId(ctx utils.LCOSContext, productId string, objectType uint8) (*cdt_ab_test.CdtAbTestRule, *lcos_error.LCOSError)
	GetCdtAbTestAvailableRule(ctx utils.LCOSContext, productId string, groupTag, objectType uint8) ([]uint64, bool, *lcos_error.LCOSError)

	// for grpc
	GetActiveAbTestRuleByProductIdUsingCache(ctx utils.LCOSContext, productId string, objectType uint8) (*cdt_ab_test.CdtAbTestRule, *lcos_error.LCOSError)
}

func NewCdtAbTestService(abTestRuleDao cdt_ab_test.CdtAbTestRuleDao, autoUpdateRuleDao auto_update_rule.CDTAutoUpdateRuleTabDAO, manualUpdateRuleDao manual_update_rule.CDTManualUpdateRuleTabDAO, aggregateMaskedChannelCdtDao aggregate_masked_channel_cdt3.AggregateMaskedChannelCdtDao) *cdtAbTestService {
	return &cdtAbTestService{
		abTestRuleDao:                abTestRuleDao,
		autoUpdateRuleDao:            autoUpdateRuleDao,
		manualUpdateRuleDao:          manualUpdateRuleDao,
		aggregateMaskedChannelCdtDao: aggregateMaskedChannelCdtDao,
	}
}

type cdtAbTestService struct {
	abTestRuleDao                cdt_ab_test.CdtAbTestRuleDao
	autoUpdateRuleDao            auto_update_rule.CDTAutoUpdateRuleTabDAO
	manualUpdateRuleDao          manual_update_rule.CDTManualUpdateRuleTabDAO
	aggregateMaskedChannelCdtDao aggregate_masked_channel_cdt3.AggregateMaskedChannelCdtDao
}

func (c *cdtAbTestService) CreateCdtAbTestRule(ctx utils.LCOSContext, ruleName, productId string, objectType uint8, testGroupList cdt_ab_test.TestGroupList) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())

	// 1. 校验test group是否合法
	if err := c.checkTestGroupList(ctx, productId, objectType, testGroupList); err != nil {
		logger.CtxLogErrorf(ctx, "check test group error: %s", err.Msg)
		return err
	}

	// 2. 创建ab test rule
	rule := &cdt_ab_test.CdtAbTestRule{
		Region:        region,
		RuleName:      ruleName,
		ProductId:     productId,
		TestGroupList: testGroupList,
		RuleStatus:    edd_constant.AbTestRuleStatusInactive,
		ObjectType:    objectType,
		Operator:      ctx.GetUserEmail(),
	}
	return c.abTestRuleDao.CreateCdtAbTestRule(ctx, rule)
}

func (c *cdtAbTestService) UpdateCdtAbTestRule(ctx utils.LCOSContext, ruleId uint64, ruleName, productId string, testGroupList cdt_ab_test.TestGroupList) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())

	// 2. 校验ab test rule的状态是否可以更新
	rule, err := c.abTestRuleDao.GetCdtAbTestRuleById(ctx, ruleId)
	if err != nil {
		return err
	}
	if rule == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cdt ab test rule not found")
	}
	if rule.RuleStatus != edd_constant.AbTestRuleStatusInactive {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "can only update ab test rule with Inactive status")
	}

	// 1. 检查更新后的test group是否合法
	if err := c.checkTestGroupList(ctx, productId, rule.ObjectType, testGroupList); err != nil {
		logger.CtxLogErrorf(ctx, "check test group error: %s", err.Msg)
		return err
	}

	// 3. 更新ab test rule
	updateMap := map[string]interface{}{
		"region":          region,
		"rule_name":       ruleName,
		"product_id":      productId,
		"test_group_list": testGroupList,
		"operator":        ctx.GetUserEmail(),
	}
	return c.abTestRuleDao.UpdateCdtAbTestRuleById(ctx, ruleId, updateMap)
}

func (c *cdtAbTestService) DeleteCdtAbTestRule(ctx utils.LCOSContext, ruleId uint64) *lcos_error.LCOSError {
	rule, err := c.abTestRuleDao.GetCdtAbTestRuleById(ctx, ruleId)
	if err != nil {
		return err
	}
	if rule == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cdt ab test rule not found")
	}
	if rule.RuleStatus != edd_constant.AbTestRuleStatusInactive {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "can only delete ab test rule with Inactive status")
	}
	return c.abTestRuleDao.DeleteCdtAbTestRuleById(ctx, ruleId)
}

func (c *cdtAbTestService) EnableCdtAbTestRule(ctx utils.LCOSContext, ruleId uint64) *lcos_error.LCOSError {
	// 1. 校验规则是否存在且状态是否是inactive
	rule, err := c.abTestRuleDao.GetCdtAbTestRuleById(ctx, ruleId)
	if err != nil {
		return err
	}
	if rule == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cdt ab test rule not found")
	}
	if rule.RuleStatus != edd_constant.AbTestRuleStatusInactive {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "can only enable ab test rule with Inactive status")
	}

	// 对于M channel = xxxx，当用户已经创建了M channel aggregated rule，再Active CDT AB Test Rule时，限制用户active的动作，并报错
	if rule.ObjectType == edd_constant.CdtObject {
		err = c.checkAggregateMChannelCDT(ctx, rule.ProductId)
		if err != nil {
			logger.CtxLogErrorf(ctx, err.Msg)
			return err
		}
	}

	// 2. 校验test group
	if err = c.checkTestGroupList(ctx, rule.ProductId, rule.ObjectType, rule.TestGroupList); err != nil {
		return err
	}
	// 3. 校验此渠道是否存在其他正在生效的规则
	activeAbTestRule, err := c.GetActiveAbTestRuleByProductId(ctx, rule.ProductId, rule.ObjectType)
	if err != nil {
		return err
	}
	if activeAbTestRule != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cannot enable this rule because there is another active rule for the same product")
	}
	// 4. 更新规则状态
	return c.abTestRuleDao.UpdateCdtAbTestRuleById(ctx, ruleId, map[string]interface{}{
		"rule_status": edd_constant.AbTestRuleStatusActive,
	})
}

func (c *cdtAbTestService) DeployCdtAbTestRule(ctx utils.LCOSContext, ruleId uint64, deployGroupTag uint8) *lcos_error.LCOSError {
	rule, err := c.abTestRuleDao.GetCdtAbTestRuleById(ctx, ruleId)
	if err != nil {
		return err
	}
	if rule == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cdt ab test rule not found")
	}
	if rule.RuleStatus != edd_constant.AbTestRuleStatusActive {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "can only deploy ab test rule with Active status")
	}
	if len(rule.TestGroupList) <= int(deployGroupTag) {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "deploy test group not found")
	}

	fn := func() *lcos_error.LCOSError {
		for i, group := range rule.TestGroupList {
			groupTag := uint8(i)
			if groupTag == deployGroupTag {
				if groupTag == edd_constant.AbTestGroupTagA {
					// group a一定是当前正在生效的规则，不需要处理
					continue
				}
				// 部署其他test group，如果有当前正在生效的tag 0自动规则，则需要将其置为disable
				err = c.autoUpdateRuleDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{
					"product_id":     rule.ProductId,
					"cdt_type":       rule.ObjectType,
					"is_site_line":   constant.TRUE,
					"is_lm":          constant.FALSE,
					"status_id in":   []uint8{constant.Active, constant.Upcoming},
					"test_group_tag": edd_constant.AbTestGroupTagA,
				}, map[string]interface{}{
					"status_id":       constant.Disable,
					"expiration_time": time.Now().Unix(), // nolint
				})
				if err != nil {
					return err
				}
				if group.CdtType == edd_constant.AbTestCdtTypeAutoGeneration {
					// 如果test group选择了自动更新规则，那么需要将规则的group tag置为0
					if err = c.autoUpdateRuleDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": group.AutoUpdateRuleId}, map[string]interface{}{"test_group_tag": edd_constant.AbTestGroupTagA}); err != nil {
						return err
					}
				}
			} else {
				// 弃用的group，如果是自动更新规则，需要把状态置为disabled，过期时间置为现在。如果是手动上传数据，则不需要做任何处理
				if group.CdtType == edd_constant.AbTestCdtTypeAutoGeneration {
					err = c.autoUpdateRuleDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": group.AutoUpdateRuleId}, map[string]interface{}{
						"status_id":       constant.Disable,
						"expiration_time": time.Now().Unix(), // nolint
					})
					if err != nil {
						return err
					}
				}
			}
		}
		return c.abTestRuleDao.UpdateCdtAbTestRuleById(ctx, ruleId, map[string]interface{}{
			"rule_status": edd_constant.AbTestRuleStatusDeployed,
		})
	}
	return ctx.Transaction(fn)
}

func (c *cdtAbTestService) ListCdtAbTestRuleByParamsWithPaging(ctx utils.LCOSContext, ruleId uint64, productId string, pageNo, pageSize uint32) ([]*cdt_ab_test.CdtAbTestRule, uint32, *lcos_error.LCOSError) {
	region := ctx.GetCountry()

	queryParams := make(map[string]interface{})
	queryParams["region"] = strings.ToUpper(region)
	if ruleId != 0 {
		queryParams["id"] = ruleId
	}
	if productId != "" {
		queryParams["product_id"] = productId
	}
	return c.abTestRuleDao.ListCdtAbTestRuleByParamsWithPaging(ctx, queryParams, pageNo, pageSize)
}

func (c *cdtAbTestService) GetActiveAbTestRuleByProductId(ctx utils.LCOSContext, productId string, objectType uint8) (*cdt_ab_test.CdtAbTestRule, *lcos_error.LCOSError) {
	abTestRuleList, err := c.abTestRuleDao.ListCdtAbTestRuleByParams(ctx, map[string]interface{}{"product_id": productId, "object_type": objectType, "rule_status": edd_constant.AbTestRuleStatusActive})
	if err != nil {
		return nil, err
	}
	if len(abTestRuleList) == 0 {
		return nil, err
	}
	return abTestRuleList[0], nil
}

func (c *cdtAbTestService) GetCdtAbTestAvailableRule(ctx utils.LCOSContext, productId string, groupTag, objectType uint8) ([]uint64, bool, *lcos_error.LCOSError) {
	// 1. 获取当前正在生效的自动更新规则
	autoUpdateRuleList, err := c.autoUpdateRuleDao.SearchAutoUpdateRule(ctx, map[string]interface{}{
		"product_id":     productId,
		"cdt_type":       objectType, // leadtime auto gen rule only
		"is_site_line":   constant.TRUE,
		"is_lm":          constant.FALSE,
		"status_id":      constant.Active,
		"test_group_tag": groupTag,
	})
	if err != nil {
		return nil, false, err
	}
	autoUpdateRuleIdList := make([]uint64, 0, len(autoUpdateRuleList))
	for _, autoUpdateRule := range autoUpdateRuleList {
		autoUpdateRuleIdList = append(autoUpdateRuleIdList, autoUpdateRule.ID)
	}

	// 2. 校验手动上传数据是否可选
	manualUpdateData, err := c.manualUpdateRuleDao.GetCdtLocationDataByParams(ctx, map[string]interface{}{"product_id": productId, "location_level": constant.Country, "is_lm": constant.FALSE, "is_site_line": constant.TRUE, "object_type": objectType}, "")
	if err != nil {
		return nil, false, err
	}
	manualUpdateAvailable := len(manualUpdateData) != 0
	if groupTag == edd_constant.AbTestGroupTagA {
		// 如果是group a，那么限制只可选当前正在生效的规则，如果存在自动更新规则，那么手动上传数据不可选
		if len(autoUpdateRuleIdList) != 0 {
			manualUpdateAvailable = false
		}
	}

	return autoUpdateRuleIdList, manualUpdateAvailable, nil
}

func (c *cdtAbTestService) GetActiveAbTestRuleByProductIdUsingCache(ctx utils.LCOSContext, productId string, objectType uint8) (*cdt_ab_test.CdtAbTestRule, *lcos_error.LCOSError) {
	queryExec := localcache.NewLocalCacheQueryExecutor()
	obj, err := queryExec.Find(ctx, constant.LogisticCdtAbTestRuleNamespace, cdt_ab_test.GetCacheKey(productId, objectType))
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, err.Error())
	}
	if obj == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "active ab test rule not found")
	}
	if abTestRule, ok := obj.(*cdt_ab_test.CdtAbTestRule); ok {
		abTestRuleCopy := *abTestRule
		return &abTestRuleCopy, nil
	}
	return nil, nil
}

func (c *cdtAbTestService) checkTestGroupList(ctx utils.LCOSContext, productId string, objectType uint8, testGroupList cdt_ab_test.TestGroupList) *lcos_error.LCOSError {
	// 1. group list长度校验
	if len(testGroupList) < edd_constant.AbTestGroupNumLimitMin {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Length of test group list should be more than %d", edd_constant.AbTestGroupNumLimitMin)
	}
	if len(testGroupList) > config.GetCdtAbTestGroupNumLimitMax() {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Length of test group list should be less than %d", config.GetCdtAbTestGroupNumLimitMax())
	}

	// 2. test group有效性校验
	var manualUpdateCdtTypeCount int
	buyerGroupMap := make(map[uint32]uint8, 10)
	for i, group := range testGroupList {
		groupTag := uint8(i)

		// 1. group a校验，group a必须选择当前线上正在实际生效的规则
		if groupTag == edd_constant.AbTestGroupTagA {
			autoUpdateRules, err := c.autoUpdateRuleDao.SearchAutoUpdateRule(ctx, map[string]interface{}{
				"product_id":     productId,
				"cdt_type":       objectType,
				"is_site_line":   constant.TRUE,
				"is_lm":          constant.FALSE,
				"status_id":      constant.Active,
				"test_group_tag": groupTag,
			})
			if err != nil {
				return err
			}
			if len(autoUpdateRules) != 0 {
				// 存在tag=0且生效的自动更新规则，那么group a只能选自动更新，否则报错
				if group.CdtType != edd_constant.AbTestCdtTypeAutoGeneration {
					return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Group A can only choose the effective cdt type: Auto Generation")
				}
			} else {
				// 不存在存在tag=0且生效的自动更新规则，那么group a只能选手动更新，否则报错
				if group.CdtType != edd_constant.AbTestCdtTypeManualUpdate {
					return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Group A can only choose the effective cdt type: Manual Update")
				}
			}

		}

		// 2. 校验test group选择的leadtime数据是否有效
		if group.CdtType == edd_constant.AbTestCdtTypeAutoGeneration {
			// test group使用的是自动更新规则，则需要校验此自动更新规则是否是active状态且不会定时轮转为disabled
			autoUpdateRule, err := c.autoUpdateRuleDao.GetAutoUpdateRuleByID(ctx, group.AutoUpdateRuleId)
			if err != nil {
				return err
			}
			if autoUpdateRule == nil {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "%s check error: auto update rule[%d] not exists", edd_constant.AbTestGroupNameMap[groupTag], group.AutoUpdateRuleId)
			}
			if autoUpdateRule.ProductID != productId {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "%s check error: auto update rule[%d] not belong to product %s", edd_constant.AbTestGroupNameMap[groupTag], group.AutoUpdateRuleId, productId)
			}
			if autoUpdateRule.TestGroupTag != groupTag {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "%s check error: auto udpate rule[%d] not belong to this test group", edd_constant.AbTestGroupNameMap[groupTag], group.AutoUpdateRuleId)
			}
			if autoUpdateRule.StatusID != constant.Active {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "%s check error: auto update rule[%d] status is not active", edd_constant.AbTestGroupNameMap[groupTag], group.AutoUpdateRuleId)
			}
			if autoUpdateRule.ExpirationTime != 0 {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "%s check error: auto update rule[%d] will be expired at %s", edd_constant.AbTestGroupNameMap[groupTag], group.AutoUpdateRuleId, utils.FormatTimestamp(autoUpdateRule.ExpirationTime, constant.DateAndTimeFormat))
			}
		} else {
			manualUpdateCdtTypeCount++
			// test group使用的是手动上传数据，则需要是否存在手动上传数据
			manualUpdateData, err := c.manualUpdateRuleDao.GetCdtLocationDataByParams(
				ctx,
				map[string]interface{}{
					"product_id":     productId,
					"location_level": constant.Country,
					"is_lm":          constant.FALSE,
					"is_site_line":   constant.TRUE,
					"object_type":    objectType,
				},
				"",
			)
			if err != nil {
				return err
			}
			if len(manualUpdateData) == 0 {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "%s check error: manual update data not found for product %s", edd_constant.AbTestGroupNameMap[groupTag], productId)
			}
		}

		// 3. buyer group重复校验
		if groupTag != edd_constant.AbTestGroupTagA && len(group.BuyerGroup) > 5 {
			// 非group a最多只允许选择5个buyer id尾数
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "%s check error: at most 5 buyer id can be chosen if is not Group A", edd_constant.AbTestGroupNameMap[groupTag])
		}
		for _, buyerId := range group.BuyerGroup {
			if dupGroupTag, ok := buyerGroupMap[buyerId]; ok {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Buyer ID[%d] duplicated between %s and %s", buyerId, edd_constant.AbTestGroupNameMap[groupTag], edd_constant.AbTestGroupNameMap[dupGroupTag])
			} else {
				buyerGroupMap[buyerId] = groupTag
			}
		}
	}
	// 至多只允许有一个test group选择手动上传数据
	if manualUpdateCdtTypeCount > 1 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "Manual Update can only be chosen in one test group")
	}
	// 校验buyer id是否有空缺 0-9
	for buyerId := uint32(0); buyerId < 10; buyerId++ {
		if _, ok := buyerGroupMap[buyerId]; !ok {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Buyer ID[%d] missing in all test groups", buyerId)
		}
	}

	return nil
}

func (c *cdtAbTestService) checkAggregateMChannelCDT(ctx utils.LCOSContext, productId string) *lcos_error.LCOSError {
	existModels, lcosErr := c.aggregateMaskedChannelCdtDao.SearchAggregateMaskedChannelCdt(
		ctx,
		map[string]interface{}{
			"rule_status in":    []int{edd_constant.Active, edd_constant.UpComing},
			"masked_product_id": productId,
		},
	)
	if lcosErr != nil {
		return lcosErr
	}

	if len(existModels) != 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf(
			"for M channel [%v], there is an upcoming/active m aggregated rule, so you are not allowed to activate a CDT AB testing rule at the same time", productId))
	}
	return nil
}
