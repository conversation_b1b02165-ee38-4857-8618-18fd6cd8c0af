package cdt_common

import (
	"bufio"
	"encoding/csv"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"io"
	"net/http"
	"os"
	"time"
)

func ParseCSVFile(ctx utils.LCOSContext, fileUrl string) ([][]string, *lcos_error.LCOSError) {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return nil, err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := os.Open(filePath)
	if fileErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, fileErr.Error())
	}
	defer file.Close()

	//defer os.Remove(filePath)

	r := csv.NewReader(file)
	r.LazyQuotes = true
	var resultList [][]string
	maxLoopSize := config.GetMaxLoopSizeForFileRead(ctx) * 100
	var loopIndex int
	for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
		lineNum++
		row, err := r.Read()
		if err != nil && err != io.EOF {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}
		// 跳过表头
		if lineNum == 1 {
			continue
		}
		if err == io.EOF {
			break
		}
		resultList = append(resultList, row)
	}
	if loopIndex >= maxLoopSize { // over max loop size, need to report error
		return nil, lcos_error.NewLCOSError(lcos_error.LoopOverMaxSize, fmt.Sprintf("loop over max size, max size=%d", maxLoopSize))
	}
	return resultList, nil
}

func ParseCSVFileLongTime(ctx utils.LCOSContext, fileUrl string) ([][]string, *lcos_error.LCOSError) {
	var resp *http.Response
	var err error
	for i := 0; i < int(config.GetRequestMaxRetryTimes(ctx)); i++ {
		resp, err = http.Get(fileUrl) // nolint
		if err == nil {
			break
		}
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	defer resp.Body.Close()

	bur := bufio.NewReader(resp.Body)
	r := csv.NewReader(bur)
	r.LazyQuotes = true
	var resultList [][]string
	maxLoopSize := config.GetMaxLoopSizeForFileRead(ctx) * 10 //一千万的数据
	var loopIndex int
	for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
		lineNum++
		row, err := r.Read()

		if err != nil && err != io.EOF {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}
		// 跳过表头
		if lineNum == 1 {
			continue
		}
		if err == io.EOF {
			break
		}
		resultList = append(resultList, row)
	}
	if loopIndex >= maxLoopSize { // over max loop size, need to report error
		return nil, lcos_error.NewLCOSError(lcos_error.LoopOverMaxSize, fmt.Sprintf("loop over max size, max size=%d", maxLoopSize))
	}
	return resultList, nil
}
