package cdt_common

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"os"
	"sort"
	"strconv"
	"time"
)

// ParseCepFile 根据入参的url解析cep文件，并且校验cep之间没有发生重叠
func ParseCepFile(ctx utils.LCOSContext, fileUrl string, numberLimit int) ([]*CepRange, *lcos_error.LCOSError) {
	var cepRangeList []*CepRange
	// 根据传入的文件url解析
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return nil, err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_dest_postcode.xlsx")
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}

		// 跳过表头和空白行
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		// 将row[0]和row[1]组合为数据存为数据库
		if row[0] != "" && row[1] != "" {
			left, err1 := strconv.Atoi(row[0])
			right, err2 := strconv.Atoi(row[1])
			if err1 != nil || err2 != nil {
				return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, fmt.Sprintf("file is not valid|line_num=%v", lineNum))
			}
			cepRangeList = append(cepRangeList, &CepRange{left, right})
		} else {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, fmt.Sprintf("file is not valid|line_num=%v", lineNum))
		}
	}

	// 解析出来的文件为空
	if len(cepRangeList) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "file is empty")
	}

	if len(cepRangeList) > numberLimit {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cep range was limited to %d, input number:%d", numberLimit, len(cepRangeList)))
	}

	// 检查传入的cep range是否有重叠
	sort.Slice(cepRangeList, func(i, j int) bool {
		if cepRangeList[i].CepLeft < cepRangeList[j].CepLeft {
			return true
		} else {
			return false
		}
	})

	var sortedCepRangeList []*CepRange
	sortedCepRangeList = append(sortedCepRangeList, &CepRange{cepRangeList[0].CepLeft, cepRangeList[0].CepRight})
	for i := 1; i < len(cepRangeList); i++ {
		// 存在重叠
		if cepRangeList[i].CepLeft <= cepRangeList[i-1].CepRight {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "file parse error, cep range overlap")
		}
		sortedCepRangeList = append(sortedCepRangeList, &CepRange{cepRangeList[i].CepLeft, cepRangeList[i].CepRight})
	}
	return sortedCepRangeList, nil
}
