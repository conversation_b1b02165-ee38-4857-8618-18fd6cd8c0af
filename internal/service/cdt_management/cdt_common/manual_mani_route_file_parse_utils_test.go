package cdt_common

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"testing"
)

func Test_transferRowToStruct(t *testing.T) {
	type args struct {
		row        []string
		isCB       bool
		region     string
		objectType uint8
	}
	tests := []struct {
		name  string
		args  args
		want1 string
	}{
		{
			name: "the length of row is not valid",
			args: args{
				row:        []string{"-1"},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "the length of row is not valid",
		},
		{
			name: "origin location level is not valid",
			args: args{
				row:        []string{"error location level", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location level is not valid",
		},
		{
			name: "destination location level is not valid",
			args: args{
				row:        []string{"-1", "1", "1", "1", "1", "error location level", "1", "1", "1", "1", "1", "1", "1", "1"},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "destination location level is not valid",
		},
		{
			name: "for CDT Object type and CB Channel, origin location level can only be -1",
			args: args{
				row:        []string{"1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "for CDT Object type and CB Channel, origin location level can only be -1",
		},
		{
			name: "origin location info is not valid|region is not the same",
			args: args{
				row:        []string{"1", "BR", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location info is not valid, error=cannot upload region:[BR] route info in region:[SG]",
		},
		{
			name: "origin location info is not valid|country level, location info wrong",
			args: args{
				row:        []string{"-1", "SG", "", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location info is not valid, error=state/city/district/cep range/postcode should be blank",
		},
		{
			name: "origin location info is not valid|state level, location info wrong",
			args: args{
				row:        []string{"0", "SG", "", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "origin location info is not valid|city level, location info wrong",
			args: args{
				row:        []string{"1", "SG", "", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "origin location info is not valid|district level, location info wrong",
			args: args{
				row:        []string{"2", "SG", "", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "origin location info is not valid| level|origin location level is postcode",
			args: args{
				row:        []string{"9", "SG", "", "", "", "-1", "", "", "", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "origin location info is not valid| level|origin location level is cep range",
			args: args{
				row:        []string{"4", "SG", "", "", "", "-1", "", "", "", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "origin location info is not valid|location level is not valid",
			args: args{
				row:        []string{"41", "SG", "", "", "", "-1", "", "", "", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location info is not valid, error=location level is not valid",
		},
		{
			name: "destination location info is not valid| level|location level is not valid",
			args: args{
				row:        []string{"-1", "", "", "", "", "-1", "BR", "", "", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "destination location info is not valid, error=cannot upload region:[BR] route info in region:[SG]",
		},
		{
			name: "destination location info is not valid| level|location level is state",
			args: args{
				row:        []string{"-1", "", "", "", "", "0", "SG", "", "1", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "destination location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "destination location info is not valid| level|location level is city",
			args: args{
				row:        []string{"-1", "", "", "", "", "1", "SG", "", "1", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "destination location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "destination location info is not valid| level|location level is district",
			args: args{
				row:        []string{"-1", "", "", "", "", "2", "SG", "", "1", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "destination location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "destination location info is not valid| level|location level is cep range",
			args: args{
				row:        []string{"-1", "", "", "", "", "4", "SG", "", "1", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "destination location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "destination location info is not valid| level|location level is postcode",
			args: args{
				row:        []string{"-1", "", "", "", "", "9", "SG", "", "1", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "destination location info is not valid, error=location info not correspond to location level",
		},
		{
			name: "destination location info is not valid| level|location level is cep range but not cep range region",
			args: args{
				row:        []string{"-1", "", "", "", "", "4", "SG", "", "", "", "1", "2", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "destination location level cannot be cep range when region is non cep range region",
		},
		{
			name: "destination location info is not valid| level|location level is postcode but is cep range region",
			args: args{
				row:        []string{"-1", "", "", "", "", "9", "BR", "", "", "", "", "", "1", ""},
				isCB:       false,
				region:     "BR",
				objectType: edd_constant.CdtObject,
			},
			want1: "destination location level cannot be postcode when region is cep range",
		},
		{
			name: "the length of row is not valid|object type = lead",
			args: args{
				row:        []string{"-1", "", "", "", "", "0", "SG", "State", "", "", "", "", "", ""},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "the length of row is not valid",
		},
		{
			name: "update event is not valid",
			args: args{
				row:        []string{"-1", "", "", "", "", "0", "SG", "State", "", "", "", "", "", "", "update event1", "", "", "", "", "1", "2"},
				isCB:       false,
				region:     "SG",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "event:[update event1] is not valid for local product, has to be one of SOC Inbound/SOC Outbound/LM Hub Inbound/Out for Delivery",
		},
		{
			name: "for object type lead, has to has at least one update event",
			args: args{
				row:        []string{"-1", "SG", "", "", "", "1", "SG", "State", "City", "", "", "", "", "", "", "", "", "", "1.1", "2.2"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "for object type lead, has to has at least one update event",
		},
		{
			name: "the length of row is not valid",
			args: args{
				row:        []string{"-1", "SG", "", "", "", "1", "SG", "State", "City", "", "", "", "", "1.1"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "the length of row is not valid",
		},
		{
			name: "min delta or max delta need to be valid float",
			args: args{
				row:        []string{"-1", "SG", "", "", "", "1", "SG", "State", "City", "", "", "", "", "", "", "", "", "TWS Inbound", "not valid delta", "2.2"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "min delta or max delta need to be valid float",
		},
		{
			name: "Min Delta should from -7 to 30",
			args: args{
				row:        []string{"-1", "SG", "", "", "", "1", "SG", "State", "City", "", "", "", "", "", "", "", "", "TWS Inbound", "-19", "2.2"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "Min Delta should from -7 to 30",
		},
		{
			name: "Max Delta should from -7 to 30",
			args: args{
				row:        []string{"-1", "SG", "", "", "", "1", "SG", "State", "City", "", "", "", "", "", "", "", "", "TWS Inbound", "-1", "31"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "Max Delta should from -7 to 30",
		},
		{
			name: "cep range left is not valid",
			args: args{
				row:        []string{"-1", "", "", "", "", "4", "BR", "", "", "", "cep range left", "cep range right", "", "", "", "", "", "TWS Inbound", "-1", "29"},
				isCB:       true,
				region:     "BR",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "cep range left is not valid",
		},
		{
			name: "cep range right is not valid",
			args: args{
				row:        []string{"-1", "", "", "", "", "4", "BR", "", "", "", "1001", "cep range right", "", "", "", "", "", "TWS Inbound", "-1", "29"},
				isCB:       true,
				region:     "BR",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "cep range right is not valid",
		},
		{
			name: "cep range right mush be greater than left",
			args: args{
				row:        []string{"-1", "", "", "", "", "4", "BR", "", "", "", "1001", "1000", "", "", "", "", "", "TWS Inbound", "-1", "29"},
				isCB:       true,
				region:     "BR",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "cep range right mush be greater than left",
		},
		{
			name: "valid cep range data",
			args: args{
				row:        []string{"-1", "", "", "", "", "4", "BR", "", "", "", "1001", "1002", "", "", "", "", "", "TWS Inbound", "-1", "29"},
				isCB:       true,
				region:     "BR",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "",
		},
		{
			name: "valid location data",
			args: args{
				row:        []string{"-1", "", "", "", "", "1", "SG", "State", "City", "", "", "", "", "", "", "", "", "TWS Inbound", "-1", "29"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "",
		},
		{
			name: "valid postcode data",
			args: args{
				row:        []string{"-1", "", "", "", "", "9", "SG", "", "", "", "", "", "123123", "", "", "", "", "TWS Inbound", "-1", "29"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "",
		},
		{
			name: "valid postcode data for cdt",
			args: args{
				row:        []string{"-1", "", "", "", "", "9", "SG", "", "", "", "", "", "123123", "-1", "29"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "",
		},

		// =========================================================================================================================================================================
		// SPLN-28302 newly add
		{
			name: "for Lead Object type and CB Channel, update event is one of TWS Inbound / TWS Outbound, origin location level can only be -1",
			args: args{
				row:        []string{"1", "SG", "State", "City", "", "1", "SG", "State", "City", "", "", "", "", "TWS Inbound", "", "", "", "", "1.1", "2.2"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.LeadTimeObject,
			},
			want1: "for Lead Object type and CB Channel, update event is one of TWS Inbound / TWS Outbound, origin location level can only be -1",
		},
		{
			name: "origin location info is not valid, error=state/city/district/cep range/postcode should be blank",
			args: args{
				row:        []string{"-1", "SG", "State", "City", "", "1", "SG", "State", "City", "", "", "", "", "1.1", "2.2"},
				isCB:       true,
				region:     "SG",
				objectType: edd_constant.CdtObject,
			},
			want1: "origin location info is not valid, error=state/city/district/cep range/postcode should be blank",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, got1 := transferRowToStruct(0, tt.args.row, tt.args.isCB, tt.args.region, tt.args.objectType)
			if got1 != tt.want1 {
				t.Errorf("transferRowToStruct() got1 = [%v], want [%v]", got1, tt.want1)
			} else {
				t.Log(got1)
			}
		})
	}
}
