package cdt_common

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

// GenerateQueryForStatusForAutoUpdateRule 由于upcoming active disable存在状态轮转，需要单独生成sql
func GenerateQueryForStatusForAutoUpdateRule(statusID uint8, nowTimestamp uint32) map[string]interface{} {
	switch statusID {
	case constant.Upcoming:
		return map[string]interface{}{"status_id": constant.Upcoming, "_OR_": map[string]interface{}{"effective_time >": nowTimestamp}} //状态为upcoming，且生效时间还没有到
	case constant.Active:
		return map[string]interface{}{"status_id in": []uint8{constant.Upcoming, constant.Active}, "effective_time >": 0, "effective_time <=": nowTimestamp, "_OR_": map[string]interface{}{"expiration_time": 0, "expiration_time >": nowTimestamp}}
	case constant.Disable:
		return map[string]interface{}{"status_id in": []uint8{constant.Upcoming, constant.Active, constant.Disable}, "expiration_time >": 0, "expiration_time <=": nowTimestamp}
	default:
		return map[string]interface{}{"status_id": statusID}
	}
}

// GenerateQueryForStatusForManualManipulationRule 由于upcoming active disable存在状态轮转，需要单独生成sql
func GenerateQueryForStatusForManualManipulationRule(statusID uint8, nowTimestamp uint32) map[string]interface{} {
	switch statusID {
	case constant.Upcoming:
		return map[string]interface{}{"status_id": constant.Upcoming, "effective_date >": nowTimestamp} //状态为upcoming，且生效时间还没有到
	case constant.Active:
		return map[string]interface{}{"status_id in": []uint8{constant.Upcoming, constant.Active}, "effective_date >": 0, "effective_date <=": nowTimestamp, "_OR_": map[string]interface{}{"expiration_date": 0, "expiration_date >": nowTimestamp}}
	case constant.Disable:
		return map[string]interface{}{"status_id in": []uint8{constant.Upcoming, constant.Active, constant.Disable}, "expiration_date >": 0, "expiration_date <=": nowTimestamp}
	default:
		return map[string]interface{}{"status_id": statusID}
	}
}

/*
isOriginAddressAllowedForCBProduct
SPLN-28302: For CB product, when update event is one of Destination Inbound/LM Hub inbound/Out For Delivery, origin location is allowed
*/
func isOriginAddressAllowedForCBProduct(updateEvent uint8) bool {
	return utils.CheckInUint8(updateEvent, []uint8{edd_constant.DestinationInbound, edd_constant.LMHubInbound, edd_constant.OutForDelivery})
}

func IsOriginLocationNeeded(cbFlag bool, updateEvent uint8) bool {
	return !cbFlag || isOriginAddressAllowedForCBProduct(updateEvent)
}

func IsCbLmEvent(updateEvent uint8) bool {
	return utils.CheckInUint8(updateEvent, []uint8{edd_constant.DestinationInbound, edd_constant.LMHubInbound, edd_constant.OutForDelivery})
}
