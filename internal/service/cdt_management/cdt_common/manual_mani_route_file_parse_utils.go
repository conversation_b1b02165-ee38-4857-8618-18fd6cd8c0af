package cdt_common

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"os"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

type CepOverLap struct {
	Left  int
	Right int
	RowId int
}

func generateRouteKey(route *RouteUpload) string {
	return fmt.Sprintf("%s:%s:%s:%s:%s:%s:%s:%s:%s:%d", route.OriginState, route.OriginCity, route.OriginDistrict, route.DestinationState, route.DestinationCity, route.DestinationDistrict, route.DestinationPostcode, route.DestinationCEPRangeInitial, route.DestinationCEPRangeFinal, route.UpdateEvent)
}

func CheckIsDuplicate(routes []*RouteUpload) *lcos_error.LCOSError {
	routeMap := make(map[string]*RouteUpload)
	for _, route := range routes {
		if _, ok := routeMap[generateRouteKey(route)]; ok {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("row:[%d] and row:[%d] are the same location and update event. Please check it again.", route.Row, routeMap[generateRouteKey(route)].Row))
		}
		routeMap[generateRouteKey(route)] = route
	}
	return nil
}

func checkLocationAndLocationLevel(locationLevel int8, region, state, city, district, cepRangeLeft, cepRangeRight, postcode string, currentRegion string) string {

	if region != "" && region != currentRegion {
		return fmt.Sprintf("cannot upload region:[%s] route info in region:[%s]", region, currentRegion)
	}

	switch locationLevel {
	case constant.Country:
		if !(state == "" && city == "" && district == "" && cepRangeLeft == "" && cepRangeRight == "" && postcode == "") {
			return "state/city/district/cep range/postcode should be blank"
		}
	case constant.State:
		if !(region != "" && state != "" && city == "" && district == "" && cepRangeLeft == "" && cepRangeRight == "" && postcode == "") {
			return "location info not correspond to location level"
		}
	case constant.City:
		if !(region != "" && state != "" && city != "" && district == "" && cepRangeLeft == "" && cepRangeRight == "" && postcode == "") {
			return "location info not correspond to location level"
		}
	case constant.District:
		if !(region != "" && state != "" && city != "" && district != "" && cepRangeLeft == "" && cepRangeRight == "" && postcode == "") {
			return "location info not correspond to location level"
		}
	case constant.CepRange:
		if !(region != "" && state == "" && city == "" && district == "" && cepRangeLeft != "" && cepRangeRight != "" && postcode == "") {
			return "location info not correspond to location level"
		}
	case constant.CDTPostcode:
		if !(region != "" && state == "" && city == "" && district == "" && cepRangeLeft == "" && cepRangeRight == "" && postcode != "") {
			return "location info not correspond to location level"
		}
	default:
		return "location level is not valid"
	}
	return ""
}

func transferRowToStruct(rowId int, row []string, isCB bool, region string, objectType uint8, titleMap map[string]int) ([]*RouteUpload, string) {
	if len(row) < len(titleMap) {
		return nil, "the length of row is not valid"
	}

	// 用于从titleMap得到每个字段的下标，并从row取值
	value := func(key string) string {
		if index, ok := titleMap[key]; ok {
			return row[index]
		}

		return ""
	}

	originLocationLevelInt, err1 := strconv.Atoi(value("origin_location_level"))
	if err1 != nil {
		return nil, "origin location level is not valid"
	}
	originLocationLevel := int8(originLocationLevelInt)

	destinationLocationLevelInt, err2 := strconv.Atoi(value("destination_location_level"))
	if err2 != nil {
		return nil, "destination location level is not valid"
	}
	destinationLocationLevel := int8(destinationLocationLevelInt)

	tmpRouteUpload := &RouteUpload{
		Row:                        rowId,
		OriginLocationLevel:        originLocationLevel,
		OriginRegion:               strings.TrimSpace(value("origin_region")),
		OriginState:                strings.TrimSpace(value("origin_state")),
		OriginCity:                 strings.TrimSpace(value("origin_city")),
		OriginDistrict:             strings.TrimSpace(value("origin_district")),
		DestinationLocationLevel:   destinationLocationLevel,
		DestinationRegion:          strings.TrimSpace(value("destination_region")),
		DestinationState:           strings.TrimSpace(value("destination_state")),
		DestinationCity:            strings.TrimSpace(value("destination_city")),
		DestinationDistrict:        strings.TrimSpace(value("destination_district")),
		DestinationCEPRangeInitial: strings.TrimSpace(value("destination_cep_initial")),
		DestinationCEPRangeFinal:   strings.TrimSpace(value("destination_cep_final")),
		DestinationPostcode:        strings.TrimSpace(value("destination_postcode")),
	}

	// SPLN-28302
	// for Object Type = CDT and CB channel, origin location level can only be country
	if objectType == edd_constant.CdtObject && !IsOriginLocationNeeded(isCB, edd_constant.ShippedOut) && tmpRouteUpload.OriginLocationLevel != constant.Country {
		return nil, "for CDT Object type and CB Channel, origin location level can only be -1"
	}

	originLocationLevelMsg := checkLocationAndLocationLevel(tmpRouteUpload.OriginLocationLevel, tmpRouteUpload.OriginRegion, tmpRouteUpload.OriginState, tmpRouteUpload.OriginCity, tmpRouteUpload.OriginDistrict, "", "", "", region)
	if len(originLocationLevelMsg) != 0 {
		return nil, fmt.Sprintf("origin location info is not valid, error=%s", originLocationLevelMsg)
	}

	destinationLocationLevelMsg := checkLocationAndLocationLevel(tmpRouteUpload.DestinationLocationLevel, tmpRouteUpload.DestinationRegion, tmpRouteUpload.DestinationState, tmpRouteUpload.DestinationCity, tmpRouteUpload.DestinationDistrict, tmpRouteUpload.DestinationCEPRangeInitial, tmpRouteUpload.DestinationCEPRangeFinal, tmpRouteUpload.DestinationPostcode, region)
	if len(destinationLocationLevelMsg) != 0 {
		return nil, fmt.Sprintf("destination location info is not valid, error=%s", destinationLocationLevelMsg)
	}

	// for cep range region, destination location level cannot be postcode
	if cdt.IsCepRangeCountry(region) && tmpRouteUpload.DestinationLocationLevel == constant.CDTPostcode {
		return nil, "destination location level cannot be postcode when region is cep range"
	} else if !cdt.IsCepRangeCountry(region) && tmpRouteUpload.DestinationLocationLevel == constant.CepRange { // for non cep range region, destination location level cannot be cep range
		return nil, "destination location level cannot be cep range when region is non cep range region"
	}

	// validate min delta and max delta
	var minDelta, maxDelta float64
	minDeltaString := strings.TrimSpace(value("min_delta"))
	maxDeltaString := strings.TrimSpace(value("max_delta"))

	// validate min delta max delta
	minDelta, err1 = strconv.ParseFloat(minDeltaString, 64)
	maxDelta, err2 = strconv.ParseFloat(maxDeltaString, 64)
	if err1 != nil || err2 != nil {
		return nil, "min delta or max delta need to be valid float"
	}
	minDelta, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", minDelta), 64)
	maxDelta, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", maxDelta), 64)
	if minDelta < constant.LimitMinDelta || minDelta > constant.LimitMaxDelta {
		return nil, "Min Delta should from -7 to 30"
	}
	if maxDelta < constant.LimitMinDelta || maxDelta > constant.LimitMaxDelta {
		return nil, "Max Delta should from -7 to 30"
	}
	tmpRouteUpload.MinDelta = minDelta
	tmpRouteUpload.MaxDelta = maxDelta

	// 处理update event
	var updateEventList []uint8
	updateEventMap := make(map[uint8]bool)
	eventIdxStart := titleMap["update_event1"]
	var eventIdxEnd int
	if objectType == edd_constant.LeadTimeObject {
		eventIdxEnd = titleMap["update_event8"]
	} else {
		eventIdxEnd = titleMap["update_event3"]
	}

	for i := eventIdxStart; i <= eventIdxEnd; i++ {
		eventString := strings.TrimSpace(row[i])
		if eventString != "" {
			// SPLN-31665 对于CDT上传的manipulate数据，只允许特定的event类型
			if objectType == edd_constant.CdtObject && !utils.CheckInString(eventString, edd_constant.CDTManipulationEventList) {
				return nil, fmt.Sprintf("for CDT Object type, update event is one of %v", edd_constant.CDTManipulationEventList)
			}

			updateEventInt, err := edd_constant.GetUpdateEvent(eventString, isCB)
			if err != nil {
				return nil, err.Error()
			}

			// SPLN-28302 for CB channel, if update event is one of TWS Inbound / TWS Outbound, origin location level can only be -1
			if !IsOriginLocationNeeded(isCB, updateEventInt) && tmpRouteUpload.OriginLocationLevel != constant.Country {
				return nil, "for Lead Object type and CB Channel, update event is one of TWS Inbound / TWS Outbound, origin location level can only be -1"
			}

			if _, ok := updateEventMap[updateEventInt]; !ok {
				updateEventList = append(updateEventList, updateEventInt)
				updateEventMap[updateEventInt] = true
			}
		}
	}
	if len(updateEventList) <= 0 {
		return nil, "for object type lead, has to has at least one update event"
	}

	if tmpRouteUpload.DestinationCEPRangeInitial != "" && tmpRouteUpload.DestinationCEPRangeFinal != "" {
		left, err := strconv.Atoi(tmpRouteUpload.DestinationCEPRangeInitial)
		if err != nil {
			return nil, "cep range left is not valid"
		}

		right, err := strconv.Atoi(tmpRouteUpload.DestinationCEPRangeFinal)
		if err != nil {
			return nil, "cep range right is not valid"
		}

		if right < left {
			return nil, "cep range right mush be greater than left"
		}
	}

	returnModels := make([]*RouteUpload, 0, len(updateEventList))
	if len(updateEventList) == 0 {
		returnModels = append(returnModels, tmpRouteUpload)
	} else {
		for _, singleUpdateEvent := range updateEventList {
			returnModels = append(returnModels, &RouteUpload{
				Row:                        tmpRouteUpload.Row,
				OriginLocationLevel:        tmpRouteUpload.OriginLocationLevel,
				OriginRegion:               tmpRouteUpload.OriginRegion,
				OriginState:                tmpRouteUpload.OriginState,
				OriginCity:                 tmpRouteUpload.OriginCity,
				OriginDistrict:             tmpRouteUpload.OriginDistrict,
				DestinationLocationLevel:   tmpRouteUpload.DestinationLocationLevel,
				DestinationRegion:          tmpRouteUpload.DestinationRegion,
				DestinationState:           tmpRouteUpload.DestinationState,
				DestinationCity:            tmpRouteUpload.DestinationCity,
				DestinationDistrict:        tmpRouteUpload.DestinationDistrict,
				DestinationCEPRangeInitial: tmpRouteUpload.DestinationCEPRangeInitial,
				DestinationCEPRangeFinal:   tmpRouteUpload.DestinationCEPRangeFinal,
				DestinationPostcode:        tmpRouteUpload.DestinationPostcode,
				MinDelta:                   tmpRouteUpload.MinDelta,
				MaxDelta:                   tmpRouteUpload.MaxDelta,
				UpdateEvent:                singleUpdateEvent,
			})
		}
	}
	return returnModels, ""
}

func ParseRouteFile(ctx utils.LCOSContext, fileUrl, region string, objectType uint8, isCB bool) ([]*RouteUpload, *lcos_error.LCOSError) {

	var result []*RouteUpload

	// filePath := "/Users/<USER>/Downloads/manual_manipulation_route_nobr.xlsx"
	// 根据传入的文件url解析
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return nil, err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_dest_postcode.xlsx")
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	titleMap := make(map[string]int)
	// 定义一个左右 range 的数组
	//cepRangeList := make([]CepOverLap, 0)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}

		// 这里处理并记录表头的顺序
		if lineNum == 1 {
			titleMap = handleFileTitleSequence(ctx, objectType, row)
			continue
		}

		// 跳过表头和空白行
		if serviceable_util.IsBlankRow(row) {
			continue
		}
		if lineNum > config.GetMutableConf(ctx).RouteMaxUploadSize {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, fmt.Sprintf("line num is %d the size of upload file should be not more than %d lines", lineNum, config.GetMutableConf(ctx).RouteMaxUploadSize))
		}
		routeModels, errMsg := transferRowToStruct(lineNum, row, isCB, region, objectType, titleMap)
		if len(errMsg) > 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, fmt.Sprintf("row=%d|error=[%s]", lineNum, errMsg))
		}
		result = append(result, routeModels...)
	}
	/*// 如果 destination level == CepRange 则校验 是否上传有重叠cep range
	if len(cepRangeList) > 1 {
		sort.SliceStable(cepRangeList, func(i, j int) bool {
			return cepRangeList[i].Left < cepRangeList[j].Right
		})
		var tmp int
		var tmpRowId int
		for idx := range cepRangeList {
			if idx == 0 {
				tmp = cepRangeList[idx].Right
				tmpRowId = cepRangeList[idx].RowId
				continue
			}
			if cepRangeList[idx].Left <= tmp {
				errMsg := fmt.Sprintf("cep range have over lap between row %d and row %d", tmpRowId, cepRangeList[idx].RowId)
				logger.LogError(errMsg)
				return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
			}
			tmp = cepRangeList[idx].Right
			tmpRowId = cepRangeList[idx].RowId
		}
	}
	*/
	if lcosErr := CheckIsDuplicate(result); lcosErr != nil {
		return nil, lcosErr
	}
	return result, nil
}

// 上传的表格文件中的表头，与代码中的字段之间的映射，忽略大小写
var routeCdtFileTileMapToCodeTitle = map[string]string{
	"origin location level":         "origin_location_level",
	"origin region":                 "origin_region",
	"origin state":                  "origin_state",
	"origin city":                   "origin_city",
	"origin district":               "origin_district",
	"destination location level":    "destination_location_level",
	"destination region":            "destination_region",
	"destination state":             "destination_state",
	"destination city":              "destination_city",
	"destination district":          "destination_district",
	"destination cep range initial": "destination_cep_initial",
	"destination cep range final":   "destination_cep_final",
	"destination postcode":          "destination_postcode",
	"cdt_event1":                    "update_event1",
	"cdt_event2":                    "update_event2",
	"cdt_event3":                    "update_event3",
	"edt_cdt_min_delta":             "min_delta",
	"edt_cdt_max_delta":             "max_delta",
}

var routeLeadFileTileMapToCodeTitle = map[string]string{
	"origin location level":      "origin_location_level",
	"origin_country":             "origin_region",
	"origin_state":               "origin_state",
	"origin_city":                "origin_city",
	"origin_district":            "origin_district",
	"destination location level": "destination_location_level",
	"destination_country":        "destination_region",
	"destination_state":          "destination_state",
	"destionation_city":          "destination_city", // 这个在旧模板是错误拼写的title
	"destination_city":           "destination_city", // 这个为了兼容旧模板的拼写错误
	"destination_district":       "destination_district",
	"destination_cep_initial":    "destination_cep_initial",
	"destination_cep_final":      "destination_cep_final",
	"destination_postcode":       "destination_postcode",
	"update_event1":              "update_event1",
	"update_event2":              "update_event2",
	"update_event3":              "update_event3",
	"update_event4":              "update_event4",
	"update_event5":              "update_event5",
	"update_event6":              "update_event6",
	"update_event7":              "update_event7",
	"update_event8":              "update_event8",
	"edd_leadtime_min_delta":     "min_delta",
	"edd_leadtime_max_delta":     "max_delta",
}

// 处理表头，识别出表格中每一列对应代码中的什么字段
func handleFileTitleSequence(ctx utils.LCOSContext, objectType uint8, titles []string) map[string]int {
	resultMap := make(map[string]int)
	for index, fileTitle := range titles {
		codeTitle := ""
		ok := false
		if objectType == edd_constant.CdtObject {
			codeTitle, ok = routeCdtFileTileMapToCodeTitle[strings.ToLower(fileTitle)]
		} else {
			codeTitle, ok = routeLeadFileTileMapToCodeTitle[strings.ToLower(fileTitle)]
		}

		if !ok {
			logger.CtxLogErrorf(ctx, "unrecognized file title[%s]|columns[%c]", fileTitle, index+'A')
			continue
		}

		resultMap[codeTitle] = index
	}

	return resultMap
}
