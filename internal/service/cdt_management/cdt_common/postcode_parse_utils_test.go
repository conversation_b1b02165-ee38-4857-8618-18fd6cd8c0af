package cdt_common

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"reflect"
	"testing"
)

func Test_parsePostcode(t *testing.T) {
	type args struct {
		filePath string
	}
	tests := []struct {
		name  string
		args  args
		want  []string
		want1 *lcos_error.LCOSError
	}{
		{
			name: "upload postcode",
			args: args{
				filePath: "/Users/<USER>/Desktop/Postcode.xlsx",
			},
			want:  nil,
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			_, got1 := ParsePostcode(ctx, tt.args.filePath)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("parsePostcode() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
