package cdt_common

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"os"
	"strings"
	"time"
)

func ParsePostcode(ctx context.Context, filePath string) ([]string, *lcos_error.LCOSError) {

	var postcodeList []string
	postcodeMap := map[string]bool{}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()
	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}

		// 跳过表头和空白行
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		// 将row[0] stands for postcode
		postcode := strings.TrimSpace(row[0])
		if postcode != "" {
			if _, ok := postcodeMap[postcode]; !ok {
				postcodeList = append(postcodeList, postcode)
				postcodeMap[postcode] = true
			} else {
				return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("postcode duplicated|postcode=%s", postcode))
			}
		}
	}
	return postcodeList, nil
}

// DownloadAndParsePostcode parse file for postcode
func DownloadAndParsePostcode(ctx utils.LCOSContext, fileUrl string) ([]string, *lcos_error.LCOSError) {
	// 根据传入的文件url解析
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return nil, err
	}
	return ParsePostcode(ctx, filePath)
}
