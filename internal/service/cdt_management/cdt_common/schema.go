package cdt_common

type CepRange struct {
	CepLeft  int
	CepRight int
}

type RouteUpload struct {
	Row                        int
	OriginLocationLevel        int8
	OriginRegion               string
	OriginState                string
	OriginCity                 string
	OriginDistrict             string
	DestinationLocationLevel   int8
	DestinationRegion          string
	DestinationState           string
	DestinationCity            string
	DestinationDistrict        string
	DestinationCEPRangeInitial string
	DestinationCEPRangeFinal   string
	DestinationPostcode        string
	MinDelta                   float64
	MaxDelta                   float64
	UpdateEvent                uint8
}

func IsContainsCepRange(cepRanges []*CepRange, target *CepRange) bool {
	for _, item := range cepRanges {
		if item.CepLeft == target.CepLeft && item.CepRight == target.CepRight {
			return true
		}
	}
	return false
}
