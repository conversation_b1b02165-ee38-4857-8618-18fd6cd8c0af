package cdt_overview

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_overview"
	auto_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	manual_manipulation_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	auto_update_rule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/channel_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcos_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	"sort"
	"strings"
)

type CdtRuleOverviewServiceInterface interface {
	ListRecordByParams(ctx utils.LCOSContext, request *cdt_overview.ListRecordRequest) ([]*cdt_overview.ListRecordResponse, *lcos_error.LCOSError)
}

type cdtRuleOverviewService struct {
	manualManipulationDao manual_manipulation_rule2.CDTManualManipulationRuleTabDAO
	manualUpdateDao       manual_update_rule.CDTManualUpdateRuleTabDAO
	laneManualUpdateDao   lane_manual_update_rule.LaneCdtManualUpdateRuleDAO
	autoUpdateDao         auto_rule2.CDTAutoUpdateRuleTabDAO
	cdtCalculationService auto_update_rule.CdtCalculationServiceInterface
}

func NewCdtRuleOverviewService(manualManipulationDao manual_manipulation_rule2.CDTManualManipulationRuleTabDAO, manualUpdateDao manual_update_rule.CDTManualUpdateRuleTabDAO, laneManualUpdateDao lane_manual_update_rule.LaneCdtManualUpdateRuleDAO, autoUpdateDao auto_rule2.CDTAutoUpdateRuleTabDAO, cdtCalculationService auto_update_rule.CdtCalculationServiceInterface) *cdtRuleOverviewService {
	return &cdtRuleOverviewService{
		manualManipulationDao: manualManipulationDao,
		manualUpdateDao:       manualUpdateDao,
		laneManualUpdateDao:   laneManualUpdateDao,
		autoUpdateDao:         autoUpdateDao,
		cdtCalculationService: cdtCalculationService,
	}
}

var _ CdtRuleOverviewServiceInterface = (*cdtRuleOverviewService)(nil)

func (c *cdtRuleOverviewService) getProductName(id string, query map[string]*channel_service.Channel) string {
	if _, exists := query[id]; exists {
		return query[id].ChannelName
	} else {
		return "invaild productid " + id
	}
}

func (c *cdtRuleOverviewService) updateRecordFromMap(recordMap map[string]map[uint8]*cdt_overview.ListRecordResponse, productId string, cdtType uint8, defaultRecord *cdt_overview.ListRecordResponse, updateFunc func(resp *cdt_overview.ListRecordResponse)) {
	productMap, ok := recordMap[productId]
	if !ok {
		productMap = make(map[uint8]*cdt_overview.ListRecordResponse, 2)
	}
	recordMap[productId] = productMap
	record, ok := productMap[cdtType]
	if !ok {
		record = defaultRecord
	}
	updateFunc(record)
	productMap[cdtType] = record
}

func (c *cdtRuleOverviewService) ListRecordByParams(ctx utils.LCOSContext, request *cdt_overview.ListRecordRequest) ([]*cdt_overview.ListRecordResponse, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	var returnList []*cdt_overview.ListRecordResponse
	// 获取当前的时间戳
	currentTimestamp := uint32(recorder.Now(ctx).Unix())
	// 按ProductId+ObjectType分组存放统计结果数据
	// productMap := make(map[string]*cdt_overview.ListRecordResponse)
	recordMap := make(map[string]map[uint8]*cdt_overview.ListRecordResponse) // SPLN-27376 区分cdt数据和leadtime数据
	channelMap, lcosErr := product_service.GetAllProducts(ctx, region)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 1. 统计自动更新规则
	var queryMap = make(map[string]interface{})
	if request.ProductID != nil {
		queryMap["product_id"] = *request.ProductID
	}
	queryMap["region"] = region
	queryMap["is_lm"] = 0 // SPLN-26162 cancel is_lm, set all to 0
	queryMap["is_site_line"] = constant.ENABLED
	models, lcosErr := c.autoUpdateDao.SearchAutoUpdateRule(ctx, queryMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, _model := range models {
		defaultResp := &cdt_overview.ListRecordResponse{
			ProductID:             _model.ProductID,
			ObjectType:            _model.CdtType,
			ProductName:           c.getProductName(_model.ProductID, channelMap),
			CBType:                _model.CBType,
			IsLM:                  0,
			CalculatingIds:        make([]uint64, 0),
			DraftIds:              make([]uint64, 0),
			UpcomingIds:           make([]uint64, 0),
			ActiveIds:             make([]uint64, 0),
			ManualManiActiveIds:   make([]uint64, 0),
			ManualManiUpcomingIds: make([]uint64, 0),
		}
		c.updateRecordFromMap(recordMap, _model.ProductID, _model.CdtType, defaultResp, func(resp *cdt_overview.ListRecordResponse) {
			// 状态统计
			if _model.StatusID == constant.Draft {
				if _model.CTime > resp.DraftTime {
					resp.DraftTime = _model.CTime
				}
				resp.DraftIds = append(resp.DraftIds, _model.ID)
			} else if _model.StatusID == constant.Upcoming || _model.EffectiveTime > currentTimestamp {
				resp.UpcomingIds = append(resp.UpcomingIds, _model.ID)
			} else if (_model.StatusID == constant.Upcoming || _model.StatusID == constant.Active) && (_model.EffectiveTime > 0 && _model.EffectiveTime <= currentTimestamp) && (_model.ExpirationTime == 0 || _model.ExpirationTime > currentTimestamp) {
				if _model.CTime > resp.ActiveTime {
					resp.ActiveTime = _model.CTime
				}
				resp.ActiveIds = append(resp.ActiveIds, _model.ID)
			} else if _model.StatusID == constant.Calculating {
				if _model.CTime > resp.CalcTime {
					resp.CalcTime = _model.CTime
				}
				resp.CalculatingIds = append(resp.CalculatingIds, _model.ID)
			}
		})
	}

	// 2. 统计手动修正规则
	var manuQueryMap = make(map[string]interface{})
	if request.ProductID != nil {
		manuQueryMap["product_id"] = *request.ProductID
	}
	manuQueryMap["region"] = region
	manuQueryMap["is_lm"] = 0
	manuQueryMap["is_site_line"] = constant.ENABLED
	manualManiModels, lcosErr := c.manualManipulationDao.SearchManualManipulationRuleRecordsGeneral(ctx, manuQueryMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, _model := range manualManiModels {
		defaultResp := &cdt_overview.ListRecordResponse{
			ProductID:             _model.ProductID,
			ObjectType:            _model.ObjectType,
			ProductName:           c.getProductName(_model.ProductID, channelMap),
			LineId:                "",
			CBType:                uint8(_model.CBType),
			CalculatingIds:        make([]uint64, 0),
			DraftIds:              make([]uint64, 0),
			UpcomingIds:           make([]uint64, 0),
			ActiveIds:             make([]uint64, 0),
			ManualManiActiveIds:   make([]uint64, 0),
			ManualManiUpcomingIds: make([]uint64, 0),
		}
		c.updateRecordFromMap(recordMap, _model.ProductID, _model.ObjectType, defaultResp, func(resp *cdt_overview.ListRecordResponse) {
			// 统计手动修正规则状态
			if _model.StatusID == constant.Upcoming && _model.EffectiveDate > currentTimestamp {
				resp.ManualManiUpcomingIds = append(resp.ManualManiUpcomingIds, _model.ID)
			}
			if (_model.StatusID == constant.Active || _model.StatusID == constant.Upcoming) && (_model.EffectiveDate > 0 && _model.EffectiveDate <= currentTimestamp) && (_model.ExpirationDate == 0 || _model.ExpirationDate > currentTimestamp) {
				resp.ManualManiActiveIds = append(resp.ManualManiActiveIds, _model.ID)
			}
		})
	}

	// 3. 统计手动上传数据
	manualUpdateCdt := make(map[string]bool)
	manualUpdateLeadtime := make(map[string]bool)
	// 3.1 统计Product维度手动上传数据
	// 3.1.1 统计Product维度手动上传Location数据
	locationData, lcosErr := c.manualUpdateDao.GetCdtLocationDataByParams(ctx, queryMap, "")
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, _model := range locationData {
		if _model.ObjectType == edd_constant.CdtObject {
			manualUpdateCdt[_model.ProductId] = true
		} else {
			manualUpdateLeadtime[_model.ProductId] = true
		}
	}
	// 3.1.2 统计Product维度手动上传PostCode数据
	zipcodeData, lcosErr := c.manualUpdateDao.GetCdtZipcodeDataByParams(ctx, queryMap, "")
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, _model := range zipcodeData {
		if _model.ObjectType == edd_constant.CdtObject {
			manualUpdateCdt[_model.ProductId] = true
		} else {
			manualUpdateLeadtime[_model.ProductId] = true
		}
	}
	// 3.1.3 统计Product维度手动上传CepRange数据
	postcodeData, lcosErr := c.manualUpdateDao.GetCdtPostcodeDataByParams(ctx, queryMap, "")
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, _model := range postcodeData {
		if _model.ObjectType == edd_constant.CdtObject {
			manualUpdateCdt[_model.ProductId] = true
		} else {
			manualUpdateLeadtime[_model.ProductId] = true
		}
	}
	// 3.1.4 更新对应record的ManualUpdate字段为true
	for productId := range manualUpdateCdt {
		defaultProductResp := &cdt_overview.ListRecordResponse{
			ProductID:             productId,
			ObjectType:            edd_constant.CdtObject,
			ProductName:           c.getProductName(productId, channelMap),
			CalculatingIds:        make([]uint64, 0),
			DraftIds:              make([]uint64, 0),
			UpcomingIds:           make([]uint64, 0),
			ActiveIds:             make([]uint64, 0),
			ManualManiActiveIds:   make([]uint64, 0),
			ManualManiUpcomingIds: make([]uint64, 0),
			ManualUpdate:          constant.ENABLED,
		}
		c.updateRecordFromMap(recordMap, productId, edd_constant.CdtObject, defaultProductResp, func(resp *cdt_overview.ListRecordResponse) {
			resp.ManualUpdate = constant.ENABLED
		})
	}
	// 3.2 统计Lane维度手动上传数据
	// 3.2.1 统计Lane维度手动上传Location数据
	var laneLocationData []*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab
	if request.ProductID != nil {
		laneLocationData, lcosErr = c.laneManualUpdateDao.GetLaneCdtLocationDataByProductId(ctx, region, *request.ProductID)
		if lcosErr != nil {
			return nil, lcosErr
		}
	} else {
		laneLocationData, lcosErr = c.laneManualUpdateDao.GetAllLaneCdtLocationData(ctx, region)
		if lcosErr != nil {
			return nil, lcosErr
		}
	}
	for _, _model := range laneLocationData {
		if _model.IsLM == 0 && _model.IsSiteLine == constant.ENABLED {
			manualUpdateLeadtime[_model.ProductId] = true
		}
	}
	// 3.2.2 统计Lane维度手动上传PostCode数据
	var lanePostCodeData []*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab
	if request.ProductID != nil {
		lanePostCodeData, lcosErr = c.laneManualUpdateDao.GetLaneCdtPostCodeDataByProductId(ctx, region, *request.ProductID)
		if lcosErr != nil {
			return nil, lcosErr
		}
	} else {
		lanePostCodeData, lcosErr = c.laneManualUpdateDao.GetAllLaneCdtPostCodeData(ctx, region)
		if lcosErr != nil {
			return nil, lcosErr
		}
	}
	for _, _model := range lanePostCodeData {
		if _model.IsLM == 0 && _model.IsSiteLine == constant.ENABLED {
			manualUpdateLeadtime[_model.ProductId] = true
		}
	}
	// 3.2.3 统计Lane维度手动上传CepRange数据
	var laneCepRangeData []*lane_manual_update_rule.LaneCdtManualUpdateCepRangeDataTab
	if request.ProductID != nil {
		laneCepRangeData, lcosErr = c.laneManualUpdateDao.GetLaneCdtCepRangeDataByProductId(ctx, region, *request.ProductID)
		if lcosErr != nil {
			return nil, lcosErr
		}
	} else {
		laneCepRangeData, lcosErr = c.laneManualUpdateDao.GetAllLaneCdtCepRangeData(ctx, region)
		if lcosErr != nil {
			return nil, lcosErr
		}
	}
	for _, _model := range laneCepRangeData {
		if _model.IsLM == 0 && _model.IsSiteLine == constant.ENABLED {
			manualUpdateLeadtime[_model.ProductId] = true
		}
	}
	// 3.2.4 更新对应record的ManualUpdate字段为true
	for productId := range manualUpdateLeadtime {
		defaultLaneResp := &cdt_overview.ListRecordResponse{
			ProductID:             productId,
			ObjectType:            edd_constant.LeadTimeObject,
			ProductName:           c.getProductName(productId, channelMap),
			CalculatingIds:        make([]uint64, 0),
			DraftIds:              make([]uint64, 0),
			UpcomingIds:           make([]uint64, 0),
			ActiveIds:             make([]uint64, 0),
			ManualManiActiveIds:   make([]uint64, 0),
			ManualManiUpcomingIds: make([]uint64, 0),
			ManualUpdate:          constant.ENABLED,
		}
		c.updateRecordFromMap(recordMap, productId, edd_constant.LeadTimeObject, defaultLaneResp, func(resp *cdt_overview.ListRecordResponse) {
			resp.ManualUpdate = constant.ENABLED
		})
	}

	// 4. 统计region维度的Product cdt数据
	lcosService := lcos_service.NewLCOSService(ctx, region)
	// 4.1 封装请求参数
	cdtReqs := make([]*cdt_calculation.CdtProductInfo, 0)
	for productId, productMap := range recordMap {
		// 只用product维度数据计算cdt
		resp, ok := productMap[edd_constant.CdtObject]
		if !ok {
			continue
		}
		// CDT只需要直接计算product维度的值
		productReq := &cdt_calculation.CdtProductInfo{
			QueryID:    productId,
			ProductID:  productId,
			IsCB:       resp.CBType,
			IsSiteLine: constant.ENABLED,
			Region:     region,
			SellerAddr: &cdt_calculation.AddressInfo{},
			BuyerAddr:  &cdt_calculation.AddressInfo{},
		}
		cdtReqs = append(cdtReqs, productReq)
	}
	// 4.2 请求LCOS服务，计算cdt数据
	cdtResult, err := lcosService.BatchGetCdtInfo(ctx, cdtReqs, edd_constant.CdtObject)
	if len(cdtResult) == 0 || err != nil {
		logger.CtxLogErrorf(ctx, "cdt overview calculate cdt error, cdt result length is zero or err is not nil|err=%v", err)
	}
	leadtimeResult, err := lcosService.BatchGetCdtInfo(ctx, cdtReqs, edd_constant.LeadTimeObject)
	if len(leadtimeResult) == 0 || err != nil {
		logger.CtxLogErrorf(ctx, "cdt overview calcualte leadtime error, leadtime result length is zero or err is not nil|err=%v", err)
	}
	for productId, productMap := range recordMap {
		if resp, ok := productMap[edd_constant.CdtObject]; ok {
			if cdt, ok := cdtResult[productId]; ok {
				resp.CdtMax = cdt.CdtMax
				resp.CdtMin = cdt.CdtMin
			}
			returnList = append(returnList, resp)
		}
		if resp, ok := productMap[edd_constant.LeadTimeObject]; ok {
			if leadtime, ok := leadtimeResult[productId]; ok {
				resp.LeadTimeMax = leadtime.CdtMax
				resp.LeadTimeMin = leadtime.CdtMin
			}
			returnList = append(returnList, resp)
		}
	}
	// 对数据进行排序
	draftList := make([]*cdt_overview.ListRecordResponse, 0)
	calcList := make([]*cdt_overview.ListRecordResponse, 0)
	activeList := make([]*cdt_overview.ListRecordResponse, 0)
	otherList := make([]*cdt_overview.ListRecordResponse, 0)
	for _, item := range returnList {
		if item.DraftTime > 0 {
			draftList = append(draftList, item)
		} else if item.CalcTime > 0 {
			calcList = append(calcList, item)
		} else if item.ActiveTime > 0 {
			activeList = append(activeList, item)
		} else {
			otherList = append(otherList, item)
		}
	}
	sort.SliceStable(draftList, func(i, j int) bool {
		return draftList[i].DraftTime > draftList[j].DraftTime
	})
	sort.SliceStable(calcList, func(i, j int) bool {
		return calcList[i].CalcTime > calcList[j].CalcTime
	})
	sort.SliceStable(activeList, func(i, j int) bool {
		return activeList[i].ActiveTime > activeList[j].ActiveTime
	})
	sort.SliceStable(otherList, func(i, j int) bool {
		return otherList[i].ProductID > otherList[j].ProductID
	})
	returnSortList := make([]*cdt_overview.ListRecordResponse, 0)
	returnSortList = append(returnSortList, draftList...)
	returnSortList = append(returnSortList, calcList...)
	returnSortList = append(returnSortList, activeList...)
	returnSortList = append(returnSortList, otherList...)
	return returnSortList, nil
}
