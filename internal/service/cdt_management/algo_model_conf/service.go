package algo_model_conf

import (
	"fmt"
	algo_model_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/algo_model_conf"
	Logger "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/algo_model_conf"
	"sort"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/algo_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
)

type AlgoModelConfServiceInterface interface {
	ListAlgoModelConf(ctx utils.LCOSContext, request *algo_model_conf2.ListAlgoModelConfRequest, modelType edd_constant.AlgoModelType) (*ListAlgoModelConfResponse, *lcos_error.LCOSError)
	ListAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.ListAlgoSimulationRequest, modelType edd_constant.AlgoModelType) (*ListEDDSimulationResponse, *lcos_error.LCOSError)
	ListRouteAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.ListAlgoSimulationRequest, modelType edd_constant.AlgoModelType) (*ListRouteSimulationResponse, *lcos_error.LCOSError)
	GetDetailAlgoModelConf(ctx utils.LCOSContext, request *algo_model_conf2.GetDetailAlgoModelConfRequest, modelType edd_constant.AlgoModelType) (*GetDetailAlgoModelConfResponse, *lcos_error.LCOSError)
	DeployAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.DeployAlgoSimulationRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError
	RevokeAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.RevokeAlgoSimulationRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError
	DeployRouteAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.DeployAlgoSimulationRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError
	RevokeRouteAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.RevokeAlgoSimulationRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError
	ToggleAlgoModelConf(ctx utils.LCOSContext, request *algo_model_conf2.ToggleAlgoModelConfRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError

	// SPLN-34309 获取推荐配置
	QueryAllMetadata(ctx utils.LCOSContext, modelType edd_constant.AlgoModelType) ([]*MetaData, *lcos_error.LCOSError)
	DeployAllMetadata(ctx utils.LCOSContext, request *algo_model_conf2.DeployAllMetaDataRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError
	ListAllRecommendMetadata(ctx utils.LCOSContext, request *algo_model_conf2.ListRecommendMetaDataRequest, modelType edd_constant.AlgoModelType) ([]*RecommendMetaData, *lcos_error.LCOSError)
	ReplaceRecommendMetadata(ctx utils.LCOSContext, request *algo_model_conf2.ReplaceRecommendMetaDataRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError
	ReplaceRouteRecommendMetadata(ctx utils.LCOSContext, request *algo_model_conf2.ReplaceRouteRecommendMetaDataRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError
	ClearRouteRecommendMetadata(ctx utils.LCOSContext, request *algo_model_conf2.ClearRecommendMetaDataRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError
	GetDetailMetaData(ctx utils.LCOSContext, request *algo_model_conf2.GetDetailAlgoModelConfRequest, modelType edd_constant.AlgoModelType) (*GetDetailAlgoModelConfResponse, *lcos_error.LCOSError)
}

type AlgoModelConfService struct {
	algoModelConfDao algo_model_conf.AlgoModelConfTabDAO
}

func NewAlgoModelConfService(algoModelConfDao algo_model_conf.AlgoModelConfTabDAO) *AlgoModelConfService {
	return &AlgoModelConfService{
		algoModelConfDao: algoModelConfDao,
	}
}

func (s *AlgoModelConfService) ListAlgoModelConf(ctx utils.LCOSContext, request *algo_model_conf2.ListAlgoModelConfRequest, modelType edd_constant.AlgoModelType) (*ListAlgoModelConfResponse, *lcos_error.LCOSError) {
	/*
		分页查询 Algo 模型配置列表
	*/
	confList, lcosErr := s.getAllAlgoModelConf(ctx, modelType, request.BusinessType)
	if lcosErr != nil {
		return nil, lcosErr
	}

	result := make([]*AlgoModelConf, 0, len(confList))
	for _, conf := range confList {
		if request.ProductId > 0 && conf.ProductId != request.ProductId {
			continue
		}
		if request.EntityType > 0 && conf.EntityType != request.EntityType {
			continue
		}
		if request.DeployStatus > 0 && conf.DeployStatus != request.DeployStatus {
			continue
		}
		if request.IsReserve != nil {
			if !conf.IsBothFlow() && *request.IsReserve != conf.IsReverseFlow() {
				continue
			}
		}
		result = append(result, conf)
	}
	total := uint32(len(result))
	resp := &ListAlgoModelConfResponse{
		Total:  total,
		PageNo: request.PageNo,
		Count:  request.Count,
		List:   make([]*AlgoModelConf, 0),
	}

	sort.Slice(result, func(i, j int) bool {
		// 有限按照 refresh time 逆序排序，兜底按照 productId 排序
		return result[i].RefreshTime > result[j].RefreshTime || result[i].ProductId > result[j].ProductId
	})

	// slice confList with pageno and count
	offset := (request.PageNo - 1) * request.Count
	if offset < total {
		end := offset + request.Count
		if end > total {
			end = total
		}
		resp.List = result[offset:end]
	}
	return resp, nil
}

func (s *AlgoModelConfService) GetDetailAlgoModelConf(ctx utils.LCOSContext, request *algo_model_conf2.GetDetailAlgoModelConfRequest, modelType edd_constant.AlgoModelType) (*GetDetailAlgoModelConfResponse, *lcos_error.LCOSError) {
	/*
		获取 EDD 模型详情

		1. 基本信息
		2. 开启状态
		3. 部署中的仿真任务列表
	*/
	eddModelConf, lcosErr := s.getAlgoModelConf(ctx, request.ProductId, modelType)
	if lcosErr != nil {
		return nil, lcosErr
	}
	allSimulations, lcosErr := s.getAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return nil, lcosErr
	}
	routeSimulations, lcosErr := s.getRouteAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return nil, lcosErr
	}
	allSimulations = append(allSimulations, routeSimulations...)

	// lcos 存储的 EDD 模型状态
	lcosAlgoModelConf, lcosErr := s.algoModelConfDao.GetAlgoModelConf(ctx, strconv.Itoa(request.ProductId), modelType)
	if lcosErr != nil && lcosErr.RetCode != lcos_error.RecordNotFoundErrorCode {
		return nil, lcosErr
	}
	enableStatus := constant.DISABLED
	if lcosAlgoModelConf != nil {
		enableStatus = lcosAlgoModelConf.EnableStatus
	}
	resp := &GetDetailAlgoModelConfResponse{
		ProductId:   request.ProductId,
		Enabled:     enableStatus,
		RefreshTime: eddModelConf.RefreshTime,
		DeployList:  make([]*AlgoSimulation, 0, len(allSimulations)),
	}
	for _, simulation := range allSimulations {
		// 返回正在部署和已经部署的仿真任务
		if simulation.IsUpcomingOrDeploy() {
			if !simulation.ByRouteGroup {
				resp.ModelVersion = simulation.ModelVersion
				resp.BusinessType = simulation.BusinessType
			}
			resp.DeployList = append(resp.DeployList, simulation)
		}
	}
	sort.Slice(resp.DeployList, func(i, j int) bool {
		a, b := resp.DeployList[i], resp.DeployList[i]
		if a.OriginZone != b.OriginZone {
			return a.OriginZone < b.OriginZone
		}
		if a.DestinationZone != b.DestinationZone {
			return a.DestinationZone < b.DestinationZone
		}
		return int64(a.DeployStatus) > int64(b.DeployStatus)
	})

	return resp, nil
}

func (s *AlgoModelConfService) checkUpdateAlgoModelConf(ctx utils.LCOSContext) *lcos_error.LCOSError {
	/*
		当地时间凌晨0点前后一分钟不允许修改配置（deploy、revoke、toggle）
	*/
	now := utils.GetTimestamp(ctx)
	midnightToday := utils.GetDateBeginTimestamp(ctx, ctx.GetCountry())
	midnightTomorrow := midnightToday + 24*3600
	oneMin := 60

	// 今天凌晨0点前后一分钟
	todayDiff := int(now) - int(midnightToday)
	// 次日凌晨0点前后一分钟
	tomorrowDiff := int(now) - int(midnightTomorrow)

	if (-oneMin < todayDiff && todayDiff < oneMin) || (-oneMin < tomorrowDiff && tomorrowDiff < oneMin) {
		return lcos_error.NewLCOSError(lcos_error.AlgoModelUpdateError, "Can not operate at between 23:59:00 and 00:01:00, please wait.")
	}
	return nil
}

func (s *AlgoModelConfService) DeployAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.DeployAlgoSimulationRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError {
	/*
		部署选择的仿真任务，启用该任务的参数作为 EDD 模型的特征参数控制

		1. 已经有 upcoming 任务，不允许部署
		2. 仅当该任务为 pending deploy 状态，允许部署
	*/
	lcosErr := s.checkUpdateAlgoModelConf(ctx)
	if lcosErr != nil {
		return lcosErr
	}
	allSimulations, lcosErr := s.getAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return lcosErr
	}
	var targetSimulation *AlgoSimulation
	for _, simulation := range allSimulations {
		if simulation.IsUpcoming() {
			return lcos_error.NewLCOSError(lcos_error.AlgoModelUpdateError, "Upcoming weight exists!")
		}
		if simulation.ConfigId == request.ConfigId {
			if simulation.DeployStatus != edd_constant.AlgoModelDeployStatusPendingDeploy {
				return lcos_error.NewLCOSError(lcos_error.AlgoModelUpdateError, "Only Pending Deploy weight can be deployed!")
			}
			targetSimulation = simulation
		}
	}
	if targetSimulation == nil {
		return lcos_error.NewLCOSError(lcos_error.RecordNotFoundErrorCode, fmt.Sprintf("Weight: %d not exists", request.ConfigId))
	}
	return algo_service.DeployAlgoSimulation(ctx, ctx.GetCountry(), request.ProductId, modelType, request.ConfigId, ctx.GetUserEmail())
}

func (s *AlgoModelConfService) RevokeAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.RevokeAlgoSimulationRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError {
	/*
		撤销已经在 upcoming 的仿真任务

		1. 必须已经有 Deploy 状态的任务；
		2. 只允许撤销 Upcoming 状态的任务
	*/
	lcosErr := s.checkUpdateAlgoModelConf(ctx)
	if lcosErr != nil {
		return lcosErr
	}
	allSimulations, lcosErr := s.getAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return lcosErr
	}
	var targetSimulation *AlgoSimulation
	hasDeploy := false
	for _, simulation := range allSimulations {
		if simulation.IsDeploy() {
			hasDeploy = true
		}
		if simulation.ConfigId == request.ConfigId {
			if !simulation.IsUpcoming() {
				return lcos_error.NewLCOSError(lcos_error.AlgoModelUpdateError, "Only Upcoming weight can be revoked!")
			}
			targetSimulation = simulation
		}
	}
	if targetSimulation == nil {
		return lcos_error.NewLCOSError(lcos_error.RecordNotFoundErrorCode, fmt.Sprintf("Weight: %d not exists", request.ConfigId))
	}
	if !hasDeploy {
		return lcos_error.NewLCOSError(lcos_error.AlgoModelUpdateError, "No Deploy weight, can not revoke")
	}
	return algo_service.RevokeAlgoSimulation(ctx, ctx.GetCountry(), request.ProductId, modelType, request.ConfigId, ctx.GetUserEmail())
}

func (s *AlgoModelConfService) DeployRouteAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.DeployAlgoSimulationRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError {
	// 1. 检查当前时间是否允许配置变更
	lcosErr := s.checkUpdateAlgoModelConf(ctx)
	if lcosErr != nil {
		return lcosErr
	}

	// 2. 检查划窗是否存在并且划窗状态是否为pending
	routeSimulationList, lcosErr := s.getRouteAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return lcosErr
	}
	var targetSimulation *AlgoSimulation
	for _, simulation := range routeSimulationList {
		if simulation.ConfigId == request.ConfigId {
			if simulation.DeployStatus != edd_constant.AlgoModelDeployStatusPendingDeploy {
				return lcos_error.NewLCOSError(lcos_error.AlgoModelUpdateError, "Only Pending Deploy weight can be deployed!")
			}
			targetSimulation = simulation
			break
		}
	}
	if targetSimulation == nil {
		return lcos_error.NewLCOSError(lcos_error.RecordNotFoundErrorCode, fmt.Sprintf("Weight: %d not exists", request.ConfigId))
	}

	// 3. 部署route维度划窗时必须已部署product维度的默认划窗
	productSimulationList, lcosErr := s.getAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return lcosErr
	}
	var defaultDeployed bool
	for _, simulation := range productSimulationList {
		if simulation.IsUpcomingOrDeploy() {
			defaultDeployed = true
			break
		}
	}
	if !defaultDeployed {
		return lcos_error.NewLCOSErrorf(lcos_error.AlgoModelUpdateError, "Product level default weight not deployed!")
	}

	// 4. 部署route划窗
	return algo_service.DeployRouteAlgoSimulation(ctx, ctx.GetCountry(), request.ProductId, modelType, request.ConfigId, ctx.GetUserEmail())
}

func (s *AlgoModelConfService) RevokeRouteAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.RevokeAlgoSimulationRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError {
	// 1. 检查当前时间是否允许配置变更
	lcosErr := s.checkUpdateAlgoModelConf(ctx)
	if lcosErr != nil {
		return lcosErr
	}

	// 2. 检查划窗是否存在
	routeSimulationList, lcosErr := s.getRouteAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return lcosErr
	}
	var targetSimulation *AlgoSimulation
	for _, simulation := range routeSimulationList {
		if simulation.ConfigId == request.ConfigId {
			if !simulation.IsUpcomingOrDeploy() {
				return lcos_error.NewLCOSError(lcos_error.AlgoModelUpdateError, "Only Upcoming/Deployed route level weight can be revoked!")
			}
			targetSimulation = simulation
			break
		}
	}
	if targetSimulation == nil {
		return lcos_error.NewLCOSError(lcos_error.RecordNotFoundErrorCode, fmt.Sprintf("Weight: %d not exists", request.ConfigId))
	}
	return algo_service.RevokeRouteAlgoSimulation(ctx, ctx.GetCountry(), request.ProductId, modelType, request.ConfigId, ctx.GetUserEmail())
}

func (s *AlgoModelConfService) ToggleAlgoModelConf(ctx utils.LCOSContext, request *algo_model_conf2.ToggleAlgoModelConfRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError {
	/*
		1. 所有状态允许关闭
		2. 如果有 deploy 的仿真任务，则允许开启

		// 刷新缓存？ TODO
	*/
	lcosErr := s.checkUpdateAlgoModelConf(ctx)
	if lcosErr != nil {
		return lcosErr
	}
	eddModel, lcosErr := s.getAlgoModelConf(ctx, request.ProductId, modelType)
	if lcosErr != nil {
		return lcosErr
	}

	var effectiveTime uint32 = 0
	if request.Enabled == constant.ENABLED {
		allSimulations, lcosErr := s.getAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
		if lcosErr != nil {
			return lcosErr
		}
		// 获取当前部署的仿真任务
		deployFlag := canSimulationBeDeploy(allSimulations)
		// 不存在已经部署或在部署中的仿真任务，不允许开启
		if !deployFlag {
			return lcos_error.NewLCOSError(lcos_error.AlgoModelUpdateError, "No Deploy weight exists!")
		}
		effectiveTime = utils.GetDateBeginTimestamp(ctx, ctx.GetCountry()) + 24*3600
	}

	// lcos 维护的 EDD Model 状态记录
	lcosEDDModelConf, lcosErr := s.algoModelConfDao.GetAlgoModelConf(ctx, strconv.Itoa(request.ProductId), modelType)
	if lcosErr != nil && lcosErr.RetCode != lcos_error.RecordNotFoundErrorCode {
		return lcosErr
	}
	if request.Enabled == constant.DISABLED {
		if lcosEDDModelConf == nil {
			return nil
		}
		// disable 需要通知 algo
		lcosErr = algo_service.DisableAlgoModelConf(ctx, ctx.GetCountry(), request.ProductId, modelType, ctx.GetUserEmail())
		if lcosErr != nil {
			return lcosErr
		}
	}

	if lcosEDDModelConf == nil {
		return s.algoModelConfDao.CreateAlgoModelConf(ctx, &algo_model_conf.AlgoModelConfigTab{
			ProductID:     strconv.Itoa(request.ProductId),
			IsCB:          eddModel.CBType,
			Region:        eddModel.Region,
			EnableStatus:  request.Enabled,
			Operator:      ctx.GetUserEmail(),
			EffectiveTime: effectiveTime,
			ModelType:     modelType,
		})
	} else {
		return s.algoModelConfDao.UpdateAlgoModelConf(ctx, strconv.Itoa(request.ProductId), modelType, map[string]interface{}{
			"operator":       ctx.GetUserEmail(),
			"enable_status":  request.Enabled,
			"effective_time": effectiveTime,
		})
	}
}

func (s *AlgoModelConfService) getAllAlgoModelConf(ctx utils.LCOSContext, modelType edd_constant.AlgoModelType, bizType edd_constant.BusinessType) ([]*AlgoModelConf, *lcos_error.LCOSError) {
	/*
		获取所有 EDD 模型配置

		1. 按照 product 列表进行返回，允许没有模型配置
	*/
	region := ctx.GetCountry()
	eddModelList, lcosErr := algo_service.ListAlgoModelConf(ctx, region, modelType, bizType)
	if lcosErr != nil {
		return nil, lcosErr
	}
	channelMap, lcosErr := product_service.GetAllProducts(ctx, region)
	if lcosErr != nil {
		return nil, lcosErr
	}

	confList := make([]*AlgoModelConf, 0, len(eddModelList))

	for _, eddModel := range eddModelList {
		product := channelMap[strconv.Itoa(eddModel.ProductId)]
		cbType := edd_constant.CBTypeLocal
		flowType := constant.BothProdFlowType
		if product != nil {
			cbType = product.CBType
			flowType = product.FlowType
		}
		confList = append(confList, &AlgoModelConf{
			Region:          region,
			ProductId:       eddModel.ProductId,
			CBType:          cbType,
			EntityType:      edd_constant.CBTypeToEntityTypeMap[cbType],
			DeployStatus:    eddModel.Status.ToStatus(),
			RefreshTime:     eddModel.RefreshTime,
			ModelVersion:    eddModel.ModelVersion,
			BusinessType:    eddModel.BizType.ToType(modelType),
			ProductFlowType: flowType,
			WithRouteGroup:  eddModel.WithRouteGroup,
		})
	}
	return confList, nil
}

func canSimulationBeDeploy(allSimulations []*AlgoSimulation) bool {
	// 获取当前部署的仿真任务
	var deployFlag bool
	for _, simulation := range allSimulations {
		if simulation.IsUpcomingOrDeploy() {
			deployFlag = true
			break
		}
	}
	return deployFlag
}

func filterSimulationByProduct(allSimulations []*AlgoSimulation, productID int) []*AlgoSimulation {
	var (
		returnedSimulations = make([]*AlgoSimulation, 0, len(allSimulations))
	)

	for _, simulation := range allSimulations {
		if simulation.ProductID == productID {
			returnedSimulations = append(returnedSimulations, simulation)
		}
	}
	return returnedSimulations
}

func (s *AlgoModelConfService) checkAllProductHaveUpcomingOrDeployWeight(ctx utils.LCOSContext, productIDList []int, modelType edd_constant.AlgoModelType, modelVersion string) *lcos_error.LCOSError {
	// 渠道可部署条件，需要存在【upcoming或deploy】状态的【渠道维度】划窗，只需要检查渠道维度划窗状态
	allSimulations, lcosErr := s.getAllAlgoSimulations(ctx, modelType, modelVersion)
	if lcosErr != nil {
		return lcosErr
	}

	for _, productID := range productIDList {
		productSimulations := filterSimulationByProduct(allSimulations, productID)
		deployFlag := canSimulationBeDeploy(productSimulations)
		if !deployFlag {
			return lcos_error.NewLCOSErrorf(lcos_error.AlgoModelUpdateError, "No Deploy weight exists for product:[%d]!", productID)
		}
	}
	return nil
}

// SPLN-34657
// 检查deploy all能否成功
func (s *AlgoModelConfService) checkCanDeployAll(ctx utils.LCOSContext, productIDList []int, modelType edd_constant.AlgoModelType, modelVersion string, businessType edd_constant.BusinessType) (map[int]*AlgoModelConf, *lcos_error.LCOSError) {
	var (
		algoProductMap = make(map[int]*AlgoModelConf, len(productIDList))
	)

	// 1. 检查当前时间是否可以更新模型
	lcosErr := s.checkUpdateAlgoModelConf(ctx)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 2. 检查是否存在模型配置，如果有渠道不存在模型配置，直接返回报错
	confList, lcosErr := s.getAllAlgoModelConf(ctx, modelType, businessType)
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, conf := range confList {
		if conf.ModelVersion == modelVersion {
			algoProductMap[conf.ProductId] = conf
		}
	}
	for _, productId := range productIDList {
		if _, ok := algoProductMap[productId]; !ok {
			return nil, lcos_error.NewLCOSError(lcos_error.RecordNotFoundErrorCode, fmt.Sprintf("productId: %d model config not exists", productId))
		}
	}

	// 3. 检查是否每个渠道都存在部署或在部署中的仿真任务
	return algoProductMap, s.checkAllProductHaveUpcomingOrDeployWeight(ctx, productIDList, modelType, modelVersion)
}

func (s *AlgoModelConfService) getAlgoModelConf(ctx utils.LCOSContext, productId int, modelType edd_constant.AlgoModelType) (*AlgoModelConf, *lcos_error.LCOSError) {
	/*
		查询指定 productId 对应的 EDD 模型配置
	*/
	confList, lcosErr := s.getAllAlgoModelConf(ctx, modelType, 0)
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, conf := range confList {
		if conf.ProductId == productId {
			return conf, nil
		}
	}
	return nil, lcos_error.NewLCOSError(lcos_error.RecordNotFoundErrorCode, fmt.Sprintf("productId: %d model config not exists", productId))
}

// getAlgoSimulationByProductID 获取某个渠道的渠道维度划窗
func (s *AlgoModelConfService) getAlgoSimulationByProductID(ctx utils.LCOSContext, productId int, modelType edd_constant.AlgoModelType, modelVersion string) ([]*AlgoSimulation, *lcos_error.LCOSError) {
	allSimulations, lcosErr := s.getAllAlgoSimulations(ctx, modelType, modelVersion)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return filterSimulationByProduct(allSimulations, productId), nil
}

// getRouteAlgoSimulationByProductID 获取某个渠道的route维度划窗
func (s *AlgoModelConfService) getRouteAlgoSimulationByProductID(ctx utils.LCOSContext, productId int, modelType edd_constant.AlgoModelType, modelVersion string) ([]*AlgoSimulation, *lcos_error.LCOSError) {
	allSimulations, lcosErr := s.getAllRouteAlgoSimulations(ctx, modelType, modelVersion)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return filterSimulationByProduct(allSimulations, productId), nil
}

// getAllAlgoSimulations 获取所有渠道维度划窗
func (s *AlgoModelConfService) getAllAlgoSimulations(ctx utils.LCOSContext, modelType edd_constant.AlgoModelType, modelVersion string) ([]*AlgoSimulation, *lcos_error.LCOSError) {

	region := ctx.GetCountry()
	eddSimulationList, lcosErr := algo_service.ListAlgoSimulation(ctx, region, modelVersion, modelType)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(eddSimulationList) == 0 {
		return make([]*AlgoSimulation, 0), nil
	}

	simulationList := make([]*AlgoSimulation, 0, len(eddSimulationList))
	for _, simulation := range eddSimulationList {
		tmp := &AlgoSimulation{
			DeployStatus:      simulation.Status.ToStatus(),
			ConfigId:          simulation.ConfigId,
			Operator:          simulation.Operator,
			OperateTime:       simulation.OperateTime,
			simulationResult:  simulation.SimulationResult,
			ModelVersion:      simulation.ModelVersion,
			SlideWinID:        simulation.SlideWinID,
			SlideWinParams:    simulation.SlideWinParams,
			SimulationVersion: simulation.SimulationVersion,
			SlideWinFlag:      simulation.SlideWinFlag,
			ProductID:         simulation.ProductId,
			BusinessType:      simulation.BizType.ToType(modelType),
			RefreshTime:       simulation.RefreshTime,
		}
		tmp.fillMetrics(modelType)
		simulationList = append(simulationList, tmp)
	}
	return simulationList, nil
}

// getAllRouteAlgoSimulations 获取所有route维度划窗
func (s *AlgoModelConfService) getAllRouteAlgoSimulations(ctx utils.LCOSContext, modelType edd_constant.AlgoModelType, modelVersion string) ([]*AlgoSimulation, *lcos_error.LCOSError) {
	region := ctx.GetCountry()
	eddSimulationList, lcosErr := algo_service.ListRouteAlgoSimulation(ctx, region, modelVersion, modelType)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(eddSimulationList) == 0 {
		return make([]*AlgoSimulation, 0), nil
	}

	simulationList := make([]*AlgoSimulation, 0, len(eddSimulationList))
	for _, simulation := range eddSimulationList {
		tmp := &AlgoSimulation{
			DeployStatus:      simulation.Status.ToStatus(),
			ConfigId:          simulation.ConfigId,
			Operator:          simulation.Operator,
			OperateTime:       simulation.OperateTime,
			simulationResult:  simulation.SimulationResult,
			ModelVersion:      simulation.ModelVersion,
			SlideWinID:        simulation.SlideWinID,
			SlideWinParams:    simulation.SlideWinParams,
			SimulationVersion: simulation.SimulationVersion,
			SlideWinFlag:      simulation.SlideWinFlag,
			ProductID:         simulation.ProductId,
			BusinessType:      simulation.BizType.ToType(modelType),
			RefreshTime:       simulation.RefreshTime,
			ByRouteGroup:      simulation.ByRouteGroup,
			OriginZone:        simulation.OriginZone,
			DestinationZone:   simulation.DestinationZone,
		}
		tmp.fillMetrics(modelType)
		simulationList = append(simulationList, tmp)
	}
	return simulationList, nil
}

func postHandleAlgoSimulation(allSimulations []*AlgoSimulation, filterList []*algo_model_conf2.SimulationFilterOp, sortList []string) []*AlgoSimulation {
	simulationList := make([]*AlgoSimulation, 0, len(allSimulations))
	for _, simulation := range allSimulations {
		matched := true
		for _, filterOp := range filterList {
			switch filterOp.Op {
			case "=":
				matched = simulation.simulationResult[filterOp.Field] == filterOp.Value
			case ">":
				matched = simulation.simulationResult[filterOp.Field] > filterOp.Value
			case ">=":
				matched = simulation.simulationResult[filterOp.Field] >= filterOp.Value
			case "<":
				matched = simulation.simulationResult[filterOp.Field] < filterOp.Value
			case "<=":
				matched = simulation.simulationResult[filterOp.Field] <= filterOp.Value
			case "!=":
				matched = simulation.simulationResult[filterOp.Field] != filterOp.Value
			}
			if !matched {
				break
			}
		}
		if !matched {
			continue
		}
		simulationList = append(simulationList, simulation)
	}
	sort.Slice(simulationList, func(i, j int) bool {
		for _, sortField := range sortList {
			// precision、accuracy 默认逆序，range 默认升序
			if simulationList[i].simulationResult[sortField] != simulationList[j].simulationResult[sortField] {
				if sortField == edd_constant.EDDSimulationNameInitialRange ||
					sortField == edd_constant.EDDSimulationNameFinalRange ||
					sortField == edd_constant.EDTSimulationNameRange ||
					sortField == edd_constant.EDDSimulationNameInitialLateRate ||
					sortField == edd_constant.EDDSimulationNameFinalLateRate ||
					sortField == edd_constant.EDTSimulationNameLateRate {
					return simulationList[i].simulationResult[sortField] < simulationList[j].simulationResult[sortField]
				}
				return simulationList[i].simulationResult[sortField] > simulationList[j].simulationResult[sortField]
			}
		}
		// 默认按照 Status 逆序，其次按照 configId
		return simulationList[i].DeployStatus > simulationList[j].DeployStatus || simulationList[i].ConfigId > simulationList[j].ConfigId
	})
	return simulationList
}

func (s *AlgoModelConfService) ListAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.ListAlgoSimulationRequest, modelType edd_constant.AlgoModelType) (*ListEDDSimulationResponse, *lcos_error.LCOSError) {
	/*
		分页&过滤仿真任务

		1. 支持 filter 控制
		2. 支持 sort by 排序
	*/
	allSimulations, lcosErr := s.getAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return nil, lcosErr
	}
	simulationList := postHandleAlgoSimulation(allSimulations, request.FilterList, request.SortList)
	// slice simulationList with pageno and count
	total := uint32(len(simulationList))
	resp := &ListEDDSimulationResponse{
		Total:  total,
		PageNo: request.PageNo,
		Count:  request.Count,
		List:   make([]*AlgoSimulation, 0),
	}

	// slice confList with pageno and count
	offset := (request.PageNo - 1) * request.Count
	if offset < total {
		end := offset + request.Count
		if end > total {
			end = total
		}
		resp.List = simulationList[offset:end]
	}
	return resp, nil
}

func (s *AlgoModelConfService) ListRouteAlgoSimulation(ctx utils.LCOSContext, request *algo_model_conf2.ListAlgoSimulationRequest, modelType edd_constant.AlgoModelType) (*ListRouteSimulationResponse, *lcos_error.LCOSError) {
	allSimulations, lcosErr := s.getRouteAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return nil, lcosErr
	}
	simulationList := postHandleAlgoSimulation(allSimulations, request.FilterList, request.SortList)

	// 将划窗按照route进行分组
	var (
		routeSimulationList []*RouteAlgoSimulation
		routeMap            = make(map[string]*RouteAlgoSimulation)
	)
	for _, simulation := range simulationList {
		routeKey := utils.GenKey(":", simulation.OriginZone, simulation.DestinationZone)
		routeSimulation, ok := routeMap[routeKey]
		if !ok {
			// 新的route分组
			routeSimulation = &RouteAlgoSimulation{
				OriginZone:      simulation.OriginZone,
				DestinationZone: simulation.DestinationZone,
			}
			routeSimulationList = append(routeSimulationList, routeSimulation)
		}
		routeSimulation.Children = append(routeSimulation.Children, simulation)
		routeMap[routeKey] = routeSimulation
	}

	// 按照origin zone和dest zone的字典序进行排序，保证route划窗顺序的稳定性以进行分页
	sort.SliceStable(routeSimulationList, func(i, j int) bool {
		if routeSimulationList[i].OriginZone == routeSimulationList[j].OriginZone {
			return routeSimulationList[i].DestinationZone < routeSimulationList[j].DestinationZone
		}
		return routeSimulationList[i].OriginZone < routeSimulationList[j].OriginZone
	})

	// 分页返回
	total := uint32(len(routeSimulationList))
	resp := &ListRouteSimulationResponse{
		Total:  total,
		PageNo: request.PageNo,
		Count:  request.Count,
		List:   make([]*RouteAlgoSimulation, 0),
	}

	// slice confList with pageno and count
	offset := (request.PageNo - 1) * request.Count
	if offset < total {
		end := offset + request.Count
		if end > total {
			end = total
		}
		resp.List = routeSimulationList[offset:end]
	}
	return resp, nil
}

func (s *AlgoModelConfService) QueryAllMetadata(ctx utils.LCOSContext, modelType edd_constant.AlgoModelType) ([]*MetaData, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	metaDataList, lcosErr := algo_service.QueryAllMetaData(ctx, region, modelType)
	if lcosErr != nil {
		return nil, lcosErr
	}

	metaDataResult := make([]*MetaData, 0, len(metaDataList))

	for _, metaData := range metaDataList {
		tmp := &MetaData{
			BusinessType:         metaData.BizType.ToType(modelType),
			WeightType:           metaData.WeightType,
			TrainSetDate:         metaData.TrainSetDate,
			TestSetDate:          metaData.TestSetDate,
			Status:               metaData.Status,
			ModelVersion:         metaData.ModelVersion,
			simulationResult:     metaData.SimulationResult,
			RefreshTime:          metaData.RefreshTime,
			IsAllProductDeployed: metaData.IsAllProductDeployed,
		}
		tmp.fillMetrics(modelType)
		metaDataResult = append(metaDataResult, tmp)
	}

	// 按照weight type排序，小的在前面
	sort.SliceStable(metaDataResult, func(i, j int) bool {
		return metaDataResult[i].WeightType < metaDataResult[j].WeightType
	})

	return metaDataResult, nil
}

func (s *AlgoModelConfService) DeployAllMetadata(ctx utils.LCOSContext, request *algo_model_conf2.DeployAllMetaDataRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	operator := ctx.GetUserEmail()
	productList, lcosErr := algo_service.DeployAllMetaData(ctx, region, modelType, request.NewModelVersion, request.OldModelVersion, request.WeightType, request.BusinessType, operator)
	if lcosErr != nil {
		return lcosErr
	}

	// SPLN-34657
	// 补充渠道层的开关。和单个的逻辑相比，只有enable的情况，不会有disable的情况
	var (
		productStringList           = make([]string, 0, len(productList))
		algoModelMap                = make(map[string]*algo_model_conf.AlgoModelConfigTab)
		updatedProductList          = make([]int, 0, len(productList))
		missingProductList          = make([]int, 0, len(productList))
		addedProductList            = make([]int, 0, len(productList))
		alreadyEffectiveProductList = make([]int, 0, len(productList))
		addedProductModelList       = make([]*algo_model_conf.AlgoModelConfigTab, 0, len(productList))
		effectiveTime               = utils.GetDateBeginTimestamp(ctx, ctx.GetCountry()) + 24*3600
	)
	for _, productId := range productList {
		productStringList = append(productStringList, strconv.Itoa(productId))
	}

	// 1. 先检查是否所有渠道都可以都可以部署
	confMap, deployErr := s.checkCanDeployAll(ctx, productList, modelType, request.NewModelVersion, request.BusinessType)
	if deployErr != nil {
		return deployErr
	}

	// 2. 先检查是否有开关
	algoModelList, modelErr := s.algoModelConfDao.SearchAlgoModelConf(ctx, map[string]interface{}{"product_id in": productStringList, "region": region, "model_type": modelType})
	if modelErr != nil {
		return modelErr
	}
	for _, algoModel := range algoModelList {
		algoModelMap[algoModel.ProductID] = algoModel
	}

	// 3. 比对渠道层开关，相应地更新开关
	for _, productIdInt := range productList {
		if item, ok := algoModelMap[strconv.Itoa(productIdInt)]; ok {
			// 对于已经生效的开关，跳过
			if item.IsEffective(ctx) {
				alreadyEffectiveProductList = append(alreadyEffectiveProductList, productIdInt)
				continue
			}
			// 对于现在还未生效的模型，更新状态和生效时间
			updatedProductList = append(updatedProductList, productIdInt)
		} else {
			// 对于没创建的模型，创建渠道的模型开关
			if conf, ok1 := confMap[productIdInt]; !ok1 {
				missingProductList = append(missingProductList, productIdInt)
			} else {
				addedProductList = append(addedProductList, productIdInt)
				addedProductModelList = append(addedProductModelList, &algo_model_conf.AlgoModelConfigTab{
					ProductID:     strconv.Itoa(productIdInt),
					IsCB:          conf.CBType,
					Region:        region,
					EnableStatus:  1,
					EffectiveTime: effectiveTime,
					Operator:      operator,
					ModelType:     modelType,
				})
			}
		}
	}

	// 4. 开启事务，写入开关数据
	fc := func() *lcos_error.LCOSError {
		// 先更新数据
		for _, item := range updatedProductList {
			if updateErr := s.algoModelConfDao.UpdateAlgoModelConf(ctx, strconv.Itoa(item), modelType, map[string]interface{}{
				"operator":       ctx.GetUserEmail(),
				"enable_status":  constant.ENABLED,
				"effective_time": effectiveTime,
			}); updateErr != nil {
				return updateErr
			}
		}

		// 新增数据
		if len(addedProductModelList) > 0 {
			if insertErr := s.algoModelConfDao.BatchCreateAlgoModelConf(ctx, addedProductModelList); insertErr != nil {
				return insertErr
			}
		}
		return nil
	}

	dbErr := ctx.Transaction(fc)
	if dbErr != nil {
		return dbErr
	}

	// 5. 打印日志
	Logger.CtxLogInfof(ctx, "successfully deploy all, added:[%v], updated:[%v], already effective:[%v], missing:[%v]", addedProductList, updatedProductList, alreadyEffectiveProductList, missingProductList)
	return nil
}

// ListAllRecommendMetadata 渠道推荐划窗列表页只展示渠道维度划窗，不需要查询route维度
func (s *AlgoModelConfService) ListAllRecommendMetadata(ctx utils.LCOSContext, request *algo_model_conf2.ListRecommendMetaDataRequest, modelType edd_constant.AlgoModelType) ([]*RecommendMetaData, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	metaDataList, lcosErr := algo_service.ListAlgoSimulation(ctx, region, request.ModelVersion, modelType)
	if lcosErr != nil {
		return nil, lcosErr
	}
	recommendMetaData := make([]*RecommendMetaData, 0, len(metaDataList))
	for _, metaData := range metaDataList {
		if !metaData.IsRecommend() {
			continue
		}
		// 过滤渠道
		if len(request.ProductIdList) > 0 && !utils.ContainsInt(request.ProductIdList, metaData.ProductId) {
			continue
		}
		// 过滤business type
		if metaData.BizType.ToType(modelType) != 0 && request.BusinessType != 0 && metaData.BizType.ToType(modelType) != request.BusinessType {
			continue
		}
		tmp := &RecommendMetaData{
			simulationResult: metaData.SimulationResult,
			ProductId:        metaData.ProductId,
			ConfigId:         metaData.ConfigId,
		}
		tmp.fillMetrics(modelType)
		recommendMetaData = append(recommendMetaData, tmp)
	}

	return recommendMetaData, nil
}

func (s *AlgoModelConfService) ReplaceRecommendMetadata(ctx utils.LCOSContext, request *algo_model_conf2.ReplaceRecommendMetaDataRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	return algo_service.ReplaceRecommendModel(ctx, region, request.OldConfigId, request.NewConfigId)
}

func (s *AlgoModelConfService) ReplaceRouteRecommendMetadata(ctx utils.LCOSContext, request *algo_model_conf2.ReplaceRouteRecommendMetaDataRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	return algo_service.ReplaceRouteRecommendModel(ctx, region, request.OldConfigId, request.NewConfigId)
}

func (s *AlgoModelConfService) ClearRouteRecommendMetadata(ctx utils.LCOSContext, request *algo_model_conf2.ClearRecommendMetaDataRequest, modelType edd_constant.AlgoModelType) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	return algo_service.ReplaceRouteRecommendModel(ctx, region, request.ConfigId, 0)
}

func (s *AlgoModelConfService) GetDetailMetaData(ctx utils.LCOSContext, request *algo_model_conf2.GetDetailAlgoModelConfRequest, modelType edd_constant.AlgoModelType) (*GetDetailAlgoModelConfResponse, *lcos_error.LCOSError) {
	/*
		获取 Meta 模型详情
		和 GetDetailAlgoModelConf 整体逻辑相似，不同的是使用slide win flag来决定是否为当前使用中的推荐配置

	*/
	allSimulations, lcosErr := s.getAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return nil, lcosErr
	}
	routeSimulations, lcosErr := s.getRouteAlgoSimulationByProductID(ctx, request.ProductId, modelType, request.ModelVersion)
	if lcosErr != nil {
		return nil, lcosErr
	}
	allSimulations = append(allSimulations, routeSimulations...)

	resp := &GetDetailAlgoModelConfResponse{
		ProductId:  request.ProductId,
		DeployList: make([]*AlgoSimulation, 0, len(allSimulations)),
	}
	for _, simulation := range allSimulations {
		// 返回正在部署和已经部署的仿真任务
		if simulation.IsCurrentWeightOnMainPage() {
			resp.DeployList = append(resp.DeployList, simulation)
			if !simulation.ByRouteGroup {
				resp.RefreshTime = uint64(simulation.RefreshTime)
				resp.BusinessType = simulation.BusinessType
				resp.ModelVersion = simulation.ModelVersion
				resp.Weight = simulation.GetWeight()
			}
		}
	}
	sort.Slice(resp.DeployList, func(i, j int) bool {
		a, b := resp.DeployList[i], resp.DeployList[j]
		if a.OriginZone != b.OriginZone {
			return a.OriginZone < b.OriginZone
		}
		if a.DestinationZone != b.DestinationZone {
			return a.DestinationZone < b.DestinationZone
		}
		return int64(a.DeployStatus) > int64(b.DeployStatus)
	})
	return resp, nil
}

var _ AlgoModelConfServiceInterface = (*AlgoModelConfService)(nil)
