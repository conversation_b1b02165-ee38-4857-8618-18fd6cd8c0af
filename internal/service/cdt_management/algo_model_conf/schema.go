package algo_model_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
)

type AlgoModelConf struct {
	ProductId    int                          `json:"product_id"`
	EntityType   uint8                        `json:"entity_type"`
	CBType       uint8                        `json:"cb_type"`
	DeployStatus edd_constant.AlgoModelStatus `json:"deploy_status"`
	RefreshTime  uint64                       `json:"refresh_time"`
	Region       string                       `json:"region"`

	ModelVersion string                    `json:"model_version"`
	BusinessType edd_constant.BusinessType `json:"business_type"`

	ProductFlowType int  `json:"product_flow_type"`
	WithRouteGroup  bool `json:"with_route_group"`
}

func (a *AlgoModelConf) IsForwardFlow() bool {
	return a.ProductFlowType == constant.ForwardProdFlowType
}

func (a *AlgoModelConf) IsReverseFlow() bool {
	return a.ProductFlowType == constant.ReverseProdFlowType
}

func (a *AlgoModelConf) IsBothFlow() bool {
	return a.ProductFlowType == constant.BothProdFlowType
}

type ListAlgoModelConfResponse struct {
	List   []*AlgoModelConf `json:"list"`
	Total  uint32           `json:"total"`
	PageNo uint32           `json:"pageno"`
	Count  uint32           `json:"count"`
}

type GetDetailAlgoModelConfResponse struct {
	ProductId   int               `json:"product_id"`
	Enabled     uint8             `json:"enabled"`
	RefreshTime uint64            `json:"refresh_time"`
	DeployList  []*AlgoSimulation `json:"deploy_list"`

	BusinessType edd_constant.BusinessType `json:"business_type"`
	ModelVersion string                    `json:"model_version"`
	Weight       float32                   `json:"weight"`
}

type EDDMetric struct {
	InitialAccuracy float32 `json:"initial_accuracy"`
	InitialRange    float32 `json:"initial_range"`
	InitialLateRate float32 `json:"initial_late_rate"`
	FinalAccuracy   float32 `json:"final_accuracy"`
	FinalRange      float32 `json:"final_range"`
	FinalLateRate   float32 `json:"final_late_rate"`
}

type EDTMetric struct {
	Accuracy float32 `json:"accuracy"`
	Range    float32 `json:"range"`
	LateRate float32 `json:"late_rate"`
	MinAvg   float32 `json:"min_avg"`
	MaxAvg   float32 `json:"max_avg"`
}

type CommonMetrics struct { // 因为json字段相同的key存在的话，不会序列化这个字段，因此将EDT/EDT重名的指标放在这里
	Weight float32 `json:"weight"` // 单量占比
}

type AlgoSimulation struct {
	DeployStatus edd_constant.AlgoModelStatus `json:"deploy_status"`
	ConfigId     uint64                       `json:"config_id"`
	Operator     string                       `json:"operator"`
	OperateTime  int64                        `json:"operate_time"`

	*EDDMetric
	*EDTMetric
	*CommonMetrics
	simulationResult map[string]float32 // 用于方便排序取值

	ModelVersion      string                        `json:"model_version"`
	SlideWinID        uint64                        `json:"slide_win_id"`
	SlideWinParams    string                        `json:"slide_win_params"`
	SimulationVersion string                        `json:"simulation_version"`
	SlideWinFlag      edd_constant.SlideWinFlagType `json:"slide_win_flag"`
	ProductID         int                           `json:"product_id"`
	BusinessType      edd_constant.BusinessType     `json:"business_type"`
	RefreshTime       int64                         `json:"refresh_time"`

	ByRouteGroup    bool   `json:"by_route_group"`
	OriginZone      string `json:"originZone"`
	DestinationZone string `json:"destinationZone"`
}

func (e *AlgoSimulation) fillMetrics(modelType edd_constant.AlgoModelType) {
	e.simulationResult = handleSimulationResult(e.simulationResult)
	e.CommonMetrics = fillCommonMetrics(e.simulationResult)
	if modelType == edd_constant.EDDModelType {
		e.EDDMetric = fillEDDMetrics(e.simulationResult)
	} else if modelType == edd_constant.EDTModelType {
		e.EDTMetric = fillEDTMetrics(e.simulationResult)
	}
}

func (e *AlgoSimulation) IsCurrentWeightOnMainPage() bool {
	return e.SlideWinFlag == edd_constant.SlideWinFlagRecommend || e.SlideWinFlag == edd_constant.SlideWinFlagInheritedAndRecommend
}

func (e *AlgoSimulation) IsUpcomingOrDeploy() bool {
	return e.DeployStatus == edd_constant.AlgoModelDeployStatusDeploy || e.DeployStatus == edd_constant.AlgoModelDeployStatusUpcoming
}

func (e *AlgoSimulation) IsUpcoming() bool {
	return e.DeployStatus == edd_constant.AlgoModelDeployStatusUpcoming
}

func (e *AlgoSimulation) IsDeploy() bool {
	return e.DeployStatus == edd_constant.AlgoModelDeployStatusDeploy
}

func (e *AlgoSimulation) IsPendingDeploy() bool {
	return e.DeployStatus == edd_constant.AlgoModelDeployStatusPendingDeploy
}

func (e *AlgoSimulation) GetWeight() float32 {
	if e != nil && e.CommonMetrics != nil {
		return e.CommonMetrics.Weight
	}
	return 0
}

type RouteAlgoSimulation struct {
	OriginZone      string            `json:"origin_zone"`
	DestinationZone string            `json:"destination_zone"`
	Children        []*AlgoSimulation `json:"children"`
}

type EDDSimulationFilterOp struct {
	Field string  `json:"field"`
	Op    string  `json:"op"`
	Value float32 `json:"value"`
}

type ListEDDSimulationResponse struct {
	List   []*AlgoSimulation `json:"list"`
	Total  uint32            `json:"total"`
	PageNo uint32            `json:"pageno"`
	Count  uint32            `json:"count"`
}

type ListRouteSimulationResponse struct {
	List   []*RouteAlgoSimulation `json:"list"`
	Total  uint32                 `json:"total"`
	PageNo uint32                 `json:"pageno"`
	Count  uint32                 `json:"count"`
}

type MetaData struct {
	BusinessType edd_constant.BusinessType    `json:"business_type"`
	WeightType   edd_constant.WeightType      `json:"weight_type"`
	TrainSetDate string                       `json:"train_set_date"`
	TestSetDate  string                       `json:"test_set_date"`
	Status       edd_constant.AlgoModelStatus `json:"status"`
	ModelVersion string                       `json:"model_version"`
	*EDDMetric
	*EDTMetric
	*CommonMetrics
	simulationResult     map[string]float32 // 用于方便排序取值
	RefreshTime          uint64             `json:"refresh_time"`
	IsAllProductDeployed int                `json:"is_all_product_deployed"`
}

func (e *MetaData) fillMetrics(modelType edd_constant.AlgoModelType) {
	e.simulationResult = handleSimulationResult(e.simulationResult)
	e.CommonMetrics = fillCommonMetrics(e.simulationResult)
	if modelType == edd_constant.EDDModelType {
		e.EDDMetric = fillEDDMetrics(e.simulationResult)
	} else if modelType == edd_constant.EDTModelType {
		e.EDTMetric = fillEDTMetrics(e.simulationResult)
	}
}

type RecommendMetaData struct {
	ConfigId  uint64 `json:"config_id"`
	ProductId int    `json:"product_id"`
	*EDDMetric
	*EDTMetric
	*CommonMetrics
	simulationResult map[string]float32 // 用于方便排序取值
}

func (e *RecommendMetaData) fillMetrics(modelType edd_constant.AlgoModelType) {
	e.simulationResult = handleSimulationResult(e.simulationResult)
	e.CommonMetrics = fillCommonMetrics(e.simulationResult)
	if modelType == edd_constant.EDDModelType {
		e.EDDMetric = fillEDDMetrics(e.simulationResult)
	} else if modelType == edd_constant.EDTModelType {
		e.EDTMetric = fillEDTMetrics(e.simulationResult)
	}
}
