package algo_model_conf

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"

func fillEDDMetrics(simulationResult map[string]float32) *EDDMetric {
	return &EDDMetric{
		InitialAccuracy: simulationResult[edd_constant.EDDSimulationNameInitialAccuracy],
		InitialRange:    simulationResult[edd_constant.EDDSimulationNameInitialRange],
		InitialLateRate: simulationResult[edd_constant.EDDSimulationNameInitialLateRate],
		FinalAccuracy:   simulationResult[edd_constant.EDDSimulationNameFinalAccuracy],
		FinalRange:      simulationResult[edd_constant.EDDSimulationNameFinalRange],
		FinalLateRate:   simulationResult[edd_constant.EDDSimulationNameFinalLateRate],
	}
}

func fillEDTMetrics(simulationResult map[string]float32) *EDTMetric {
	return &EDTMetric{
		Accuracy: simulationResult[edd_constant.EDTSimulationNameAccuracy],
		Range:    simulationResult[edd_constant.EDTSimulationNameRange],
		LateRate: simulationResult[edd_constant.EDTSimulationNameLateRate],
		MinAvg:   simulationResult[edd_constant.EDTSimulationNameMinAvg],
		MaxAvg:   simulationResult[edd_constant.EDTSimulationNameMaxAvg],
	}
}

func fillCommonMetrics(simulationResult map[string]float32) *CommonMetrics {
	return &CommonMetrics{
		Weight: simulationResult[edd_constant.CommonSimulationNameWeight],
	}
}

// 由于指标名经过重命名，因此查询时需要适配
func handleSimulationResult(simulationResult map[string]float32) map[string]float32 {
	// 先拷贝一份
	var result = make(map[string]float32)
	for key, value := range simulationResult {
		result[key] = value
	}

	// EDD
	// 新增late rate
	if item, ok := result[edd_constant.EDDSimulationNameInitialAccuracy]; ok {
		result[edd_constant.EDDSimulationNameInitialLateRate] = 1 - item
	}
	if item, ok := result[edd_constant.EDDSimulationNameFinalAccuracy]; ok {
		result[edd_constant.EDDSimulationNameFinalLateRate] = 1 - item
	}
	// 替换accuracy为precision
	if item, ok := result[edd_constant.EDDSimulationNameInitialPrecision]; ok {
		result[edd_constant.EDDSimulationNameInitialAccuracy] = item
	}
	if item, ok := result[edd_constant.EDDSimulationNameFinalPrecision]; ok {
		result[edd_constant.EDDSimulationNameFinalAccuracy] = item
	}

	// EDT
	// 新增late rate
	if item, ok := result[edd_constant.EDTSimulationNameAccuracy]; ok {
		result[edd_constant.EDTSimulationNameLateRate] = 1 - item
	}
	// 替换accuracy为precision
	if item, ok := result[edd_constant.EDTSimulationNamePrecision]; ok {
		result[edd_constant.EDTSimulationNameAccuracy] = item
	}
	return result
}
