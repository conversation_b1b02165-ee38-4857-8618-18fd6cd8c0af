package edd_history

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/edd_util"
	edd_history2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"sort"
)

type EDDHistoryInterface interface {
	ListEDDHistories(ctx utils.LCOSContext, request *edd_history2.ListEDDHistoryRequest) (interface{}, *lcos_error.LCOSError)
}

type eddHistoryConf struct {
	eddHistoryDao edd_history.EddHistoryDAO
}

func (e *eddHistoryConf) ListEDDHistories(ctx utils.LCOSContext, request *edd_history2.ListEDDHistoryRequest) (interface{}, *lcos_error.LCOSError) {
	region := ctx.GetCountry()

	eddHistories, lcosErr := e.eddHistoryDao.GetEDDHistoryBySlsTn(ctx, request.SLSTN, edd_history.TableName(ctx, ctx.GetCountry()), true)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "get hbase failed: %s", lcosErr.Msg)
		return nil, lcosErr
	}
	logger.CtxLogInfof(ctx, "successfully get edd history:[%s] for sls_tn:[%s]", utils.MarshToStringWithoutError(eddHistories), request.SLSTN)
	// sort
	sort.Slice(eddHistories, func(i, j int) bool {
		return eddHistories[i].Ctime > eddHistories[j].Ctime
	})

	responses := make([]*edd_history2.EDDHistory, 0, len(eddHistories))
	for i, singleEdd := range eddHistories {
		var isFirstPush bool
		if i == len(eddHistories)-1 {
			isFirstPush = true
		}
		responses = append(responses, transferEDDHistoryToEDDHistoryResponse(region, singleEdd, isFirstPush))
	}

	// 统计更新次数
	var autoUpdateTimes, manualUpdateTimes int
	for i := len(responses) - 2; i >= 0; i-- { // 第一次推送不记次数
		if responses[i].Mode == edd_constant.AutoUpdateEdd {
			// 自动更新只有edd max更新了才记次数
			if edd_util.CheckCanUpdateEddMax(responses[i].UpdateType) {
				autoUpdateTimes++
			}
			responses[i].EddUpdateTimes = autoUpdateTimes
		} else {
			manualUpdateTimes++
			responses[i].EddUpdateTimes = manualUpdateTimes
		}
	}

	return responses, nil
}

func NewEDDHistoryConf(eddHistoryDao edd_history.EddHistoryDAO) *eddHistoryConf {
	return &eddHistoryConf{
		eddHistoryDao: eddHistoryDao,
	}
}

var _ EDDHistoryInterface = (*eddHistoryConf)(nil)
