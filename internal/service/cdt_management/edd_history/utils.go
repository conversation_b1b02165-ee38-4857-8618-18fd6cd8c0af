package edd_history

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/datetime"
	edd_history2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
)

func transferEDDHistoryToEDDHistoryResponse(region string, eddHistory *edd_history.EddHistoryTab, isFirstPush bool) *edd_history2.EDDHistory {
	history := &edd_history2.EDDHistory{
		UpdateTime:        eddHistory.Ctime,
		EddMin:            eddHistory.EddMin,
		EddMax:            eddHistory.Edd,
		UpdateType:        eddHistory.UpdateType,
		EventTime:         eddHistory.EventTime,
		Mode:              eddHistory.GetDataType(),
		Event:             utils.NewUint8(eddHistory.UpdateEvent),
		AlgoEddModifyFlag: eddHistory.AlgoEddModifyFlag,
		IsFallbackEddFlag: eddHistory.IsFallbackEddFlag,
		IsLateEddUpdate:   eddHistory.IsLateEddUpdate,
	}
	if !isFirstPush && !eddHistory.IsDDLOnlyEDD() {
		if eddHistory.EddMin != 0 && eddHistory.LastEddMin != 0 {
			history.EddMinDiff = datetime.GetDiffDaysFromTimestamp(eddHistory.EddMin, eddHistory.LastEddMin, region)
		} else {
			history.EddMinDiff = edd_constant.SpecialEddDiff // 更新前或更新后没有edd min的特殊情况
		}
		history.EddMaxDiff = datetime.GetDiffDaysFromTimestamp(eddHistory.Edd, eddHistory.LastEDD, region)

		if history.EddMinDiff != edd_constant.SpecialEddDiff && history.EddMinDiff < 0 {
			history.EddMinDirection = edd_constant.DeductTrending
		}
		if history.EddMinDiff != edd_constant.SpecialEddDiff && history.EddMinDiff > 0 {
			history.EddMinDirection = edd_constant.ExtendTrending
		}
		if history.EddMaxDiff < 0 {
			history.EddMaxDirection = edd_constant.DeductTrending
		}
		if history.EddMaxDiff > 0 {
			history.EddMaxDirection = edd_constant.ExtendTrending
		}
		history.Event = utils.NewUint8(eddHistory.UpdateEvent)
		if !eddHistory.IsManualUpdateEDD() {
			history.EventTime = eddHistory.EventTime
		} else {
			history.TaskId = eddHistory.EDDUpdateTaskID
			history.EddMinAdjustment = eddHistory.EddMinAdjustment
			history.EddMaxAdjustment = eddHistory.Adjustment
		}
	}
	if eddHistory.NewEddProcess != nil {
		history.LeadTimeInfo = eddHistory.NewEddProcess.LeadTimeProcess
		history.DeltaInfo = eddHistory.NewEddProcess.LeadTimeDeltaProcess
		history.NwdInfo = eddHistory.NewEddProcess.NonWorkingDaysProcess
	}
	if eddHistory.WaitingWaybillInfo != nil {
		history.DDLInfo = &edd_history2.DDLInfo{
			NextEvent: eddHistory.WaitingWaybillInfo.UpdateEvent,
			DDL:       eddHistory.WaitingWaybillInfo.DDL,
		}
	}
	return history
}
