package auto_update_rule

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"
	"github.com/stretchr/testify/assert"
	"strings"
	"testing"
)

func getEDD(eventTime uint32, cdtMax, holidayExtra float64) int64 {
	return int64(eventTime) + int64((cdtMax+holidayExtra)*24*3600)

}

func Test_calculateHolidayExt(t *testing.T) {

	t.Skip()

	type args struct {
		cbLmInboundDate uint32
		holidayList     []string
		cdt             float64
		region          string
	}

	eventTime := 1664979069 // 2022-10-05 22:11:09 +0800

	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "edd not over today",
			args: args{
				cbLmInboundDate: uint32(eventTime),
				holidayList:     []string{"2022-10-06"},
				cdt:             0.05,
				region:          "SG",
			},
			want: "2022-10-05",
		},
		{
			name: "edd over today, tomorrow is holiday",
			args: args{
				cbLmInboundDate: uint32(eventTime),
				holidayList:     []string{"2022-10-06"},
				cdt:             1,
				region:          "SG",
			},
			want: "2022-10-07",
		},
		{
			name: "edd over today, tomorrow is not holiday",
			args: args{
				cbLmInboundDate: uint32(eventTime),
				holidayList:     []string{"2022-10-07"},
				cdt:             1,
				region:          "SG",
			},
			want: "2022-10-06",
		},
		{
			name: "edd over today, more than one days are holiday",
			args: args{
				cbLmInboundDate: uint32(eventTime),
				holidayList:     []string{"2022-10-06", "2022-10-07", "2022-10-10", "2022-10-14", "2022-10-19", "2022-10-20"},
				cdt:             10,
				region:          "SG",
			},
			want: "2022-10-21",
		},
		{
			name: "edd is a whole month",
			args: args{
				cbLmInboundDate: uint32(eventTime),
				holidayList:     []string{"2022-10-06", "2022-10-07", "2022-10-10", "2022-10-14", "2022-10-19", "2022-10-20"},
				cdt:             31,
				region:          "SG",
			},
			want: "2022-11-11",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := calculateHolidayExt(tt.args.cbLmInboundDate, tt.args.holidayList, tt.args.cdt, tt.args.region)
			edd := getEDD(uint32(eventTime), tt.args.cdt, got)
			eddString := pickup.TransferTimeStampToTime(uint32(edd), tt.args.region).Format("2006-01-02")
			if eddString != tt.want {
				t.Errorf("calculated edd is not correct|expect=%s|actual=%s", tt.want, eddString)
			} else {
				t.Logf("event_time=%s|cdt=%.2f|holidayList=[%s]|edd=%s", pickup.TransferTimeStampToTime(uint32(eventTime), tt.args.region).Format("2006-01-02"), tt.args.cdt, strings.Join(got1, ","), eddString)
			}
		})
	}
}

func Test_calculateExtendedEdd(t *testing.T) {

	t.Skip()

	type args struct {
		startEventTimestamp int64
		extensions          int
		nonWorkingDayList   []string
		region              string
	}

	startEventTime := 1662696000 // 2022-09-09 12:00:00 +0800

	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test case 1",
			args: args{
				startEventTimestamp: int64(startEventTime),
				extensions:          1,
				nonWorkingDayList:   []string{"2022-09-08", "2022-09-10", "2022-09-11"},
				region:              "",
			},
			want: "2022-09-12",
		},
		{
			name: "test case 2",
			args: args{
				startEventTimestamp: int64(startEventTime),
				extensions:          2,
				nonWorkingDayList:   []string{"2022-09-08", "2022-09-10", "2022-09-11"},
				region:              "",
			},
			want: "2022-09-13",
		},
		{
			name: "test case 3",
			args: args{
				startEventTimestamp: int64(startEventTime),
				extensions:          0,
				nonWorkingDayList:   []string{"2022-09-08", "2022-09-10", "2022-09-11"},
				region:              "",
			},
			want: "2022-09-09",
		},
		{
			name: "test case 4",
			args: args{
				startEventTimestamp: int64(startEventTime),
				extensions:          0,
				nonWorkingDayList:   []string{"2022-09-09", "2022-09-10", "2022-09-11"},
				region:              "",
			},
			want: "2022-09-12",
		},
		{
			name: "test case 5",
			args: args{
				startEventTimestamp: int64(startEventTime),
				extensions:          -1,
				nonWorkingDayList:   []string{"2022-09-08", "2022-09-10", "2022-09-11"},
				region:              "",
			},
			want: "2022-09-09",
		},
		{
			name: "test case 6",
			args: args{
				startEventTimestamp: int64(startEventTime),
				extensions:          -1,
				nonWorkingDayList:   []string{"2022-09-08", "2022-09-09", "2022-09-11"},
				region:              "",
			},
			want: "2022-09-10",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			extendedEdd, _, nonWorkingList := calculateExtendedEdd(tt.args.startEventTimestamp, tt.args.extensions, tt.args.nonWorkingDayList, tt.args.region)
			extendedEddString := pickup.TransferTimeStampToTime(uint32(extendedEdd), tt.args.region).Format("2006-01-02")
			if extendedEddString != tt.want {
				t.Errorf("calculated edd is not correct|expect=%s|actual=%s", tt.want, extendedEddString)
			} else {
				t.Logf("extended_edd=%s|holidayList=[%s]", pickup.TransferTimeStampToTime(uint32(extendedEdd), tt.args.region).Format("2006-01-02"), strings.Join(nonWorkingList, ","))
			}
		})
	}
}

func Test_generateCdtInfoMinMaxByEventTime(t *testing.T) {

	// 初始化 CdtExtraData 结构体
	// 初始化 CdtExtraData 结构体
	extraData := &common_utils.CdtExtraData{}

	// 周一
	// 初始化 DayGroup 结构体
	dayGroup1 := &common_utils.DayGroup{
		DayName:     "Monday",
		Days:        []uint32{1},
		LeadTimeMin: 1.11,
		LeadTimeMax: 1.12,
		DDLBackward: 1.13,
	}

	timeBucket11 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    0,
		EndHour:      4,
		LeadTimeMin:  1.14,
		LeadTimeMax:  1.15,
		DDLBackward:  1.16,
	}

	timeBucket12 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    16,
		EndHour:      20,
		LeadTimeMin:  1.17,
		LeadTimeMax:  1.18,
		DDLBackward:  1.19,
	}
	// 将 TimeBucket 添加到 DayGroup 的 TimeBuckets 切片中
	dayGroup1.TimeBuckets = append(dayGroup1.TimeBuckets, timeBucket11)
	dayGroup1.TimeBuckets = append(dayGroup1.TimeBuckets, timeBucket12)

	// 将 DayGroup 添加到 CdtExtraData 的 DayGroups 切片中
	extraData.DayGroups = append(extraData.DayGroups, dayGroup1)

	// 周二
	// 初始化 DayGroup 结构体, dayGroup无效，只有time bucket有效
	dayGroup2 := &common_utils.DayGroup{
		DayName:     "Monday",
		Days:        []uint32{2},
		LeadTimeMin: 0,
		LeadTimeMax: 0,
		DDLBackward: 5.0,
	}

	timeBucket21 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    0,
		EndHour:      4,
		LeadTimeMin:  1.20,
		LeadTimeMax:  1.21,
		DDLBackward:  1.22,
	}

	timeBucket22 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    16,
		EndHour:      20,
		LeadTimeMin:  1.23,
		LeadTimeMax:  1.24,
		DDLBackward:  1.25,
	}
	// 将 TimeBucket 添加到 DayGroup 的 TimeBuckets 切片中
	dayGroup2.TimeBuckets = append(dayGroup2.TimeBuckets, timeBucket21)
	dayGroup2.TimeBuckets = append(dayGroup2.TimeBuckets, timeBucket22)

	// 将 DayGroup 添加到 CdtExtraData 的 DayGroups 切片中
	extraData.DayGroups = append(extraData.DayGroups, dayGroup2)

	// 周三
	// 初始化 DayGroup 结构体, 只有dayGroup有效，time bucket无效
	dayGroup3 := &common_utils.DayGroup{
		DayName:     "Monday",
		Days:        []uint32{3},
		LeadTimeMin: 0,
		LeadTimeMax: 1.26,
		DDLBackward: 1.27,
	}

	timeBucket31 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    4,
		EndHour:      8,
		LeadTimeMin:  1.28,
		LeadTimeMax:  1.29,
		DDLBackward:  1.30,
	}

	timeBucket32 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    16,
		EndHour:      20,
		LeadTimeMin:  1.31,
		LeadTimeMax:  1.32,
		DDLBackward:  1.33,
	}

	// 将 TimeBucket 添加到 DayGroup 的 TimeBuckets 切片中
	dayGroup3.TimeBuckets = append(dayGroup3.TimeBuckets, timeBucket31)
	dayGroup3.TimeBuckets = append(dayGroup3.TimeBuckets, timeBucket32)

	// 将 DayGroup 添加到 CdtExtraData 的 DayGroups 切片中
	extraData.DayGroups = append(extraData.DayGroups, dayGroup3)

	var dayGroup4 *common_utils.DayGroup
	extraData.DayGroups = append(extraData.DayGroups, dayGroup4)

	for _, dt := range extraData.DayGroups {
		jsonData, err := json.Marshal(dt)
		if err != nil {
			fmt.Println("JSON Marshal error:", err)
			return
		}
		fmt.Println(string(jsonData))
	}

	ctx := context.Background()
	mconfig := &cf.MutableConfig{}
	cf.SetMutableConfig(mconfig)
	cf.GetMutableConf(ctx).DayGroupAndTimeBucketConfig = cf.DayGroupAndTimeBucketConfig{
		UseDayGroupAndTimeBucket: 3,
	}

	cases := make([]int64, 0)
	cases = append(cases, 1709486194) // time bucket和dayGroup都有效时 命中time bucket
	cases = append(cases, 1709500594) // time bucket和dayGroup都有效时 未命中time bucket

	cases = append(cases, 1709583394) // time bucket有效，dayGroup无效时 命中timeBucket
	cases = append(cases, 1709590594) // time bucket有效，dayGroup无效时 未命中timeBucket

	cases = append(cases, 1709666194) // time bucket无效，dayGroup有效时 命中dayGroupBucket
	cases = append(cases, 1709590594) // time bucket无效，dayGroup有效时 未命中time bucket

	for i, c := range cases {
		jsonData, err := json.Marshal(extraData)
		if err != nil {
			fmt.Println("JSON Marshal error:", err)
			return
		}
		LeadTimeMin := 1.111111
		LeadTimeMax := 3.333333
		cdtInfo := &cdt_calculation.CdtInfo{
			OriginLocationId: 1,
			ProductId:        "123",
			TplUniqueKey:     "abc",
			LaneCode:         "xyz",
			IsSiteLine:       1,
			OverrideType:     "type",
			CdtExtraData:     string(jsonData),
			EddRangeLimit:    10,
			DDLForwardCDT:    2.5,
			DDLBackwardCDT:   2.22222,
			LeadTimeMin:      &LeadTimeMin,
			LeadTimeMax:      &LeadTimeMax,
		}
		cdtInfo.LeadTimeMin, cdtInfo.LeadTimeMax, cdtInfo.DDLBackwardCDT, _ = generateCdtInfoMinMaxByEventTime(ctx, c, cdtInfo, "ID")
		if i == 0 {
			assert.Equalf(t, cdtInfo.GetLeadTimeMin(), timeBucket11.LeadTimeMin, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMin(), timeBucket11.LeadTimeMin)
			assert.Equalf(t, cdtInfo.GetLeadTimeMax(), timeBucket11.LeadTimeMax, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMax(), timeBucket11.LeadTimeMax)
		}

		if i == 1 {
			assert.NotEqualf(t, cdtInfo.GetLeadTimeMin(), timeBucket11.LeadTimeMin, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMin(), timeBucket11.LeadTimeMin)
			assert.NotEqualf(t, cdtInfo.GetLeadTimeMax(), timeBucket11.LeadTimeMax, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMax(), timeBucket11.LeadTimeMax)
		}

		if i == 2 {
			assert.Equalf(t, cdtInfo.GetLeadTimeMin(), timeBucket21.LeadTimeMin, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMin(), timeBucket21.LeadTimeMin)
			assert.Equalf(t, cdtInfo.GetLeadTimeMax(), timeBucket21.LeadTimeMax, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMax(), timeBucket21.LeadTimeMax)
		}

		if i == 3 {
			assert.NotEqualf(t, cdtInfo.GetLeadTimeMin(), timeBucket21.LeadTimeMin, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMin(), timeBucket21.LeadTimeMin)
			assert.NotEqualf(t, cdtInfo.GetLeadTimeMax(), timeBucket21.LeadTimeMax, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMax(), timeBucket21.LeadTimeMax)
		}

		if i == 4 {
			assert.Equalf(t, cdtInfo.GetLeadTimeMin(), dayGroup3.LeadTimeMin, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMin(), dayGroup3.LeadTimeMin)
			assert.Equalf(t, cdtInfo.GetLeadTimeMax(), dayGroup3.LeadTimeMax, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMax(), dayGroup3.LeadTimeMax)
		}

		if i == 5 {
			assert.NotEqualf(t, cdtInfo.GetLeadTimeMin(), dayGroup3.LeadTimeMin, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMin(), dayGroup3.LeadTimeMin)
			assert.NotEqualf(t, cdtInfo.GetLeadTimeMax(), dayGroup3.LeadTimeMax, "case %d LeadTimeMin not equal expect, expect: %v, actual: %v", i, cdtInfo.GetLeadTimeMax(), dayGroup3.LeadTimeMax)
		}
	}
	//fmt.Println(fmt.Sprintf("LeadTimeMin := %v, LeadTimeMax = %v, DDLBackwardCDT = %v", *cdtInfo.LeadTimeMin, *cdtInfo.LeadTimeMax, cdtInfo.DDLBackwardCDT))
}
