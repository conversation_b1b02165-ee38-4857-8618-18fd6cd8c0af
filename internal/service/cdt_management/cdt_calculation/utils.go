package auto_update_rule

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/day_group_and_time_bucket_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/cdt"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/delta"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/pis_service"
	jsoniter "github.com/json-iterator/go"
)

// 检查是否为有效的地址，不能存在有上级地址无下级地址的情况
func checkIsValidAddress(addr *cdt_calculation.AddressInfo) error {
	if addr.DistrictLocationId != nil && *addr.DistrictLocationId != 0 {
		if addr.CityLocationId == nil || *addr.CityLocationId == 0 || addr.StateLocationId == nil || *addr.StateLocationId == 0 {
			return errors.New("address is not valid")
		} else {
			return nil
		}
	}
	if addr.CityLocationId != nil && *addr.CityLocationId != 0 {
		if addr.StateLocationId == nil || *addr.StateLocationId == 0 {
			return errors.New("address is not valid")
		} else {
			return nil
		}
	}
	return nil
}

// 获取addr中最低一级的location id和level
func getLowestLevelLocation(addr *cdt_calculation.AddressInfo) (int, int) {
	if addr.DistrictLocationId != nil && *addr.DistrictLocationId != 0 {
		return *addr.DistrictLocationId, int(constant.District)
	}
	if addr.CityLocationId != nil && *addr.CityLocationId != 0 {
		return *addr.CityLocationId, int(constant.City)
	}
	if addr.StateLocationId != nil && *addr.StateLocationId != 0 {
		return *addr.StateLocationId, int(constant.State)
	}
	return 0, -1
}

// get location id by location level. For cep range and postcode level return district id
func getLocationByLevel(addr *cdt_calculation.AddressInfo, locationLevel int) int {
	switch locationLevel {
	case int(constant.State):
		return *addr.StateLocationId
	case int(constant.City):
		return *addr.CityLocationId
	case int(constant.District):
		return *addr.DistrictLocationId
	default:
		return 0
	}
}

// 获取上层Level的信息
func getUpperLevelLocation(locationLevel int, addr *cdt_calculation.AddressInfo) (int, int) {
	switch locationLevel {
	case int(constant.City):
		return *addr.StateLocationId, int(constant.State)
	case int(constant.District):
		return *addr.CityLocationId, int(constant.City)
	default:
		return 0, -1
	}
}

// generate all postcode query
func generatePostcodeQuery(objectType uint8, sellerAddr, buyerAddr *cdt_calculation.AddressInfo, productID string, isSiteLine, isLM, isCB uint8, tplUniqueKey string, region string, updateEvent uint8, dataSource uint8) []*cdt_calculation.CdtQuery {
	var countryLocationID = 0
	var queries []*cdt_calculation.CdtQuery

	isCepRangeRegion := cdt.IsCepRangeCountry(region)
	originLocationNeeded := cdt_common.IsOriginLocationNeeded(isCB == constant.CBType, updateEvent)

	if isCepRangeRegion {
		if postcode, err := strconv.Atoi(strings.ReplaceAll(*buyerAddr.PostalCode, "-", "")); err == nil {
			if originLocationNeeded {
				if sellerAddr.DistrictLocationId != nil && *sellerAddr.DistrictLocationId != 0 {
					queries = append(queries, &cdt_calculation.CdtQuery{
						ObjectType:                  objectType,
						ProductID:                   productID,
						IsSiteLine:                  isSiteLine,
						OriginLocationLevel:         constant.District,
						OriginLocationId:            *sellerAddr.DistrictLocationId,
						DestinationLocationLevel:    constant.CepRange,
						DestinationCepRangePostcode: utils.NewInt(postcode),
						QueryType:                   constant.CdtQueryCepRange,
						UpdateEvent:                 updateEvent,
						CdtDataSource:               dataSource,
					})
				}
				if sellerAddr.CityLocationId != nil && *sellerAddr.CityLocationId != 0 {
					queries = append(queries, &cdt_calculation.CdtQuery{
						ObjectType:                  objectType,
						ProductID:                   productID,
						IsSiteLine:                  isSiteLine,
						OriginLocationLevel:         constant.City,
						OriginLocationId:            *sellerAddr.CityLocationId,
						DestinationLocationLevel:    constant.CepRange,
						DestinationCepRangePostcode: utils.NewInt(postcode),
						QueryType:                   constant.CdtQueryCepRange,
						UpdateEvent:                 updateEvent,
						CdtDataSource:               dataSource,
					})
				}
				if sellerAddr.StateLocationId != nil && *sellerAddr.StateLocationId != 0 {
					queries = append(queries, &cdt_calculation.CdtQuery{
						ObjectType:                  objectType,
						ProductID:                   productID,
						IsSiteLine:                  isSiteLine,
						OriginLocationLevel:         constant.State,
						OriginLocationId:            *sellerAddr.StateLocationId,
						DestinationLocationLevel:    constant.CepRange,
						DestinationCepRangePostcode: utils.NewInt(postcode),
						QueryType:                   constant.CdtQueryCepRange,
						UpdateEvent:                 updateEvent,
						CdtDataSource:               dataSource,
					})
				}
			}
			queries = append(queries, &cdt_calculation.CdtQuery{
				ObjectType:                  objectType,
				ProductID:                   productID,
				IsSiteLine:                  isSiteLine,
				OriginLocationLevel:         constant.Country,
				OriginLocationId:            countryLocationID,
				DestinationLocationLevel:    constant.CepRange,
				DestinationCepRangePostcode: utils.NewInt(postcode),
				QueryType:                   constant.CdtQueryCepRange,
				UpdateEvent:                 updateEvent,
				CdtDataSource:               dataSource,
			})
		}
	} else {
		if originLocationNeeded {
			if sellerAddr.DistrictLocationId != nil && *sellerAddr.DistrictLocationId != 0 {
				queries = append(queries, &cdt_calculation.CdtQuery{
					ObjectType:               objectType,
					ProductID:                productID,
					IsSiteLine:               isSiteLine,
					OriginLocationLevel:      constant.District,
					OriginLocationId:         *sellerAddr.DistrictLocationId,
					DestinationLocationLevel: constant.CDTPostcode,
					DestinationPostcode:      buyerAddr.PostalCode,
					QueryType:                constant.CdtQueryPostcode,
					UpdateEvent:              updateEvent,
					CdtDataSource:            dataSource,
				})
			}
			if sellerAddr.CityLocationId != nil && *sellerAddr.CityLocationId != 0 {
				queries = append(queries, &cdt_calculation.CdtQuery{
					ObjectType:               objectType,
					ProductID:                productID,
					IsSiteLine:               isSiteLine,
					OriginLocationLevel:      constant.City,
					OriginLocationId:         *sellerAddr.CityLocationId,
					DestinationLocationLevel: constant.CDTPostcode,
					DestinationPostcode:      buyerAddr.PostalCode,
					QueryType:                constant.CdtQueryPostcode,
					UpdateEvent:              updateEvent,
					CdtDataSource:            dataSource,
				})
			}
			if sellerAddr.StateLocationId != nil && *sellerAddr.StateLocationId != 0 {
				queries = append(queries, &cdt_calculation.CdtQuery{
					ObjectType:               objectType,
					ProductID:                productID,
					IsSiteLine:               isSiteLine,
					OriginLocationLevel:      constant.State,
					OriginLocationId:         *sellerAddr.StateLocationId,
					DestinationLocationLevel: constant.CDTPostcode,
					DestinationPostcode:      buyerAddr.PostalCode,
					QueryType:                constant.CdtQueryPostcode,
					UpdateEvent:              updateEvent,
					CdtDataSource:            dataSource,
				})
			}
		}
		queries = append(queries, &cdt_calculation.CdtQuery{
			ObjectType:               objectType,
			ProductID:                productID,
			IsSiteLine:               isSiteLine,
			OriginLocationLevel:      constant.Country,
			OriginLocationId:         countryLocationID,
			DestinationLocationLevel: constant.CDTPostcode,
			DestinationPostcode:      buyerAddr.PostalCode,
			QueryType:                constant.CdtQueryPostcode,
			UpdateEvent:              updateEvent,
			CdtDataSource:            dataSource,
		})
	}
	return queries
}

// generate location cdt query. For full query, generate all possible level
func generateLocationCdtQuery(ctx context.Context, objectType uint8, lowestOriginLocationIDLevel, lowestDestinationLocationLevel int, sellerAddr, buyerAddr *cdt_calculation.AddressInfo, productID string,
	isSiteLine, isLM uint8, tplUniqueKey string, updateEvent uint8, dataSource uint8, equalLevel bool) ([]*cdt_calculation.CdtQuery, *lcos_error.LCOSError) {
	// for full query, reset location level to district
	lowestOriginLocationID := getLocationByLevel(sellerAddr, lowestOriginLocationIDLevel)
	lowestDestinationLocationID := getLocationByLevel(buyerAddr, lowestDestinationLocationLevel)
	tmpOriginLocationID, tmpDestinationLocationID := lowestOriginLocationID, lowestDestinationLocationID
	tmpOriginLocationLevel, tmpDestinationLocationLevel := lowestOriginLocationIDLevel, lowestDestinationLocationLevel
	maxLoopSize := config.GetMaxLoopSizeForLocationLevel(ctx)
	queries := make([]*cdt_calculation.CdtQuery, 0, maxLoopSize*maxLoopSize)
	//todo 改一下equallevel 的query
	var loopIndex1, loopIndex2 int
	for loopIndex1 = 0; loopIndex1 < maxLoopSize; loopIndex1++ {
		for loopIndex2 = 0; loopIndex2 < maxLoopSize; loopIndex2++ {
			if tmpDestinationLocationID == 0 && tmpOriginLocationID == 0 {
				break
			}
			if equalLevel && !(loopIndex1 == 0 && loopIndex2 == 0) && (tmpOriginLocationLevel != tmpDestinationLocationLevel) {
				//需要平级地址兜底，但是level不相同时 跳过
				if tmpOriginLocationID == 0 {
					break
				}
				tmpOriginLocationID, tmpOriginLocationLevel = getUpperLevelLocation(tmpOriginLocationLevel, sellerAddr)
				continue
			}
			storedLocationID := tmpDestinationLocationID
			queries = append(queries, &cdt_calculation.CdtQuery{
				ObjectType:               objectType,
				ProductID:                productID,
				IsSiteLine:               isSiteLine,
				OriginLocationLevel:      int8(tmpOriginLocationLevel),
				OriginLocationId:         tmpOriginLocationID,
				DestinationLocationLevel: int8(tmpDestinationLocationLevel),
				DestinationLocationId:    utils.NewInt(storedLocationID),
				QueryType:                constant.CdtQueryLocation,
				UpdateEvent:              updateEvent,
				CdtDataSource:            dataSource,
			})
			if tmpOriginLocationID == 0 {
				break
			}
			tmpOriginLocationID, tmpOriginLocationLevel = getUpperLevelLocation(tmpOriginLocationLevel, sellerAddr)
		}
		if loopIndex2 >= maxLoopSize {
			return nil, lcos_error.NewLCOSError(lcos_error.LoopOverMaxSize, fmt.Sprintf("loop over max size, max_size=%d", maxLoopSize))
		}
		if tmpDestinationLocationID == 0 && tmpOriginLocationID == 0 {
			break
		}
		tmpOriginLocationID = lowestOriginLocationID
		tmpOriginLocationLevel = lowestOriginLocationIDLevel
		tmpDestinationLocationID, tmpDestinationLocationLevel = getUpperLevelLocation(tmpDestinationLocationLevel, buyerAddr)
	}

	if loopIndex1 >= maxLoopSize {
		return nil, lcos_error.NewLCOSError(lcos_error.LoopOverMaxSize, fmt.Sprintf("loop over max size, max_size=%d", maxLoopSize))
	}

	return queries, nil
}

func generateShipOutCdtQuery(queries []*cdt_calculation.CdtQuery) []*cdt_calculation.CdtQuery {
	cdtQueries := make([]*cdt_calculation.CdtQuery, 0, len(queries)*2)
	for _, singleQuery := range queries {
		cdtQueries = append(cdtQueries, singleQuery)
		if singleQuery.UpdateEvent != edd_constant.ShippedOut && utils.CheckInUint8(singleQuery.UpdateEvent, edd_constant.AllShippedOutEventEnumList) {
			shipOutQuery := &cdt_calculation.CdtQuery{
				ObjectType:                  singleQuery.ObjectType,
				ProductID:                   singleQuery.ProductID,
				LaneCode:                    singleQuery.LaneCode,
				UpdateEvent:                 edd_constant.ShippedOut,
				IsSiteLine:                  singleQuery.IsSiteLine,
				OriginLocationLevel:         singleQuery.OriginLocationLevel,
				OriginLocationId:            singleQuery.OriginLocationId,
				DestinationLocationLevel:    singleQuery.DestinationLocationLevel,
				DestinationLocationId:       singleQuery.DestinationLocationId,
				DestinationCepRangePostcode: singleQuery.DestinationCepRangePostcode,
				DestinationPostcode:         singleQuery.DestinationPostcode,
				QueryType:                   singleQuery.QueryType,
				CdtDataSource:               singleQuery.CdtDataSource,
			}
			cdtQueries = append(cdtQueries, shipOutQuery)
		}
	}
	return cdtQueries
}

func productInWhiteList(product string, whiteList []string) bool {
	hit := false
	for _, v := range whiteList {
		if v == product {
			hit = true
			break
		}
	}
	return hit
}

func convertGetQuotationReq(productID string, sellerAddr, buyerAdd *cdt_calculation.AddressInfo, skuInfo *cdt_calculation.CdtSkuInfo) *pis_service.GetQuotationReq {
	req := &pis_service.GetQuotationReq{
		ShopId: skuInfo.ShopId,
		//ChannelId:  productID,
		SourceType: pis_service.SourceTypeFromLCOS,
		RateLocation: &pis_service.RateLocation{
			PickupPostcode:  *sellerAddr.PostalCode,
			DeliverPostcode: *buyerAdd.PostalCode,
		},
	}
	for _, v := range skuInfo.SkuInfo {
		req.Skus = append(req.Skus, &pis_service.Sku{
			ItemId:    v.ItemId,
			Length:    v.Length,
			Width:     v.Width,
			Height:    v.Height,
			Weight:    v.Weight,
			Quantity:  v.Quantity,
			ItemPrice: v.ItemPrice,
		})
	}
	return req
}

func calculateHolidayExt(cbLmInboundDate uint32, holidayList []string, cdt float64, region string) (float64, []string) {

	nonWorkingDayProcess := make([]string, 0, 10)

	nowDate := pickup.TransferTimeStampToTime(cbLmInboundDate, region)
	// 检查cbLmInboundDate+cdt是否大于今天，不大于的话，直接返回0
	nowDayNight := pickup.TransferTimeStampToTime(cbLmInboundDate+uint32(cdt*3600*24), region)
	newStartDay := time.Date(nowDate.Year(), nowDate.Month(), nowDate.Day()+1, 0, 0, 0, 0, nowDate.Location())
	if nowDayNight.Day() == nowDate.Day() && nowDayNight.Month() == nowDate.Month() && nowDayNight.Year() == nowDate.Year() {
		return 0.0, nonWorkingDayProcess
	}

	realCdt := int(nowDayNight.Sub(newStartDay).Hours()/24) + 1

	// 将holiday list转为holiday map
	holidayMap := make(map[string]bool)
	for _, holiday := range holidayList {
		holidayMap[holiday] = true
	}

	// 计算holiday ext
	holidayExt := 0
	workingDay := 0
	for i := 1; i <= constant.ExtendsDays; i++ {
		currentDate := time.Date(nowDate.Year(), nowDate.Month(), nowDate.Day()+i, nowDate.Hour(), nowDate.Minute(), nowDate.Second(), nowDate.Nanosecond(), nowDate.Location())
		currentDayString := currentDate.Format("2006-01-02")
		var holidayFlag bool
		if _, holidayFlag = holidayMap[currentDayString]; holidayFlag {
			nonWorkingDayProcess = append(nonWorkingDayProcess, currentDayString)
			holidayExt++
		} else {
			workingDay++
		}
		if workingDay >= realCdt && !holidayFlag {
			break
		}
	}
	return float64(holidayExt), nonWorkingDayProcess
}

// calculateExtendedEdd
// use the old edd to calculate the new extended edd with extensions
// return extended edd, extended days, extended holiday list
func calculateExtendedEdd(startEventTimestamp int64, extensions int, nonWorkingDayList []string, region string) (int64, int, []string) {
	var extendedEDD int64
	var extensionDays int
	nonWorkingDayProcess := make([]string, 0, 10)

	// 将holiday list转为holiday map
	holidayMap := make(map[string]bool)
	for _, holiday := range nonWorkingDayList {
		holidayMap[holiday] = true
	}

	startDate := pickup.TransferTimeStampToTime(uint32(startEventTimestamp), region)

	if extensions >= 0 {

		// if today is holiday, need to add extension
		currentDate := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), startDate.Hour(), startDate.Minute(), startDate.Second(), startDate.Nanosecond(), startDate.Location())
		currentDayString := currentDate.Format("2006-01-02")
		if _, ok := holidayMap[currentDayString]; ok {
			extensions++
		}

		workingDay := 0
		// for extension larger than 0, will loop over until working day larger than extensions
		for i := 0; i <= constant.ExtendsDays; i++ {
			currentDate = time.Date(startDate.Year(), startDate.Month(), startDate.Day()+i, startDate.Hour(), startDate.Minute(), startDate.Second(), startDate.Nanosecond(), startDate.Location())
			currentDayString = currentDate.Format("2006-01-02")
			var holidayFlag bool
			if _, holidayFlag = holidayMap[currentDayString]; holidayFlag {
				nonWorkingDayProcess = append(nonWorkingDayProcess, currentDayString)
			} else if i != 0 {
				workingDay++
			}
			if i != 0 {
				extensionDays++
			}
			if workingDay >= extensions && !holidayFlag {
				break
			}
		}
		extendedEDD = startEventTimestamp + int64(extensionDays)*24*3600
	} else {
		endEventTimestamp := startEventTimestamp + int64(extensions)*24*3600
		endEventDate := pickup.TransferTimeStampToTime(uint32(endEventTimestamp), region)
		var i int
		for i = 0; i <= constant.ExtendsDays; i++ {
			currentDate := time.Date(endEventDate.Year(), endEventDate.Month(), endEventDate.Day()+i, endEventDate.Hour(), endEventDate.Minute(), endEventDate.Second(), endEventDate.Nanosecond(), endEventDate.Location())
			currentDayString := currentDate.Format("2006-01-02")
			if _, ok := holidayMap[currentDayString]; ok {
				nonWorkingDayProcess = append(nonWorkingDayProcess, currentDayString)
			} else {
				break
			}
		}
		extendedEDDDate := time.Date(endEventDate.Year(), endEventDate.Month(), endEventDate.Day()+i, endEventDate.Hour(), endEventDate.Minute(), endEventDate.Second(), endEventDate.Nanosecond(), endEventDate.Location())
		extensionDays = int(extendedEDDDate.Sub(startDate).Hours() / 24)
		extendedEDD = extendedEDDDate.Unix()
	}
	return extendedEDD, extensionDays, nonWorkingDayProcess
}

func generateCdtInfoMinMaxByEventTime(ctx context.Context, eventTime int64, cdtInfo *cdt_calculation.CdtInfo, region string) (*float64, *float64, float64, bool) {
	if cdtInfo == nil {
		return utils.NewFloat64(0), utils.NewFloat64(0), 0, false
	}
	cdtMin := cdtInfo.GetLeadTimeMin()
	cdtMax := cdtInfo.GetLeadTimeMax()
	ddLBackward := cdtInfo.GetDDLBackwardCDT()
	var useDayGroupOrTimeBucket bool
	if !config.GetNeedUseGroupDayAndTimeBucketConf(ctx, region, day_group_and_time_bucket_constant.EddUse) {
		return utils.NewFloat64(cdtMin), utils.NewFloat64(cdtMax), ddLBackward, useDayGroupOrTimeBucket
	}

	weekDay, hour := common_utils.GetWeekDayAndHourByTimeStamp(eventTime, region)

	cdtExtraDataString := cdtInfo.CdtExtraData
	var cdtExtraData common_utils.CdtExtraData
	err := jsoniter.Unmarshal([]byte(cdtExtraDataString), &cdtExtraData)
	if err != nil {
		logger.CtxLogErrorf(ctx, "unmarshal CdtExtraData failed|product_id=%s, CdtExtraData=%s", cdtInfo.ProductId, cdtInfo.CdtExtraData)
		return utils.NewFloat64(cdtMin), utils.NewFloat64(cdtMax), ddLBackward, useDayGroupOrTimeBucket
	}

	for _, dayGroup := range cdtExtraData.DayGroups {
		if dayGroup == nil {
			continue
		}
		// 判断当前时间是否在该dayGroup内
		days := dayGroup.Days
		if !utils.CheckInUint32(weekDay, days) {
			continue
		}

		if dayGroup.LeadTimeMax > 0 {
			cdtMin = dayGroup.LeadTimeMin
			cdtMax = dayGroup.LeadTimeMax
			ddLBackward = dayGroup.DDLBackward
			useDayGroupOrTimeBucket = true
		}

		for _, timeBucket := range dayGroup.TimeBuckets {
			if timeBucket == nil {
				continue
			}
			// 判断当前时间是否在该timeBucket内（左闭右开）
			if timeBucket.StartHour <= hour && timeBucket.EndHour > hour && timeBucket.LeadTimeMax > 0 {
				cdtMin = timeBucket.LeadTimeMin
				cdtMax = timeBucket.LeadTimeMax
				ddLBackward = timeBucket.DDLBackward
				useDayGroupOrTimeBucket = true
			}
		}
	}
	return utils.NewFloat64(cdtMin), utils.NewFloat64(cdtMax), ddLBackward, useDayGroupOrTimeBucket
}

func getCdtDeltaInfo(cdtInfo *cdt_calculation.CdtInfo) (float64, float64) {
	if cdtInfo == nil || cdtInfo.CdtProcess == nil || cdtInfo.CdtProcess.ManualManipulationProcess == nil {
		return 0, 0
	}
	return cdtInfo.CdtProcess.ManualManipulationProcess.CdtMinDelta, cdtInfo.CdtProcess.ManualManipulationProcess.CdtMaxDelta
}

func setActualLeadTime(cdtInfo *cdt_calculation.CdtInfo) {
	deltaMin, deltaMax := getCdtDeltaInfo(cdtInfo)
	if cdtInfo == nil || cdtInfo.LeadTimeMin == nil || cdtInfo.LeadTimeMax == nil {
		return
	}

	*cdtInfo.LeadTimeMin, *cdtInfo.LeadTimeMax = delta.CalculateByDelta(cdtInfo.GetLeadTimeMin(), cdtInfo.GetLeadTimeMax(), deltaMin, deltaMax)
}

func setManualUpdateProcess(cdtInfo *cdt_calculation.CdtInfo) {
	if cdtInfo == nil || cdtInfo.CdtProcess == nil || cdtInfo.CdtProcess.ManualUpdateProcess == nil {
		return
	}

	leadTimeMin := *cdtInfo.LeadTimeMin
	leadTimeMax := *cdtInfo.LeadTimeMax

	cdtInfo.CdtProcess.ManualUpdateProcess.CdtMin = leadTimeMin
	cdtInfo.CdtProcess.ManualUpdateProcess.CdtMax = leadTimeMax
}

func setAutoUpdateProcess(cdtInfo *cdt_calculation.CdtInfo) {
	if cdtInfo == nil || cdtInfo.CdtProcess == nil || cdtInfo.CdtProcess.AutoUpdateProcess == nil {
		return
	}

	leadTimeMin := *cdtInfo.LeadTimeMin
	leadTimeMax := *cdtInfo.LeadTimeMax

	cdtInfo.CdtProcess.AutoUpdateProcess.CdtMin = leadTimeMin
	cdtInfo.CdtProcess.AutoUpdateProcess.CdtMax = leadTimeMax
}

func getAbTestResult(groupTag uint32, abTestRule *cdt_ab_test.CdtAbTestRule) (uint8, bool) {
	hitGroupTag := edd_constant.AbTestGroupTagA // 默认使用group a
	skipAutoUpdateRule := false                 // 默认不跳过匹配自动更新规则
	// TestGroupList的下标就代表对应的group（0：A，1：B）
	if abTestRule != nil && len(abTestRule.TestGroupList) > int(groupTag) {
		hitGroupTag = uint8(groupTag)
		testGroup := abTestRule.TestGroupList[hitGroupTag]
		skipAutoUpdateRule = testGroup.CdtType == edd_constant.AbTestCdtTypeManualUpdate
	}
	return hitGroupTag, skipAutoUpdateRule
}
