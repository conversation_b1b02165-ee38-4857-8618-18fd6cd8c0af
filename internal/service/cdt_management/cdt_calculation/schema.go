package auto_update_rule

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
)

type ListAutoUpdateRuleResponse struct {
	PageNo uint32                                   `json:"pageno"`
	Count  uint32                                   `json:"count"`
	Total  uint32                                   `json:"total"`
	List   []*auto_update_rule.CDTAutoUpdateRuleTab `json:"list"`
}

const (
	ACTION_IS_ALLOWED  = 0
	ACTION_NOT_ALLOWED = -1
)

type ActionResult struct {
	ActionResult int    `json:"action_result"` // 是否允许enable/disable,0-可以，-1-不可以
	Message      string `json:"message"`
}

type TimePeriodResponse struct {
	TimePeriod int `json:"time_period"`
}

var (
	CatModuleName_AlgoPredictEDD    = "algoPredictEdd"
	CatInterfaceName_AlgoPredictEDD = "PredictEdd"
)
