package auto_update_rule

import (
	"fmt"
	edd_auto_update "git.garena.com/shopee/bg-logistics/algo/sls/edd-auto-update"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_pushing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lts_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/datetime"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	model_ab_test "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	edd_history2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/algo_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lpop_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/waybill_center_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/mohae/deepcopy"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/graymachine"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/cdt"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	apollo_config "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	auto_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/pis_service"
)

type CdtCalculationServiceInterface interface {
	GetAutoUpdateCdtInfoByProduct(ctx utils.LCOSContext, objectType uint8, productId string, isSiteLine uint8, isLM uint8, isCB uint8, region string, tplUniqueKey string, laneCode string, updateEvent uint8, sellerAddr, buyerAddr *cdt_calculation.AddressInfo, dataSource uint8, groupTag, startTimeType uint8) (*cdt_calculation.CdtInfo, *lcos_error.LCOSError)
	GetCourierDeliveryTimeByProduct(ctx utils.LCOSContext, objectType uint8, productID string, isSiteLine uint8, isLM uint8, isCB uint8, region string, tplUniqueKey, laneCode string, updateEvent uint8, sellerAddr, buyerAdd *cdt_calculation.AddressInfo, skuInfo *cdt_calculation.CdtSkuInfo, dataSource uint8, groupTag uint8, skipAutoUpdateRule bool, needEqualLevels bool, startTimeType uint8, requestTime uint32, cdtScene pb.CdtScene) ([]*cdt_calculation.CdtInfo, *lcos_error.LCOSError)
	BatchGetCdtInfoByProducts(cxt utils.LCOSContext, objectType uint8, productsInfo []*cdt_calculation.CdtProductInfo, dataSource uint8) ([]*cdt_calculation.CdtReply, *lcos_error.LCOSError)
	BatchGetCdtInfoByProductsWithGreySwitch(ctx utils.LCOSContext, objectType uint8, productsInfos []*cdt_calculation.CdtProductInfo, dataSource uint8) ([]*cdt_calculation.CdtReply, *lcos_error.LCOSError)

	BatchGetCdtInfoByProductsWithConcurrency(ctx utils.LCOSContext, objectType uint8, productsInfo []*cdt_calculation.CdtProductInfo) ([]*cdt_calculation.CdtReply, *lcos_error.LCOSError)

	// SPLN-22214 calculate lm edd and push to oms
	GetCdtInfoForTracking(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, cbLmInboundDate int64) (*cdt_calculation.EddCalculationInfo, *cdt_calculation.EDDProcess, *lcos_error.LCOSError)
	// SPLN-26145 product 90020 get edd from wbc
	GetEddFromWbc(ctx utils.LCOSContext, product *cdt_calculation.CdtProductInfo, forderId uint64, cbLmInboundDate int64) (*cdt_calculation.EddCalculationInfo, *cdt_calculation.EDDProcess, *lcos_error.LCOSError)

	// SPLN-23295 get edd
	GetEDDInfo(ctx utils.LCOSContext, productsInfo *cdt_calculation.CdtProductInfo, forderId uint64, eventTime int64) (int64, int64, *cdt_calculation.EDDProcess, *lcos_error.LCOSError)
	// SPLN-29072 get edd calculate info, include cdt, nwd, edd config, edd process, ddl cdt
	GetEddCalculationInfo(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, forderId uint64, eventTime int64) (*cdt_calculation.EddCalculationInfo, *cdt_calculation.DDLCalculationInfo, *cdt_calculation.EDDProcess, *lcos_error.LCOSError)

	// SPLN-33284
	GetEDDInfoByAlgo(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, slsTn string, eventTime int64, eventTrackingCode string) (int64, int64, *algo_service.PredictEDDInfo, *cdt_calculation.EDDProcess, *lcos_error.LCOSError)

	// SPLN-24285 get extension edd
	GetExtendedEDDInfo(ctx utils.LCOSContext, extendedEDD *cdt_calculation.SingleExtendedEDDInfo) (*cdt_calculation.SingleExtendedEDDResponse, *lcos_error.LCOSError)

	// SPLN-30606 get edd info
	QueryEDDInfo(ctx utils.LCOSContext, query *cdt_calculation.QueryEDDInfoRequest) (*edd_history2.EddHistoryTab, *lcos_error.LCOSError)

	// SPLN-31544 support lps get rule
	ListMChannelRuleByRegion(ctx utils.LCOSContext, region string) ([]*pb.MChannelRuleList, *lcos_error.LCOSError)
	ListMChannelGreyConfigByRegion(ctx utils.LCOSContext, region string) ([]*pb.MChannelGreyConfigList, *lcos_error.LCOSError)

	ListABTestRuleByRegion(ctx utils.LCOSContext, region string) ([]*pb.ABTestRuleList, *lcos_error.LCOSError)

	// SPLN-33865 批量获取修正规则数据
	BatchGetEDTManualManipulationForAlgoModel(ctx utils.LCOSContext, productInfo []*cdt_calculation.CdtProductInfo) map[string]cdt_calculation.DeltaReply

	GetFallbackEddInfo(ctx utils.LCOSContext, region string, forderId uint64, daysToDelivery uint32) (int64, int64, *lcos_error.LCOSError)
}

type CdtCalculationService struct {
	autoUpdateDao         auto_rule2.CDTAutoUpdateRuleTabDAO
	autoUpdateDataDao     auto_update_data.CDTAutoUpdateDataDAO
	manualUpdateDao       manual_update_rule.CDTManualUpdateRuleTabDAO
	manualManipulationDao manual_manipulation_rule.CDTManualManipulationRuleTabDAO
	slsHolidayDao         sls_holiday.SlsHolidayDAO
	lpsHolidayDao         lps_holiday.LPSHolidayDAO
	tplLineIDRefDao       tpl_id_line_id_ref.TPLIDLineIDRefDAO

	eddHistoryDao edd_history2.EddHistoryDAO

	abTestService cdt_ab_test.CdtAbTestService

	automatedVolumeGenerationDataDao automated_volume_generation_data.AutomatedVolumeGenerationDataDao
	automatedVolumeRuleDao           automated_volume.AutomatedVolumeGenerationRuleDao
}

func NewCdtCalculationService(autuUpdateDao auto_rule2.CDTAutoUpdateRuleTabDAO, autoUpdateDataDao auto_update_data.CDTAutoUpdateDataDAO, manualUpdateDao manual_update_rule.CDTManualUpdateRuleTabDAO, manualManipulationDao manual_manipulation_rule.CDTManualManipulationRuleTabDAO, slsHolidayDao sls_holiday.SlsHolidayDAO, lpsHolidayDao lps_holiday.LPSHolidayDAO, tplLineIDRefDao tpl_id_line_id_ref.TPLIDLineIDRefDAO, eddHistoryDao edd_history2.EddHistoryDAO, abTestService cdt_ab_test.CdtAbTestService, automatedVolumeGenerationDataDao automated_volume_generation_data.AutomatedVolumeGenerationDataDao, automatedVolumeRuleDao automated_volume.AutomatedVolumeGenerationRuleDao) *CdtCalculationService {
	return &CdtCalculationService{
		autoUpdateDao:                    autuUpdateDao,
		autoUpdateDataDao:                autoUpdateDataDao,
		manualUpdateDao:                  manualUpdateDao,
		manualManipulationDao:            manualManipulationDao,
		slsHolidayDao:                    slsHolidayDao,
		lpsHolidayDao:                    lpsHolidayDao,
		tplLineIDRefDao:                  tplLineIDRefDao,
		eddHistoryDao:                    eddHistoryDao,
		abTestService:                    abTestService,
		automatedVolumeGenerationDataDao: automatedVolumeGenerationDataDao,
		automatedVolumeRuleDao:           automatedVolumeRuleDao,
	}
}

var _ CdtCalculationServiceInterface = (*CdtCalculationService)(nil)

// getQuery
// @Description: generate fallback query for auto update / manual update / manual manipulation
// @receiver c
// @param productID
// @param isSiteLine
// @param isLM
// @param isCB
// @param tplUniqueKey
// @param sellerAddr
// @param buyerAddr
// @param region
// @param originLocationLevel             for auto update / manual manipulation, this could be get from rule itself
// @param destinationLocationLevel        for auto update / manual manipulation, this could be get from rule itself
// @param fullQuery                       for manual update rule, this could be true to indicate get all possible queries
// @return []*cdt_calculation.CdtQuery
// @return *lcos_error.LCOSError
func (c *CdtCalculationService) getQuery(ctx utils.LCOSContext, objectType uint8, productID, laneCode string, laneCodeFlag bool, updateEvent uint8, isSiteLine uint8, isLM uint8, isCB uint8, tplUniqueKey string, sellerAddr, buyerAddr *cdt_calculation.AddressInfo, region string,
	originLocationLevel, destinationLocationLevel int, fullQuery bool, dataSource uint8, equalLevel bool) ([]*cdt_calculation.CdtQuery, *lcos_error.LCOSError) {
	if objectType == edd_constant.LeadTimeObject && updateEvent == edd_constant.PickupDone {
		// Pickup Done触发EDD场景，根据region和productId判断目前是否支持匹配leadtime数据
		if !cf.IsLeadtimeObjectAvailable(ctx, region, productID) {
			objectType = edd_constant.CdtObject
		}
	}

	// 检查传入的addr有效
	err1 := checkIsValidAddress(sellerAddr)
	err2 := checkIsValidAddress(buyerAddr)
	if err1 != nil || err2 != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "address is not valid")
	}

	var countryLocationID = 0
	// get initial location level

	var postcodeQueries, locationQueries []*cdt_calculation.CdtQuery
	var queries []*cdt_calculation.CdtQuery
	var queryErr *lcos_error.LCOSError
	if cdt_common.IsOriginLocationNeeded(isCB == constant.CBType, updateEvent) {
		postcodeQueryFlag := cdt.IsPostcodeNeeded(destinationLocationLevel)
		// only when destinationLocationLevel is larger than cep range or full query enabled, will add postcode query
		if (postcodeQueryFlag || fullQuery) && buyerAddr.IsPostcodeValid() {
			postcodeQueries = generatePostcodeQuery(objectType, sellerAddr, buyerAddr, productID, isSiteLine, isLM, isCB, tplUniqueKey, region, updateEvent, dataSource)
		}
		if postcodeQueryFlag || fullQuery || equalLevel {
			// postcodeQuery stand for auto update rule with destination level of postcode, full query stands for manual update and manual manipulation
			// both of them need full location query
			// need to reset location level to the lowest level
			_, originLocationLevel = getLowestLevelLocation(sellerAddr)
			_, destinationLocationLevel = getLowestLevelLocation(buyerAddr)
		}
		locationQueries, queryErr = generateLocationCdtQuery(ctx, objectType, originLocationLevel, destinationLocationLevel, sellerAddr, buyerAddr, productID, isSiteLine, isLM, tplUniqueKey, updateEvent, dataSource, equalLevel)
		if queryErr != nil {
			return nil, queryErr
		}
	} else {
		postcodeQueryFlag := cdt.IsPostcodeNeeded(destinationLocationLevel)
		if (postcodeQueryFlag || fullQuery) && buyerAddr.IsPostcodeValid() {
			postcodeQueries = generatePostcodeQuery(objectType, sellerAddr, buyerAddr, productID, isSiteLine, isLM, isCB, tplUniqueKey, region, updateEvent, dataSource)
		}
		if postcodeQueryFlag || fullQuery || equalLevel {
			// postcodeQuery stand for auto update rule with destination level of postcode, full query stands for manual update and manual manipulation
			// both of them need full location query
			// need to reset location level to the lowest level
			_, destinationLocationLevel = getLowestLevelLocation(buyerAddr)
		}
		locationQueries, queryErr = generateLocationCdtQuery(ctx, objectType, int(constant.Country), destinationLocationLevel, sellerAddr, buyerAddr, productID, isSiteLine, isLM, tplUniqueKey, updateEvent, dataSource, equalLevel)
		if queryErr != nil {
			return nil, queryErr
		}
	}

	//spln-31544 equalLevel  skipPostcode
	if !equalLevel {
		queries = append(queries, postcodeQueries...)
	}
	queries = append(queries, locationQueries...)

	// 补充country level级别的query
	queries = append(queries, &cdt_calculation.CdtQuery{
		ObjectType:               objectType,
		ProductID:                productID,
		IsSiteLine:               isSiteLine,
		OriginLocationLevel:      constant.Country,
		OriginLocationId:         countryLocationID,
		DestinationLocationLevel: constant.Country,
		DestinationLocationId:    utils.NewInt(countryLocationID),
		QueryType:                constant.CdtQueryLocation,
		UpdateEvent:              updateEvent,
		CdtDataSource:            dataSource,
	})

	// add lane code
	if laneCodeFlag {
		for _, singleQuery := range queries {
			singleQuery.LaneCode = utils.NewString(laneCode)
		}
	}

	// shipOut  must be added after each pickUp Done and dropOff Done.
	return generateShipOutCdtQuery(queries), nil
}

func (c *CdtCalculationService) getManualUpdateCdtInfo(ctx utils.LCOSContext, objectType uint8, productID, laneCode string, laneCodeFlag bool, updateEvent uint8, isSiteLine uint8, isLM uint8, isCB uint8, tplUniqueKey string, sellerAddr, buyerAddr *cdt_calculation.AddressInfo, region string, dataSource uint8, equalLevel bool) ([]*cdt_calculation.CdtInfo, []*cdt_calculation.CdtQuery, bool, *lcos_error.LCOSError) {
	// try to find lane manual update cdt first
	if laneCodeFlag {
		queries, lcosErr := c.getQuery(ctx, objectType, productID, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, tplUniqueKey, sellerAddr, buyerAddr, region, 0, 0, true, dataSource, equalLevel)
		if lcosErr != nil {
			return nil, nil, false, lcosErr
		}

		cdtInfo, realCdtQuery, lcosErr := c.manualUpdateDao.QueryCdtInfoUsingCache(ctx, productID, isSiteLine, isLM, region, queries, equalLevel)
		if lcosErr == nil && cdtInfo != nil {
			return cdtInfo, realCdtQuery, true, nil
		}
	}

	// cannot find lane manual update cdt, try to find product manual update
	logger.CtxLogInfof(ctx, "failed to find lane manual update cdt info|product_id=%s, is_site_line=%d, is_lm=%d, lane_code=%s", productID, isSiteLine, isLM, laneCode)
	queries, lcosErr := c.getQuery(ctx, objectType, productID, "", false, updateEvent, isSiteLine, isLM, isCB, tplUniqueKey, sellerAddr, buyerAddr, region, 0, 0, true, dataSource, equalLevel)
	if lcosErr != nil {
		return nil, nil, false, lcosErr
	}

	cdtInfo, realCdtQuery, lcosErr := c.manualUpdateDao.QueryCdtInfoUsingCache(ctx, productID, isSiteLine, isLM, region, queries, equalLevel)
	if lcosErr == nil && cdtInfo != nil {
		return cdtInfo, realCdtQuery, false, nil
	}

	// find not any manual update data
	errMsg := fmt.Sprintf("failed to find product manual update cdt info|product_id=%s, is_site_line=%d, is_lm=%d", productID, isSiteLine, isLM)
	logger.CtxLogInfof(ctx, errMsg)
	return nil, nil, false, lcos_error.NewLCOSError(lcos_error.NotFoundManualUpdateDataErrorCode, errMsg)
}

// getCdtManualManipulation
// SPLN-34371
// 单独进行修正规则的获取，不进行后续的修复调整
// SPLN-34535
// 新增根据request time获取历史修正规则的功能。
// 如果传入的request time为0，则和历史功能保持一致
// 如果传入的reuqest time大于0，则获取指定时间生效的修正规则
// ！！！注意，由于相关的数据进行过各种切换，目前查询历史修正规则仅支持
// 1. route维度的修正规则数据（当前location维度的修正规则数据的前端入口已经下线，理论上不会再有新的location维度数据）
// 2. 纯放在内存中的cdt route维度数据（由于性能问题，优化需求将route维度的数据全部放在内存中，并做了切换开关，目前已经全市场完成切换，如果回滚，则此需求时效）
// 3. 仅针对cdt(EDT)生效，EDD相关的修正规则存在于hbase中，此部分数据没有存在于habse中
func (c *CdtCalculationService) getCdtManualManipulation(ctx utils.LCOSContext, objectType uint8, productId, laneCode string, laneCodeFlag bool, updateEvent uint8, isSiteLine uint8, isLM uint8, isCB uint8,
	region string, tplUniqueKey string, seller, buyer *cdt_calculation.AddressInfo, dataSource uint8, equalLevel bool, requestTime uint32, cdtScene pb.CdtScene) (*cdt_calculation.CdtManualManipulation, *cdt_calculation.CdtQuery, *lcos_error.LCOSError) {
	var (
		reg                         = strings.ToUpper(region)
		cdtDelta                    *cdt_calculation.CdtManualManipulation
		realManualManipulationQuery *cdt_calculation.CdtQuery
		lcErr                       *lcos_error.LCOSError
	)

	// get manual manipulation rule from cache
	manualManipulationQuerys, err := c.getQuery(ctx, objectType, productId, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, tplUniqueKey, seller, buyer, region, 0, 0, true, dataSource, equalLevel)
	//logger.CtxLogInfof(ctx, "manualManipulationQuerys: %s", utils.MarshToStringWithoutError(manualManipulationQuerys))
	if err != nil {
		return nil, nil, err
	}
	//logger.CtxLogInfof(ctx, "generated manual manipulation query:[%v]", utils.MarshToStringWithoutError(manualManipulationQuerys))
	cdtDelta, realManualManipulationQuery, lcErr = c.manualManipulationDao.QueryManualManipulationRouteUsingCache(ctx, reg, productId, manualManipulationQuerys, requestTime, cdtScene)
	if cdtDelta == nil || lcErr != nil {
		cdtDelta, realManualManipulationQuery, lcErr = c.manualManipulationDao.QueryManualManipulationUsingCache(ctx, manualManipulationQuerys)
	}
	return cdtDelta, realManualManipulationQuery, lcErr
}

// cdtManualManipulate 对CDT进行手动修正
func (c *CdtCalculationService) cdtManualManipulate(ctx utils.LCOSContext, objectType uint8, cdtInfo *cdt_calculation.CdtInfo, productId, laneCode string, laneCodeFlag bool, updateEvent uint8, isSiteLine uint8, isLM uint8, isCB uint8,
	region string, tplUniqueKey string, seller, buyer *cdt_calculation.AddressInfo, dataSource uint8, equalLevel bool, requestTime uint32, cdtScene pb.CdtScene) *lcos_error.LCOSError {
	midResult := cdtInfo.CdtProcess
	if midResult == nil {
		midResult = &cdt_calculation.CdtProcess{}
	}
	cdtDelta, realManualManipulationQuery, lcErr := c.getCdtManualManipulation(ctx, objectType, productId, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB,
		region, tplUniqueKey, seller, buyer, dataSource, equalLevel, requestTime, cdtScene)
	//logger.CtxLogInfof(ctx, "realManualManipulationQuery: %s", utils.MarshToStringWithoutError(realManualManipulationQuery))
	if lcErr == nil && cdtDelta != nil {
		// calculate cdt with manual manipulation rule
		midResult.SetManualManipulationQuery(realManualManipulationQuery)
		midResult.SetManualManipulationProcess(cdtDelta)
		if cdtInfo.LeadTimeMin != nil && cdtInfo.LeadTimeMax != nil && cdtDelta.CdtMinDelta != nil && cdtDelta.CdtMaxDelta != nil {
			*cdtInfo.LeadTimeMax += *cdtDelta.CdtMaxDelta
			*cdtInfo.LeadTimeMin += *cdtDelta.CdtMinDelta
			if *cdtInfo.LeadTimeMax < 0 {
				*cdtInfo.LeadTimeMax = 0
			}
			if *cdtInfo.LeadTimeMin < 0 {
				*cdtInfo.LeadTimeMin = 0
			}
			if *cdtInfo.LeadTimeMin > *cdtInfo.LeadTimeMax {
				*cdtInfo.LeadTimeMax = *cdtInfo.LeadTimeMin
			}
		} else {
			logger.CtxLogErrorf(ctx, "cdtInfo.LeadTimeMin[%v]|cdtInfo.LeadTimeMax[%v]|cdtDelta.CdtMinDelta[%v]|cdtDelta.CdtMaxDelta[%v] is nil", cdtInfo.LeadTimeMin, cdtInfo.LeadTimeMax, cdtDelta.CdtMinDelta, cdtDelta.CdtMaxDelta)
		}
	}
	// approximate rounding & extreme value check
	cdtMaximum := config.GetFloat64WithContext(ctx, constant.CdtMaximum, 100.0)
	if cdtInfo.LeadTimeMax != nil {
		*cdtInfo.LeadTimeMax, _ = strconv.ParseFloat(fmt.Sprintf("%.4f", *cdtInfo.LeadTimeMax), 64)
		if *cdtInfo.LeadTimeMax > cdtMaximum {
			_ = monitor.AwesomeReportEvent(ctx, constant.CdtOverMaximum, productId, constant.StatusError, "error")
		}
		if *cdtInfo.LeadTimeMax == 0.0 {
			_ = monitor.AwesomeReportEvent(ctx, constant.CdtEqualZero, productId, constant.StatusError, "error")
		}
	}
	if cdtInfo.LeadTimeMin != nil {
		*cdtInfo.LeadTimeMin, _ = strconv.ParseFloat(fmt.Sprintf("%.4f", *cdtInfo.LeadTimeMin), 64)
		if *cdtInfo.LeadTimeMin > cdtMaximum {
			_ = monitor.AwesomeReportEvent(ctx, constant.CdtOverMaximum, productId, constant.StatusError, "error")
		}
		if *cdtInfo.LeadTimeMin == 0.0 {
			_ = monitor.AwesomeReportEvent(ctx, constant.CdtEqualZero, productId, constant.StatusError, "error")
		}
	}
	cdtInfo.CdtProcess = midResult
	return nil
}

// GetAutoUpdateCdtInfoByProduct 获取自动更新CDT
func (c *CdtCalculationService) GetAutoUpdateCdtInfoByProduct(ctx utils.LCOSContext, objectType uint8, productId string, isSiteLine uint8, isLM uint8, isCB uint8, region string, tplUniqueKey string, laneCode string, updateEvent uint8, sellerAddr, buyerAddr *cdt_calculation.AddressInfo, dataSource uint8, groupTag, startTimeType uint8) (*cdt_calculation.CdtInfo, *lcos_error.LCOSError) {
	laneCodeFlag := len(laneCode) > 0 // 是否匹配lane维度数据

	// 1. 查找自动更新规则
	var rule *auto_update_rule.CDTAutoUpdateRuleTab
	var err *lcos_error.LCOSError
	_, isGrayMachine := os.LookupEnv(graymachine.ENV_RULE_GRAY_MACHINE_FLAG_KEY)
	if isGrayMachine {
		// 若为灰度机器，检查是否存在可用的即将生效的自动计算规则
		rule, err = c.autoUpdateDao.SearchIncomingAutoUpdateRuleUsingCacheForGrayTest(ctx, productId, isSiteLine, isLM, objectType, groupTag, startTimeType)
	}
	if err != nil || rule == nil {
		// 若为非灰度机器，或者灰度机器无法找到即将生效的自动规则，则查找处于生效状态的规则
		rule, err = c.autoUpdateDao.SearchActiveAutoUpdateRuleUsingCache(ctx, productId, isSiteLine, isLM, objectType, groupTag, startTimeType)
	}
	if err != nil || rule == nil {
		// 无法匹配到自动更新规则
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "auto update rule not found")
	}

	// 2. 生成cdt匹配条件
	queries, err := c.getQuery(ctx, objectType, productId, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, tplUniqueKey, sellerAddr, buyerAddr, region, int(rule.OriginLocationLevel), int(rule.DestinationLocationLevel), false, dataSource, constant.NotNeedEqualLevels)
	if err != nil {
		return nil, err
	}
	if laneCodeFlag {
		productQueries, err := c.getQuery(ctx, objectType, productId, "", false, updateEvent, isSiteLine, isLM, isCB, tplUniqueKey, sellerAddr, buyerAddr, region, int(rule.OriginLocationLevel), int(rule.DestinationLocationLevel), false, dataSource, constant.NotNeedEqualLevels)
		if err != nil {
			return nil, err
		}
		queries = append(queries, productQueries...)
	}

	// 3. 根据地址信息兜底匹配cdt
	cdtVersion, err := c.autoUpdateDataDao.GetCdtVersionByAutoUpdateRuleIDUsingCache(ctx, rule.ID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "cannot find cdt version|auto_update_rule=%d, error=%v", rule.ID, err.Msg)
		return nil, err
	}
	cdtInfos, _, err := c.autoUpdateDataDao.GetCdtInfoByCdtVersionUsingCache(ctx, rule, cdtVersion, queries, constant.NotNeedEqualLevels)
	if err != nil {
		logger.CtxLogErrorf(ctx, "query auto update failed|auto_update_id=%d, error=%v", rule.ID, err.Msg)
		return nil, err
	}
	if len(cdtInfos) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cannot find auto update cdt info")
	}
	return cdtInfos[0], nil
}

// GetCourierDeliveryTimeByProduct
// add cdt process to record mid-result
func (c *CdtCalculationService) GetCourierDeliveryTimeByProduct(ctx utils.LCOSContext, objectType uint8, productID string, isSiteLine uint8, isLM uint8, isCB uint8, region string, tplUniqueKey string, laneCode string, updateEvent uint8, sellerAddr, buyerAdd *cdt_calculation.AddressInfo, skuInfo *cdt_calculation.CdtSkuInfo, dataSource uint8,
	groupTag uint8, skipAutoUpdateRule bool, needEqualLevels bool, startTimeType uint8, requestTime uint32, cdtScene pb.CdtScene) (returnedCdtInfo []*cdt_calculation.CdtInfo, cdtErr *lcos_error.LCOSError) {
	// 1. 查询是否存在有效的自动计算规则
	// 2. 判断是否使用第三方服务获取CDT (目前只有BR市场会有这样的逻辑)
	// 3. 没有自动规则或者不需要使用第三方服务获取CDT的话需要获取手动上传规则
	// 4. 最后需要使用手动修正规则修正
	var (
		errorCode    = "0"
		errorMessage = ""
	)
	defer func() {
		if !cf.GetTimeServiceReport(ctx) {
			return
		}
		if cdtErr != nil {
			errorCode = fmt.Sprintf("%d", cdtErr.RetCode)
			errorMessage = cdtErr.Msg
		}
		updateEventString, _ := edd_constant.GetUpdateEventString(updateEvent)
		_ = metrics.CounterIncr(constant.MetricTimeServiceFunc, map[string]string{
			"func":          constant.GetCourierDeliveryTimeByProduct,
			"region":        strings.ToLower(region),
			"product_id":    productID,
			"update_event":  updateEventString,
			"lane_code":     laneCode,
			"error_code":    errorCode,
			"error_message": errorMessage,
		})
	}()

	var laneCodeFlag bool // 是否匹配lane维度数据
	if objectType == edd_constant.LeadTimeObject && len(laneCode) > 0 {
		// 只有EDD场景（leadtime）且传了lane code才可以匹配lane维度数据
		laneCodeFlag = true
	}

	var cdtInfo *cdt_calculation.CdtInfo
	var cdtInfos []*cdt_calculation.CdtInfo

	var lcosErr *lcos_error.LCOSError

	var queries []*cdt_calculation.CdtQuery

	midResult := &cdt_calculation.CdtProcess{}
	var realCdtQuery *cdt_calculation.CdtQuery     // record which query is really is use for cdt
	var realCdtQueries []*cdt_calculation.CdtQuery // records which query is really is use for cdt

	var autoRule *auto_update_rule.CDTAutoUpdateRuleTab

	// 若为灰度机器，检查是否存在可用的即将生效的自动计算规则
	_, isGrayMachine := os.LookupEnv(graymachine.ENV_RULE_GRAY_MACHINE_FLAG_KEY)
	if isGrayMachine && !skipAutoUpdateRule {
		autoRule, lcosErr = c.autoUpdateDao.SearchIncomingAutoUpdateRuleUsingCacheForGrayTest(ctx, productID, isSiteLine, isLM, objectType, groupTag, startTimeType)
	}

	//没有即将生效的规则的灰度机器或非灰度机器查询生效中的规则
	if autoRule == nil && !skipAutoUpdateRule {
		autoRule, lcosErr = c.autoUpdateDao.SearchActiveAutoUpdateRuleUsingCache(ctx, productID, isSiteLine, isLM, objectType, groupTag, startTimeType)
	}

	if autoRule == nil || lcosErr != nil {
		// check if need get cdt from thirdParty service
		if !cf.GetConf(ctx).Cdt.IsThirdPartyServiceDowngrade() && skuInfo != nil && productInWhiteList(productID, cf.GetConf(ctx).Cdt.GetThirdPartyCdtWhiteList()) {
			pisClient := pis_service.NewPISService(ctx, region)
			req := convertGetQuotationReq(productID, sellerAddr, buyerAdd, skuInfo)
			resp, err := pisClient.GetQuotation(ctx, ctx.GetRequestId(), req)
			if err != nil {
				logger.CtxLogErrorf(ctx, "Get cdt_info from third_party service failed, err:%v", err)
			} else {
				returnedCdt := float64(resp.DeliveryEstimateTransitTimeBusinessDays + resp.DeliveryAdditionalTransitTimeBusinessDays)
				cdtInfo = &cdt_calculation.CdtInfo{
					ProductId:   productID,
					IsSiteLine:  isSiteLine,
					LeadTimeMin: utils.NewFloat64(returnedCdt),
					LeadTimeMax: utils.NewFloat64(returnedCdt), // SPLN-30962, make edt min same as edt max
				}
			}
		}

		if cdtInfo == nil {
			// try to find manual update data
			cdtInfos, realCdtQueries, laneCodeFlag, lcosErr = c.getManualUpdateCdtInfo(ctx, objectType, productID, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, tplUniqueKey, sellerAddr, buyerAdd, region, dataSource, needEqualLevels)
			if lcosErr != nil {
				return nil, lcosErr
			}

			if len(cdtInfos) > 0 && !needEqualLevels {
				cdtInfo = cdtInfos[0]
				realCdtQuery = realCdtQueries[0]
				cdtInfo.SetIgnoreLeadTimeMinFlag(objectType, cdtInfo.GetLeadTimeMin() < 0)

				// SPLN-23295 fill manual update info
				midResult.SetCdtQuery(realCdtQuery)
				midResult.SetManualUpdateProcess(cdtInfo)
				midResult.GroupTag = groupTag
				logger.CtxLogInfof(ctx, "successfully find manual update cdt info|cdt_query=[%s]|reply=[%s]", utils.MarshToStringWithoutError(realCdtQuery), utils.MarshToStringWithoutError(cdtInfo))
			}
		}
	} else {
		// get queries by location level
		queries, lcosErr = c.getQuery(ctx, objectType, productID, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, tplUniqueKey, sellerAddr, buyerAdd, region, int(autoRule.OriginLocationLevel), int(autoRule.DestinationLocationLevel), false, dataSource, needEqualLevels)
		if lcosErr != nil {
			return nil, lcosErr
		}
		// for lane auto update cdt, need to add lane code empty query
		if laneCodeFlag {
			productQueries, lcosErr := c.getQuery(ctx, objectType, productID, "", false, updateEvent, isSiteLine, isLM, isCB, tplUniqueKey, sellerAddr, buyerAdd, region, int(autoRule.OriginLocationLevel), int(autoRule.DestinationLocationLevel), false, dataSource, needEqualLevels)
			if lcosErr != nil {
				return nil, lcosErr
			}
			queries = append(queries, productQueries...)
		}
		//logger.CtxLogInfof(ctx, "generated auto update query:[%v]", utils.MarshToStringWithoutError(queries)) // ignore error

		// 找到了有效的自动规则，需要计算其cdt
		cdtVersion, lcosErr := c.autoUpdateDataDao.GetCdtVersionByAutoUpdateRuleIDUsingCache(ctx, autoRule.ID)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "cannot find cdt version|auto_update_rule=%d, error=%v", autoRule.ID, lcosErr.Msg)
			return nil, lcosErr
		}
		cdtInfos, realCdtQueries, lcosErr = c.autoUpdateDataDao.GetCdtInfoByCdtVersionUsingCache(ctx, autoRule, cdtVersion, queries, needEqualLevels)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "query auto update failed|auto_update_id=%d, error=%v", autoRule.ID, lcosErr.Msg)
			return nil, lcosErr
		}

		if len(cdtInfos) > 0 && !needEqualLevels {
			cdtInfo = cdtInfos[0]
			realCdtQuery = realCdtQueries[0]
			cdtInfo.SetIgnoreLeadTimeMinFlag(objectType, autoRule.CalculateEddMin == constant.FALSE)
			cdtInfo.EddRangeLimit = int64(autoRule.EddRangeLimit)

			// SPLN-23295 fill auto update info
			midResult.SetCdtQuery(realCdtQuery)
			midResult.SetAutoUpdateProcess(cdtInfo, autoRule.ID, cdtVersion)
			midResult.GroupTag = groupTag
			logger.CtxLogInfof(ctx, "successfully find auto update cdt info|cdt_query=[%s], auto_rule_id=%d, auto_update_version=%v|reply=[%s]", utils.MarshToStringWithoutError(realCdtQuery), autoRule.ID, cdtVersion, utils.MarshToStringWithoutError(cdtInfo))
		}
	}

	if cdtInfo != nil {
		cdtInfo.CdtProcess = midResult
	}

	//对手动上传或者自动规则的结果进行修正
	if needEqualLevels {
		for i, oriCdtInfo := range cdtInfos {
			if err := c.cdtManualManipulate(ctx, objectType, oriCdtInfo, productID, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, region, tplUniqueKey, sellerAddr, buyerAdd, dataSource, constant.NotNeedEqualLevels, requestTime, cdtScene); err != nil {
				errMsg := fmt.Sprintf("cdt manual manipulate error: %s", err.Msg)
				logger.CtxLogErrorf(ctx, errMsg)
				return nil, lcos_error.NewLCOSError(err.RetCode, errMsg)
			}
			if oriCdtInfo != nil && oriCdtInfo.CdtProcess != nil {
				oriCdtInfo.CdtProcess.CdtQueryInfo = realCdtQueries[i]
			}
		}
	} else {
		if cdtInfo == nil {
			errMsg := fmt.Sprintf("query cdt info failed|product_id=%v, is_site_line=%v,is_lm=%v", productID, isSiteLine, isLM)
			logger.CtxLogInfof(ctx, "%s", "query cdt info failed")
			return nil, lcos_error.NewLCOSError(lcos_error.NotFoundCdtInfoErrorCode, errMsg)
		}
		if err := c.cdtManualManipulate(ctx, objectType, cdtInfo, productID, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, region, tplUniqueKey, sellerAddr, buyerAdd, dataSource, constant.NotNeedEqualLevels, requestTime, cdtScene); err != nil {
			errMsg := fmt.Sprintf("cdt manual manipulate error: %s", err.Msg)
			logger.CtxLogErrorf(ctx, errMsg)
			return nil, lcos_error.NewLCOSError(err.RetCode, errMsg)
		}
		cdtInfos = []*cdt_calculation.CdtInfo{cdtInfo}
	}
	return cdtInfos, nil
}

func (c *CdtCalculationService) getVolumeQueryForOneLevel(ctx utils.LCOSContext, productID string, fChannelID string, tmpOriginLocationLevel, tmpDestinationLocationLevel, tmpOriginLocationID, tmpDestinationLocationID int, dataSource uint8) *cdt_calculation.VolumeQuery {
	query := &cdt_calculation.VolumeQuery{
		ProductID:                productID,
		FChannelID:               fChannelID,
		OriginLocationLevel:      int8(tmpOriginLocationLevel),
		OriginLocationId:         tmpOriginLocationID,
		DestinationLocationLevel: int8(tmpDestinationLocationLevel),
		DestinationLocationId:    utils.NewInt(tmpDestinationLocationID),
		VolumeDataSource:         dataSource,
	}
	return query
}

func (c *CdtCalculationService) GetMChannelCDTAndVolume(ctx utils.LCOSContext, objectType uint8, productInfo *cdt_calculation.CdtProductInfo, dataSource uint8) (*cdt_calculation.CdtReply, *lcos_error.LCOSError) {
	cdtReply := &cdt_calculation.CdtReply{}
	fchannelCdtAndVolumes := make([]cdt_calculation.FChannelCdtAndVolume, 0, len(productInfo.FChannelList))
	fChannelVolumeInfosMap := make(map[string][]*cdt_calculation.VolumeWithLocationLevel)
	var volumelcosErr *lcos_error.LCOSError
	needVolume := productInfo.NeedVolume == constant.NeedVolume
	if needVolume {
		//SPLN-31544 需要计算单量
		if cf.GetEnv(ctx) == cf.LIVE && strings.ToUpper("grpclivetest") == cf.GetModuleName() {
			_ = monitor.AwesomeReportEvent(ctx, constant.AggregateCdtGetInfoPDP, constant.CalcNeedVolume, constant.StatusSuccess, "")
		}
		fChannelVolumeInfosMap, volumelcosErr = c.getFChannelVolume(ctx, productInfo, dataSource)
	}
	//计算m-channel下 每个f-channel的cdt
	for _, fChannel := range productInfo.FChannelList {
		// 1. 匹配AB测试分组
		abTestRule, _ := c.abTestService.GetActiveAbTestRuleByProductIdUsingCache(ctx, fChannel.FChannelID, objectType)
		hitGroupTag, skipAutoUpdateRule := getAbTestResult(productInfo.GroupTag, abTestRule)

		fchannelCdtAndVolume := cdt_calculation.FChannelCdtAndVolume{}
		//对于每个fchannel 循环查找所有level的cdt信息
		fChannelCdtInfos, lcosErr := c.GetCourierDeliveryTimeByProduct(ctx, objectType, fChannel.FChannelID, fChannel.IsSiteLine,
			0, fChannel.IsCB, productInfo.Region, "", productInfo.LaneCode, productInfo.UpdateEvent, productInfo.SellerAddr, productInfo.BuyerAddr, productInfo.SkuInfo, dataSource, hitGroupTag, skipAutoUpdateRule, constant.NeedEqualLevels, productInfo.StartFCodeType, productInfo.RequestTime, productInfo.CdtScene)
		if lcosErr != nil {
			if lcosErr.RetCode != lcos_error.NotFoundCDTRuleErrorCode && lcosErr.RetCode != lcos_error.NotFoundManualUpdateDataErrorCode {
				return nil, lcosErr
			}
			fchannelCdtAndVolume.Retcode = lcosErr.RetCode
			fchannelCdtAndVolume.Message = lcosErr.Msg
		} else {
			var cdtLocations []cdt_calculation.CdtWithLocationLevel
			for _, fChannelCdtInfo := range fChannelCdtInfos {
				cdtLocation := cdt_calculation.CdtWithLocationLevel{
					CdtMax:              fChannelCdtInfo.GetLeadTimeMax(),
					CdtMin:              fChannelCdtInfo.GetLeadTimeMin(),
					OriginLocationLevel: int(fChannelCdtInfo.GetCdtProcess().CdtQueryInfo.OriginLocationLevel),
					DestLocationLevel:   int(fChannelCdtInfo.GetCdtProcess().CdtQueryInfo.DestinationLocationLevel),
					CdtExtraData:        fChannelCdtInfo.GetCdtExtraData(),
					CdtProcess:          fChannelCdtInfo.GetCdtProcess(),
				}
				cdtLocations = append(cdtLocations, cdtLocation)
			}
			fchannelCdtAndVolume.CdtInfos = cdtLocations
		}
		//fchannel cdt计算完后拼接单量信息
		fchannelCdtAndVolume.FChannelID = fChannel.FChannelID
		if fChannelVolumeInfos, ok := fChannelVolumeInfosMap[fChannel.FChannelID]; ok {
			var volumeLocations []cdt_calculation.VolumeWithLocationLevel
			for _, fChannelVolumeInfo := range fChannelVolumeInfos {
				volumeLocation := cdt_calculation.VolumeWithLocationLevel{
					Volume:              fChannelVolumeInfo.Volume,
					OriginLocationLevel: fChannelVolumeInfo.OriginLocationLevel,
					DestLocationLevel:   fChannelVolumeInfo.DestLocationLevel,
				}
				volumeLocations = append(volumeLocations, volumeLocation)
			}
			fchannelCdtAndVolume.VolumeInfos = volumeLocations
		} else if needVolume {
			fchannelCdtAndVolume.Retcode = lcos_error.NotFoundFChannelVolumeErrorCode
			fchannelCdtAndVolume.Message += " need volume but f-channel can't find volume"
		} else if volumelcosErr != nil {
			fchannelCdtAndVolume.Retcode = volumelcosErr.RetCode
			fchannelCdtAndVolume.Message += volumelcosErr.Msg
		}
		fchannelCdtAndVolumes = append(fchannelCdtAndVolumes, fchannelCdtAndVolume)
	}

	cdtReply.FchannelCdtAndVolumes = fchannelCdtAndVolumes
	return cdtReply, nil
}

func (c *CdtCalculationService) getFChannelVolumeQueries(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, dataSource uint8) ([]*cdt_calculation.VolumeQuery, *lcos_error.LCOSError) {
	if err := checkIsValidAddress(productInfo.SellerAddr); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "seller address is not valid")
	}
	if err := checkIsValidAddress(productInfo.BuyerAddr); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "buyer address is not valid")
	}

	_, tmpOriginLocationLevel := getLowestLevelLocation(productInfo.SellerAddr)
	_, tmpDestinationLocationLevel := getLowestLevelLocation(productInfo.BuyerAddr)
	tmpOriginLocationID, tmpDestinationLocationID := getLocationByLevel(productInfo.SellerAddr, tmpOriginLocationLevel), getLocationByLevel(productInfo.BuyerAddr, tmpDestinationLocationLevel)
	var volumeQueries []*cdt_calculation.VolumeQuery

	for loop := 0; loop < constant.MaxVolumeLoop; loop++ {
		volumeQuery := c.getVolumeQueryForOneLevel(ctx, productInfo.MChannelID, productInfo.ProductID, tmpOriginLocationLevel, tmpDestinationLocationLevel, tmpOriginLocationID, tmpDestinationLocationID, dataSource)
		//查询所有f-channel在同一等级的结果
		volumeQueries = append(volumeQueries, volumeQuery)
		if tmpOriginLocationLevel == tmpDestinationLocationLevel && tmpDestinationLocationLevel == int(constant.Country) {
			//兜底到最高层级 跳出
			break
		}

		//地址向上兜底
		if tmpOriginLocationLevel != tmpDestinationLocationLevel {
			minLevel := tmpOriginLocationLevel
			if tmpOriginLocationLevel > tmpDestinationLocationLevel {
				minLevel = tmpDestinationLocationLevel
			}
			tmpOriginLocationLevel = minLevel
			tmpDestinationLocationLevel = minLevel
			tmpOriginLocationID = getLocationByLevel(productInfo.SellerAddr, tmpOriginLocationLevel)
			tmpDestinationLocationID = getLocationByLevel(productInfo.BuyerAddr, tmpDestinationLocationLevel)
		} else {
			tmpOriginLocationID, tmpOriginLocationLevel = getUpperLevelLocation(tmpOriginLocationLevel, productInfo.SellerAddr)
			tmpDestinationLocationID, tmpDestinationLocationLevel = getUpperLevelLocation(tmpDestinationLocationLevel, productInfo.BuyerAddr)
		}
	}
	return volumeQueries, nil
}

func (c *CdtCalculationService) getFChannelVolume(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, dataSource uint8) (map[string][]*cdt_calculation.VolumeWithLocationLevel, *lcos_error.LCOSError) {
	//统计该m-channel下所有f-channel的单量，再根据规则计算cdt
	//1. 查找生效的单量规则
	//2. 拿到该条生效rule对应的最新版本
	//3. 查找每个f-channel 对应的accurate location层级和所有兜底层级的volume 信息
	volumeRule, err := c.automatedVolumeRuleDao.SearchActiveAutomatedVolumeGenerationRuleByMChannelUsingCache(ctx, productInfo.ProductID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "cannot find volume rule|product_id=%d, error=%v", productInfo.ProductID, err.Msg)
		return nil, err
	}
	//2. 拿到该条生效rule对应的最新版本
	volumeVersion, err := c.automatedVolumeGenerationDataDao.GetVolumeVersionByAutoUpdateRuleIDUsingCache(ctx, volumeRule.Id)
	if err != nil {
		logger.CtxLogErrorf(ctx, "cannot find volume version|auto_volume_rule=%d, error=%v", volumeRule.Id, err.Msg)
		return nil, err
	}
	// 检查传入的addr有效
	err1 := checkIsValidAddress(productInfo.SellerAddr)
	err2 := checkIsValidAddress(productInfo.BuyerAddr)
	if err1 != nil || err2 != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "address is not valid")
	}
	_, tmpOriginLocationLevel := getLowestLevelLocation(productInfo.SellerAddr)
	_, tmpDestinationLocationLevel := getLowestLevelLocation(productInfo.BuyerAddr)
	tmpOriginLocationID, tmpDestinationLocationID := getLocationByLevel(productInfo.SellerAddr, tmpOriginLocationLevel), getLocationByLevel(productInfo.BuyerAddr, tmpDestinationLocationLevel)

	//3. 计算每个f-channel的地址兜底volume
	fchannelVolumeInfoMap := make(map[string][]*cdt_calculation.VolumeWithLocationLevel)
	for loop := 0; loop < 5; loop++ {
		var volumeQueries []*cdt_calculation.VolumeQuery
		//3.1 根据地址信息兜底
		for _, fChannel := range productInfo.FChannelList {
			volumeQuery := c.getVolumeQueryForOneLevel(ctx, productInfo.ProductID, fChannel.FChannelID, tmpOriginLocationLevel, tmpDestinationLocationLevel, tmpOriginLocationID, tmpDestinationLocationID, dataSource)
			//查询所有f-channel在同一等级的结果
			volumeQueries = append(volumeQueries, volumeQuery)
		}
		//3.2 匹配volume 多个fchannel在同一层级的结果
		volumeInfos, err := c.automatedVolumeGenerationDataDao.GetVolumeInfosByVolumeVersionUsingCache(ctx, volumeRule, volumeVersion, volumeQueries)
		if err != nil {
			logger.CtxLogErrorf(ctx, "query auto volume failed|auto_volume_id=%d, error=%v", volumeRule.Id, err.Msg)
			continue
		}

		for fChannel, volumeInfo := range volumeInfos {
			nowVolumeInfo := &cdt_calculation.VolumeWithLocationLevel{
				Volume:              volumeInfo.Volume,
				OriginLocationLevel: tmpOriginLocationLevel,
				DestLocationLevel:   tmpDestinationLocationLevel,
			}
			fchannelVolumeInfoMap[fChannel] = append(fchannelVolumeInfoMap[fChannel], nowVolumeInfo)
		}
		if tmpOriginLocationLevel == tmpDestinationLocationLevel && tmpDestinationLocationLevel == int(constant.Country) {
			//兜底到最高层级 跳出
			break
		}

		//地址向上兜底
		if tmpOriginLocationLevel != tmpDestinationLocationLevel {
			minLevel := tmpOriginLocationLevel
			if tmpOriginLocationLevel > tmpDestinationLocationLevel {
				minLevel = tmpDestinationLocationLevel
			}
			tmpOriginLocationLevel = minLevel
			tmpDestinationLocationLevel = minLevel
			tmpOriginLocationID = getLocationByLevel(productInfo.SellerAddr, tmpOriginLocationLevel)
			tmpDestinationLocationID = getLocationByLevel(productInfo.BuyerAddr, tmpDestinationLocationLevel)
		} else {
			tmpOriginLocationID, tmpOriginLocationLevel = getUpperLevelLocation(tmpOriginLocationLevel, productInfo.SellerAddr)
			tmpDestinationLocationID, tmpDestinationLocationLevel = getUpperLevelLocation(tmpDestinationLocationLevel, productInfo.BuyerAddr)
		}
	}
	return fchannelVolumeInfoMap, nil
}

func (c *CdtCalculationService) BatchGetCdtInfoByProducts(ctx utils.LCOSContext, objectType uint8, productsInfos []*cdt_calculation.CdtProductInfo, dataSource uint8) ([]*cdt_calculation.CdtReply, *lcos_error.LCOSError) {
	var results []*cdt_calculation.CdtReply
	for _, productInfo := range productsInfos {
		cdtReply := &cdt_calculation.CdtReply{
			QueryId: productInfo.QueryID,
		}
		var lcosErr *lcos_error.LCOSError
		cdtInfo := &cdt_calculation.CdtInfo{}
		mChannelCDT := &cdt_calculation.CdtReply{}

		if len(productInfo.FChannelList) != 0 {
			//SPLN-31544 需要计算Fchannel 的cdt 信息
			if cf.GetEnv(ctx) == cf.LIVE && strings.ToUpper("grpclivetest") == cf.GetModuleName() {
				_ = monitor.AwesomeReportEvent(ctx, constant.AggregateCdtGetInfoPDP, constant.CalcNeedMChannelCdt, constant.StatusSuccess, "")
			}
			mChannelCDT, lcosErr = c.GetMChannelCDTAndVolume(ctx, objectType, productInfo, dataSource)
		} else {
			if cf.GetEnv(ctx) == cf.LIVE && strings.ToUpper("grpclivetest") == cf.GetModuleName() {
				_ = monitor.AwesomeReportEvent(ctx, constant.AggregateCdtGetInfoPDP, constant.CalcOld, constant.StatusSuccess, "")
			}

			// 1. 匹配AB测试分组
			abTestRule, _ := c.abTestService.GetActiveAbTestRuleByProductIdUsingCache(ctx, productInfo.ProductID, objectType)
			hitGroupTag, skipAutoUpdateRule := getAbTestResult(productInfo.GroupTag, abTestRule)

			// 如果是非点线，需要检查是否可以通过3pl id获取line id
			var cdtInfos []*cdt_calculation.CdtInfo
			cdtInfos, lcosErr = c.GetCourierDeliveryTimeByProduct(ctx, objectType, productInfo.ProductID, productInfo.IsSiteLine,
				0, productInfo.IsCB, productInfo.Region, "", productInfo.LaneCode, productInfo.UpdateEvent, productInfo.SellerAddr, productInfo.BuyerAddr, productInfo.SkuInfo,
				dataSource, hitGroupTag, skipAutoUpdateRule, constant.NotNeedEqualLevels, productInfo.StartFCodeType, productInfo.RequestTime, productInfo.CdtScene)
			if lcosErr == nil && len(cdtInfos) > 0 {
				cdtInfo = cdtInfos[0]
			}
		}

		interfaceName := fmt.Sprintf("product_id:[%v], is_site_line:[%v]", productInfo.ProductID, productInfo.IsSiteLine)
		if lcosErr != nil {
			_ = metrics.CounterIncr(constant.MetricProductStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(lcosErr.RetCode)), "product": productInfo.ProductID})
			// SPLN-20433 增加cdt计算错误的日志
			logger.CtxLogErrorf(ctx, lcosErr.Msg)
			_ = monitor.AwesomeReportEvent(ctx, constant.CdtGetInfo, interfaceName, constant.StatusError, lcosErr.Msg)
			cdtReply.Error = lcosErr
		} else {
			_ = metrics.CounterIncr(constant.MetricProductStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(0), "product": productInfo.ProductID})
			_ = monitor.AwesomeReportEvent(ctx, constant.CdtGetInfo, interfaceName, constant.StatusSuccess, "success")
			cdtReply.IgnoreCdtMin = cdtInfo.IgnoreLeadTimeMin()
			cdtReply.EddRangeLimit = cdtInfo.GetEddRangeLimit()
			cdtReply.CdtMax = cdtInfo.GetLeadTimeMax()
			cdtReply.CdtMin = cdtInfo.GetLeadTimeMin()
			cdtReply.CdtProcess = cdtInfo.GetCdtProcess()
			cdtReply.FchannelCdtAndVolumes = mChannelCDT.FchannelCdtAndVolumes
			cdtReply.CdtExtraData = cdtInfo.GetCdtExtraData()
			// report cdt min and cdt max
			_ = metrics.GaugeSet(constant.MetricsCDTMaxReport, cdtReply.CdtMax, map[string]string{"product_id": productInfo.ProductID, "region": productInfo.Region})
			_ = metrics.GaugeSet(constant.MetricsCDTMinReport, cdtReply.CdtMin, map[string]string{"product_id": productInfo.ProductID, "region": productInfo.Region})
		}
		results = append(results, cdtReply)
	}
	return results, nil
}

const (
	checkoutCdtSwitchCompare = 1
	checkoutCdtSwitchGrey    = 2
	checkoutCdtSwitchFull    = 3
)

func (c *CdtCalculationService) BatchGetCdtInfoByProductsWithGreySwitch(ctx utils.LCOSContext, objectType uint8, productsInfos []*cdt_calculation.CdtProductInfo, dataSource uint8) ([]*cdt_calculation.CdtReply, *lcos_error.LCOSError) {
	greyConfig := apollo_config.GetCheckoutCdtSwitchDatasourceConfig(ctx)
	// greyConfig.SwitchMode: 切换模式
	// - 0: 不切换
	// - 1: 流量对比
	// - 2: 灰度切换
	// - 3: 全量切换
	// greyConfig.ComparePercentage: 流量对比百分比
	// greyConfig.SwitchPercentage: 流量切换百分比
	switch greyConfig.SwitchMode {
	case checkoutCdtSwitchFull:
		return c.BatchGetCdtInfoByProductsWithConcurrency(ctx, objectType, productsInfos)
	case checkoutCdtSwitchGrey:
		if utils.CheckPercent(ctx, greyConfig.SwitchPercentage) {
			_ = monitor.AwesomeReportEvent(ctx, "CheckoutCdtDatasourceSwitch", "GreySwitchCodis", constant.StatusSuccess, "")
			return c.BatchGetCdtInfoByProductsWithConcurrency(ctx, objectType, productsInfos)
		} else {
			_ = monitor.AwesomeReportEvent(ctx, "CheckoutCdtDatasourceSwitch", "GreySwitchDB", constant.StatusSuccess, "")
			return c.BatchGetCdtInfoByProducts(ctx, objectType, productsInfos, dataSource)
		}
	case checkoutCdtSwitchCompare:
		retV1, errV1 := c.BatchGetCdtInfoByProducts(ctx, objectType, productsInfos, dataSource)
		if utils.CheckPercent(ctx, greyConfig.ComparePercentage) {
			retV2, errV2 := c.BatchGetCdtInfoByProductsWithConcurrency(ctx, objectType, productsInfos)
			// 对比和上报结果差异
			if compareCdtReplyDiff(retV1, errV1, retV2, errV2) {
				diff := fmt.Sprintf("retV1: %s\nerrV1: %s\n\nretV2: %s\nerrV2: %s", utils.MarshToStringWithoutError(retV1), utils.MarshToStringWithoutError(errV1), utils.MarshToStringWithoutError(retV2), utils.MarshToStringWithoutError(errV2))
				logger.CtxLogErrorf(ctx, "checkout cdt switch datasource compare diff: %s", diff)
				_ = monitor.AwesomeReportEvent(ctx, "CheckoutCdtDatasourceSwitch", "Compare", constant.StatusError, diff)
			} else {
				_ = monitor.AwesomeReportEvent(ctx, "CheckoutCdtDatasourceSwitch", "Compare", constant.StatusSuccess, "")
			}
		}
		return retV1, errV1
	default:
		return c.BatchGetCdtInfoByProducts(ctx, objectType, productsInfos, dataSource)
	}
}

func (c *CdtCalculationService) BatchGetCdtInfoByProductsWithConcurrency(ctx utils.LCOSContext, objectType uint8, productInfoList []*cdt_calculation.CdtProductInfo) ([]*cdt_calculation.CdtReply, *lcos_error.LCOSError) {
	// 1. mchannel 参数转换为fchannel
	productInfoListWithFChannels, needVolumeProductInfoList := c.preMChannelHandle(ctx, productInfoList)

	// 2. 处理fchannel cdt
	cdtReplies, lcosErr := c.MultiGetCdtInfoByProducts(ctx, objectType, productInfoListWithFChannels)
	if cdtReplies == nil {
		return nil, lcosErr
	}

	fChannelVolumeInfosMap, _ := c.MultiGetVolumeInfoByProductsWithConcurrency(ctx, needVolumeProductInfoList)

	//3. 映射回mchannel
	return c.postMChannelHandle(ctx, cdtReplies, fChannelVolumeInfosMap)

}

func (c *CdtCalculationService) postMChannelHandle(ctx utils.LCOSContext, cdtReplies []*cdt_calculation.CdtReply, fChannelVolumeInfosMap map[string]map[string][]cdt_calculation.VolumeWithLocationLevel) ([]*cdt_calculation.CdtReply, *lcos_error.LCOSError) {
	var (
		mchannelResultMap = make(map[string]*cdt_calculation.CdtReply) //key为(queryid + mchannel) value为对应的所有的fchannel的cdt和volume
		returnCdtReplies  []*cdt_calculation.CdtReply
	)

	for _, cdtReply := range cdtReplies {
		if cdtReply.NeedEqualLevels != constant.NeedEqualLevels {
			mchannelResultMap[cdtReply.QueryIdCopy] = cdtReply
			continue
		}
		value, ok := mchannelResultMap[cdtReply.QueryIdCopy]
		if !ok {
			value = &cdt_calculation.CdtReply{
				QueryId: cdtReply.QueryId,
				ItemId:  cdtReply.ItemId,
			}
		}
		if value.Error != nil && value.Error.RetCode != lcos_error.NotFoundCDTRuleErrorCode && value.Error.RetCode != lcos_error.NotFoundManualUpdateDataErrorCode {
			continue
		}

		if cdtReply.Error != nil {
			if cdtReply.Error.RetCode != lcos_error.NotFoundCDTRuleErrorCode && cdtReply.Error.RetCode != lcos_error.NotFoundManualUpdateDataErrorCode {
				value.Error = &lcos_error.LCOSError{
					RetCode: cdtReply.Error.RetCode,
					Msg:     cdtReply.Error.Msg,
				}
			} else {
				//将fchannel的报错填回去 mchannel的FchannelCdtAndVolumes中
				fchannelCdtAndVolumeInfo := cdt_calculation.FChannelCdtAndVolume{
					Retcode:    cdtReply.Error.RetCode,
					Message:    cdtReply.Error.Msg,
					FChannelID: cdtReply.CdtProcess.CdtQueryInfo.ProductID,
				}
				if cdtReply.NeedVolume == constant.NeedVolume {
					fchannelCdtAndVolumeInfo.Retcode = lcos_error.NotFoundFChannelVolumeErrorCode
					fchannelCdtAndVolumeInfo.Message += " need volume but f-channel can't find volume"
				}
				value.FchannelCdtAndVolumes = append(value.FchannelCdtAndVolumes, fchannelCdtAndVolumeInfo)
			}
		} else {
			for _, fchannelCdtAndVolume := range cdtReply.FchannelCdtAndVolumes {
				subFchannelCdtAndVolume := getVolumeInfoList(cdtReply, fchannelCdtAndVolume, fChannelVolumeInfosMap)
				value.FchannelCdtAndVolumes = append(value.FchannelCdtAndVolumes, subFchannelCdtAndVolume)
			}
		}

		mchannelResultMap[cdtReply.QueryIdCopy] = value
	}

	for _, mchannelResult := range mchannelResultMap {
		returnCdtReplies = append(returnCdtReplies, mchannelResult)
	}

	return returnCdtReplies, nil
}

func (c *CdtCalculationService) MultiGetVolumeInfoByProductsWithConcurrency(ctx utils.LCOSContext, productInfoList []*cdt_calculation.CdtProductInfo) (map[string]map[string][]cdt_calculation.VolumeWithLocationLevel, *lcos_error.LCOSError) {
	var (
		volumeProcessList                = make([]*singleVolumeQueryProcess, 0)
		returnVolumeWithLocationLevelMap = make(map[string]map[string][]cdt_calculation.VolumeWithLocationLevel)
	)

	for _, singleProductInfo := range productInfoList { //处理每一个fchannel
		autoVolumeRule, volumeVersion, err := c.SingleGetVolumeRuleAndVersion(ctx, singleProductInfo)
		if err != nil {
			logger.CtxLogErrorf(ctx, "cant find volume rule or version err is[%v]", err)
		} else {
			volumeProcess := c.ConstructVolumeQueryProcess(ctx, singleProductInfo, constant.CdtQueryDataSourceCodis, autoVolumeRule, volumeVersion)
			// 放入单量列表，准备处理  一个quey对应一个process
			volumeProcessList = append(volumeProcessList, volumeProcess)
		}
	}

	c.batchGetVolumeWithEqualLevel(ctx, volumeProcessList)

	// 对批量结果做循环处理
	for _, volumeProcess := range volumeProcessList {
		mchannelVolumeKey := volumeProcess.QueryID + volumeProcess.Param.MChannelID
		fchannelVolumeMap := make(map[string][]cdt_calculation.VolumeWithLocationLevel)

		var volumeLocations []cdt_calculation.VolumeWithLocationLevel
		queryResult := &volumeProcess.Result
		// 填写结果值
		if queryResult.Err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.MetricsVolumeReport, "failed", constant.StatusError, queryResult.Err.Msg)
			logger.CtxLogErrorf(ctx, "get volume but error: %s", queryResult.Err)
			continue
		}

		for _, resultInfo := range queryResult.VolumeInfos {
			if resultInfo == nil {
				continue
			}

			volumeLocation := cdt_calculation.VolumeWithLocationLevel{
				Volume:              resultInfo.Volume,
				OriginLocationLevel: int(resultInfo.OriginLocationLevel),
				DestLocationLevel:   int(resultInfo.DestinationLocationLevel),
			}
			volumeLocations = append(volumeLocations, volumeLocation)
		}
		fchannelVolumeMap[volumeProcess.Param.ProductID] = volumeLocations

		if len(volumeLocations) == 0 {
			continue
		}
		//对于mchannel的返回 加上特殊的标识
		if _, ok := returnVolumeWithLocationLevelMap[mchannelVolumeKey]; ok {
			returnVolumeWithLocationLevelMap[mchannelVolumeKey][volumeProcess.Param.ProductID] = volumeLocations
			continue
		}
		returnVolumeWithLocationLevelMap[mchannelVolumeKey] = fchannelVolumeMap
	}

	return returnVolumeWithLocationLevelMap, nil
}

// MultiGetCdtInfoByProducts Auto/Manual都是MGet版本的GetCdtInfo
func (c *CdtCalculationService) MultiGetCdtInfoByProducts(ctx utils.LCOSContext, objectType uint8, productInfoList []*cdt_calculation.CdtProductInfo) ([]*cdt_calculation.CdtReply, *lcos_error.LCOSError) {
	var (
		results                 = make([]*cdt_calculation.CdtReply, 0, len(productInfoList)) // 对外返回的结果列表
		autoProcessList         = make([]*singleCdtQueryProcess, 0)
		fChannelAutoProcessList = make([]*singleCdtQueryProcess, 0)
		manualProcessList       = make([]*singleManualCdtQueryProcess, 0)
	)

	// 先区分开Manual/Auto，分别进行批量获取
	for _, singleProductInfo := range productInfoList {
		autoRule, lcosErr := c.SingleGetAutoRule(ctx, objectType, singleProductInfo.ProductID, singleProductInfo.IsSiteLine, 0, uint8(singleProductInfo.GroupTag), singleProductInfo.StartFCodeType)
		// 找不到auto rule的放进manual列表中进行处理
		if autoRule == nil || lcosErr != nil {
			logger.CtxLogInfof(ctx, "manual process|QueryId=[%s], ItemId=[%d]", singleProductInfo.QueryID, singleProductInfo.ItemId)
			manualProcessList = append(manualProcessList, c.ConstructManualCdtQueryProcess(singleProductInfo, objectType, constant.CdtQueryDataSourceCodis))
		} else {
			// 能找到auto rule的放到auto列表中处理
			logger.CtxLogInfof(ctx, "auto process|QueryId=[%s], ItemId=[%d]", singleProductInfo.QueryID, singleProductInfo.ItemId)
			cdtProcess := c.ConstructAutoCdtQueryProcess(ctx, singleProductInfo, objectType, constant.CdtQueryDataSourceCodis, autoRule)
			if singleProductInfo.NeedEqualLevel {
				// 放入f-channel列表，准备处理
				fChannelAutoProcessList = append(fChannelAutoProcessList, cdtProcess)
			} else {
				autoProcessList = append(autoProcessList, cdtProcess) // 放入列表，准备处理
			}
		}
	}

	// 以MGet的形式计算CDT
	c.batchHandleManualCdtProcess(ctx, manualProcessList)
	c.batchHandleAutoCdtProcess(ctx, autoProcessList)
	c.batchHandleCalcCdtWithEqualLevel(ctx, fChannelAutoProcessList)

	// 对批量结果做循环处理
	for _, process := range manualProcessList {
		results = append(results, c.constructManualCdtReply(process))
	}

	for _, process := range autoProcessList {
		results = append(results, c.constructAutoCdtReply(process))
	}

	for _, process := range fChannelAutoProcessList {
		results = append(results, c.constructFChannelAutoCdtReply(process))
	}

	return results, nil
}

func (c *CdtCalculationService) constructFChannelAutoCdtReply(p *singleCdtQueryProcess) *cdt_calculation.CdtReply {
	cdtReply := &cdt_calculation.CdtReply{
		QueryId:         p.QueryID,
		ItemId:          p.ItemId,
		QueryIdCopy:     p.QueryID,
		NeedVolume:      p.Param.NeedVolume,
		NeedEqualLevels: p.Param.needEqualLevel,
	}

	queryResult := &p.Result

	// 填写结果值
	if queryResult.Err != nil {
		cdtReply.Error = queryResult.Err
		cdtQuery := &cdt_calculation.CdtQuery{
			ProductID: p.Param.ProductID,
		}
		cdtReply.CdtProcess = &cdt_calculation.CdtProcess{
			CdtQueryInfo: cdtQuery,
		}
	} else {
		fchannelCdtAndVolume := cdt_calculation.FChannelCdtAndVolume{}
		var cdtLocations []cdt_calculation.CdtWithLocationLevel
		for _, resultInfo := range queryResult.CdtInfos {
			if resultInfo == nil {
				continue
			}
			cdtLocation := cdt_calculation.CdtWithLocationLevel{
				CdtMax:              resultInfo.GetLeadTimeMax(),
				CdtMin:              resultInfo.GetLeadTimeMin(),
				OriginLocationLevel: int(resultInfo.GetCdtProcess().CdtQueryInfo.OriginLocationLevel),
				DestLocationLevel:   int(resultInfo.GetCdtProcess().CdtQueryInfo.DestinationLocationLevel),
				CdtExtraData:        resultInfo.GetCdtExtraData(),
				CdtProcess:          resultInfo.GetCdtProcess(),
			}
			cdtLocations = append(cdtLocations, cdtLocation)
		}
		fchannelCdtAndVolume.CdtInfos = cdtLocations
		fchannelCdtAndVolume.FChannelID = p.Param.ProductID
		cdtReply.FchannelCdtAndVolumes = append(cdtReply.FchannelCdtAndVolumes, fchannelCdtAndVolume)
	}
	//对于mchannel的返回 加上特殊的标识
	cdtReply.QueryIdCopy += p.Param.MChannelID

	return cdtReply
}

func (c *CdtCalculationService) constructAutoCdtReply(p *singleCdtQueryProcess) *cdt_calculation.CdtReply {
	cdtReply := &cdt_calculation.CdtReply{
		QueryId:         p.QueryID,
		ItemId:          p.ItemId,
		QueryIdCopy:     p.QueryID,
		NeedEqualLevels: p.Param.needEqualLevel,
	}

	queryResult := &p.Result
	// 填写结果值
	if queryResult.Err != nil {
		cdtReply.Error = queryResult.Err
	} else {
		cdtReply.IgnoreCdtMin = queryResult.CdtInfo.IgnoreLeadTimeMin()
		cdtReply.EddRangeLimit = queryResult.CdtInfo.GetEddRangeLimit()
		cdtReply.CdtMax = queryResult.CdtInfo.GetLeadTimeMax()
		cdtReply.CdtMin = queryResult.CdtInfo.GetLeadTimeMin()
		cdtReply.CdtProcess = queryResult.CdtInfo.GetCdtProcess()
		cdtReply.CdtExtraData = queryResult.CdtInfo.GetCdtExtraData()
		cdtReply.CdtProcess = queryResult.CdtInfo.GetCdtProcess()
	}

	return cdtReply
}

func (c *CdtCalculationService) constructManualCdtReply(p *singleManualCdtQueryProcess) *cdt_calculation.CdtReply {
	cdtReply := &cdt_calculation.CdtReply{
		QueryId:         p.QueryID,
		ItemId:          p.ItemId,
		QueryIdCopy:     p.QueryID,
		NeedVolume:      p.Param.NeedVolume,
		NeedEqualLevels: p.Param.NeedEqualLevels,
	}

	// 填写结果值
	if p.Result.Err != nil {
		cdtReply.Error = p.Result.Err
		cdtQuery := &cdt_calculation.CdtQuery{
			ProductID: p.Param.ProductID,
		}
		cdtReply.CdtProcess = &cdt_calculation.CdtProcess{
			CdtQueryInfo: cdtQuery,
		}
	} else {
		if len(p.Result.CdtInfos) != 0 && !p.Param.NeedEqualLevels {
			//原有逻辑
			queryResultInfo := p.Result.CdtInfos[0]
			cdtReply.IgnoreCdtMin = queryResultInfo.IgnoreLeadTimeMin()
			cdtReply.EddRangeLimit = queryResultInfo.GetEddRangeLimit()
			cdtReply.CdtMax = queryResultInfo.GetLeadTimeMax()
			cdtReply.CdtMin = queryResultInfo.GetLeadTimeMin()
			cdtReply.CdtProcess = queryResultInfo.GetCdtProcess()
			cdtReply.CdtExtraData = queryResultInfo.GetCdtExtraData()
		} else if len(p.Result.CdtInfos) != 0 {
			//新逻辑
			fchannelCdtAndVolume := cdt_calculation.FChannelCdtAndVolume{}
			cdtLocations := make([]cdt_calculation.CdtWithLocationLevel, 0, len(p.Result.CdtInfos))
			for _, resultInfo := range p.Result.CdtInfos {
				cdtLocation := cdt_calculation.CdtWithLocationLevel{
					CdtMax:              resultInfo.GetLeadTimeMax(),
					CdtMin:              resultInfo.GetLeadTimeMin(),
					OriginLocationLevel: int(resultInfo.GetCdtProcess().CdtQueryInfo.OriginLocationLevel),
					DestLocationLevel:   int(resultInfo.GetCdtProcess().CdtQueryInfo.DestinationLocationLevel),
					CdtExtraData:        resultInfo.GetCdtExtraData(),
					CdtProcess:          resultInfo.GetCdtProcess(),
				}
				cdtLocations = append(cdtLocations, cdtLocation)
			}
			fchannelCdtAndVolume.CdtInfos = cdtLocations
			fchannelCdtAndVolume.FChannelID = p.Param.ProductID
			cdtReply.FchannelCdtAndVolumes = []cdt_calculation.FChannelCdtAndVolume{fchannelCdtAndVolume}
		}
	}
	if p.Param.NeedEqualLevels {
		//对于mchannel的返回 加上特殊的标识
		cdtReply.QueryIdCopy += p.Param.MChannelID
	}

	return cdtReply
}

func (c *CdtCalculationService) GetHolidays(ctx utils.LCOSContext, productID string, isSiteLine, isLM uint8, stateID int, region string, disableHolidays, disableWeekends bool) ([]string, *lcos_error.LCOSError) {
	if isSiteLine == constant.TRUE {
		return c.lpsHolidayDao.GetHolidays(ctx, productID, stateID, region, isLM, disableHolidays, disableWeekends)
	} else {
		return c.slsHolidayDao.GetHolidays(ctx, productID, stateID, region, isLM, disableHolidays, disableWeekends)
	}
}

func mergeHolidayListAndSort(holidayList []string) []string {
	holidayMap := make(map[string]bool)
	tmpHolidayList := make([]string, 0, len(holidayList))
	for _, holiday := range holidayList {
		if _, ok := holidayMap[holiday]; !ok {
			tmpHolidayList = append(tmpHolidayList, holiday)
			holidayMap[holiday] = true
		}
	}
	sort.Strings(tmpHolidayList)
	return tmpHolidayList
}

func mergeWeekendList(weekendList []int32) []int32 {
	weekendMap := make(map[int32]bool)
	tmpWeekendList := make([]int32, 0, len(weekendList))
	for _, weekend := range weekendList {
		if _, ok := weekendMap[weekend]; !ok {
			tmpWeekendList = append(tmpWeekendList, weekend)
			weekendMap[weekend] = true
		}
	}
	return tmpWeekendList
}

func (c *CdtCalculationService) GetAllCdtHolidays(ctx utils.LCOSContext, productID string, isSiteLine uint8, stateID int, region string, lineIDList []*cdt_calculation.LineInfo) ([]string, []string, *lcos_error.LCOSError) {
	lcosCtx := utils.NewCommonCtx(ctx)

	var (
		nonWorkingDayLineList []string
		holidayList           []string

		productHolidayFlag     bool
		disableProductHolidays bool
		disableProductWeekends bool
	)

	lpopService := lpop_service.NewLpopService()

	// 获取渠道的起始region属性
	var fromRegion string
	productInfo, err := product_service.GetProductInfoByProductIdWithLocalCache(ctx, productID)
	if err != nil {
		// 异常场景：无法获取到渠道信息，默认开启nonworking day
		logger.CtxLogErrorf(ctx, "cannot get product from country|product_id=%s, cause=%s", productID, err.Msg)
	} else {
		fromRegion = productInfo.FromCountry
	}

	if len(lineIDList) > 0 {
		for _, singleLineInfo := range lineIDList {
			// 市场维度apollo配置，判断EDD计算是否忽略非工作日
			var disableHolidays, disableWeekends bool
			if utils.CheckInUint32(singleLineInfo.SubType, constant.CBFirstMileFlagList) {
				// CB FM的线（C_FM, C_OFM）根据起始region判断是否获取nwd
				disableHolidays, disableWeekends = cf.IsEddDisableHolidays(ctx, fromRegion), cf.IsEddDisableWeekends(ctx, fromRegion)
			} else {
				// 非FM的线根据目的region判断是否获取nwd
				disableHolidays, disableWeekends = cf.IsEddDisableHolidays(ctx, region), cf.IsEddDisableWeekends(ctx, region)
			}
			logger.CtxLogInfof(ctx, "get edd disable line nonworking days config|line_id=%s, from_region=%s, region=%s, disable_holidays=%t, disable_weekends=%t", singleLineInfo.LineID, fromRegion, region, disableHolidays, disableWeekends)

			if disableHolidays && disableWeekends {
				// line所属region同时跳过holiday和weekend，那么可以跳过获取nwd的逻辑
				continue
			}

			holidays, lcosErr := lpopService.GetThreePLHolidaysWithLruCache(lcosCtx, singleLineInfo.LineID, isSiteLine, int32(stateID), region, disableHolidays, disableWeekends)
			// for lm edd and line of C-FM, need to get product holidays as fallback
			if lcosErr != nil || len(holidays) == 0 {
				if singleLineInfo.SubType == constant.C_FM {
					productHolidayFlag = true
					disableProductHolidays = disableHolidays
					disableProductWeekends = disableWeekends
					nonWorkingDayLineList = append(nonWorkingDayLineList, singleLineInfo.LineID)
				}
			} else {
				for _, singleLpopHoliday := range holidays {
					holidayList = append(holidayList, singleLpopHoliday.GetHoliday())
				}
				nonWorkingDayLineList = append(nonWorkingDayLineList, singleLineInfo.LineID)
			}
		}
	}

	// 检查能否获取3pl holiday，不能的话，需要获取sls/lps的holiday
	if productHolidayFlag {
		productHolidayList, _ := c.GetHolidays(lcosCtx, productID, isSiteLine, 0, stateID, region, disableProductHolidays, disableProductWeekends)
		holidayList = append(holidayList, productHolidayList...)
	}
	return mergeHolidayListAndSort(holidayList), nonWorkingDayLineList, nil
}

func (c *CdtCalculationService) GetCdtInfoForTracking(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, cbLmInboundDate int64) (*cdt_calculation.EddCalculationInfo, *cdt_calculation.EDDProcess, *lcos_error.LCOSError) {

	cdtResults, lcosErr := c.BatchGetCdtInfoByProducts(ctx, edd_constant.LeadTimeObject, []*cdt_calculation.CdtProductInfo{productInfo}, constant.CdtQueryDataSourceDB)
	if lcosErr != nil {
		return nil, nil, lcosErr
	}
	if len(cdtResults) <= 0 {
		return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "cannot get cdt reply")
	}
	if cdtResults[0].Error != nil {
		return nil, nil, lcos_error.NewLCOSError(cdtResults[0].Error.RetCode, cdtResults[0].Error.Msg)
	}
	var lineList []*cdt_calculation.LineInfo
	cdtInfo := cdtResults[0]
	lineList = append(lineList, productInfo.LineList...)

	holidayList, lineIdList, lcosErr := c.GetAllCdtHolidays(ctx, productInfo.ProductID, productInfo.IsSiteLine, *productInfo.BuyerAddr.StateLocationId, productInfo.Region, lineList)
	if lcosErr != nil {
		return nil, nil, lcosErr
	}

	eddCalculation := &cdt_calculation.EddCalculationInfo{
		CalculateEddMin: !cdtInfo.IgnoreCdtMin,
		EddRangeLimit:   cdtInfo.EddRangeLimit,
	}
	eddProcess := &cdt_calculation.EDDProcess{
		CdtProcess: cdtInfo.CdtProcess,
		NonWorkingDayProcess: &cdt_calculation.NonWorkingDayProcess{
			LineIdList: lineIdList,
		},
	}

	// 计算和填充leadtime max和edd max holiday extend
	cdtMaxHolidayExt, cdtMaxHolidayList := calculateHolidayExt(uint32(cbLmInboundDate), holidayList, cdtInfo.CdtMax, productInfo.Region)
	eddCalculation.LeadTimeMax = cdtInfo.CdtMax
	eddCalculation.EddMaxHolidayExt = cdtMaxHolidayExt
	eddProcess.NonWorkingDayProcess.LeadTimeMaxNwdList = cdtMaxHolidayList

	// 如果需要计算edd min，计算和填充leadtime min和edd min holiday extend
	if eddCalculation.CalculateEddMin {
		cdtMinHolidayExt, cdtMinHolidayList := calculateHolidayExt(uint32(cbLmInboundDate), holidayList, cdtInfo.CdtMin, productInfo.Region)
		eddCalculation.LeadTimeMin = cdtInfo.CdtMin
		eddCalculation.EddMinHolidayExt = cdtMinHolidayExt
		eddProcess.NonWorkingDayProcess.LeadTimeMinNwdList = cdtMinHolidayList
	}

	return eddCalculation, eddProcess, nil
}

// SPLN-26145 product 90020 get edd from wbc
func (c *CdtCalculationService) GetEddFromWbc(ctx utils.LCOSContext, product *cdt_calculation.CdtProductInfo, forderId uint64, cbLmInboundDate int64) (*cdt_calculation.EddCalculationInfo, *cdt_calculation.EDDProcess, *lcos_error.LCOSError) {
	dataSource := constant.CdtQueryDataSourceDB
	// 1. get cdt from wbc, if failure then get cdt from manual data uploaded by biz
	var cdtInfo *cdt_calculation.CdtInfo
	wbcStaticService := waybill_center_service.NewWayBillCenterStaticService(product.Region, cf.GetConf(ctx).WBCStaticService.MaxRetryTimes)
	cdt, err := wbcStaticService.GetCdtByForderId(ctx, forderId)
	if err == nil && cdt != 0 {
		cdtInfo = &cdt_calculation.CdtInfo{
			ProductId:   product.ProductID,
			IsSiteLine:  product.IsSiteLine,
			LeadTimeMin: utils.NewFloat64(cdt),
			LeadTimeMax: utils.NewFloat64(cdt),
			CdtProcess:  &cdt_calculation.CdtProcess{},
		}
	}
	if cdtInfo == nil {
		if err != nil {
			logger.CtxLogErrorf(ctx, "get cdt from wbc error: %s", err.Msg)
		}
		// todo for 90020, will not query by lane code, only query product level. Need to fix in the future
		queries, err := c.getQuery(ctx, edd_constant.LeadTimeObject, product.ProductID, "", false, product.UpdateEvent, product.IsSiteLine, 0, product.IsCB, "", product.SellerAddr, product.BuyerAddr, product.Region, 0, 0, true, dataSource, constant.NotNeedEqualLevels)
		if err != nil {
			return nil, nil, err
		}
		var realCdtQuery *cdt_calculation.CdtQuery
		cdtInfos, realCdtQueries, cdtErr := c.manualUpdateDao.QueryCdtInfoUsingCache(ctx, product.ProductID, product.IsSiteLine, 0, product.Region, queries, constant.NotNeedEqualLevels)
		if cdtErr != nil || len(cdtInfos) == 0 {
			return nil, nil, cdtErr
		}
		if len(realCdtQueries) != 0 {
			realCdtQuery = realCdtQueries[0]
		}
		cdtInfo = cdtInfos[0]

		cdtInfo.CdtProcess = &cdt_calculation.CdtProcess{}
		cdtInfo.CdtProcess.SetCdtQuery(realCdtQuery)
		cdtInfo.CdtProcess.SetManualUpdateProcess(cdtInfo)
	}
	// 2. manual manipulate and calculate cdt
	if err := c.cdtManualManipulate(ctx, edd_constant.LeadTimeObject, cdtInfo, product.ProductID, "", false, product.UpdateEvent, product.IsSiteLine, 0, product.IsCB, product.Region, "", product.SellerAddr, product.BuyerAddr, dataSource, constant.NotNeedEqualLevels, 0, pb.CdtScene_Normal); err != nil {
		return nil, nil, err
	}
	eddCalculation := &cdt_calculation.EddCalculationInfo{
		CalculateEddMin: false,
		LeadTimeMax:     cdtInfo.GetLeadTimeMax(),
	}
	// 3. calculate holidays extend
	holidayList, lineIdList, lcosErr := c.GetAllCdtHolidays(ctx, product.ProductID, product.IsSiteLine, *product.BuyerAddr.StateLocationId, product.Region, product.LineList)
	if lcosErr != nil {
		return nil, nil, lcosErr
	}
	holidayExt, usedHolidayList := calculateHolidayExt(uint32(cbLmInboundDate), holidayList, eddCalculation.LeadTimeMax, product.Region)
	eddCalculation.EddMaxHolidayExt = holidayExt
	return eddCalculation, &cdt_calculation.EDDProcess{
		CdtProcess: cdtInfo.CdtProcess,
		NonWorkingDayProcess: &cdt_calculation.NonWorkingDayProcess{
			LeadTimeMaxNwdList: usedHolidayList,
			LineIdList:         lineIdList,
		},
	}, nil
}

func (c *CdtCalculationService) GetEDDInfoByAlgo(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, slsTn string, eventTime int64, eventTrackingCode string) (int64, int64, *algo_service.PredictEDDInfo, *cdt_calculation.EDDProcess, *lcos_error.LCOSError) {
	/*
		调用 Algo 模型计算 EDD

		1. 调用 algo api 计算 EDD（不需额外计算NWD）
		2. Manual Manipulate 计算
		3. 调整并跳过 EDD 为 NWD 的日期
	*/
	var (
		eventType, _ = edd_constant.GetUpdateEventString(productInfo.UpdateEvent)
		productId, _ = strconv.Atoi(productInfo.ProductID)
		buyerId      = strconv.Itoa(int(productInfo.BuyerId))

		eddMin int64
		eddMax int64
	)

	// 1. 调用 algo 预测 edd
	algoEddInfo, lcosErr := algo_service.PredictEDD(ctx, productInfo.Region, productId, slsTn, buyerId, eventTrackingCode, eventType, eventTime)
	if lcosErr != nil {
		return 0, 0, nil, nil, lcosErr
	}
	_ = monitor.AwesomeReportEvent(ctx, CatModuleName_AlgoPredictEDD, CatInterfaceName_AlgoPredictEDD, constant.StatusSuccess, "")
	// algo 基于 tracking binlog 的 event 计算 EDD，可能跟传参不同，需要日志记录
	logger.CtxLogInfof(ctx, "got result slsTn:%s, eventTime:%v, eventType:%s, algo_resp:%s", slsTn, eventTime, eventType, utils.MarshToStringWithoutError(algoEddInfo))
	if !algoEddInfo.IsSameEvent(eventType) {
		_ = monitor.AwesomeReportEvent(ctx, CatModuleName_AlgoPredictEDD, CatInterfaceName_AlgoPredictEDD, "algo_return_diff_event_type", fmt.Sprintf("slsTn:%s, eventTime:%v, eventType:%s, algo_resp:%v", slsTn, eventTime, eventType, utils.MarshToStringWithoutError(algoEddInfo)))
		logger.CtxLogInfof(ctx, "got diff event_type, slsTn:%s, eventTime:%v, eventType:%s, algo_resp:%v", slsTn, eventTime, eventType, utils.MarshToStringWithoutError(algoEddInfo))
	}

	algoEddMin, algoEddMax, lcosErr := algoEddInfo.ParseEddTime(productInfo.Region)
	if lcosErr != nil {
		return 0, 0, nil, nil, lcosErr
	}

	// 2. manual manipulate and calculate cdt
	var (
		eddMinManipulateExt, eddMaxManipulateExt float64
	)
	cdtInfo := &cdt_calculation.CdtInfo{
		ProductId:   productInfo.ProductID,
		IsSiteLine:  productInfo.IsSiteLine,
		LeadTimeMin: utils.NewFloat64(0), // 传入0进行修正值累加，避免精度丢失
		LeadTimeMax: utils.NewFloat64(0),
		CdtProcess:  &cdt_calculation.CdtProcess{},
	}
	cdtInfo.CdtProcess.SetAutoUpdateProcess(cdtInfo, 0, 0)
	// SPLN-34854 algo edd流程优先匹配lane维度修正规则，如果匹配不到则再尝试匹配product维度修正规则, edd 不需要使用历史信息
	cdtDelta, realQuerry, lcosErr := c.getCdtManualManipulation(ctx, edd_constant.LeadTimeObject, productInfo.ProductID, productInfo.LaneCode, true, productInfo.UpdateEvent, productInfo.IsSiteLine, 0, productInfo.IsCB, productInfo.Region, "", productInfo.SellerAddr, productInfo.BuyerAddr, constant.CdtQueryDataSourceDB, constant.NotNeedEqualLevels, 0, pb.CdtScene_Normal)
	if lcosErr != nil || cdtDelta == nil {
		cdtDelta, realQuerry, lcosErr = c.getCdtManualManipulation(ctx, edd_constant.LeadTimeObject, productInfo.ProductID, "", false, productInfo.UpdateEvent, productInfo.IsSiteLine, 0, productInfo.IsCB, productInfo.Region, "", productInfo.SellerAddr, productInfo.BuyerAddr, constant.CdtQueryDataSourceDB, constant.NotNeedEqualLevels, 0, pb.CdtScene_Normal)
	}
	if lcosErr == nil && cdtDelta != nil {
		eddMinManipulateExt = cdtDelta.GetRoundCdtMinDelta() // 使用四舍五入的结果
		eddMaxManipulateExt = cdtDelta.GetRoundCdtMaxDelta()
		cdtInfo.CdtProcess.SetManualManipulationQuery(realQuerry)
		cdtInfo.CdtProcess.SetManualManipulationProcess(cdtDelta)
	}

	// 3. 获取非工作日信息
	holidayList, lineIdList, lcosErr := c.GetAllCdtHolidays(ctx, productInfo.ProductID, productInfo.IsSiteLine, *productInfo.BuyerAddr.StateLocationId, productInfo.Region, productInfo.LineList)
	if lcosErr != nil {
		return 0, 0, nil, nil, lcosErr
	}
	eddProcess := &cdt_calculation.EDDProcess{
		CdtProcess: cdtInfo.GetCdtProcess(),
		NonWorkingDayProcess: &cdt_calculation.NonWorkingDayProcess{
			LineIdList: lineIdList,
		},
	}
	// algo 模型预测 edd 已经将 holiday 纳入考量，如果预测 edd 时间落入 holiday 需要额外跳过这一天
	eddMinHolidayExt, eddMinHolidayList := c.calculateAlgoEddHolidayExt(algoEddMin, holidayList, eddMinManipulateExt, productInfo.Region)
	eddProcess.NonWorkingDayProcess.LeadTimeMinNwdList = eddMinHolidayList

	eddMaxHolidayExt, eddMaxHolidayList := c.calculateAlgoEddHolidayExt(algoEddMax, holidayList, eddMaxManipulateExt, productInfo.Region)
	eddProcess.NonWorkingDayProcess.LeadTimeMaxNwdList = eddMaxHolidayList

	// 将额外修正值累加到 algo edd
	eddMin = c.calculateEdd(algoEddMin, eddMinManipulateExt, eddMinHolidayExt)
	eddMax = c.calculateEdd(algoEddMax, eddMaxManipulateExt, eddMaxHolidayExt)

	if eddMin > eddMax { // 修正
		logger.CtxLogInfof(ctx, "invalid edd time, slsTn:%s, eddMin=%d, eddMax=%s, algo_resp:%v", slsTn, eddMin, eddMax, utils.MarshToStringWithoutError(algoEddInfo))
		eddMax = eddMin
	}

	return eddMin, eddMax, algoEddInfo, eddProcess, nil
}

func (c *CdtCalculationService) calculateAlgoEddHolidayExt(edd int64, holidayList []string, leadTime float64, region string) (float64, []string) {
	/*
		algo 模型预测edd已经将holiday 纳入考量，如果预测edd时间落入holiday需要额外跳过这一天
	*/
	var (
		holidayMap = make(map[string]bool)
		holidayExt = 0
		nwdProcess = make([]string, 0, 10)
		start      = pickup.TransferTimeStampToTime(uint32(edd), region)
		end        = pickup.TransferTimeStampToTime(uint32(edd+int64(leadTime*3600*24)), region)
	)

	for _, holiday := range holidayList {
		holidayMap[holiday] = true
	}

	// 遍历当前日期到未来日期，直到没有落到 NWD
	// SPLN-34498 目前仅处理delta大于等于0的情况，对于小于0的情况，返回延长为0，且holiday列表为空
	if start.After(end) {
		return float64(holidayExt), nwdProcess
	}

	// 遍历checker，查找
	startTime := time.Date(start.Year(), start.Month(), start.Day(), 12, 0, 0, 0, start.Location()) // 防止时区切换可能出现的问题，使用中午12点作为日期标记
	endTime := time.Date(end.Year(), end.Month(), end.Day(), 12, 0, 0, 0, end.Location())
	for i := 0; i < 10000 && (!startTime.After(endTime.AddDate(0, 0, holidayExt))); i++ { // 设置循环上限为10000，防止可能出现的死循环
		currentDateString := startTime.Format(constant.DateFormat)
		if _, holidayFlag := holidayMap[currentDateString]; holidayFlag {
			nwdProcess = append(nwdProcess, currentDateString)
			holidayExt++
		}
		startTime = startTime.AddDate(0, 0, 1)
	}
	return float64(holidayExt), nwdProcess
}

func (c *CdtCalculationService) calculateEdd(eventTime int64, leadTime, holidayExt float64) int64 {
	return eventTime + int64((leadTime+holidayExt)*24*3600)
}

func (c *CdtCalculationService) GetEDDInfo(ctx utils.LCOSContext, productsInfo *cdt_calculation.CdtProductInfo, forderId uint64, eventTime int64) (int64, int64, *cdt_calculation.EDDProcess, *lcos_error.LCOSError) {
	// 1. 计算leadtime和nonworking day数据
	var err *lcos_error.LCOSError
	var eddCalculation *cdt_calculation.EddCalculationInfo
	var eddProcess *cdt_calculation.EDDProcess
	// SPLN-26145 product 90020 get edd from wbc
	if productsInfo.ProductID == "90020" {
		if eddCalculation, eddProcess, err = c.GetEddFromWbc(ctx, productsInfo, forderId, eventTime); err != nil {
			return 0, 0, nil, err
		}
	} else {
		if eddCalculation, eddProcess, err = c.GetCdtInfoForTracking(ctx, productsInfo, eventTime); err != nil {
			return 0, 0, nil, err
		}
	}

	// 2. 计算EDD
	var eddMin, eddMax int64
	eddMax = c.calculateEdd(eventTime, eddCalculation.LeadTimeMax, eddCalculation.EddMaxHolidayExt)
	eddMin = c.calculateEdd(eventTime, eddCalculation.LeadTimeMin, eddCalculation.EddMinHolidayExt)
	eddMin = datetime.CalculateEDDMinByEDDMax(eddMax, eddMin, eddCalculation.CalculateEddMin, eddCalculation.EddRangeLimit, productsInfo.Region)
	return eddMin, eddMax, eddProcess, nil
}

func (c *CdtCalculationService) GetExtendedEDDInfo(ctx utils.LCOSContext, req *cdt_calculation.SingleExtendedEDDInfo) (*cdt_calculation.SingleExtendedEDDResponse, *lcos_error.LCOSError) {
	holidayList, lineIdList, lcosErr := c.GetAllCdtHolidays(ctx, req.ProductID, req.IsSiteLine, req.StateLocationID, req.Region, req.LineIDList)
	if lcosErr != nil {
		return nil, lcosErr
	}
	logger.CtxLogInfof(ctx, "successfully get holidays:[%s]|product_id=[%s],state_location_id=[%d]", strings.Join(holidayList, ","), req.ProductID, req.StateLocationID)
	extendedEddMax, eddMaxDiff, eddMaxHolidayList := calculateExtendedEdd(req.Edd, req.Adjustment, holidayList, req.Region)

	var extendedEddMin int64
	var eddMinDiff int
	var eddMinHolidayList []string
	if req.EddMin > 0 {
		extendedEddMin, eddMinDiff, eddMinHolidayList = calculateExtendedEdd(req.EddMin, req.EddMinAdjustment, holidayList, req.Region)
	}

	return &cdt_calculation.SingleExtendedEDDResponse{
		SlsTN:                     req.SlsTN,
		ExtendedEdd:               extendedEddMax,
		ExtendedNonWorkingDayList: eddMaxHolidayList,
		ExtendedDays:              eddMaxDiff,

		ExtendedEddMin:     extendedEddMin,
		EddMinExtendedDays: eddMinDiff,
		NonWorkingDayProcess: &cdt_calculation.NonWorkingDayProcess{
			LeadTimeMinNwdList: eddMinHolidayList,
			LeadTimeMaxNwdList: eddMaxHolidayList,
			LineIdList:         lineIdList,
		},
	}, nil
}

// GetLeadTimeInfo 仅EDD场景。获取EDD计算所使用的基础数据（不包含非工作日）
func (c *CdtCalculationService) GetLeadTimeInfo(ctx utils.LCOSContext, product *cdt_calculation.CdtProductInfo, dataSource uint8, groupTag uint8, skipAutoUpdateRule bool) (*cdt_calculation.CdtInfo, *lcos_error.LCOSError) {
	interfaceName := fmt.Sprintf("product_id:[%s], is_site_line:[%d]", product.ProductID, product.IsSiteLine)
	cdtInfos, err := c.GetCourierDeliveryTimeByProduct(ctx, edd_constant.LeadTimeObject, product.ProductID, product.IsSiteLine, 0, product.IsCB, product.Region, "", product.LaneCode, product.UpdateEvent, product.SellerAddr, product.BuyerAddr, product.SkuInfo, dataSource, groupTag, skipAutoUpdateRule, constant.NotNeedEqualLevels, product.StartFCodeType, product.RequestTime, product.CdtScene)
	if err != nil || len(cdtInfos) == 0 {
		_ = metrics.CounterIncr(constant.MetricProductStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(err.RetCode)), "product": product.ProductID})
		_ = monitor.AwesomeReportEvent(ctx, constant.CdtGetInfo, interfaceName, constant.StatusError, err.Msg)

		logger.CtxLogErrorf(ctx, err.Msg)
		return nil, err
	}

	_ = metrics.CounterIncr(constant.MetricProductStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(0), "product": product.ProductID})
	_ = monitor.AwesomeReportEvent(ctx, constant.CdtGetInfo, interfaceName, constant.StatusSuccess, "success")
	_ = metrics.GaugeSet(constant.MetricsCDTMaxReport, cdtInfos[0].GetLeadTimeMax(), map[string]string{"product_id": product.ProductID, "region": product.Region})
	_ = metrics.GaugeSet(constant.MetricsCDTMinReport, cdtInfos[0].GetLeadTimeMin(), map[string]string{"product_id": product.ProductID, "region": product.Region})
	return cdtInfos[0], nil
}

// GetLeadTimeInfoFromWbc 仅EDD场景。90020渠道特殊逻辑，从WBC获取EDD计算所使用的基础数据（不包含非工作日）
func (c *CdtCalculationService) GetLeadTimeInfoFromWbc(ctx utils.LCOSContext, product *cdt_calculation.CdtProductInfo, forderId uint64, dataSource uint8) (*cdt_calculation.CdtInfo, *lcos_error.LCOSError) {
	// 1. get cdt from wbc, if failure then get cdt from manual data uploaded by biz
	var cdtInfo *cdt_calculation.CdtInfo
	wbcStaticService := waybill_center_service.NewWayBillCenterStaticService(product.Region, cf.GetConf(ctx).WBCStaticService.MaxRetryTimes)
	cdt, err := wbcStaticService.GetCdtByForderId(ctx, forderId)
	if err == nil && cdt != 0 {
		cdtInfo = &cdt_calculation.CdtInfo{
			ProductId:   product.ProductID,
			IsSiteLine:  product.IsSiteLine,
			LeadTimeMin: utils.NewFloat64(0),
			LeadTimeMax: utils.NewFloat64(cdt),
		}
		cdtInfo.SetIgnoreLeadTimeMinFlag(edd_constant.LeadTimeObject, true) // 从wbc获取cdt时，默认不计算edd min
		cdtInfo.CdtProcess = &cdt_calculation.CdtProcess{}
	}
	if cdtInfo == nil {
		queries, err := c.getQuery(ctx, edd_constant.LeadTimeObject, product.ProductID, "", false, product.UpdateEvent, product.IsSiteLine, 0, product.IsCB, "", product.SellerAddr, product.BuyerAddr, product.Region, 0, 0, true, dataSource, constant.NotNeedEqualLevels)
		if err != nil {
			return nil, err
		}
		var realCdtQuery *cdt_calculation.CdtQuery
		cdtInfos, realCdtQueries, err := c.manualUpdateDao.QueryCdtInfoUsingCache(ctx, product.ProductID, product.IsSiteLine, 0, product.Region, queries, constant.NotNeedEqualLevels)
		if err != nil {
			return nil, err
		}
		if len(cdtInfos) == 0 {
			errMsg := fmt.Sprintf("[90020] query cdt info failed|product_id=%v, is_site_line=%v,is_lm=%v", product.ProductID, product.IsSiteLine, 0)
			return nil, lcos_error.NewLCOSError(lcos_error.NotFoundCdtInfoErrorCode, errMsg)
		}
		if len(realCdtQueries) != 0 {
			realCdtQuery = realCdtQueries[0]
		}
		cdtInfo = cdtInfos[0]
		cdtInfo.SetIgnoreLeadTimeMinFlag(edd_constant.LeadTimeObject, cdtInfo.GetLeadTimeMin() < 0)
		cdtInfo.CdtProcess = &cdt_calculation.CdtProcess{}
		cdtInfo.CdtProcess.SetCdtQuery(realCdtQuery)
		cdtInfo.CdtProcess.SetManualUpdateProcess(cdtInfo)
	}
	// 2. manual manipulate and calculate cdt
	if err := c.cdtManualManipulate(ctx, edd_constant.LeadTimeObject, cdtInfo, product.ProductID, "", false, product.UpdateEvent, product.IsSiteLine, 0, product.IsCB, product.Region, "", product.SellerAddr, product.BuyerAddr, dataSource, constant.NotNeedEqualLevels, 0, pb.CdtScene_Normal); err != nil {
		return nil, err
	}
	return cdtInfo, nil
}

func (c *CdtCalculationService) GetProductNonWorkingDays(ctx utils.LCOSContext, productId string, isSiteLine, isLM uint8, stateId int, region string, disableHolidays, disableWeekends bool) ([]string, []int32, *lcos_error.LCOSError) {
	if isSiteLine == constant.TRUE {
		return c.lpsHolidayDao.GetNonWorkingDaysInfo(ctx, productId, stateId, region, isLM, disableHolidays, disableWeekends)
	} else {
		return c.slsHolidayDao.GetNonWorkingDaysInfo(ctx, productId, stateId, region, isLM, disableHolidays, disableWeekends)
	}
}

// GetNonWorkingDaysInfo 获取指定line的非工作日信息
func (c *CdtCalculationService) GetNonWorkingDaysInfo(ctx utils.LCOSContext, productId string, isSiteLine uint8, stateId int, region string, lineList []*cdt_calculation.LineInfo) ([]string, []int32, *lcos_error.LCOSError) {
	if len(lineList) == 0 {
		return nil, nil, nil
	}

	var (
		holidayList []string
		weekendList []int32

		productHolidayFlag     bool
		disableProductHolidays bool
		disableProductWeekends bool
	)

	// 获取渠道的起始region属性
	var fromRegion string
	productInfo, err := product_service.GetProductInfoByProductIdWithLocalCache(ctx, productId)
	if err != nil {
		// 异常场景：无法获取到渠道信息，默认开启nonworking day
		logger.CtxLogErrorf(ctx, "cannot get product origin country|product_id=%s, cause=%s", productId, err.Msg)
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEddDisableNonworkingDaysReport, "GetProductOriginRegionError", constant.StatusError, err.Msg)
	} else {
		fromRegion = productInfo.FromCountry
	}

	lpopService := lpop_service.NewLpopService()
	for _, lineInfo := range lineList {
		// 市场维度apollo配置，判断EDD计算是否忽略非工作日
		var disableHolidays, disableWeekends bool
		if utils.CheckInUint32(lineInfo.SubType, constant.CBFirstMileFlagList) {
			// CB FM的线（C_FM, C_OFM）根据起始region判断是否获取nwd
			disableHolidays, disableWeekends = cf.IsEddDisableHolidays(ctx, fromRegion), cf.IsEddDisableWeekends(ctx, fromRegion)
		} else {
			// 非FM的线根据目的region判断是否获取nwd
			disableHolidays, disableWeekends = cf.IsEddDisableHolidays(ctx, region), cf.IsEddDisableWeekends(ctx, region)
		}
		logger.CtxLogInfof(ctx, "get edd disable line nonworking days config|line_id=%s, from_region=%s, region=%s, disable_holidays=%t, disable_weekends=%t", lineInfo.LineID, fromRegion, region, disableHolidays, disableWeekends)

		if disableHolidays && disableWeekends {
			// line所属region同时跳过holiday和weekend，那么可以跳过获取nwd的逻辑
			continue
		}

		lineHolidayList, lineWeekendList, err := lpopService.Get3PLNonWorkingDaysWithCache(ctx, lineInfo.LineID, isSiteLine, uint64(stateId), disableHolidays, disableWeekends)
		if err != nil {
			if lineInfo.SubType == constant.C_FM {
				productHolidayFlag = true
				disableProductHolidays = disableHolidays
				disableProductWeekends = disableWeekends
			}
			continue
		}
		if len(lineHolidayList) == 0 && len(lineWeekendList) == 0 {
			if lineInfo.SubType == constant.C_FM {
				productHolidayFlag = true
				disableProductHolidays = disableHolidays
				disableProductWeekends = disableWeekends
			}
			continue
		}
		holidayList = append(holidayList, lineHolidayList...)
		weekendList = append(weekendList, lineWeekendList...)
	}

	if productHolidayFlag {
		// 与原逻辑保持一致，忽略这个error
		productHolidayList, productWeekendList, _ := c.GetProductNonWorkingDays(ctx, productId, isSiteLine, 0, stateId, region, disableProductHolidays, disableProductWeekends)
		if len(productHolidayList) != 0 || len(productWeekendList) != 0 {
			holidayList = append(holidayList, productHolidayList...)
			weekendList = append(weekendList, productWeekendList...)
		}
	}

	return mergeHolidayListAndSort(holidayList), mergeWeekendList(weekendList), nil
}

func (c *CdtCalculationService) GetEddCalculationInfo(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, forderId uint64, eventTime int64) (*cdt_calculation.EddCalculationInfo, *cdt_calculation.DDLCalculationInfo, *cdt_calculation.EDDProcess, *lcos_error.LCOSError) {
	eddCalculation := &cdt_calculation.EddCalculationInfo{}
	ddlCalculation := &cdt_calculation.DDLCalculationInfo{}
	eddProcess := &cdt_calculation.EDDProcess{}

	dataSource := constant.CdtQueryDataSourceDB
	// 0. 匹配AB测试分组
	hitGroupTag := edd_constant.AbTestGroupTagA // 默认使用group a
	skipAutoUpdateRule := false                 // 默认不跳过匹配自动更新规则
	// GetEddCalculationInfo 这个函数是计算EDD的，所以Object type是leadTime
	abTestRule, _ := c.abTestService.GetActiveAbTestRuleByProductIdUsingCache(ctx, productInfo.ProductID, edd_constant.LeadTimeObject)
	if abTestRule != nil && productInfo.BuyerId != 0 {
		// ab test rule存在，且传了buyer id，则根据buyer id匹配命中的test group
		targetBuyerGroup := productInfo.BuyerId % 10
		for i, testGroup := range abTestRule.TestGroupList {
			groupTag := uint8(i)
			if utils.InUint32Slice(uint32(targetBuyerGroup), testGroup.BuyerGroup) {
				hitGroupTag = groupTag
				skipAutoUpdateRule = testGroup.CdtType == edd_constant.AbTestCdtTypeManualUpdate
				break
			}
		}
	}

	// 1. 计算当前update event的cdt
	var cdtInfo *cdt_calculation.CdtInfo
	var err *lcos_error.LCOSError
	if productInfo.ProductID == "90020" {
		cdtInfo, err = c.GetLeadTimeInfoFromWbc(ctx, productInfo, forderId, dataSource)
	} else {
		cdtInfo, err = c.GetLeadTimeInfo(ctx, productInfo, dataSource, hitGroupTag, skipAutoUpdateRule)
	}
	if err != nil {
		return nil, nil, nil, err
	}
	if cdtInfo == nil {
		errMsg := fmt.Sprintf("query cdt info failed|product_id=%v, is_site_line=%v, update_event=%v", productInfo.ProductID, productInfo.IsSiteLine, productInfo.UpdateEvent)
		return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.NotFoundCdtInfoErrorCode, errMsg)
	}

	// SPLN-32248 通过event筛选出对应day group和time bucket的 min/max，重新赋值给cdtInfo的min/max
	var useDayGroupOrTimeBucket bool
	cdtInfo.LeadTimeMin, cdtInfo.LeadTimeMax, cdtInfo.DDLBackwardCDT, useDayGroupOrTimeBucket = generateCdtInfoMinMaxByEventTime(ctx, eventTime, cdtInfo, productInfo.Region)

	// 如果使用了dayGroup和timeBucket数据，需要重新计算修正后的值,并且在process中更新
	if useDayGroupOrTimeBucket {
		setManualUpdateProcess(cdtInfo)
		setAutoUpdateProcess(cdtInfo)
		setActualLeadTime(cdtInfo)
	}
	if cdtInfo != nil {
		*cdtInfo.LeadTimeMin, _ = strconv.ParseFloat(fmt.Sprintf("%.4f", *cdtInfo.LeadTimeMin), 64)
		*cdtInfo.LeadTimeMax, _ = strconv.ParseFloat(fmt.Sprintf("%.4f", *cdtInfo.LeadTimeMax), 64)
	}

	eddCalculation.SetCdtInfo(cdtInfo)
	ddlCalculation.ForwardCdt = cdtInfo.GetDDLForwardCDT()   // nolint
	ddlCalculation.BackwardCdt = cdtInfo.GetDDLBackwardCDT() // nolint
	eddProcess.CdtProcess = cdtInfo.GetCdtProcess()          // nolint

	// 2. 获取非工作日信息
	holidayList, weekendList, err := c.GetNonWorkingDaysInfo(ctx, productInfo.ProductID, productInfo.IsSiteLine, productInfo.BuyerAddr.GetStateLocationId(), productInfo.Region, productInfo.LineList)
	if err != nil {
		return nil, nil, nil, err
	}
	eddCalculation.Holiday = holidayList
	eddCalculation.Weekend = weekendList

	// 3. 匹配下一个update event的ddl cdt
	nextEventCdtInfo, err := c.GetAutoUpdateCdtInfoByProduct(ctx, edd_constant.LeadTimeObject, productInfo.ProductID, productInfo.IsSiteLine, 0, productInfo.IsCB, productInfo.Region, "", productInfo.LaneCode, productInfo.NextEvent, productInfo.SellerAddr, productInfo.BuyerAddr, dataSource, hitGroupTag, productInfo.StartFCodeType)
	if err != nil {
		// next event匹配ddl cdt失败不影响当前event的计算
		logger.CtxLogErrorf(ctx, "get deadline cdt failed|product_id=%s, update_event=%d, cause=%s", productInfo.ProductID, productInfo.NextEvent, err.Msg)
		return eddCalculation, ddlCalculation, eddProcess, nil
	}
	ddlCalculation.NextEventForwardCdt = nextEventCdtInfo.GetDDLForwardCDT()
	ddlCalculation.NextEventBackwardCdt = nextEventCdtInfo.GetDDLBackwardCDT()

	return eddCalculation, ddlCalculation, eddProcess, nil
}

func (c *CdtCalculationService) QueryEDDInfo(ctx utils.LCOSContext, query *cdt_calculation.QueryEDDInfoRequest) (*edd_history2.EddHistoryTab, *lcos_error.LCOSError) {
	histories, lcosErr := c.eddHistoryDao.GetEDDHistoryBySlsTn(ctx, query.SlsTN, edd_history2.TableName(ctx, strings.ToUpper(query.Region)), false)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(histories) <= 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.EDDHistoryNotFound, "cannot find edd history|sls_tn=[%s], region=[%s]", query.SlsTN, query.Region)
	}

	// find the latest edd
	returnedEDD := histories[0]
	for _, singleHistory := range histories {
		if singleHistory.Ctime > returnedEDD.Ctime {
			returnedEDD = singleHistory
		}
	}
	return returnedEDD, nil
}

func (c *CdtCalculationService) SingleGetAutoRule(ctx utils.LCOSContext, objectType uint8, productID string, isSiteLine uint8, isLM, hitGroupTag, startTimeType uint8) (*auto_update_rule.CDTAutoUpdateRuleTab, *lcos_error.LCOSError) {
	var autoRule *auto_update_rule.CDTAutoUpdateRuleTab
	var lcosErr *lcos_error.LCOSError

	// 若为灰度机器，检查是否存在可用的即将生效的自动计算规则
	_, isGrayMachine := os.LookupEnv(graymachine.ENV_RULE_GRAY_MACHINE_FLAG_KEY)
	if isGrayMachine {
		autoRule, lcosErr = c.autoUpdateDao.SearchIncomingAutoUpdateRuleUsingCacheForGrayTest(ctx, productID, isSiteLine, isLM, objectType, hitGroupTag, startTimeType)
	}

	//没有即将生效的规则的灰度机器或非灰度机器查询生效中的规则
	if autoRule == nil {
		autoRule, lcosErr = c.autoUpdateDao.SearchActiveAutoUpdateRuleUsingCache(ctx, productID, isSiteLine, isLM, objectType, hitGroupTag, startTimeType)
	}

	return autoRule, lcosErr
}

func (c *CdtCalculationService) SingleGetVolumeRuleAndVersion(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo) (*automated_volume.AutomatedVolumeGenerationRuleTab, uint32, *lcos_error.LCOSError) {
	volumeRule, err := c.automatedVolumeRuleDao.SearchActiveAutomatedVolumeGenerationRuleByMChannelUsingCache(ctx, productInfo.MChannelID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "cannot find volume rule|product_id=%d, error=%v", productInfo.MChannelID, err.Msg)
		return nil, 0, err
	}
	volumeVersion, err := c.automatedVolumeGenerationDataDao.GetVolumeVersionByAutoUpdateRuleIDUsingCache(ctx, volumeRule.Id)
	if err != nil {
		logger.CtxLogErrorf(ctx, "cannot find volume version|auto_volume_rule=%d, error=%v", volumeRule.Id, err.Msg)
		return nil, 0, err
	}
	return volumeRule, volumeVersion, nil
}

func (c *CdtCalculationService) SingleCalcNonAutoCdt(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, objectType uint8, dataSource uint8) *CdtQueryResult {
	productID := productInfo.ProductID
	isSiteLine := productInfo.IsSiteLine

	isCB := productInfo.IsCB
	region := productInfo.Region

	laneCode := productInfo.LaneCode
	updateEvent := productInfo.UpdateEvent
	sellerAddr := productInfo.SellerAddr
	buyerAddr := productInfo.BuyerAddr
	skuInfo := productInfo.SkuInfo

	result := &CdtQueryResult{}

	needEqualLevels := constant.NotNeedEqualLevels
	if len(productInfo.FChannelList) != 0 {
		needEqualLevels = constant.NeedEqualLevels
	}
	var cdtInfos []*cdt_calculation.CdtInfo
	cdtInfos, result.Err = c.singleCalcNonAutoCdt(ctx, objectType, productID, isSiteLine, 0, isCB, region, "", laneCode, updateEvent, sellerAddr, buyerAddr, skuInfo, dataSource, needEqualLevels)
	if len(cdtInfos) > 0 {
		result.CdtInfo = cdtInfos[0]
	}
	// 监控上报
	reportBatchItemCdt(result.Err, updateEvent, region, productID, laneCode)

	return result
}

func (c *CdtCalculationService) FChannelSingleCalcNonAutoCdt(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, objectType uint8, dataSource uint8) ([]*cdt_calculation.CdtInfo, *lcos_error.LCOSError) {
	productID := productInfo.ProductID
	isSiteLine := productInfo.IsSiteLine

	isCB := productInfo.IsCB
	region := productInfo.Region

	laneCode := productInfo.LaneCode
	updateEvent := productInfo.UpdateEvent
	sellerAddr := productInfo.SellerAddr
	buyerAddr := productInfo.BuyerAddr
	skuInfo := productInfo.SkuInfo
	needEqualLevels := productInfo.NeedEqualLevel
	var lcosErr *lcos_error.LCOSError
	var cdtInfos []*cdt_calculation.CdtInfo

	cdtInfos, lcosErr = c.singleCalcNonAutoCdt(ctx, objectType, productID, isSiteLine, 0, isCB, region, "", laneCode, updateEvent, sellerAddr, buyerAddr, skuInfo, dataSource, needEqualLevels)
	reportBatchItemCdt(lcosErr, updateEvent, region, productID, laneCode)

	return cdtInfos, lcosErr
}

func (c *CdtCalculationService) singleCalcNonAutoCdt(ctx utils.LCOSContext, objectType uint8, productID string, isSiteLine uint8, isLM uint8, isCB uint8, region string, tplUniqueKey string, laneCode string, updateEvent uint8, sellerAddr, buyerAdd *cdt_calculation.AddressInfo, skuInfo *cdt_calculation.CdtSkuInfo,
	dataSource uint8, needEqualLevels bool) ([]*cdt_calculation.CdtInfo, *lcos_error.LCOSError) {
	var cdtInfo *cdt_calculation.CdtInfo
	var cdtInfos []*cdt_calculation.CdtInfo

	var lcosErr *lcos_error.LCOSError
	midResult := &cdt_calculation.CdtProcess{}
	var realCdtQuery *cdt_calculation.CdtQuery     // record which query is really is use for cdt
	var realCdtQueries []*cdt_calculation.CdtQuery // records which query is really is use for cdt
	var laneCodeFlag bool                          // 是否匹配lane维度数据
	if objectType == edd_constant.LeadTimeObject && len(laneCode) > 0 {
		// 只有EDD场景（leadtime）且传了lane code才可以匹配lane维度数据
		laneCodeFlag = true
	}

	// check if need get cdt from thirdParty service
	if !cf.GetConf(ctx).Cdt.IsThirdPartyServiceDowngrade() && skuInfo != nil && productInWhiteList(productID, cf.GetConf(ctx).Cdt.GetThirdPartyCdtWhiteList()) {
		pisClient := pis_service.NewPISService(ctx, region)
		req := convertGetQuotationReq(productID, sellerAddr, buyerAdd, skuInfo)
		resp, err := pisClient.GetQuotation(ctx, ctx.GetRequestId(), req)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Get cdt_info from third_party service failed, err:%v", err)
		} else {
			returnedCdt := float64(resp.DeliveryEstimateTransitTimeBusinessDays + resp.DeliveryAdditionalTransitTimeBusinessDays)
			cdtInfo = &cdt_calculation.CdtInfo{
				ProductId:   productID,
				IsSiteLine:  isSiteLine,
				LeadTimeMin: utils.NewFloat64(returnedCdt),
				LeadTimeMax: utils.NewFloat64(returnedCdt), // SPLN-30962, make edt min same as edt max
			}
		}
	}

	if cdtInfo == nil {
		// try to find manual update data
		cdtInfos, realCdtQueries, laneCodeFlag, lcosErr = c.getManualUpdateCdtInfo(ctx, objectType, productID, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, tplUniqueKey, sellerAddr, buyerAdd, region, dataSource, needEqualLevels)
		if lcosErr != nil {
			return nil, lcosErr
		}

		if cdtInfos != nil && !needEqualLevels {
			if len(cdtInfos) == 0 || len(realCdtQueries) == 0 {
				errMsg := fmt.Sprintf("get cdtInfos empty or get realCdtQueries empty|product_id=%v, is_site_line=%v,is_lm=%v", productID, isSiteLine, isLM)
				logger.CtxLogInfof(ctx, "%s", "get cdtInfos empty or get realCdtQueries empty")
				return nil, lcos_error.NewLCOSError(lcos_error.NotFoundCdtInfoErrorCode, errMsg)
			}
			cdtInfo = cdtInfos[0]
			realCdtQuery = realCdtQueries[0]

			cdtInfo.SetIgnoreLeadTimeMinFlag(objectType, cdtInfo.GetLeadTimeMin() < 0)

			// SPLN-23295 fill manual update info
			midResult.SetCdtQuery(realCdtQuery)
			midResult.SetManualUpdateProcess(cdtInfo)
			logger.CtxLogInfof(ctx, "successfully find manual update cdt info|cdt_query=[%s]|reply=[%s]", utils.MarshToStringWithoutError(realCdtQuery), utils.MarshToStringWithoutError(cdtInfo))
		}
	}

	if needEqualLevels {
		//对手动上传或者自动规则的结果进行修正
		for i, oriCdtInfo := range cdtInfos {
			if err := c.cdtManualManipulate(ctx, objectType, oriCdtInfo, productID, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, region, tplUniqueKey, sellerAddr, buyerAdd, dataSource, constant.NotNeedEqualLevels, 0, pb.CdtScene_Normal); err != nil {
				errMsg := fmt.Sprintf("cdt manual manipulate error: %s", err.Msg)
				logger.CtxLogErrorf(ctx, errMsg)
				return nil, lcos_error.NewLCOSError(err.RetCode, errMsg)
			}
			if oriCdtInfo != nil && oriCdtInfo.CdtProcess != nil {
				oriCdtInfo.CdtProcess.CdtQueryInfo = realCdtQueries[i]
			}
		}
	} else {
		if cdtInfo == nil {
			errMsg := fmt.Sprintf("query cdt info failed|product_id=%v, is_site_line=%v,is_lm=%v", productID, isSiteLine, isLM)
			logger.CtxLogInfof(ctx, "%s", "query cdt info failed")
			return nil, lcos_error.NewLCOSError(lcos_error.NotFoundCdtInfoErrorCode, errMsg)
		}

		cdtInfo.CdtProcess = midResult

		if err := c.cdtManualManipulate(ctx, objectType, cdtInfo, productID, laneCode, laneCodeFlag, updateEvent, isSiteLine, isLM, isCB, region, tplUniqueKey, sellerAddr, buyerAdd, dataSource, constant.NotNeedEqualLevels, 0, pb.CdtScene_Normal); err != nil {
			errMsg := fmt.Sprintf("cdt manual manipulate error: %s", err.Msg)
			logger.CtxLogErrorf(ctx, errMsg)
			return nil, lcos_error.NewLCOSError(err.RetCode, errMsg)
		}
		cdtInfos = []*cdt_calculation.CdtInfo{cdtInfo}
	}
	return cdtInfos, lcosErr
}

func (c *CdtCalculationService) ConstructAutoCdtQueryProcess(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, objectType uint8, dataSource uint8, autoUpdateRule *auto_update_rule.CDTAutoUpdateRuleTab) *singleCdtQueryProcess {
	productID := productInfo.ProductID
	isSiteLine := productInfo.IsSiteLine
	isCB := productInfo.IsCB
	region := productInfo.Region
	laneCode := productInfo.LaneCode
	updateEvent := productInfo.UpdateEvent
	sellerAddr := productInfo.SellerAddr
	buyerAddr := productInfo.BuyerAddr
	itemID := productInfo.ItemId
	needVolume := productInfo.NeedVolume
	needEqualLevel := productInfo.NeedEqualLevel
	mChannelID := productInfo.MChannelID

	var laneCodeFlag bool // 是否匹配lane维度数据
	if objectType == edd_constant.LeadTimeObject && len(laneCode) > 0 {
		// 只有EDD场景（leadtime）且传了lane code才可以匹配lane维度数据
		laneCodeFlag = true
	}

	return newSingleCdtQueryProcess(productInfo.QueryID, itemID, objectType, productID, laneCode, laneCodeFlag, updateEvent, isSiteLine, 0, isCB, "", sellerAddr, buyerAddr, region, false, dataSource, autoUpdateRule, needEqualLevel, needVolume, mChannelID, productInfo.RequestTime, productInfo.CdtScene)
}

func (c *CdtCalculationService) ConstructVolumeQueryProcess(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, dataSource uint8, autoVolumeUpdateRule *automated_volume.AutomatedVolumeGenerationRuleTab, volumeVersion uint32) *singleVolumeQueryProcess {
	productID := productInfo.ProductID
	sellerAddr := productInfo.SellerAddr
	buyerAddr := productInfo.BuyerAddr
	itemID := productInfo.ItemId
	needEqualLevel := productInfo.NeedEqualLevel
	mChannelID := productInfo.MChannelID
	region := productInfo.Region

	return newSingleVolumeQueryProcess(productInfo.QueryID, itemID, productID, sellerAddr, buyerAddr, region, dataSource, autoVolumeUpdateRule, needEqualLevel, mChannelID, volumeVersion, productInfo)
}

func (c *CdtCalculationService) ListMChannelRuleByRegion(ctx utils.LCOSContext, region string) ([]*pb.MChannelRuleList, *lcos_error.LCOSError) {
	queryExec := localcache.NewLocalCacheQueryExecutor()
	models, dbErr := queryExec.Find(ctx, constant.AggregateMaskedChannelCdtRuleTabNamespace, region)
	if dbErr != nil || models == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, fmt.Sprintf("cannot find m-channel rule by region:[%s]", region))
	}

	mChannelRulesInCache := models.([]*aggregate_masked_channel_cdt.AggregateMaskedChannelCdtTab)
	mChannelIdRuleMap := make(map[string]*aggregate_masked_channel_cdt.AggregateMaskedChannelCdtTab)

	//对于同一个product id 的多条配置，找出当前生效的配置
	for _, mChannelRule := range mChannelRulesInCache {
		currentTimestamp := uint32(recorder.Now(ctx).Unix())
		recentRule, ok := mChannelIdRuleMap[mChannelRule.MaskedProductID]
		if ok {
			if recentRule.EffectiveTime < mChannelRule.EffectiveTime && recentRule.IsActive(currentTimestamp) && mChannelRule.IsActive(currentTimestamp) {
				//状态为upcoming/active 但实际生效的规则中，时间最晚的为最近生效的规则
				mChannelIdRuleMap[mChannelRule.MaskedProductID] = mChannelRule
			}
		} else {
			if mChannelRule.IsActive(currentTimestamp) {
				mChannelIdRuleMap[mChannelRule.MaskedProductID] = mChannelRule
			}
		}
	}

	mChannelRuleList := make([]*pb.MChannelRuleList, 0, len(mChannelIdRuleMap))
	for productID, mChannelRule := range mChannelIdRuleMap {
		maxCdtRule := pb.MChannelRuleEnum(mChannelRule.MaxCdtAggregateRule)
		minCdtRule := pb.MChannelRuleEnum(mChannelRule.MinCdtAggregateRule)
		mChannelRuleList = append(mChannelRuleList, &pb.MChannelRuleList{
			ProductId:  utils.NewString(productID),
			MaxCdtRule: &maxCdtRule,
			MinCdtRule: &minCdtRule,
		})
	}
	return mChannelRuleList, nil
}

func (c *CdtCalculationService) ListMChannelGreyConfigByRegion(ctx utils.LCOSContext, region string) ([]*pb.MChannelGreyConfigList, *lcos_error.LCOSError) {
	regionGreyConfig := apollo_config.GetAggregateMChannelGreyConfig(ctx, region)
	mChannelGreyConfigList := make([]*pb.MChannelGreyConfigList, 0, len(regionGreyConfig))

	for productId, productGreyConfig := range regionGreyConfig {
		mChannelGreyConfig := &pb.MChannelGreyConfigList{
			ProductId:  utils.NewString(productId),
			Percentage: utils.NewUint32(uint32(productGreyConfig.Percentage)),
		}
		if productGreyConfig.NewBuyerIdsList != nil {
			mChannelGreyConfig.BuyerIdsList = productGreyConfig.NewBuyerIdsList
		}
		if productGreyConfig.NewScenarioList != nil {
			mChannelGreyConfig.ScenarioList = productGreyConfig.NewScenarioList
		}
		if productGreyConfig.NewStateLocationIdList != nil {
			mChannelGreyConfig.StateLocationIdList = productGreyConfig.NewStateLocationIdList
		}
		if productGreyConfig.NewCityLocationIdList != nil {
			mChannelGreyConfig.CityLocationIdList = productGreyConfig.NewCityLocationIdList
		}
		if productGreyConfig.NewFChannelNumsList != nil {
			mChannelGreyConfig.FChannelNumsList = productGreyConfig.NewFChannelNumsList
		}
		mChannelGreyConfigList = append(mChannelGreyConfigList, mChannelGreyConfig)
	}

	if len(mChannelGreyConfigList) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("this region[%s] doesn't has m-channel grey config now", region))
	}
	return mChannelGreyConfigList, nil
}

func getVolumeInfoList(cdtReply *cdt_calculation.CdtReply, fChannelCdtAndVolume cdt_calculation.FChannelCdtAndVolume, fChannelVolumeInfosMap map[string]map[string][]cdt_calculation.VolumeWithLocationLevel) cdt_calculation.FChannelCdtAndVolume {
	if cdtReply.NeedVolume == constant.NeedVolume {
		findVolume := false
		if _, ok2 := fChannelVolumeInfosMap[cdtReply.QueryIdCopy]; ok2 {
			if _, ok3 := fChannelVolumeInfosMap[cdtReply.QueryIdCopy][fChannelCdtAndVolume.FChannelID]; ok3 {
				//找到mchannel +fchannel对应的单量 填充  找不到时的报错
				fChannelCdtAndVolume.VolumeInfos = fChannelVolumeInfosMap[cdtReply.QueryIdCopy][fChannelCdtAndVolume.FChannelID]
				findVolume = true
			}
		}
		if !findVolume {
			fChannelCdtAndVolume.Retcode = lcos_error.NotFoundFChannelVolumeErrorCode
			fChannelCdtAndVolume.Message += " need volume but f-channel can't find volume"
		}
	}
	return fChannelCdtAndVolume
}

func (c *CdtCalculationService) ListABTestRuleByRegion(ctx utils.LCOSContext, region string) ([]*pb.ABTestRuleList, *lcos_error.LCOSError) {
	var dataList []*model_ab_test.CdtAbTestRule
	// EDT使用，cdtType使用常量 edd_constant.CdtObject
	if err := common.SearchAllDataGeneral(ctx, &model_ab_test.CdtAbTestRule{}, &dataList, map[string]interface{}{"rule_status": edd_constant.AbTestRuleStatusActive, "region": region, "object_type": edd_constant.CdtObject}, false); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Msg)
	}
	dataMap := make(map[string]interface{}, len(dataList))
	abTestRuleList := make([]*pb.ABTestRuleList, 0)
	for _, rule := range dataList {
		key := rule.GetCacheKey()
		if dupRule, ok := dataMap[key]; ok {
			// 正常情况下一个渠道只应该有一个正在生效的ab test规则，如果存在多个，则上报异常
			errMsg := fmt.Sprintf("more than one ab test rule active, use rule:%+v\nduplicated rule:%+v", rule, dupRule)
			logger.CtxLogErrorf(ctx, errMsg)
			_ = monitor.AwesomeReportEvent(ctx, "ListABTestRuleByRegionReport", rule.TableName(), "list_ab_test_duplicate_error", fmt.Sprintf("use rule:%+v\nduplicated rule:%+v", rule, dupRule))
			continue
		}
		dataMap[key] = rule
		abTestRule, err := getABTestRule(rule)
		if err != nil {
			errMsg := fmt.Sprintf("get ab test rule error: %v", err.Error())
			logger.CtxLogErrorf(ctx, errMsg)
			_ = monitor.AwesomeReportEvent(ctx, "ListABTestRuleByRegionReport", rule.TableName(), "list_ab_test_rule_error", errMsg)
			continue
		}
		abTestRuleList = append(abTestRuleList, abTestRule)
	}
	return abTestRuleList, nil
}

func getABTestRule(data *model_ab_test.CdtAbTestRule) (*pb.ABTestRuleList, error) {
	if data == nil {
		return nil, fmt.Errorf("CdtAbTestRule is nil")
	}
	if len(data.TestGroupList) < 2 {
		return nil, fmt.Errorf("the length of TestGroupList is not equals to 2, TestGroupList: %v", data.TestGroupList)
	}

	ruleDetails := make([]*pb.ABTestRuleDetails, 0)

	// 获取每个product + cdt_type 对应的 buyerGroup
	for groupTag, groupValue := range data.TestGroupList {
		buyerGroupTemp := deepcopy.Copy(groupValue.BuyerGroup)
		buyerGroup, ok := buyerGroupTemp.([]uint32)
		if !ok {
			return nil, fmt.Errorf("buyerGroup is not []uint32 type, buyerGroupTemp: %v", buyerGroupTemp)
		}
		ruleDetail := &pb.ABTestRuleDetails{
			GroupTag: utils.NewUint32(uint32(groupTag)),
			Groups:   buyerGroup,
		}
		ruleDetails = append(ruleDetails, ruleDetail)

	}

	productId := data.ProductId
	mtime := data.Mtime

	return &pb.ABTestRuleList{
		ProductId:         utils.NewString(productId),
		ActiveTime:        utils.NewUint32(mtime),
		AbTestRuleDetails: ruleDetails,
	}, nil

}

func (c *CdtCalculationService) BatchGetEDTManualManipulationForAlgoModel(ctx utils.LCOSContext, productInfoList []*cdt_calculation.CdtProductInfo) map[string]cdt_calculation.DeltaReply {
	var (
		results = make(map[string]cdt_calculation.DeltaReply, len(productInfoList))
	)

	for _, productInfo := range productInfoList {

		cdtDelta, _, lcosErr := c.getCdtManualManipulation(
			ctx, edd_constant.CdtObject,
			productInfo.ProductID,
			"",
			false,
			productInfo.UpdateEvent,
			productInfo.IsSiteLine,
			0,
			productInfo.IsCB,
			productInfo.Region,
			"",
			productInfo.SellerAddr,
			productInfo.BuyerAddr,
			constant.CdtQueryDataSourceDB,
			constant.NotNeedEqualLevels,
			productInfo.RequestTime,
			productInfo.CdtScene,
		)
		if lcosErr != nil {
			results[productInfo.QueryID] = cdt_calculation.DeltaReply{
				Retcode: lcosErr.RetCode,
				Message: lcosErr.Msg,
			}
		} else {
			// 对于min和max均为0的情况，返回一个错误码，方便上游进行对应的缓存策略
			deltaMin, deltaMax := cdtDelta.GetRoundCdtMinDelta(), cdtDelta.GetRoundCdtMaxDelta() // SPLN-34371 将结果修改为四舍五入的结果
			if deltaMin == 0 && deltaMax == 0 {
				results[productInfo.QueryID] = cdt_calculation.DeltaReply{
					Retcode: lcos_error.NotFoundManualManipulationRuleErrorCode,
					Message: fmt.Sprintf("cannot find manual manipulation for product:[%s]", productInfo.ProductID),
				}
			} else {
				tmpReply := cdt_calculation.DeltaReply{
					DeltaMin: deltaMin,
					DeltaMax: deltaMax,
				}

				if cdt.IsQueryingHistoryManipulation(productInfo.RequestTime) {
					// 如果是查询历史的逻辑则需要补充修正规则的生效时间和结束时间
					record, recordErr := c.manualManipulationDao.GetManualManipulationRecord(ctx, cdtDelta.RecordId)
					if recordErr == nil && record != nil {
						tmpReply.EffectiveTime = record.EffectiveDate
						tmpReply.ExpirationTime = record.ExpirationDate
					}
				}

				results[productInfo.QueryID] = tmpReply
			}
		}
	}

	return results
}

func compareCdtReplyDiff(retV1 []*cdt_calculation.CdtReply, errV1 *lcos_error.LCOSError, retV2 []*cdt_calculation.CdtReply, errV2 *lcos_error.LCOSError) bool {
	if !lcos_error.IsSameError(errV1, errV2) {
		return true
	}

	retV2Map := make(map[string]*cdt_calculation.CdtReply, len(retV1))
	for _, replyV2 := range retV2 {
		retV2Map[replyV2.QueryId] = replyV2
	}
	for _, replyV1 := range retV1 {
		replyV2, ok := retV2Map[replyV1.QueryId]
		if !ok {
			return true
		}

		if replyV1.CdtMin != replyV2.CdtMin || replyV1.CdtMax != replyV2.CdtMax || compareFchannelCdtAndVolumesDiff(replyV1.FchannelCdtAndVolumes, replyV2.FchannelCdtAndVolumes) {
			return true
		}
	}
	return false
}

func compareFchannelCdtAndVolumesDiff(dataListV1, dataListV2 []cdt_calculation.FChannelCdtAndVolume) bool {
	if len(dataListV1) != len(dataListV2) {
		return true
	}

	dataMapV2 := make(map[string]cdt_calculation.FChannelCdtAndVolume, len(dataListV2))
	for _, dataV2 := range dataListV2 {
		dataMapV2[dataV2.FChannelID] = dataV2
	}

	for _, dataV1 := range dataListV1 {
		dataV2, ok := dataMapV2[dataV1.FChannelID]
		if !ok {
			return true
		}

		// 对比单个fchannel的cdt和单量计算结果，主要包括四个部分：
		// 1. 计算结果的retcode
		// 2. 结果结果的message
		// 3. cdt计算结果
		// 4. 单量计算结果

		// 1. 对比v1和v2返回的retcode和message是否一致
		if dataV1.Retcode != dataV2.Retcode || dataV1.Message != dataV2.Message {
			return true
		}

		// 2. 对比v1和v2的cdt计算结果是否一致
		cdtInfoMapV2 := make(map[string]cdt_calculation.CdtWithLocationLevel, len(dataV2.CdtInfos))
		for _, cdtInfoV2 := range dataV2.CdtInfos {
			key := utils.GenKey("-", strconv.Itoa(cdtInfoV2.OriginLocationLevel), strconv.Itoa(cdtInfoV2.DestLocationLevel))
			cdtInfoMapV2[key] = cdtInfoV2
		}
		for _, cdtInfoV1 := range dataV1.CdtInfos {
			key := utils.GenKey("-", strconv.Itoa(cdtInfoV1.OriginLocationLevel), strconv.Itoa(cdtInfoV1.DestLocationLevel))
			cdtInfoV2, ok := cdtInfoMapV2[key]
			if !ok {
				return true
			}
			if cdtInfoV1.CdtMin != cdtInfoV2.CdtMin || cdtInfoV1.CdtMax != cdtInfoV2.CdtMax || cdtInfoV1.CdtExtraData != cdtInfoV2.CdtExtraData {
				return true
			}
		}

		// 3. 对比v1和v2的单量计算结果是否一致
		volumeInfoMapV2 := make(map[string]cdt_calculation.VolumeWithLocationLevel, len(dataV2.VolumeInfos))
		for _, volumeInfoV2 := range dataV2.VolumeInfos {
			key := utils.GenKey("-", strconv.Itoa(volumeInfoV2.OriginLocationLevel), strconv.Itoa(volumeInfoV2.DestLocationLevel))
			volumeInfoMapV2[key] = volumeInfoV2
		}
		for _, volumeInfoV1 := range dataV1.VolumeInfos {
			key := utils.GenKey("-", strconv.Itoa(volumeInfoV1.OriginLocationLevel), strconv.Itoa(volumeInfoV1.DestLocationLevel))
			volumeInfoV2, ok := volumeInfoMapV2[key]
			if !ok {
				return true
			}
			if volumeInfoV1.Volume != volumeInfoV2.Volume {
				return true
			}
		}
	}
	return false
}

func (c *CdtCalculationService) fetchSloInfoByForderId(ctx utils.LCOSContext, region string, forderId uint64) (*waybill_center_service.SloInfo, *lcos_error.LCOSError) {
	req := &waybill_center_service.GetSloInfoWithActionRequest{
		ForderId: strconv.FormatUint(forderId, 10),
		ApiLevel: waybill_center_service.DBAndHbase,
	}
	sloInfoList, err := waybill_center_service.NewWaybillCenterService(ctx, region).GetSloInfoWithAction(ctx, []*waybill_center_service.GetSloInfoWithActionRequest{req})
	if err != nil {
		return nil, err
	}
	if len(sloInfoList) == 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "get slo info from wbc return empty")
	}
	sloInfo := sloInfoList[0]
	if sloInfo.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "get slo info from wbc return error: %s", sloInfo.Message)
	}
	return sloInfo, nil
}

func (c *CdtCalculationService) fetchWaybillEddTrackings(ctx utils.LCOSContext, region, slsTn string) ([]*schema.Tracking, []*lts_service.TrackingData, *lcos_error.LCOSError) {
	ltsService, err := lts_service.NewLTSService(region)
	if err != nil {
		return nil, nil, err
	}
	historyTrackings, err := ltsService.RetrieveTracking(ctx, slsTn, region)
	if err != nil {
		return nil, nil, err
	}
	sort.SliceStable(historyTrackings, func(i, j int) bool {
		return historyTrackings[i].ActualTime < historyTrackings[j].ActualTime
	})
	var eddTrackings []*schema.Tracking
	for _, tracking := range historyTrackings {
		eddTracking := &schema.Tracking{
			ResourceTn:        slsTn,
			ResourceId:        "",
			TrackingCode:      tracking.TrackingCode,
			TrackingName:      tracking.TrackingName,
			Description:       tracking.Description,
			ResourceStatus:    "",
			ResourceSubStatus: "",
			ActualTime:        int64(tracking.ActualTime),
			TrackingDetail:    "",
		}
		if err = edd_pushing.CheckIsEddTrackingEvent(ctx, []*schema.Tracking{eddTracking}); err != nil {
			logger.CtxLogErrorf(ctx, "ignored tracking is not edd event|region=%s, sls_tn=%s, fcode=%s, cause=%s", region, slsTn, eddTracking.TrackingCode, err.Msg)
			continue
		}
		eddTrackings = append(eddTrackings, eddTracking)
	}
	if len(eddTrackings) == 0 {
		return nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "no edd trackings found for fallback edd calculate|region=%s, sls_tn=%s", region, slsTn)
	}
	return eddTrackings, historyTrackings, nil
}

func (c *CdtCalculationService) calculateFallbackEddByAlgo(ctx utils.LCOSContext, region, slsTn, laneCode string, orderInfo *lfs_service.LogisticOrderData, lineList []*cdt_calculation.LineInfo, buyerId uint64, eddJob *edd_pushing.EDDJob) (int64, int64, *lcos_error.LCOSError) {
	// 1. 获取Algo EDD计算的前置数据
	updateEvent := eddJob.GetUpdateEvent()
	laneCode = edd_pushing.DealWithLaneCode(laneCode, updateEvent, orderInfo.CbFlag == int32(constant.TRUE))
	eventTime, err := eddJob.GetEventTime(ctx)
	if err != nil {
		return 0, 0, err
	}
	trackingCode := eddJob.GetTrackingCodeForAlgo()

	// 2. 调用time计算Algo EDD
	productInfo := &cdt_calculation.CdtProductInfo{
		QueryID:    "1",
		ProductID:  orderInfo.LogisticProductId,
		IsCB:       uint8(orderInfo.CbFlag),
		IsSiteLine: constant.TRUE,
		Region:     region,
		SellerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.SellerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.SellerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.SellerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.SellerAddress.PostCode),
		},
		BuyerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.BuyerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.BuyerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.BuyerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.BuyerAddress.PostCode),
		},
		LaneCode: laneCode,
		LineList: lineList,

		UpdateEvent: updateEvent,
		BuyerId:     buyerId,
	}
	eddMin, eddMax, _, _, err := c.GetEDDInfoByAlgo(ctx, productInfo, slsTn, eventTime, trackingCode)
	if err != nil {
		return 0, 0, err
	}

	// 3. 校验EDD结果
	eddMin, eddMax, _, err = eddJob.CheckAndGetEddForPushing(ctx, eddMin, eddMax, region, buyerId, orderInfo.LogisticProductId)
	return eddMin, eddMax, err
}

func (c *CdtCalculationService) calculateFallbackEddByData(ctx utils.LCOSContext, region, laneCode string, orderInfo *lfs_service.LogisticOrderData, lineList []*cdt_calculation.LineInfo, buyerId uint64, eddJob *edd_pushing.EDDJob) (int64, int64, *edd_history2.EddProcess, *lcos_error.LCOSError) {
	// 1. 获取EDD计算的前置数据
	updateEvent := eddJob.GetUpdateEvent()
	laneCode = edd_pushing.DealWithLaneCode(laneCode, updateEvent, orderInfo.CbFlag == int32(constant.TRUE))
	eventTime, err := eddJob.GetEventTime(ctx)
	if err != nil {
		return 0, 0, nil, err
	}

	// 2. 调用time计算用于EDD计算的LT、NWD、EDD配置等数据
	productInfo := &cdt_calculation.CdtProductInfo{
		QueryID:    "1",
		ProductID:  orderInfo.LogisticProductId,
		IsCB:       uint8(orderInfo.CbFlag),
		IsSiteLine: constant.TRUE,
		Region:     region,
		SellerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.SellerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.SellerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.SellerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.SellerAddress.PostCode),
		},
		BuyerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(int(orderInfo.BuyerAddress.StateLocationId)),
			CityLocationId:     utils.NewInt(int(orderInfo.BuyerAddress.CityLocationId)),
			DistrictLocationId: utils.NewInt(int(orderInfo.BuyerAddress.DistrictLocationId)),
			PostalCode:         utils.NewString(orderInfo.BuyerAddress.PostCode),
		},
		LaneCode: laneCode,
		LineList: lineList,

		UpdateEvent: updateEvent,
		BuyerId:     buyerId,
	}
	eddInfo, ddlInfo, eddProcess, err := c.GetEddCalculationInfo(ctx, productInfo, orderInfo.ForderId, eventTime)
	if err != nil {
		return 0, 0, nil, err
	}

	// 3. 调用data SDK计算EDD
	dataEddInfo, err := eddJob.CallDataSDK(ctx, productInfo, &pb.GetEddCalculationInfoResponse{
		EddInfo: &pb.EDDCalculationInfo{
			EddMinAvailable: utils.NewBool(eddInfo.CalculateEddMin),
			EddRangeLimit:   utils.NewInt64(eddInfo.EddRangeLimit),
			CdtMin:          utils.NewFloat64(eddInfo.LeadTimeMin),
			CdtMax:          utils.NewFloat64(eddInfo.LeadTimeMax),
			Holiday:         eddInfo.Holiday,
			Weekend:         eddInfo.Weekend,
		},
		DdlInfo: &pb.DDLCalculationInfo{
			ForwardCdt:           utils.NewFloat64(ddlInfo.ForwardCdt),
			BackwardCdt:          utils.NewFloat64(ddlInfo.BackwardCdt),
			NextEventForwardCdt:  utils.NewFloat64(ddlInfo.NextEventForwardCdt),
			NextEventBackwardCdt: utils.NewFloat64(ddlInfo.NextEventBackwardCdt),
		},
		EddProcess: eddProcess.ToPb(),
	}, eventTime, region, false)
	if err != nil {
		return 0, 0, nil, err
	}

	// 4. 校验EDD结果
	eddMin, eddMax, _, err := eddJob.CheckAndGetEddForPushing(ctx, dataEddInfo.GetEddMin(), dataEddInfo.GetEddMax(), region, buyerId, orderInfo.LogisticProductId)
	return eddMin, eddMax, dataEddInfo.GetEddProcess(), err
}

func (c *CdtCalculationService) calculateFallbackEdd(ctx utils.LCOSContext, region, slsTn string, addedTrackings []*schema.Tracking, historyTrackings []*lts_service.TrackingData) (int64, int64, *edd_history2.EddHistoryTab, *lcos_error.LCOSError) {
	// 1. 从LFS拉取订单信息
	orderInfo, err := edd_pushing.FetchLfsOrderInfo(ctx, region, slsTn)
	if err != nil {
		return 0, 0, nil, err
	}

	// 2. 从WBC获取履约链路信息
	laneInfo, err := edd_pushing.FetchWaybillLaneInfo(ctx, region, slsTn)
	if err != nil {
		return 0, 0, nil, err
	}
	var laneCode string
	if cf.GetLaneCodeEnabledFlag(ctx) {
		laneCode = strings.ReplaceAll(laneInfo.LaneCode, "/", "|")
	}
	lineList := laneInfo.GetFollowingLineIDList()
	deliverBuyerID := laneInfo.DeliverUserID

	// 3. 特殊分支：对于direct delivery渠道，从order获取订单的EDT和SBD，令EDD=max(EDT, SBD)
	if edd_pushing.IsDirectDeliveryProduct(ctx, orderInfo.LogisticProductId) {
		eddJob, lcosErr := edd_pushing.NewEDDJob(ctx, addedTrackings, historyTrackings, orderInfo, nil, slsTn, edd_auto_update.UpdateOnEvent, 0)
		if lcosErr != nil {
			return 0, 0, nil, lcosErr
		}
		eddMin, eddMax, lcosErr := eddJob.GetEDDInfoForDirectDelivery(ctx, region, orderInfo.OrderSN)
		if lcosErr != nil {
			return 0, 0, nil, lcosErr
		}
		eddHistory, _ := eddJob.GenerateEDDHistory(ctx, eddMin, eddMax, nil, edd_constant.UpdateAll, nil, deliverBuyerID, false, constant.TRUE)
		return eddMin, eddMax, eddHistory, nil
	}

	// 4. 尝试使用算法流向计算EDD
	eddJob, err := edd_pushing.NewEDDJobByAlgo(ctx, addedTrackings, historyTrackings, orderInfo, slsTn, edd_auto_update.UpdateOnEvent, 0, lineList, laneInfo.IsReturnOrder(), nil)
	if err != nil {
		return 0, 0, nil, err
	}
	var algoErr *lcos_error.LCOSError
	if algoErr = eddJob.CanSwitchAlgoEDD(ctx, region, deliverBuyerID); algoErr == nil {
		// 通过apollo开关、历史推送标记、OPS开关、Algo开关、AB测试控制使用算法流向计算EDD
		var eddMin, eddMax int64
		eddMin, eddMax, algoErr = c.calculateFallbackEddByAlgo(ctx, region, slsTn, laneCode, orderInfo, lineList, deliverBuyerID, eddJob)
		if algoErr == nil {
			eddHistory, _ := eddJob.GenerateEDDHistory(ctx, eddMin, eddMax, nil, edd_constant.UpdateAll, nil, deliverBuyerID, false, constant.TRUE)
			return eddMin, eddMax, eddHistory, nil
		}
		// 算法流向计算EDD失败，也使用工程流向兜底
	}
	logger.CtxLogErrorf(ctx, "cannot calculate edd by algo|region=%s, sls_tn=%s, cause=%s", region, slsTn, algoErr.Msg)

	// 4. 算法流向无法使用或者计算失败，则尝试通过统计学流向计算EDD
	eddJob, err = edd_pushing.NewEDDJob(ctx, addedTrackings, historyTrackings, orderInfo, nil, slsTn, edd_auto_update.UpdateOnEvent, 0)
	if err != nil {
		return 0, 0, nil, err
	}
	eddMin, eddMax, eddProcess, err := c.calculateFallbackEddByData(ctx, region, laneCode, orderInfo, lineList, deliverBuyerID, eddJob)
	if err != nil {
		return 0, 0, nil, err
	}
	eddHistory, _ := eddJob.GenerateEDDHistory(ctx, eddMin, eddMax, eddProcess, edd_constant.UpdateAll, nil, deliverBuyerID, false, constant.TRUE)
	return eddMin, eddMax, eddHistory, nil
}

func (c *CdtCalculationService) GetFallbackEddInfo(ctx utils.LCOSContext, region string, forderId uint64, daysToDelivery uint32) (int64, int64, *lcos_error.LCOSError) {
	if cf.GetMutableConf(ctx).EDDConfig.DisableFallbackEdd {
		return 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "fallback edd calculation is disabled")
	}

	region = strings.ToUpper(region)

	// 1. 调用wbc通过forder_id获取订单sls_tn
	sloInfo, err := c.fetchSloInfoByForderId(ctx, region, forderId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "calculate fallback edd info failed, fetch slo info error|region=%s, forder_id=%d, cause=%s", region, forderId, err.Msg)
		_ = monitor.AwesomeReportEvent(ctx, constant.CatFallbackEdd, "Error", "fetchSloError", fmt.Sprintf("region:%s,forder_id:%d,cause=%s", region, forderId, err.Msg))
		return 0, 0, err
	}
	if sloInfo.OrderInfo == nil || sloInfo.OrderInfo.SloTn == "" {
		message := "get slo info from wbc return empty order info"
		logger.CtxLogErrorf(ctx, "calculate fallback edd info failed, fetch slo info error|region=%s, forder_id=%d, cause=%s", region, forderId, message)
		_ = monitor.AwesomeReportEvent(ctx, constant.CatFallbackEdd, "Error", "fetchSloError", fmt.Sprintf("region:%s,forder_id:%d,cause=%s", region, forderId, message))
		return 0, 0, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, message)
	}
	slsTn := sloInfo.OrderInfo.SloTn

	// 2. 查询订单是否已经推送EDD，如果已推送则返回最新EDD
	lastEddHistory, err := c.QueryEDDInfo(ctx, &cdt_calculation.QueryEDDInfoRequest{Region: region, SlsTN: slsTn})
	if err == nil && lastEddHistory != nil {
		logger.CtxLogInfof(ctx, "return the latest edd because edd history is not empty|region=%s, sls_tn=%s", region, slsTn)
		_ = monitor.AwesomeReportEvent(ctx, constant.CatFallbackEdd, "LastEDD", constant.StatusSuccess, fmt.Sprintf("region:%s,forder_id:%d", region, forderId))
		return lastEddHistory.EddMin, lastEddHistory.Edd, nil
	}

	// 3. 开始计算fallback EDD
	var (
		useDaysToDeliveryFallback bool
		eventTime                 int64

		eddMin     int64
		eddMax     int64
		eddHistory *edd_history2.EddHistoryTab
	)
	// 3.1. 拉取订单轨迹信息并筛选pickup done轨迹
	eddTrackings, historyTrackings, err := c.fetchWaybillEddTrackings(ctx, region, slsTn)
	if err != nil {
		// 拉取轨迹信息失败，使用days_to_delivery兜底，event time为当前时间
		useDaysToDeliveryFallback = true
		eventTime = recorder.Now(ctx).Unix()
		logger.CtxLogErrorf(ctx, "fetch waybill edd trackings error, use days_to_delivery to calculate fallback edd|region=%s, sls_tn=%s, cause=%s, event_time=%d", region, slsTn, err.Msg, eventTime)
	}
	// 3.2. 尝试使用算法或者工程流向计算EDD
	if !useDaysToDeliveryFallback {
		eddMin, eddMax, eddHistory, err = c.calculateFallbackEdd(ctx, region, slsTn, eddTrackings, historyTrackings)
		if err != nil {
			// 通过data或algo计算EDD失败，使用days_to_delivery兜底，event time为最早的edd轨迹生成时间 (EarliestAndSequential)
			useDaysToDeliveryFallback = true
			eventTime = eddTrackings[0].ActualTime
			logger.CtxLogErrorf(ctx, "cannot calculate pickup done edd by algo/data, use days_to_delivery to calculate fallback edd|region=%s, sls_tn=%s, cause=%s, event_time=%d", region, slsTn, err.Msg, eventTime)
		} else {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatFallbackEdd, "AlgoOrDataEDD", constant.StatusSuccess, fmt.Sprintf("region:%s,forder_id:%d", region, forderId))
		}
	}
	// 3.3 尝试通过days_to_delivery计算兜底edd
	if useDaysToDeliveryFallback {
		logger.CtxLogInfof(ctx, "try to calculate fallback edd with days_to_delivery|region=%s, event_time=%d, days_to_delivery=%d", region, eventTime, daysToDelivery)
		// 使用days to delivery计算兜底EDD
		// fallback_EDD = pickup_done_event_time + days_to_delivery
		fallbackEdd := utils.ParseTimestampToTime(uint32(eventTime), region).AddDate(0, 0, int(daysToDelivery))
		eddMin = 0
		eddMax = fallbackEdd.Unix()
		eddHistory = &edd_history2.EddHistoryTab{
			IsLM:              constant.FALSE,
			IsFirstTime:       constant.TRUE,
			EventTime:         eventTime,
			SlsTN:             slsTn,
			EddMin:            eddMin,
			Edd:               eddMax,
			UpdateEvent:       edd_constant.ShippedOut,
			UpdateRule:        edd_constant.EarliestAndSequentialTracking,
			UpdateType:        edd_constant.UpdateAll,
			DataType:          edd_constant.AutoUpdateEdd,
			IsFallbackEddFlag: constant.TRUE,
		}
		_ = monitor.AwesomeReportEvent(ctx, constant.CatFallbackEdd, "DaysToDeliveryEDD", constant.StatusSuccess, fmt.Sprintf("region:%s,forder_id:%d", region, forderId))
	}

	// 6. 再次检查订单是否已经推送EDD，如果已推送则返回最新EDD，未推送则写入EDD history
	lastEddHistory, err = c.QueryEDDInfo(ctx, &cdt_calculation.QueryEDDInfoRequest{Region: region, SlsTN: slsTn})
	if err == nil {
		logger.CtxLogInfof(ctx, "return the latest edd because edd history is not empty|region=%s, sls_tn=%s", region, slsTn)
		_ = monitor.AwesomeReportEvent(ctx, constant.CatFallbackEdd, "LastEDD", constant.StatusSuccess, fmt.Sprintf("region:%s,forder_id:%d", region, forderId))
		return lastEddHistory.EddMin, lastEddHistory.Edd, nil
	}
	if err = c.eddHistoryDao.CreateEDDHistoryBySlsTn(ctx, eddHistory, edd_history2.TableName(ctx, region)); err != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatFallbackEdd, "Error", "SaveEddHistoryError", fmt.Sprintf("region:%s,forder_id:%d,cause=%s", region, forderId, err.Msg))
		return 0, 0, err
	}
	return eddMin, eddMax, nil
}
