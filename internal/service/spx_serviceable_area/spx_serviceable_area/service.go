package spx_serviceable_area

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/spx_serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_compare"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seatalk"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spx_service"
	"strconv"
	"strings"
)

const (
	pagingQueryBatchSize = 2000

	versionNotifyError         = "VersionNotifyError"
	activeVersionNotFoundError = "ActiveVersionNotFoundError"
	multiActiveVersionError    = "MultiActiveVersionError"
	multiIncomingVersionError  = "MultiIncomingVersionError"
)

type SpxServiceableAreaService interface {
	// SPX基础配置信息查询
	ListAllOrderAccount(ctx utils.LCOSContext, region string) ([]*spx_service.OrderAccountEnum, *lcos_error.LCOSError)
	GetLocationType(ctx utils.LCOSContext, region string) (uint8, *lcos_error.LCOSError)
	// SPX服务范围版本通知
	CreateSpxServiceableAreaVersion(ctx utils.LCOSContext, region string, planning *spx_service.ServiceableAreaScheduledPlanning) *lcos_error.LCOSError
	EnableSpxServiceableAreaVersion(ctx utils.LCOSContext, region string, planning *spx_service.ServiceableAreaScheduledPlanning) *lcos_error.LCOSError
	CancelSpxServiceableAreaVersion(ctx utils.LCOSContext, region string, planningId uint64) *lcos_error.LCOSError
	// SPX服务范围版本实际生效，由lcos-task定时调用
	ActivateSpxServiceableAreaVersion(ctx utils.LCOSContext, version *spx_serviceable_area_version.SpxServiceableAreaVersion) *lcos_error.LCOSError
	// 拉取和清理SPX服务范围配置
	PullSpxServiceableAreaData(ctx utils.LCOSContext, region string, versionId uint64) *lcos_error.LCOSError
	ClearExpiredSpxServiceableArea(ctx utils.LCOSContext) *lcos_error.LCOSError
	UpdateSpxServiceableAreaData(ctx utils.LCOSContext, region string, versionId uint64, dataList []*spx_service.ServiceableArea) *lcos_error.LCOSError
	// SPX服务范围版本查询
	CheckSpxServiceableAreaVersionExists(ctx utils.LCOSContext, region string, versionId uint64) (bool, *lcos_error.LCOSError)
	GetIncomingSpxServiceableAreaVersion(ctx utils.LCOSContext, region string) (*spx_serviceable_area_version.SpxServiceableAreaVersion, *lcos_error.LCOSError)
	GetActiveSpxServiceableAreaVersion(ctx utils.LCOSContext, region string) (*spx_serviceable_area_version.SpxServiceableAreaVersion, *lcos_error.LCOSError)
	GetLastAvailableSpxServiceableAreaVersion(ctx utils.LCOSContext, region string, effectiveTime uint32) (*spx_serviceable_area_version.SpxServiceableAreaVersion, *lcos_error.LCOSError)
	// SPX服务范围配置数据查询
	GetSpxServiceabelAreaLocationData(ctx utils.LCOSContext, region string, versionId uint64, orderAccount int, locationId uint64) (*spx_serviceable_area_data.SpxServiceableAreaLocationData, *lcos_error.LCOSError)
	GetSpxServiceabelAreaPostcodeData(ctx utils.LCOSContext, region string, versionId uint64, orderAccount int, postcode string) (*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, *lcos_error.LCOSError)
	ListSpxServiceabelAreaLocationDataByLocationId(ctx utils.LCOSContext, region string, versionId uint64, locationId uint64) ([]*spx_serviceable_area_data.SpxServiceableAreaLocationData, *lcos_error.LCOSError)
	ListSpxServiceabelAreaPostcodeDataByPostcode(ctx utils.LCOSContext, region string, versionId uint64, postcode string) ([]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, *lcos_error.LCOSError)
	ListSpxServiceabelAreaLocationDataByOrderAccount(ctx utils.LCOSContext, region string, versionId uint64, orderAccount int) ([]*spx_serviceable_area_data.SpxServiceableAreaLocationData, *lcos_error.LCOSError)
	ListSpxServiceabelAreaPostcodeDataByOrderAccount(ctx utils.LCOSContext, region string, versionId uint64, orderAccount int) ([]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, *lcos_error.LCOSError)
	ListSpxServiceabelAreaLocationDataByOrderAccountList(ctx utils.LCOSContext, region string, versionId uint64, orderAccountList []int) ([]*spx_serviceable_area_data.SpxServiceableAreaLocationData, *lcos_error.LCOSError)
	ListSpxServiceabelAreaPostcodeDataByOrderAccountList(ctx utils.LCOSContext, region string, versionId uint64, orderAccountList []int) ([]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, *lcos_error.LCOSError)
}

var _ SpxServiceableAreaService = (*spxServiceableAreaService)(nil)

type spxServiceableAreaService struct {
	mappingService order_account_mapping.OrderAccountMappingService
	versionDao     spx_serviceable_area_version.SpxServiceableAreaVersionDao
	dataDao        spx_serviceable_area_data.SpxServiceableAreaDataDao
	compareTaskDao spx_serviceable_area_compare.SpxServiceableAreaCompareTaskDao
}

func NewSpxServiceableAreaService(mappingService order_account_mapping.OrderAccountMappingService, versionDao spx_serviceable_area_version.SpxServiceableAreaVersionDao, dataDao spx_serviceable_area_data.SpxServiceableAreaDataDao, compareTaskDao spx_serviceable_area_compare.SpxServiceableAreaCompareTaskDao) *spxServiceableAreaService {
	return &spxServiceableAreaService{
		mappingService: mappingService,
		versionDao:     versionDao,
		dataDao:        dataDao,
		compareTaskDao: compareTaskDao,
	}
}

func (s *spxServiceableAreaService) ListAllOrderAccount(ctx utils.LCOSContext, region string) ([]*spx_service.OrderAccountEnum, *lcos_error.LCOSError) {
	// 转发给spx fleet order
	ret, err := spx_service.NewSpxFleetOrderService(ctx, region).GetAllOrderAccount(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get order account list from spx fleet order error: %s", err.Msg)
		return nil, err
	}
	return ret, nil
}

func (s *spxServiceableAreaService) GetLocationType(ctx utils.LCOSContext, region string) (uint8, *lcos_error.LCOSError) {
	locationType, ok := spx_serviceable_area_utils.GetLocationType(region)
	if !ok {
		return 0, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "unsupported region %s", region)
	}
	return locationType, nil
}

// CreateSpxServiceableAreaVersion 版本创建通知，版本结果状态为Scheduled
func (s *spxServiceableAreaService) CreateSpxServiceableAreaVersion(ctx utils.LCOSContext, region string, planning *spx_service.ServiceableAreaScheduledPlanning) *lcos_error.LCOSError {
	region = strings.ToUpper(region)

	// 1. 检查region是否支持spx服务范围校验
	if !spx_serviceable_area_utils.CheckRegionIsSupported(region) {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "unsupported region %s", region)
	}

	// 2. 判断是否需要接收此版本
	exists, err := s.CheckSpxServiceableAreaVersionExists(ctx, region, planning.PlanningId)
	if err != nil {
		return err
	}
	if !exists {
		version := s.initSpxServiceableAreaVersionFromPlanning(ctx, region, planning)
		if err := s.versionDao.CreateOrUpdateSpxServiceableAreaVersion(ctx, version); err != nil {
			return err
		}
	}

	// 3. 异步分批从spx拉取此版本的配置数据
	go func() {
		if err := s.PullSpxServiceableAreaData(ctx, region, planning.PlanningId); err != nil {
			// 拉取配置失败，发送告警。然后可以由dev手动触发拉取
			notifyCfg := config.GetSpxServiceableAreaNotifyConfig()
			message := fmt.Sprintf(spx_serviceable_constant.PullDataErrorMessageTemplate,
				region,
				planning.PlanningId,
				err.Msg,
			)
			_ = seatalk.NotifyWithTextMessage(ctx, notifyCfg.Webhook, message, []string{notifyCfg.Email}, false)

			logger.CtxLogErrorf(ctx, "pull spx serviceable area error|region=%d, version=%d, cause=%s", region, planning.PlanningId, err.Msg)
		}
	}()

	return nil
}

// EnableSpxServiceableAreaVersion 版本生效通知，版本结果状态为Incoming
func (s *spxServiceableAreaService) EnableSpxServiceableAreaVersion(ctx utils.LCOSContext, region string, planning *spx_service.ServiceableAreaScheduledPlanning) *lcos_error.LCOSError {
	region = strings.ToUpper(region)

	// 1. 检查region是否支持spx服务范围校验
	if !spx_serviceable_area_utils.CheckRegionIsSupported(region) {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "unsupported region %s", region)
	}

	// 2. 更新或创建版本，结果状态为Incoming
	exists, err := s.CheckSpxServiceableAreaVersionExists(ctx, region, planning.PlanningId)
	if err != nil {
		return err
	}
	if exists {
		return s.versionDao.UpdateSpxServiceableAreaVersionByVersionId(ctx, region, planning.PlanningId, map[string]interface{}{"version_status": spx_serviceable_constant.VersionStatusIncoming})
	} else {
		verion := s.initSpxServiceableAreaVersionFromPlanning(ctx, region, planning)
		verion.VersionStatus = spx_serviceable_constant.VersionStatusIncoming
		return s.versionDao.CreateOrUpdateSpxServiceableAreaVersion(ctx, verion)
	}
}

// CancelSpxServiceableAreaVersion 版本取消通知，版本结果状态为Expired
func (s *spxServiceableAreaService) CancelSpxServiceableAreaVersion(ctx utils.LCOSContext, region string, planningId uint64) *lcos_error.LCOSError {
	region = strings.ToUpper(region)

	// 1. 检查region是否支持spx服务范围校验
	if !spx_serviceable_area_utils.CheckRegionIsSupported(region) {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "unsupported region %s", region)
	}

	// 2. 检查版本是否存在
	version, err := s.versionDao.GetSpxServiceableAreaVersionByVersionId(ctx, region, planningId)
	if err != nil {
		return err
	}
	if version == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "version not found")
	}

	// 2. 将版本状态更新为Expired
	return s.versionDao.UpdateSpxServiceableAreaVersionByVersionId(ctx, region, planningId, map[string]interface{}{"version_status": spx_serviceable_constant.VersionStatusExpired})
}

// ActivateSpxServiceableAreaVersion 版本实际生效，版本结果状态为Active
func (s *spxServiceableAreaService) ActivateSpxServiceableAreaVersion(ctx utils.LCOSContext, version *spx_serviceable_area_version.SpxServiceableAreaVersion) *lcos_error.LCOSError {
	fn := func() *lcos_error.LCOSError {
		// 将过期版本的状态更新为Expired。过期版本是指生效时间在此版本之前且状态为Active、Incoming或者Scheduled的版本
		queryParams := map[string]interface{}{
			"region":            version.Region,
			"version_status in": spx_serviceable_constant.AvailableVersionStatus,
			"effective_time <":  version.EffectiveTime,
		}
		if err := s.versionDao.UpdateSpxServiceableAreaVersionByParams(ctx, queryParams, map[string]interface{}{"version_status": spx_serviceable_constant.VersionStatusExpired}); err != nil {
			return err
		}
		// 将此版本状态更新为Active
		return s.versionDao.UpdateSpxServiceableAreaVersionByVersionId(ctx, version.Region, version.VersionId, map[string]interface{}{"version_status": spx_serviceable_constant.VersionStatusActive})
	}
	return ctx.Transaction(fn)
}

// CheckSpxServiceableAreaVersionExists 校验版本是否存在并且非Expired和Cleared状态
func (s *spxServiceableAreaService) CheckSpxServiceableAreaVersionExists(ctx utils.LCOSContext, region string, versionId uint64) (bool, *lcos_error.LCOSError) {
	version, err := s.versionDao.GetSpxServiceableAreaVersionByVersionId(ctx, region, versionId)
	if err != nil {
		return false, err
	}
	if version == nil {
		return false, nil
	}
	if utils.CheckInUint8(version.VersionStatus, spx_serviceable_constant.ExpiredVersionStatus) {
		return false, nil
	}
	return true, nil
}

// PullSpxServiceableAreaData 拉取服务范围配置数据
func (s *spxServiceableAreaService) PullSpxServiceableAreaData(ctx utils.LCOSContext, region string, versionId uint64) *lcos_error.LCOSError {
	// 1. 检查此版本是否存在
	exists, err := s.CheckSpxServiceableAreaVersionExists(ctx, region, versionId)
	if err != nil {
		return err
	}
	if !exists {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "version not found")
	}

	// 2. 分批拉取spx服务范围配置数据
	var allList []*spx_service.ServiceableArea
	spxServiceIntegrationService := spx_service.NewSpxServiceIntegrationService(ctx, region)
	for i := 0; i < config.GetMaxLoopSizeForGeneral(ctx); i++ {
		subList, _, err := spxServiceIntegrationService.GetServiceableAreaByPaging(ctx, versionId, uint32(i)+1, pagingQueryBatchSize)
		if err != nil {
			return err
		}
		if len(subList) == 0 {
			break
		}
		allList = append(allList, subList...)
	}

	// 3. 将spx服务范围转换为db model并落库，同时生成服务范围对比任务
	var lineGroupList []string
	if spx_serviceable_area_utils.IsPostcodeRegion(region) {
		var postcodeList []*spx_serviceable_area_data.SpxServiceableAreaPostcodeData
		var compareTaskList []*spx_serviceable_area_compare.SpxServiceableAreaCompareTask
		var handleErr *lcos_error.LCOSError

		postcodeList, compareTaskList, lineGroupList, _, handleErr = s.handleSpxServiceableAreaPostcodeData(ctx, region, versionId, allList)
		if handleErr != nil {
			return handleErr
		}

		fn := func() *lcos_error.LCOSError {
			if err := s.dataDao.DeleteSpxServiceableAreaPostcodeData(ctx, map[string]interface{}{"region": region, "version_id": versionId}); err != nil {
				return err
			}
			if err := s.dataDao.BatchCreateSpxServiceableAreaPostcodeData(ctx, postcodeList); err != nil {
				return err
			}
			if err := s.compareTaskDao.DeleteSpxServiceableAreaCompareTask(ctx, map[string]interface{}{"region": region, "spx_version_id": versionId}); err != nil {
				return err
			}
			if err := s.compareTaskDao.BatchCreateSpxServiceableAreaCompareTask(ctx, compareTaskList); err != nil {
				return err
			}
			return nil
		}
		if err := ctx.Transaction(fn); err != nil {
			return err
		}
	} else {
		var locationList []*spx_serviceable_area_data.SpxServiceableAreaLocationData
		var compareTaskList []*spx_serviceable_area_compare.SpxServiceableAreaCompareTask
		var handleErr *lcos_error.LCOSError

		locationList, compareTaskList, lineGroupList, _, handleErr = s.handleSpxServiceableAreaLocationData(ctx, region, versionId, allList)
		if handleErr != nil {
			return handleErr
		}

		fn := func() *lcos_error.LCOSError {
			if err := s.dataDao.DeleteSpxServiceableAreaLocationData(ctx, map[string]interface{}{"region": region, "version_id": versionId}); err != nil {
				return err
			}
			if err := s.dataDao.BatchCreateSpxServiceableAreaLocationData(ctx, locationList); err != nil {
				return err
			}
			if err := s.compareTaskDao.DeleteSpxServiceableAreaCompareTask(ctx, map[string]interface{}{"region": region, "spx_version_id": versionId}); err != nil {
				return err
			}
			if err := s.compareTaskDao.BatchCreateSpxServiceableAreaCompareTask(ctx, compareTaskList); err != nil {
				return err
			}
			return nil
		}
		if err := ctx.Transaction(fn); err != nil {
			return err
		}
	}

	// 4. 将spx版本的data compared flag标记为true
	if err := s.versionDao.UpdateSpxServiceableAreaVersionByVersionId(ctx, region, versionId, map[string]interface{}{"sync_data_flag": constant.TRUE}); err != nil {
		return err
	}

	message := fmt.Sprintf(spx_serviceable_constant.SpxVersionNotifyMessageTemplate,
		region,
		utils.GetEnv(ctx),
		versionId,
		strings.Join(lineGroupList, ","),
	)
	cfg := config.GetSpxServiceableAreaNotifyConfig()
	_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, true)
	return nil
}

// ClearExpiredSpxServiceableArea 清理过期服务范围版本数据
func (s *spxServiceableAreaService) ClearExpiredSpxServiceableArea(ctx utils.LCOSContext) *lcos_error.LCOSError {
	// 1. 获取所有的过期版本（被取消的版本、已经过期的版本）
	expiredVersionList, err := s.versionDao.ListSpxServiceableAreaVersion(ctx, map[string]interface{}{"version_status": spx_serviceable_constant.VersionStatusExpired})
	if err != nil {
		return err
	}

	// 2. 清理过期版本的服务范围配置数据，同时将版本状态更新为Cleared
	for _, version := range expiredVersionList {
		// 清理服务范围配置数据，将版本状态更新为Cleared
		if err = s.clearExpiredSpxServiceableAreaVersion(ctx, version); err != nil {
			// 此过期版本配置清理失败，打印error日志，不影响其他版本配置的清理
			logger.CtxLogErrorf(ctx, "clear expired spx serviceable area error|region=%s, version_id=%d, cause=%s", version.Region, version.VersionId, err.Msg)
		}
	}
	return nil
}

func (s *spxServiceableAreaService) clearExpiredSpxServiceableAreaVersion(ctx utils.LCOSContext, version *spx_serviceable_area_version.SpxServiceableAreaVersion) *lcos_error.LCOSError {
	// SPX过期版本数据清理。不需要事务，只有最终版本状态轮转为Cleared才视为清理完成，否则可以在下一时间片重试
	// 1. 分批清理服务范围数据
	if spx_serviceable_area_utils.IsPostcodeRegion(version.Region) {
		if err := s.dataDao.DeleteSpxServiceableAreaPostcodeData(ctx, map[string]interface{}{"region": version.Region, "version_id": version.VersionId}); err != nil {
			return err
		}
	} else {
		if err := s.dataDao.DeleteSpxServiceableAreaLocationData(ctx, map[string]interface{}{"region": version.Region, "version_id": version.VersionId}); err != nil {
			return err
		}
	}
	// 2. 清理服务范围对比任务
	if err := s.compareTaskDao.DeleteSpxServiceableAreaCompareTask(ctx, map[string]interface{}{"region": version.Region, "spx_version_id": version.VersionId}); err != nil {
		return err
	}
	// 3. 将版本状态标记为Cleared
	return s.versionDao.UpdateSpxServiceableAreaVersionByVersionId(ctx, version.Region, version.VersionId, map[string]interface{}{"version_status": spx_serviceable_constant.VersionStatusCleared})
}

func (s *spxServiceableAreaService) UpdateSpxServiceableAreaData(ctx utils.LCOSContext, region string, versionId uint64, dataList []*spx_service.ServiceableArea) *lcos_error.LCOSError {
	// 1. 查询版本是否存在
	queryParams := map[string]interface{}{
		"region":            region,
		"version_id":        versionId,
		"version_status in": spx_serviceable_constant.AvailableVersionStatus,
	}
	versionList, err := s.versionDao.ListSpxServiceableAreaVersion(ctx, queryParams)
	if err != nil {
		return err
	}
	if len(versionList) == 0 {
		// 版本不存在，直接忽略
		return nil
	}

	// 2. 更新spx服务范围数据和对比任务
	var lineGroupList []string
	if spx_serviceable_area_utils.IsPostcodeRegion(region) {
		var postcodeList []*spx_serviceable_area_data.SpxServiceableAreaPostcodeData
		var compareTaskList []*spx_serviceable_area_compare.SpxServiceableAreaCompareTask
		var deletePostcodeList []string
		var err *lcos_error.LCOSError

		postcodeList, compareTaskList, lineGroupList, deletePostcodeList, err = s.handleSpxServiceableAreaPostcodeData(ctx, region, versionId, dataList)
		if err != nil {
			return err
		}
		fn := func() *lcos_error.LCOSError {
			if err := s.dataDao.DeleteSpxServiceableAreaPostcodeData(ctx, map[string]interface{}{"region": region, "version_id": versionId, "postcode in": deletePostcodeList}); err != nil {
				return err
			}
			if err := s.dataDao.BatchCreateOrUpdateSpxServiceableAreaPostcodeData(ctx, postcodeList); err != nil {
				return err
			}
			if err := s.compareTaskDao.BatchCreateSpxServiceableAreaCompareTask(ctx, compareTaskList); err != nil {
				return err
			}
			return nil
		}
		if err := ctx.Transaction(fn); err != nil {
			return err
		}
	} else {
		var locationList []*spx_serviceable_area_data.SpxServiceableAreaLocationData
		var compareTaskList []*spx_serviceable_area_compare.SpxServiceableAreaCompareTask
		var deleteLocationList []int
		var err *lcos_error.LCOSError

		locationList, compareTaskList, lineGroupList, deleteLocationList, err = s.handleSpxServiceableAreaLocationData(ctx, region, versionId, dataList)
		if err != nil {
			return err
		}
		fn := func() *lcos_error.LCOSError {
			if err := s.dataDao.DeleteSpxServiceableAreaLocationData(ctx, map[string]interface{}{"region": region, "version_id": versionId, "location_id in": deleteLocationList}); err != nil {
				return err
			}
			if err := s.dataDao.BatchCreateOrUpdateSpxServiceableAreaLocationData(ctx, locationList); err != nil {
				return err
			}
			if err := s.compareTaskDao.BatchCreateSpxServiceableAreaCompareTask(ctx, compareTaskList); err != nil {
				return err
			}
			return nil
		}
		if err := ctx.Transaction(fn); err != nil {
			return err
		}
	}

	// 3. spx版本更新通知
	message := fmt.Sprintf(spx_serviceable_constant.SpxVersionDataUpdateMessageTemplate,
		region,
		utils.GetEnv(ctx),
		versionId,
		strings.Join(lineGroupList, ","),
	)
	cfg := config.GetSpxServiceableAreaNotifyConfig()
	_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, true)
	return nil
}

// GetIncomingSpxServiceableAreaVersion 获取指定region的即将生效版本。即将生效版本判定条件：版本状态为Incoming、数据完成同步、生效时间最晚
func (s *spxServiceableAreaService) GetIncomingSpxServiceableAreaVersion(ctx utils.LCOSContext, region string) (*spx_serviceable_area_version.SpxServiceableAreaVersion, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":         region,
		"version_status": spx_serviceable_constant.VersionStatusIncoming,
		"sync_data_flag": constant.TRUE,
	}
	ret, err := s.versionDao.ListSpxServiceableAreaVersionWithOrder(ctx, queryParams, "effective_time DESC")
	if err != nil {
		return nil, err
	}
	if len(ret) == 0 {
		return nil, nil
	}
	if len(ret) > 1 {
		// 异常场景，加上报
		message := fmt.Sprintf("region: %s, version_list: %s", region, utils.MarshToStringWithoutError(ret))
		_ = monitor.AwesomeReportEvent(ctx, constant.CatSpxServiceableAreaReport, multiIncomingVersionError, constant.StatusError, message)
	}
	return ret[0], nil
}

// GetActiveSpxServiceableAreaVersion 查询指定region的生效版本
func (s *spxServiceableAreaService) GetActiveSpxServiceableAreaVersion(ctx utils.LCOSContext, region string) (*spx_serviceable_area_version.SpxServiceableAreaVersion, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":         region,
		"version_status": spx_serviceable_constant.VersionStatusActive,
	}
	versionList, err := s.versionDao.ListSpxServiceableAreaVersion(ctx, queryParams)
	if err != nil {
		return nil, err
	}
	if len(versionList) == 0 {
		return nil, nil
	}
	if len(versionList) > 1 {
		// 异常场景，有多个生效的版本
		message := fmt.Sprintf("region: %s, version_list: %s", region, utils.MarshToStringWithoutError(versionList))
		_ = monitor.AwesomeReportEvent(ctx, constant.CatSpxServiceableAreaReport, multiActiveVersionError, constant.StatusError, message)
	}
	return versionList[0], nil
}

func (s *spxServiceableAreaService) GetLastAvailableSpxServiceableAreaVersion(ctx utils.LCOSContext, region string, effectiveTime uint32) (*spx_serviceable_area_version.SpxServiceableAreaVersion, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":            region,
		"version_status in": spx_serviceable_constant.AvailableVersionStatus,
		"sync_data_flag":    constant.TRUE,
		"effective_time <=": effectiveTime,
	}
	ret, err := s.versionDao.ListSpxServiceableAreaVersionWithOrder(ctx, queryParams, "effective_time DESC")
	if err != nil {
		return nil, err
	}
	if len(ret) == 0 {
		return nil, nil
	}
	return ret[0], nil
}

// GetSpxServiceabelAreaLocationData 获取location类型的服务范围配置数据
func (s *spxServiceableAreaService) GetSpxServiceabelAreaLocationData(ctx utils.LCOSContext, region string, versionId uint64, orderAccount int, locationId uint64) (*spx_serviceable_area_data.SpxServiceableAreaLocationData, *lcos_error.LCOSError) {
	if spx_serviceable_area_utils.IsPostcodeRegion(region) {
		return nil, nil
	}

	queryParams := map[string]interface{}{
		"region":        region,
		"version_id":    versionId,
		"order_account": orderAccount,
		"location_id":   locationId,
	}
	ret, err := s.dataDao.ListSpxServiceableAreaLocationData(ctx, queryParams)
	if err != nil {
		return nil, err
	}
	if len(ret) == 0 {
		return nil, nil
	}
	return ret[0], nil
}

// GetSpxServiceabelAreaPostcodeData 获取postcode类型的服务范围配置数据
func (s *spxServiceableAreaService) GetSpxServiceabelAreaPostcodeData(ctx utils.LCOSContext, region string, versionId uint64, orderAccount int, postcode string) (*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, *lcos_error.LCOSError) {
	if !spx_serviceable_area_utils.IsPostcodeRegion(region) {
		return nil, nil
	}

	queryParams := map[string]interface{}{
		"region":        region,
		"version_id":    versionId,
		"order_account": orderAccount,
		"postcode":      postcode,
	}
	ret, err := s.dataDao.ListSpxServiceableAreaPostcodeData(ctx, queryParams)
	if err != nil {
		return nil, err
	}
	if len(ret) == 0 {
		return nil, nil
	}
	return ret[0], nil
}

func (s *spxServiceableAreaService) ListSpxServiceabelAreaLocationDataByLocationId(ctx utils.LCOSContext, region string, versionId uint64, locationId uint64) ([]*spx_serviceable_area_data.SpxServiceableAreaLocationData, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":      region,
		"version_id":  versionId,
		"location_id": locationId,
	}
	return s.dataDao.ListSpxServiceableAreaLocationData(ctx, queryParams)
}

func (s *spxServiceableAreaService) ListSpxServiceabelAreaPostcodeDataByPostcode(ctx utils.LCOSContext, region string, versionId uint64, postcode string) ([]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":     region,
		"version_id": versionId,
		"postcode":   postcode,
	}
	return s.dataDao.ListSpxServiceableAreaPostcodeData(ctx, queryParams)
}

func (s *spxServiceableAreaService) ListSpxServiceabelAreaLocationDataByOrderAccount(ctx utils.LCOSContext, region string, versionId uint64, orderAccount int) ([]*spx_serviceable_area_data.SpxServiceableAreaLocationData, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":        region,
		"version_id":    versionId,
		"order_account": orderAccount,
	}
	return s.dataDao.ListSpxServiceableAreaLocationData(ctx, queryParams)
}

func (s *spxServiceableAreaService) ListSpxServiceabelAreaPostcodeDataByOrderAccount(ctx utils.LCOSContext, region string, versionId uint64, orderAccount int) ([]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":        region,
		"version_id":    versionId,
		"order_account": orderAccount,
	}
	return s.dataDao.ListSpxServiceableAreaPostcodeData(ctx, queryParams)
}

func (s *spxServiceableAreaService) ListSpxServiceabelAreaLocationDataByOrderAccountList(ctx utils.LCOSContext, region string, versionId uint64, orderAccountList []int) ([]*spx_serviceable_area_data.SpxServiceableAreaLocationData, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":           region,
		"version_id":       versionId,
		"order_account in": orderAccountList,
	}
	return s.dataDao.ListSpxServiceableAreaLocationData(ctx, queryParams)
}

func (s *spxServiceableAreaService) ListSpxServiceabelAreaPostcodeDataByOrderAccountList(ctx utils.LCOSContext, region string, versionId uint64, orderAccountList []int) ([]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":           region,
		"version_id":       versionId,
		"order_account in": orderAccountList,
	}
	return s.dataDao.ListSpxServiceableAreaPostcodeData(ctx, queryParams)
}

func (s *spxServiceableAreaService) initSpxServiceableAreaVersionFromPlanning(ctx utils.LCOSContext, region string, planning *spx_service.ServiceableAreaScheduledPlanning) *spx_serviceable_area_version.SpxServiceableAreaVersion {
	modifyFlag := constant.FALSE
	if planning.ModifyFlag {
		modifyFlag = constant.TRUE
	}
	version := &spx_serviceable_area_version.SpxServiceableAreaVersion{
		Region:        region,
		VersionId:     planning.PlanningId,
		VersionName:   planning.PlanningName,
		EffectiveTime: planning.ActiveTime,
		VersionStatus: spx_serviceable_constant.VersionStatusScheduled,
		SyncDataFlag:  constant.FALSE,
		ModifyFlag:    modifyFlag,
		LastVersion:   planning.CopyFrom,
	}
	if lastVersion, _ := s.versionDao.GetSpxServiceableAreaVersionByVersionId(ctx, region, version.LastVersion); lastVersion != nil {
		version.LastVersionName = lastVersion.VersionName
	}
	return version
}

// importSpxServiceableAreaLocationData 解析和导入location类型的spx服务范围配置数据，同时生成对比任务
func (s *spxServiceableAreaService) importSpxServiceableAreaLocationData(ctx utils.LCOSContext, region string, versionId uint64, dataList []*spx_service.ServiceableArea) ([]string, *lcos_error.LCOSError) {
	var orderAccountSet []int
	orderAccountMap := make(map[int]bool)

	locationList := make([]*spx_serviceable_area_data.SpxServiceableAreaLocationData, 0, len(dataList))
	for _, data := range dataList {
		location, err := address_service.LocationServer.GetLocationInfoByName(ctx, region, data.State, data.City, data.District, data.Uptown)
		if err != nil {
			// 若此地址在sls无法查找到location id，则直接忽略掉这条配置
			logger.CtxLogErrorf(ctx, "location not found for spx serivceable area|region=%s, version_id=%d, state=%s, city=%s, district=%s, street=%s", region, versionId, data.State, data.City, data.District, data.Uptown)
			continue
		}
		locationId := location.GetLocationId()

		for _, orderAccount := range strings.Split(data.OrderAccount, ",") {
			orderAccountId, err := strconv.Atoi(orderAccount)
			if err != nil {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "contains invalid order account: %s", data.OrderAccount)
			}

			if _, ok := orderAccountMap[orderAccountId]; !ok {
				orderAccountMap[orderAccountId] = true
				orderAccountSet = append(orderAccountSet, orderAccountId)
			}

			locationList = append(locationList, &spx_serviceable_area_data.SpxServiceableAreaLocationData{
				Region:        region,
				VersionId:     versionId,
				OrderAccount:  orderAccountId,
				LocationId:    uint64(locationId),
				CanPickup:     data.Pickup,
				CanCodPickup:  data.CodPickup,
				CanDeliver:    data.Delivery,
				CanCodDeliver: data.CodDelivery,
			})
		}
	}

	lineGroupList, compareTaskList, err := s.generateSpxServiceableAreaCompareTasks(ctx, region, constant.LOCATION, versionId, orderAccountSet)
	if err != nil {
		return nil, err
	}

	fn := func() *lcos_error.LCOSError {
		if err := s.dataDao.DeleteSpxServiceableAreaLocationData(ctx, map[string]interface{}{"region": region, "version_id": versionId}); err != nil {
			return err
		}
		if err := s.dataDao.BatchCreateSpxServiceableAreaLocationData(ctx, locationList); err != nil {
			return err
		}
		if err := s.compareTaskDao.DeleteSpxServiceableAreaCompareTask(ctx, map[string]interface{}{"region": region, "spx_version_id": versionId}); err != nil {
			return err
		}
		if err := s.compareTaskDao.BatchCreateSpxServiceableAreaCompareTask(ctx, compareTaskList); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fn); err != nil {
		return nil, err
	}
	return lineGroupList, nil
}

func (s *spxServiceableAreaService) handleSpxServiceableAreaLocationData(ctx utils.LCOSContext, region string, versionId uint64, dataList []*spx_service.ServiceableArea) ([]*spx_serviceable_area_data.SpxServiceableAreaLocationData, []*spx_serviceable_area_compare.SpxServiceableAreaCompareTask, []string, []int, *lcos_error.LCOSError) {
	var orderAccountSet []int
	orderAccountMap := make(map[int]bool)

	var locationSet []int
	locationMap := make(map[int]bool)

	locationList := make([]*spx_serviceable_area_data.SpxServiceableAreaLocationData, 0, len(dataList))
	for _, data := range dataList {
		location, err := address_service.LocationServer.GetLocationInfoByName(ctx, region, data.State, data.City, data.District, data.Uptown)
		if err != nil {
			// 若此地址在sls无法查找到location id，则直接忽略掉这条配置
			logger.CtxLogErrorf(ctx, "location not found for spx serivceable area|region=%s, version_id=%d, state=%s, city=%s, district=%s, street=%s", region, versionId, data.State, data.City, data.District, data.Uptown)
			continue
		}
		locationId := location.GetLocationId()

		for _, orderAccount := range strings.Split(data.OrderAccount, ",") {
			orderAccountId, err := strconv.Atoi(orderAccount)
			if err != nil {
				return nil, nil, nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "contains invalid order account: %s", data.OrderAccount)
			}

			if _, ok := orderAccountMap[orderAccountId]; !ok {
				orderAccountMap[orderAccountId] = true
				orderAccountSet = append(orderAccountSet, orderAccountId)
			}

			locationList = append(locationList, &spx_serviceable_area_data.SpxServiceableAreaLocationData{
				Region:        region,
				VersionId:     versionId,
				OrderAccount:  orderAccountId,
				LocationId:    uint64(locationId),
				CanPickup:     data.Pickup,
				CanCodPickup:  data.CodPickup,
				CanDeliver:    data.Delivery,
				CanCodDeliver: data.CodDelivery,
			})
		}

		if _, ok := locationMap[locationId]; !ok {
			locationMap[locationId] = true
			locationSet = append(locationSet, locationId)
		}
	}

	lineGroupList, compareTaskList, err := s.generateSpxServiceableAreaCompareTasks(ctx, region, constant.LOCATION, versionId, orderAccountSet)
	if err != nil {
		return nil, nil, nil, nil, err
	}
	return locationList, compareTaskList, lineGroupList, locationSet, nil
}

// importSpxServiceableAreaPostcodeData 解析和导入postcode类型的spx服务范围配置数据，同时生成对比任务
func (s *spxServiceableAreaService) importSpxServiceableAreaPostcodeData(ctx utils.LCOSContext, region string, versionId uint64, dataList []*spx_service.ServiceableArea) ([]string, *lcos_error.LCOSError) {
	var orderAccountSet []int
	orderAccountMap := make(map[int]bool)

	postcodeList := make([]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, 0, len(dataList))
	for _, data := range dataList {
		for _, orderAccount := range strings.Split(data.OrderAccount, ",") {
			orderAccountId, err := strconv.Atoi(orderAccount)
			if err != nil {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "contains invalid order account: %s", data.OrderAccount)
			}

			if _, ok := orderAccountMap[orderAccountId]; !ok {
				orderAccountMap[orderAccountId] = true
				orderAccountSet = append(orderAccountSet, orderAccountId)
			}

			postcodeList = append(postcodeList, &spx_serviceable_area_data.SpxServiceableAreaPostcodeData{
				Region:        region,
				VersionId:     versionId,
				OrderAccount:  orderAccountId,
				Postcode:      data.Postcode,
				CanPickup:     data.Pickup,
				CanCodPickup:  data.CodPickup,
				CanDeliver:    data.Delivery,
				CanCodDeliver: data.CodDelivery,
			})
		}
	}

	lineGroupList, compareTaskList, err := s.generateSpxServiceableAreaCompareTasks(ctx, region, constant.POSTCODE, versionId, orderAccountSet)
	if err != nil {
		return nil, err
	}

	fn := func() *lcos_error.LCOSError {
		if err := s.dataDao.DeleteSpxServiceableAreaPostcodeData(ctx, map[string]interface{}{"region": region, "version_id": versionId}); err != nil {
			return err
		}
		if err := s.dataDao.BatchCreateSpxServiceableAreaPostcodeData(ctx, postcodeList); err != nil {
			return err
		}
		if err := s.compareTaskDao.DeleteSpxServiceableAreaCompareTask(ctx, map[string]interface{}{"region": region, "spx_version_id": versionId}); err != nil {
			return err
		}
		if err := s.compareTaskDao.BatchCreateSpxServiceableAreaCompareTask(ctx, compareTaskList); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fn); err != nil {
		return nil, err
	}
	return lineGroupList, nil
}

func (s *spxServiceableAreaService) handleSpxServiceableAreaPostcodeData(ctx utils.LCOSContext, region string, versionId uint64, dataList []*spx_service.ServiceableArea) ([]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, []*spx_serviceable_area_compare.SpxServiceableAreaCompareTask, []string, []string, *lcos_error.LCOSError) {
	var orderAccountSet []int
	orderAccountMap := make(map[int]bool)
	var postcodeSet []string
	postcodeMap := make(map[string]bool)

	postcodeList := make([]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData, 0, len(dataList))
	for _, data := range dataList {
		for _, orderAccount := range strings.Split(data.OrderAccount, ",") {
			orderAccountId, err := strconv.Atoi(orderAccount)
			if err != nil {
				return nil, nil, nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "contains invalid order account: %s", data.OrderAccount)
			}

			if _, ok := orderAccountMap[orderAccountId]; !ok {
				orderAccountMap[orderAccountId] = true
				orderAccountSet = append(orderAccountSet, orderAccountId)
			}

			postcodeList = append(postcodeList, &spx_serviceable_area_data.SpxServiceableAreaPostcodeData{
				Region:        region,
				VersionId:     versionId,
				OrderAccount:  orderAccountId,
				Postcode:      data.Postcode,
				CanPickup:     data.Pickup,
				CanCodPickup:  data.CodPickup,
				CanDeliver:    data.Delivery,
				CanCodDeliver: data.CodDelivery,
			})
		}

		if _, ok := postcodeMap[data.Postcode]; !ok {
			postcodeMap[data.Postcode] = true
			postcodeSet = append(postcodeSet, data.Postcode)
		}
	}

	lineGroupList, compareTaskList, err := s.generateSpxServiceableAreaCompareTasks(ctx, region, constant.POSTCODE, versionId, orderAccountSet)
	if err != nil {
		return nil, nil, nil, nil, err
	}
	return postcodeList, compareTaskList, lineGroupList, postcodeSet, nil
}

// generateSpxServiceableAreaCompareTasks 生成服务范围对比任务列表
func (s *spxServiceableAreaService) generateSpxServiceableAreaCompareTasks(ctx utils.LCOSContext, region string, saType uint8, versionId uint64, orderAccountList []int) ([]string, []*spx_serviceable_area_compare.SpxServiceableAreaCompareTask, *lcos_error.LCOSError) {
	orderAccountMappingList, err := s.mappingService.BatchGetOrderAccountMappingByOrderAccount(ctx, region, orderAccountList)
	if err != nil {
		return nil, nil, err
	}
	var lineGroupSet []string
	lineGroupMap := make(map[string]bool)
	compareTaskList := make([]*spx_serviceable_area_compare.SpxServiceableAreaCompareTask, 0, len(orderAccountMappingList))
	for _, mapping := range orderAccountMappingList {
		compareTaskList = append(compareTaskList, &spx_serviceable_area_compare.SpxServiceableAreaCompareTask{
			Region:           region,
			LineId:           mapping.LineId,
			GroupId:          mapping.GroupId,
			OrderAccount:     mapping.OrderAccount,
			OrderAccountName: mapping.OrderAccountName,
			SaType:           saType,
			SpxVersionId:     versionId,
			CompareStatus:    spx_serviceable_constant.CompareStatusNew,
			CompareVersion:   []uint32{},
		})

		lineGroupKey := mapping.LineId + "+" + mapping.GroupId
		if _, ok := lineGroupMap[lineGroupKey]; !ok {
			lineGroupSet = append(lineGroupSet, lineGroupKey)
			lineGroupMap[lineGroupKey] = true
		}
	}
	return lineGroupSet, compareTaskList, nil
}
