package branch

import (
	"bufio"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pis_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/station_conf"
	"io"
	"io/ioutil"
	"path"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	stringutil "git.garena.com/shopee/bg-logistics/go/chassis/utils/string"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcs_service/dto"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/pis_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/branch_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/spex_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/branch_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/postal_code_to_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spex_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	json "github.com/json-iterator/go"
)

const (
	branchErrorResultFileName                = "branch-error-result.xlsx"
	branchExportResultFileName               = "branch-export-template.xlsx"
	branchExportPartialSuccessResultFileName = "branch-partial-success-template.xlsx"
)

var (
	// timeZoneFormat = regexp.MustCompile(`^[+|-](\d+)(\.\d)?$`)
	timeFormatHHMMSS = regexp.MustCompile(`^\d\d:\d\d:\d\d$`)
	timeFormatHHMM   = regexp.MustCompile(`^\d\d:\d\d$`)
	timeFormatHMMSS  = regexp.MustCompile(`^\d:\d\d:\d\d$`)
)

// SPLN-25563: check and fix originbranch opening hours with fallback logic
func checkAndFixOriginBranchOpeningHours(ctx utils.LCOSContext, branch *OriginBranchData) {
	temp := make([]*OpeningHour, 0)
	for _, oh := range branch.OpeningHours {
		dateIsNull := false
		// check date is null or not
		if len(oh.DayOfWeek) == 0 {
			if branch.BranchType == branch_constant.Box {
				// branch type is box, mark dateIsNull flag as true, then check time is null or not
				dateIsNull = true
			} else {
				// opening hours fallback: branch type is store, date is null, then ignore this record
				logger.CtxLogInfof(ctx, "opening hours fallback: branch type is store and date is null, skip this record[branch_ref:%s, opening hours:%+v]", branch.BranchRef, oh)
				continue
			}
		}
		// check time is null/incomplete/invalid or not
		if !checkTimeFormat(oh.StartTime) || !checkTimeFormat(oh.EndTime) {
			if branch.BranchType != branch_constant.Box {
				// opening hours fallback: branch type is store, time is null, then ignore this record
				logger.CtxLogInfof(ctx, "opening hours fallback: branch type is store and time is null, skip this record[branch_ref:%s, opening hours:%+v]", branch.BranchRef, oh)
				continue
			}
			if dateIsNull {
				// opening hours fallback: branch type is box, date and time all are null, then ignore this record
				logger.CtxLogInfof(ctx, "opening hours fallback: branch type is box and data&time is null, skip this record[branch_ref:%s, opening hours:%+v]", branch.BranchRef, oh)
				continue
			}
			// opening hours fallback: branch type is box, time is null and date is not null, use default time and date
			oh.StartTime = DefaultOpeningHoursStart
			oh.EndTime = DefaultOpeningHoursEnd
			oh.DayOfWeek = []uint16{1, 2, 3, 4, 5, 6, 7}
		}
		if dateIsNull {
			// the branch type must be box
			// opening hours fallback: branch type is box, date is null and time is not null, use default time and date
			logger.CtxLogInfof(ctx, "opening hours fallback: branch type is box and date is null, use default date[branch_ref:%s, opening hours:%+v]", branch.BranchRef, oh)
			oh.StartTime = DefaultOpeningHoursStart
			oh.EndTime = DefaultOpeningHoursEnd
			oh.DayOfWeek = []uint16{1, 2, 3, 4, 5, 6, 7}
		}
		temp = append(temp, oh)
	}
	branch.OpeningHours = temp
}

// checkTimeFormat 时分秒校验，`HH:MM:SS`或`HH:MM`或`H:MM:SS`均符合规则(SPLN-28144、SPLN-30660)
func checkTimeFormat(s string) bool {
	// 数据中`HH:MM:SS`较多，易于命中，将该模式匹配置前
	if timeFormatHHMMSS.MatchString(s) {
		return true
	}
	// 数据中`HH:MM`数量较少，置后
	if timeFormatHHMM.MatchString(s) {
		return true
	}
	// 数据中`H:MM:SS`数量较少，置后
	if timeFormatHMMSS.MatchString(s) {
		return true
	}
	return false
}

// getSubStatus 设置默认的sub status，status=1->sub status=1;status=2->sub status=0,其余返回原始值
func getSubStatus(branchStatus uint8, branchSubStatus uint8) uint8 {
	subStatus := branchSubStatus
	// 设置当status为valid的时候，sub status为available
	if branchStatus == branch_constant.Valid && branchSubStatus != branch_constant.Available {
		return branch_constant.Available
	}
	// 当status为invalid的时候，sub status默认为unAvailable
	_, ok := branch_constant.SubStatusMapper[branchSubStatus]
	availableFlag := branchSubStatus == branch_constant.Available || !ok
	if branchStatus == branch_constant.Invalid && availableFlag {
		return branch_constant.Unavailable
	}

	return subStatus
}

type BranchService interface {
	// branch group
	ListBranchGroup(ctx utils.LCOSContext, request *protocol.ListBranchGroupRequest) ([]map[string]interface{}, uint32, *lcos_error.LCOSError)
	GetBranchGroupInfo(ctx utils.LCOSContext, branchGroupID uint32) (map[string]interface{}, *lcos_error.LCOSError)
	CreateBranchGroup(ctx utils.LCOSContext, request *protocol.CreateBranchGroupRequest) (*branch_group.LogisticBranchGroupTab, *lcos_error.LCOSError)
	UpdateBranchGroup(ctx utils.LCOSContext, request *protocol.UpdateBranchGroupRequest) (*branch_group.LogisticBranchGroupTab, *lcos_error.LCOSError)
	SyncBranchInfoFromChannel(ctx utils.LCOSContext, branchSupplyType uint32, region, remoteFilePath, requestID string, endpoint, accessKeyID, bucketName string, isIncremental bool) *lcos_error.LCOSError
	SyncBranchData(ctx utils.LCOSContext, branchSupplyType uint32, region, requestID string, data []pis_protocol.BranchData, operationTime uint32) (*pis_protocol.SyncBranchResponseData, *lcos_error.LCOSError)
	GetSyncBranchSupplyType(ctx utils.LCOSContext, region string) ([]uint32, *lcos_error.LCOSError)
	// SPLN-26133
	GetSyncBranchSupplyTypeForLCOSSync(ctx utils.LCOSContext, region string) ([]uint32, *lcos_error.LCOSError)
	SendSyncBranchCommand(ctx utils.LCOSContext, branchGroupID uint32) *lcos_error.LCOSError
	GetBranchGroupInfoByGroupId(ctx utils.LCOSContext, branchGroupID uint32) (*branch_group.LogisticBranchGroupTab, *lcos_error.LCOSError)
	DeleteBranchGroup(ctx utils.LCOSContext, request *protocol.DeleteBranchGroupRequest) *lcos_error.LCOSError

	// branch
	ListBranch(ctx utils.LCOSContext, request *protocol.ListBranchRequest) ([]*branch_info.LogisticBranchInfoTab, uint32, *lcos_error.LCOSError)
	// 和list branch相比，导出所有的branch信息
	ListAllBranches(ctx utils.LCOSContext, request *protocol.ExportBranchRequest, region string) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError)
	GetBranch(ctx utils.LCOSContext, branchID uint64) (*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError)
	GetBranchFromCache(ctx utils.LCOSContext, branchID uint64, branchGroupIDs []uint32) (*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError)
	FindBranch(ctx utils.LCOSContext, req *pb.SearchBranchRequest) ([]*branch_info.LogisticBranchInfoTab, []float64, *lcos_error.LCOSError)
	FindBranchGeneral(ctx utils.LCOSContext, req *SearchBranchReq) ([]*branch_info.LogisticBranchInfoTab, []float64, *lcos_error.LCOSError)
	BatchGetBranchInfoByBranchId(ctx utils.LCOSContext, batchBranchReq []*pb.GetBranchInfoByBranchIdRequest) []*pb.SingleBranchInfo
	BatchGetBranchInfoByBranchRef(ctx utils.LCOSContext, batchBranchReq []*pb.GetBranchInfoByBranchRefRequest) []*pb.SingleBranchInfo
	FindDropoffBranch(ctx utils.LCOSContext, req *pb.SearchBranchRequest) ([]*branch_info.LogisticBranchInfoTab, []float64, *lcos_error.LCOSError)

	AllBranchSupply(ctx utils.LCOSContext, region *string) ([]map[string]interface{}, *lcos_error.LCOSError)

	// 通过branch group id获取所有的branch信息
	GetAllBranchesByBranchGroupID(ctx utils.LCOSContext, branchGroupID uint64) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError)

	// for sls-api
	GetBranchByGroupIdAndLocationId(ctx utils.LCOSContext, branchGroupID uint32, locationId uint64, subStatus []pb.SubStatusEnum) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError)
	GetSubLocationsByGroupIdAndLocationId(ctx utils.LCOSContext, branchGroupID uint32, locationId uint64) (map[string][]uint64, *lcos_error.LCOSError)

	// LCOS同步branch信息，用于迁移pis branch
	SyncBranchInfo(ctx utils.LCOSContext, logID string)

	// for branch upload export, SPLN-21665
	UploadBranch(ctx utils.LCOSContext, fileUrl string, supplyType int, branchType uint32, region, operator string) (string, string, string, uint8, *lcos_error.LCOSError)
	ListBranchTaskRecords(ctx utils.LCOSContext, page, count uint32) ([]*branch_task_record.LogisticBranchTaskRecordTab, uint32, *lcos_error.LCOSError)

	// branch task record相关接口
	ListAllBranchTaskRecords(ctx utils.LCOSContext, queryMaps map[string]interface{}) ([]*branch_task_record.LogisticBranchTaskRecordTab, *lcos_error.LCOSError)
	CreateBranchTaskRecord(ctx utils.LCOSContext, branchTaskRecord *branch_task_record.LogisticBranchTaskRecordTab) *lcos_error.LCOSError
	UpdateBranchTaskRecord(ctx utils.LCOSContext, updatedMap map[string]interface{}, queryMaps map[string]interface{}) *lcos_error.LCOSError
	// 通过supply type获取到
	GetSupplyNameAndDefaultBranchType(ctx utils.LCOSContext, supplyType int, region string) (string, uint8, *lcos_error.LCOSError)

	// branch task导出相关接口
	// 生成部分成功的错误文件
	GeneratePartialSuccessResultFile(ctx utils.LCOSContext, errors []*RawUploadBranchData, fileName string, region string) (string, string, string, uint8, *lcos_error.LCOSError)
	// 生成导出文件
	GenerateExportResultFile(ctx utils.LCOSContext, exportedData []*branch_info.LogisticBranchInfoTab, fileName, region string) (string, string, string, *lcos_error.LCOSError)
	// 生成错误文件，文件中仅包含一行错误信息
	GenerateErrorResultFile(ctx utils.LCOSContext, errorMsg, region string) (string, string, string, *lcos_error.LCOSError)
	// SPLN
}

type branchService struct {
	branchDao          branch_info.BranchDao
	branchGroupDao     branch_group.BranchGroupDao
	lcsService         lcs_service.LcsService
	s3Service          s3_service.S3Service
	spexService        spex_service.SpexService
	postalCodeToGeoDao postal_code_to_geo.PostalCodeToGeoDao
	branchTaskDao      branch_task_record.BranchTaskRecordDao
	logisticStationDao station_conf.LogisticStationDao
}

// GeneratePartialSuccessResultFile
//
//	@Description:   使用解析的结果，导出partial success的文件，注意此时有可能全成功或者全失败
//	@receiver b
//	@param ctx
//	@param errors        每行导出的信息
//	@param fileName      导出的文件名，一般通过解析文件url获取
//	@param region        region
//	@return string       file url
//	@return string       file name，相比于入参的filename，会增加时间
//	@return string       file size，eg: 10.6KB
//	@return uint8        最终的任务状态
//	@return *lcos_error.LCOSError
func (b *branchService) GeneratePartialSuccessResultFile(ctx utils.LCOSContext, errors []*RawUploadBranchData, fileName string, region string) (string, string, string, uint8, *lcos_error.LCOSError) {
	isAllFailed := true  // 表示是否所有的上传record都失败
	isAllSuccess := true // 表示是否所有的上传record都成功

	templateFilePath := path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/", branchExportPartialSuccessResultFileName)

	// 打开模板
	f, err := excelize.OpenFile(templateFilePath)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file: %s", templateFilePath)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", "", "", branch_constant.BranchTaskStatusFailed, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	// 在第一列写错误信息
	for index, exportData := range errors {
		// 从第二行开始写
		// 解析时间为前端的格式
		var errorList []error
		if len(exportData.ErrorMsg) == 0 {
			exportData.ErrorMsg = "Success"
			isAllFailed = false
		} else {
			isAllSuccess = false
		}
		errorList = append(errorList,
			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", index+2), exportData.ErrorMsg),
			f.SetCellValue("Sheet1", fmt.Sprintf("B%d", index+2), exportData.ActionCode),
			f.SetCellValue("Sheet1", fmt.Sprintf("C%d", index+2), exportData.SLSBranchID),
			f.SetCellValue("Sheet1", fmt.Sprintf("D%d", index+2), exportData.TPLBranchID),
			f.SetCellValue("Sheet1", fmt.Sprintf("E%d", index+2), exportData.BranchName),
			f.SetCellValue("Sheet1", fmt.Sprintf("F%d", index+2), exportData.Status),
			f.SetCellValue("Sheet1", fmt.Sprintf("G%d", index+2), exportData.SubStatus),
			f.SetCellValue("Sheet1", fmt.Sprintf("H%d", index+2), exportData.Country),
			f.SetCellValue("Sheet1", fmt.Sprintf("I%d", index+2), exportData.State),
			f.SetCellValue("Sheet1", fmt.Sprintf("J%d", index+2), exportData.City),
			f.SetCellValue("Sheet1", fmt.Sprintf("K%d", index+2), exportData.District),
			f.SetCellValue("Sheet1", fmt.Sprintf("L%d", index+2), exportData.SubDistrict),
			f.SetCellValue("Sheet1", fmt.Sprintf("M%d", index+2), exportData.Street),
			f.SetCellValue("Sheet1", fmt.Sprintf("N%d", index+2), exportData.DetailedAddress),
			f.SetCellValue("Sheet1", fmt.Sprintf("O%d", index+2), exportData.Postcode),
			f.SetCellValue("Sheet1", fmt.Sprintf("P%d", index+2), exportData.Longitude),
			f.SetCellValue("Sheet1", fmt.Sprintf("Q%d", index+2), exportData.Latitude),
			f.SetCellValue("Sheet1", fmt.Sprintf("R%d", index+2), exportData.Phone),
			f.SetCellValue("Sheet1", fmt.Sprintf("S%d", index+2), exportData.MaxParcelStayDuration),
			f.SetCellValue("Sheet1", fmt.Sprintf("T%d", index+2), exportData.Monday),
			f.SetCellValue("Sheet1", fmt.Sprintf("U%d", index+2), exportData.Tuesday),
			f.SetCellValue("Sheet1", fmt.Sprintf("V%d", index+2), exportData.Wednesday),
			f.SetCellValue("Sheet1", fmt.Sprintf("W%d", index+2), exportData.Thursday),
			f.SetCellValue("Sheet1", fmt.Sprintf("X%d", index+2), exportData.Friday),
			f.SetCellValue("Sheet1", fmt.Sprintf("Y%d", index+2), exportData.Saturday),
			f.SetCellValue("Sheet1", fmt.Sprintf("Z%d", index+2), exportData.Sunday),
			f.SetCellValue("Sheet1", fmt.Sprintf("AA%d", index+2), exportData.Timezone),
			f.SetCellValue("Sheet1", fmt.Sprintf("AB%d", index+2), exportData.ExtraData),
			f.SetCellValue("Sheet1", fmt.Sprintf("AC%d", index+2), exportData.OpsType),
			f.SetCellValue("Sheet1", fmt.Sprintf("AD%d", index+2), exportData.LocationDescription),
		)

		for _, err1 := range errorList {
			if err1 != nil {
				errMsg := fmt.Sprintf("cannot write error msg to file|error_msg=%s", err1.Error())
				logger.CtxLogErrorf(ctx, errMsg)
				return "", "", "", branch_constant.BranchTaskStatusFailed, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
			}
		}
	}

	// 以当前的时间戳生成临时文件
	tmpFileName := fmt.Sprintf("/tmp/%s-%s.xlsx", fileName, pickup.GetCurrentTime(ctx, region).Format("20060102150405"))

	// 将空格替换为中划线，防止文件名可能存在的空格，导致文件无法导出
	reg := regexp.MustCompile(" +")
	tmpFileName = reg.ReplaceAllString(tmpFileName, "-")

	err = f.SaveAs(tmpFileName)
	if err != nil {
		logger.CtxLogErrorf(ctx, err.Error())
		return "", "", "", branch_constant.BranchTaskStatusFailed, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	// 上传文件
	fileUrl, fileName, fileSizeStr, lcosErr := b.s3Service.UploadFileToS3(ctx, config.GetConf(ctx).LCOSBranchServiceS3Config.AccessKeyID, config.GetConf(ctx).LCOSBranchServiceS3Config.BucketKey, tmpFileName, config.GetConf(ctx).LCOSBranchServiceS3Config.TimeOut, config.GetConf(ctx).LCOSBranchServiceS3Config.ExpirationDays, "")
	var taskStatus uint8
	if isAllSuccess {
		taskStatus = branch_constant.BranchTaskStatusDone
	} else if isAllFailed {
		taskStatus = branch_constant.BranchTaskStatusFailed
	} else {
		taskStatus = branch_constant.BranchTaskStatusPartialSuccess
	}
	return fileUrl, fileName, fileSizeStr, taskStatus, lcosErr
}

func parseTimeRange(openingHours branch_info.OpeningHourSlice, dayIndex int) string {
	var openingHoursStringList []string
	for _, openingHour := range openingHours {
		if utils.InUint16Slice(uint16(dayIndex), openingHour.DayOfWeek) {
			openingHoursStringList = append(openingHoursStringList, fmt.Sprintf("%s-%s", openingHour.StartTime, openingHour.EndTime))
		}
	}
	return strings.Join(openingHoursStringList, ";")
}

func (b *branchService) GenerateExportResultFile(ctx utils.LCOSContext, exportedDatas []*branch_info.LogisticBranchInfoTab, fileName, region string) (string, string, string, *lcos_error.LCOSError) {
	templateFilePath := path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/", branchExportResultFileName)

	// 打开模板
	f, err := excelize.OpenFile(templateFilePath)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file: %s, err:%v", templateFilePath, err)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	// 在第一行写错误信息
	for index, exportData := range exportedDatas {
		// 从第二行开始写
		// 将branch group ids写作列表
		var branchGroupString []string
		for _, branchGroupID := range exportData.BranchGroupIDs {
			branchGroupString = append(branchGroupString, strconv.Itoa(int(branchGroupID)))
		}
		branchStatus := branch_constant.BranchStatusValid
		if exportData.BranchStatus == branch_constant.Invalid {
			branchStatus = branch_constant.BranchStatusInvalid
		}
		branchSubStatus, ok := branch_constant.SubStatusMapper[exportData.BranchSubStatus]
		if !ok {
			logger.CtxLogErrorf(ctx, "branch sub status is not correct")
			//默认返回sub status
			branchSubStatus = branch_constant.SubStatusMapper[getDefaultSubStatus(exportData.BranchStatus)]
		}

		extraData, _ := json.MarshalToString(exportData.ExtraData)

		timeZone := ""
		if len(exportData.OpeningHours) > 0 {
			timeZone = exportData.OpeningHours[0].TimeZone
		}

		// 解析时间为前端的格式
		var errorList []error

		errorList = append(errorList,
			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", index+2), strings.Join(branchGroupString, ",")),
			f.SetCellValue("Sheet1", fmt.Sprintf("B%d", index+2), exportData.SupplyType),
			f.SetCellValue("Sheet1", fmt.Sprintf("C%d", index+2), exportData.BranchID),
			f.SetCellValue("Sheet1", fmt.Sprintf("D%d", index+2), exportData.BranchRef),
			f.SetCellValue("Sheet1", fmt.Sprintf("E%d", index+2), exportData.BranchName),
			f.SetCellValue("Sheet1", fmt.Sprintf("F%d", index+2), branchStatus),
			f.SetCellValue("Sheet1", fmt.Sprintf("G%d", index+2), branchSubStatus),
			f.SetCellValue("Sheet1", fmt.Sprintf("H%d", index+2), exportData.Region),
			f.SetCellValue("Sheet1", fmt.Sprintf("I%d", index+2), exportData.State),
			f.SetCellValue("Sheet1", fmt.Sprintf("J%d", index+2), exportData.City),
			f.SetCellValue("Sheet1", fmt.Sprintf("K%d", index+2), exportData.District),
			f.SetCellValue("Sheet1", fmt.Sprintf("L%d", index+2), exportData.SubDistrict),
			f.SetCellValue("Sheet1", fmt.Sprintf("M%d", index+2), exportData.Street),
			f.SetCellValue("Sheet1", fmt.Sprintf("N%d", index+2), exportData.DetailAddress),
			f.SetCellValue("Sheet1", fmt.Sprintf("O%d", index+2), exportData.Postalcode),
			f.SetCellValue("Sheet1", fmt.Sprintf("P%d", index+2), exportData.Longitude),
			f.SetCellValue("Sheet1", fmt.Sprintf("Q%d", index+2), exportData.Latitude),
			f.SetCellValue("Sheet1", fmt.Sprintf("R%d", index+2), exportData.BranchPhone),
			f.SetCellValue("Sheet1", fmt.Sprintf("S%d", index+2), exportData.MaxParcelStayDuration),
			f.SetCellValue("Sheet1", fmt.Sprintf("T%d", index+2), parseTimeRange(exportData.OpeningHours, 2)),
			f.SetCellValue("Sheet1", fmt.Sprintf("U%d", index+2), parseTimeRange(exportData.OpeningHours, 3)),
			f.SetCellValue("Sheet1", fmt.Sprintf("V%d", index+2), parseTimeRange(exportData.OpeningHours, 4)),
			f.SetCellValue("Sheet1", fmt.Sprintf("W%d", index+2), parseTimeRange(exportData.OpeningHours, 5)),
			f.SetCellValue("Sheet1", fmt.Sprintf("X%d", index+2), parseTimeRange(exportData.OpeningHours, 6)),
			f.SetCellValue("Sheet1", fmt.Sprintf("Y%d", index+2), parseTimeRange(exportData.OpeningHours, 7)),
			f.SetCellValue("Sheet1", fmt.Sprintf("Z%d", index+2), parseTimeRange(exportData.OpeningHours, 1)),
			f.SetCellValue("Sheet1", fmt.Sprintf("AA%d", index+2), timeZone),
			f.SetCellValue("Sheet1", fmt.Sprintf("AB%d", index+2), extraData),
			f.SetCellValue("Sheet1", fmt.Sprintf("AC%d", index+2), pickup.TransferTimeStampToTime(exportData.Mtime, exportData.Region).Format("2006-01-02 15:04:05")),
			f.SetCellValue("Sheet1", fmt.Sprintf("AD%d", index+2), exportData.LastUpdateBy),
			f.SetCellValue("Sheet1", fmt.Sprintf("AE%d", index+2), exportData.GetOpsTypeStringForExport()),
			f.SetCellValue("Sheet1", fmt.Sprintf("AF%d", index+2), exportData.LocationDescription),
		)

		for _, err1 := range errorList {
			if err1 != nil {
				errMsg := fmt.Sprintf("cannot write error msg to file|error_msg=%s", err1.Error())
				logger.CtxLogErrorf(ctx, errMsg)
				return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
			}
		}
	}

	// 以当前的时间戳生成临时文件
	tmpFileName := fmt.Sprintf("/tmp/%s-%s.xlsx", fileName, pickup.GetCurrentTime(ctx, region).Format("20060102150405"))

	// 将空格替换为中划线，防止文件名可能存在的空格，导致文件无法导出
	reg := regexp.MustCompile(" +")
	tmpFileName = reg.ReplaceAllString(tmpFileName, "-")

	err = f.SaveAs(tmpFileName)
	if err != nil {
		logger.CtxLogErrorf(ctx, err.Error())
		return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	// 上传文件
	return b.s3Service.UploadFileToS3(ctx, config.GetConf(ctx).LCOSBranchServiceS3Config.AccessKeyID, config.GetConf(ctx).LCOSBranchServiceS3Config.BucketKey, tmpFileName, config.GetConf(ctx).LCOSBranchServiceS3Config.TimeOut, config.GetConf(ctx).LCOSBranchServiceS3Config.ExpirationDays, "")

}

func (b *branchService) GenerateErrorResultFile(ctx utils.LCOSContext, errorMsg, region string) (string, string, string, *lcos_error.LCOSError) {
	templateFilePath := path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/", branchErrorResultFileName)

	// 打开模板
	f, err := excelize.OpenFile(templateFilePath)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file: %s, err:%v", templateFilePath, err)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	// 在第一行写错误信息
	err = f.SetCellValue("Sheet1", "A1", errorMsg)
	if err != nil {
		errMsg := fmt.Sprintf("cannot write error msg to file|error_msg=%s", errorMsg)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}

	fileName := "import-error"
	// 以当前的时间戳生成临时文件
	tmpFileName := fmt.Sprintf("/tmp/%s-%s.xlsx", fileName, pickup.GetCurrentTime(ctx, region).Format("20060102150405"))

	// 将空格替换为中划线，防止文件名可能存在的空格，导致文件无法导出
	reg := regexp.MustCompile(" +")
	tmpFileName = reg.ReplaceAllString(tmpFileName, "-")

	err = f.SaveAs(tmpFileName)
	if err != nil {
		logger.CtxLogErrorf(ctx, err.Error())
		return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	// 上传文件
	return b.s3Service.UploadFileToS3(ctx, config.GetConf(ctx).LCOSBranchServiceS3Config.AccessKeyID, config.GetConf(ctx).LCOSBranchServiceS3Config.BucketKey, tmpFileName, config.GetConf(ctx).LCOSBranchServiceS3Config.TimeOut, config.GetConf(ctx).LCOSBranchServiceS3Config.ExpirationDays, "")
}

func (b *branchService) GetSupplyNameAndDefaultBranchType(ctx utils.LCOSContext, supplyType int, region string) (string, uint8, *lcos_error.LCOSError) {
	// 获取 supply_type_name和default_branch_type
	supplyTypeInfoList, lcosErr := b.AllBranchSupply(ctx, &region)
	if lcosErr != nil {
		return "", 0, lcosErr
	}
	var branchType uint8
	var supplyName string
	for _, supplyTypeInfo := range supplyTypeInfoList {
		if supplyTypeInfo["branch_supply_type"].(uint32) == uint32(supplyType) {
			supplyName = supplyTypeInfo["branch_supply_name"].(string)
			// if branch groups is empty, return error
			if len(supplyTypeInfo["branch_groups"].([]map[string]interface{})) == 0 {
				return "", 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find branch group for supply type:[%d]", supplyType))
			}
			// 检查是否存在branch group，如果存在，并且只有一个，则表示使用所配置group的default branch type，否则使用store
			if len(supplyTypeInfo["branch_groups"].([]map[string]interface{})) == 1 {
				tmp := supplyTypeInfo["branch_groups"].([]map[string]interface{})
				branchType = tmp[0]["default_branch_type"].(uint8)
			} else {
				branchType = branch_constant.Store
			}
			break
		}
	}

	// 如果无法获取到branch type或者supply name，表示出错需要返回报错
	if supplyName == "" || branchType == 0 {
		errMsg := fmt.Sprintf("supply type is not valid|supply_name=%s, branch_type=%d", supplyName, branchType)
		return "", 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}
	return supplyName, branchType, nil
}

func (b *branchService) ListAllBranchTaskRecords(ctx utils.LCOSContext, queryMaps map[string]interface{}) ([]*branch_task_record.LogisticBranchTaskRecordTab, *lcos_error.LCOSError) {
	return b.branchTaskDao.QueryBranchTaskRecords(ctx, queryMaps)
}

func (b *branchService) CreateBranchTaskRecord(ctx utils.LCOSContext, branchTaskRecord *branch_task_record.LogisticBranchTaskRecordTab) *lcos_error.LCOSError {
	return b.branchTaskDao.CreateBranchTaskRecord(ctx, branchTaskRecord)
}

func (b *branchService) UpdateBranchTaskRecord(ctx utils.LCOSContext, updatedMap map[string]interface{}, queryMaps map[string]interface{}) *lcos_error.LCOSError {
	return b.branchTaskDao.UpdateBranchTaskRecord(ctx, updatedMap, queryMaps)
}

func (b *branchService) ListBranchTaskRecords(ctx utils.LCOSContext, page, count uint32) ([]*branch_task_record.LogisticBranchTaskRecordTab, uint32, *lcos_error.LCOSError) {
	return b.branchTaskDao.QueryBranchTaskRecordsPaging(ctx, map[string]interface{}{"region": strings.ToUpper(ctx.GetCountry())}, page, count)
}

func (b *branchService) UploadBranch(ctx utils.LCOSContext, fileUrl string, supplyType int, branchType uint32, region, operator string) (string, string, string, uint8, *lcos_error.LCOSError) {

	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return "", "", "", branch_constant.BranchTaskStatusFailed, err
	}

	logger.CtxLogInfof(ctx, "successfully download file url:[%s] from S3 service", fileUrl)

	//  解析模板获取到上传数据
	f, err1 := excelize.OpenFile(filePath)
	if err1 != nil {
		return "", "", "", branch_constant.BranchTaskStatusFailed, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("cannot open file: %s", filePath))
	}

	// 解析branch文件
	parseExcelDatas, lcosErr := parseBranchData(f, region)
	if lcosErr != nil {
		return "", "", "", branch_constant.BranchTaskStatusFailed, lcosErr
	}

	// 调用location server获取地址信息，调用spex获取location division id
	for _, singleParseData := range parseExcelDatas {
		// 补充branch info tab信息
		addBranchInfoForRawUploadExcelData(ctx, singleParseData, supplyType, region, branchType, operator)
		// 补充location信息和location division id
		b.addLocationInfo(ctx, singleParseData, region)
		// 同步单个branch信息
		b.syncBranchInfoByRawData(ctx, singleParseData, supplyType)
	}

	// refresh cache
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LogisticBranchInfoNamespace)

	fileName := utils.GetFileNameFromUrl(fileUrl)

	// 生成文件导出
	return b.GeneratePartialSuccessResultFile(ctx, parseExcelDatas, fileName, region)
}

func NewBranchService(branchDao branch_info.BranchDao, branchGroupDao branch_group.BranchGroupDao,
	s3Service s3_service.S3Service, spexService spex_service.SpexService, lcsService lcs_service.LcsService,
	postalCodeToGeoDao postal_code_to_geo.PostalCodeToGeoDao, branchTaskDao branch_task_record.BranchTaskRecordDao,
	logisticStationDao station_conf.LogisticStationDao) *branchService {
	return &branchService{
		branchDao:          branchDao,
		branchGroupDao:     branchGroupDao,
		s3Service:          s3Service,
		spexService:        spexService,
		lcsService:         lcsService,
		postalCodeToGeoDao: postalCodeToGeoDao,
		branchTaskDao:      branchTaskDao,
		logisticStationDao: logisticStationDao,
	}
}

func (b *branchService) syncBranchInfoByResourceID(ctx utils.LCOSContext, resourceID int, region, requestID string) *lcos_error.LCOSError {
	// 调用pis接口同步
	pisService := pis_service.NewPISService(ctx, region)
	return pisService.SyncBranch(ctx, requestID, resourceID)
}

func (b *branchService) SyncBranchInfo(ctx utils.LCOSContext, logID string) {
	// 获取日志id，方便记录请求
	requestID := logID

	// 遍历所配置的supply type和resource id映射
	for region := range config.GetMutableConf(ctx).PISBranchConfig.ResourceIDToSupplyTypeMap {

		// 获取region下的所有supply type
		supplyTypes, lcosErr := b.GetSyncBranchSupplyTypeForLCOSSync(ctx, region)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "request all sync branch supply types error|region=%s, error=%s", region, lcosErr.Msg)
			continue
		}
		logger.CtxLogInfof(ctx, "successfully get all sync branch supply types by branch groups|supply_types=%v, region=%s", supplyTypes, region)

		allSupplyTypeInfo, err := config.GetAllSupplyTypesInfoByRegion(ctx, region)
		if err != nil {
			logger.CtxLogErrorf(ctx, err.Error())
			continue
		}

		for _, supplyTypeInfo := range allSupplyTypeInfo {
			// SPLN-23126 add sync flag
			if utils.InUint32Slice(supplyTypeInfo.BranchSupplyType, supplyTypes) && supplyTypeInfo.SyncFlag {
				logger.CtxLogInfof(ctx, "ready to sync branch|supply type=%d", supplyTypeInfo.BranchSupplyType)
				lcosErr = b.syncBranchInfoByResourceID(ctx, int(supplyTypeInfo.ResourceID), region, requestID)
				if lcosErr != nil {
					logger.CtxLogErrorf(ctx, "fail to sync branch for supply type|supply_type=%d, resource_id=%d, error=%s", supplyTypeInfo.BranchSupplyType, supplyTypeInfo.ResourceID, lcosErr.Msg)
				}
			} else {
				logger.CtxLogInfof(ctx, "supply type is not allow to sync|supply type=%d", supplyTypeInfo.BranchSupplyType)
			}
		}
	}
}

func (b *branchService) ListBranch(ctx utils.LCOSContext, request *protocol.ListBranchRequest) ([]*branch_info.LogisticBranchInfoTab, uint32, *lcos_error.LCOSError) {
	condition, _ := utils.Struct2map(request)
	if request.BranchName != nil {
		condition["branch_name like"] = "%" + *request.BranchName + "%"
	}
	if request.BranchRef != nil {
		condition["branch_ref like"] = "%" + *request.BranchRef + "%"
	}
	if request.DetailAddress != nil {
		condition["detail_address like"] = "%" + *request.DetailAddress + "%"
	}
	if request.Postalcode != nil {
		condition["postalcode like"] = "%" + *request.Postalcode + "%"
	}

	if request.BranchGroupID != nil {
		branchGroup, err := b.branchGroupDao.GetBranchGroupByGroupID(ctx, *request.BranchGroupID)
		if err != nil {
			return nil, 0, err
		}
		if branchGroup == nil {
			return nil, 0, nil
		}
		condition["supply_type in"] = branchGroup.SupplyTypes
	}

	if request.LocationID == nil {
		locationNames := ops_service.GetMostDetailLocationNames(request.State, request.City, request.District, request.Street)
		if len(locationNames) > 0 {
			locationInfo, err := ops_service.GetUpLocationInfoByName(ctx, &ops_service.GetLocationInfoByNameRequest{
				LocationName: locationNames,
				Country:      ctx.GetCountry(),
			})
			if err != nil {
				return nil, 0, err
			}
			for _, v := range locationInfo {
				switch v.Level {
				case 0:
					condition["state_location_id"] = v.LocationId
				case 1:
					condition["city_location_id"] = v.LocationId
				case 2:
					condition["district_location_id"] = v.LocationId
				case 3:
					condition["street_location_id"] = v.LocationId
				}
			}
		}

	}

	list, total, err := b.branchDao.SearchBranch(ctx, request.PageNo, request.Count, condition)
	if err != nil {
		return nil, 0, err
	}

	allBranchGroup, err := b.branchGroupDao.AllBranchGroupInfo(ctx)
	if err != nil {
		return nil, 0, err
	}

	for _, branch := range list {
		branch.BranchGroupIDs = []uint32{}
		for _, v := range allBranchGroup {
			if utils.InUint32Slice(branch.SupplyType, v.SupplyTypes) {
				branch.BranchGroupIDs = append(branch.BranchGroupIDs, v.BranchGroupID)
			}
		}

		resp, err := ops_service.GetUpLocationInfo(ctx, &ops_service.GetLocationInfoByIdRequest{
			LocationId: branch.LocationID,
			Country:    ctx.GetCountry(),
		})
		if err != nil {
			logger.LogError(err)
			continue
		}
		if resp.State != nil {
			branch.State = *resp.State
		}
		if resp.City != nil {
			branch.City = *resp.City
		}
		if resp.District != nil {
			branch.District = *resp.District
		}
		if resp.Street != nil {
			branch.Street = *resp.Street
		}
	}

	return list, total, err
}

func (b *branchService) ListAllBranches(ctx utils.LCOSContext, request *protocol.ExportBranchRequest, region string) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError) {
	condition, _ := utils.Struct2map(request)
	if request.BranchName != nil {
		condition["branch_name like"] = "%" + *request.BranchName + "%"
	}
	if request.BranchRef != nil {
		condition["branch_ref like"] = "%" + *request.BranchRef + "%"
	}
	if request.DetailAddress != nil {
		condition["detail_address like"] = "%" + *request.DetailAddress + "%"
	}
	if request.Postalcode != nil {
		condition["postalcode like"] = "%" + *request.Postalcode + "%"
	}

	if request.BranchGroupID != nil {
		branchGroup, err := b.branchGroupDao.GetBranchGroupByGroupID(ctx, *request.BranchGroupID)
		if err != nil {
			return nil, err
		}
		branchGroupString, _ := json.MarshalToString(branchGroup)
		logger.CtxLogInfof(ctx, "success get branch group|branch_group=[%s]", branchGroupString)
		if branchGroup == nil {
			return nil, nil
		}
		condition["supply_type in"] = branchGroup.SupplyTypes
	}

	if request.LocationID == nil {
		locationNames := ops_service.GetMostDetailLocationNames(request.State, request.City, request.District, request.Street)
		if len(locationNames) > 0 {
			locationInfo, err := ops_service.GetUpLocationInfoByName(ctx, &ops_service.GetLocationInfoByNameRequest{
				LocationName: locationNames,
				Country:      ctx.GetCountry(),
			})
			if err != nil {
				return nil, err
			}
			for _, v := range locationInfo {
				switch v.Level {
				case 0:
					condition["state_location_id"] = v.LocationId
				case 1:
					condition["city_location_id"] = v.LocationId
				case 2:
					condition["district_location_id"] = v.LocationId
				case 3:
					condition["street_location_id"] = v.LocationId
				}
			}
		}

	}

	// 补充region信息
	condition["region"] = region
	list, err := b.branchDao.SearchAllBranches(ctx, condition)
	if err != nil {
		return nil, err
	}
	allBranchGroup, err := b.branchGroupDao.AllBranchGroupInfo(ctx)
	if err != nil {
		return nil, err
	}

	logger.CtxLogInfof(ctx, "get all branches and branch group successfully")

	for _, branch := range list {
		branch.BranchGroupIDs = []uint32{}
		for _, v := range allBranchGroup {
			if utils.InUint32Slice(branch.SupplyType, v.SupplyTypes) {
				branch.BranchGroupIDs = append(branch.BranchGroupIDs, v.BranchGroupID)
			}
		}

		resp, err := address_service.LocationServer.GetLocationInfoById(ctx, int(branch.LocationID))
		if err != nil {
			logger.LogError(err)
			continue
		}
		logger.CtxLogInfof(ctx, "successfully get location info|location_id=%d, state=%s, city=%s, district=%s, street=%s", branch.LocationID, resp.State, resp.City, resp.District, resp.Street)
		if resp.State != "" {
			branch.State = resp.State
		}
		if resp.City != "" {
			branch.City = resp.City
		}
		if resp.District != "" {
			branch.District = resp.District
		}
		if resp.Street != "" {
			branch.Street = resp.Street
		}
	}

	return list, err
}

func (b *branchService) ListBranchGroup(ctx utils.LCOSContext, request *protocol.ListBranchGroupRequest) ([]map[string]interface{}, uint32, *lcos_error.LCOSError) {
	condition, _ := utils.Struct2map(request)
	if request.BranchGroupName != nil {
		condition["branch_group_name like"] = "%" + *request.BranchGroupName + "%"
	}

	//if request.LineID != nil {
	//line, err := b.lineDao.GetLineBaseByLineId(ctx, *request.LineID)
	//if err != nil {
	//return nil, 0, err
	//}
	//if line == nil || line.BranchGroupID == 0 {
	//return []map[string]interface{}{}, 0, nil
	//}

	//condition["branch_group_id"] = line.BranchGroupID
	//}

	result, total, err := b.branchGroupDao.SearchBranchGroup(ctx, request.PageNo, request.Count, request.All, condition)
	if err != nil {
		return nil, 0, err
	}

	realResult, err := b.fillBranchGroupExtraData(ctx, result)
	if err != nil {
		return nil, 0, err
	}

	return realResult, total, nil
}

func (b *branchService) GetBranchGroupInfo(ctx utils.LCOSContext, branchGroupID uint32) (map[string]interface{}, *lcos_error.LCOSError) {
	result, err := b.branchGroupDao.GetBranchGroupByGroupID(ctx, branchGroupID)
	if err != nil {
		return nil, err
	}

	if result == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchGroupError, fmt.Sprintf("Not Found Branch Group ID[%d]", branchGroupID))
	}

	tmpResult, err := b.fillBranchGroupExtraData(ctx, []*branch_group.LogisticBranchGroupTab{result})
	if err != nil {
		return nil, err
	}

	return tmpResult[0], err
}

func (b *branchService) CreateBranchGroup(ctx utils.LCOSContext, request *protocol.CreateBranchGroupRequest) (*branch_group.LogisticBranchGroupTab, *lcos_error.LCOSError) {
	branchGroup := &branch_group.LogisticBranchGroupTab{
		BranchGroupID:     request.BranchGroupID,
		BranchGroupName:   request.BranchGroupName,
		Application:       request.Application,
		DefaultBranchType: request.DefaultBranchType,
		SupplyTypes:       request.BranchSupplyType,
		Region:            request.Region,
	}

	if c, ok := ctx.(*utils.HttpContext); ok {
		username := c.GetUserName()
		branchGroup.LastUpdateBy = username
	}

	if request.SyncTimePoints != nil {
		var tmp = []*branch_group.SyncTimePoint{}
		for _, v := range request.SyncTimePoints {
			tmp = append(tmp, &branch_group.SyncTimePoint{
				SyncTime: v,
			})
		}
		branchGroup.SyncTimePoints = branch_group.SyncTimePointSlice(tmp)
	}

	allSupplyTypes, err := b.AllBranchSupply(ctx, &request.Region)
	if err != nil {
		return nil, err
	}

	for _, supplyType := range request.BranchSupplyType {
		supplyTypeExist := false
		for _, v := range allSupplyTypes {
			if supplyType == v["branch_supply_type"].(uint32) {
				supplyTypeExist = true
				break
			}
		}
		if !supplyTypeExist {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("region:%s supplyType:%d not found", request.Region, supplyType))
		}
	}

	if err := b.branchGroupDao.CreateBranchGroup(ctx, branchGroup); err != nil {
		return nil, err
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LogisticBranchGroupNamespace)
	return branchGroup, nil
}

func (b *branchService) UpdateBranchGroup(ctx utils.LCOSContext, request *protocol.UpdateBranchGroupRequest) (*branch_group.LogisticBranchGroupTab, *lcos_error.LCOSError) {
	branchGroup, err := b.branchGroupDao.GetBranchGroupByGroupID(ctx, request.BranchGroupID)
	if err != nil {
		return nil, err
	}

	if branchGroup == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchGroupError, fmt.Sprintf("Not Found Branch Group[%d]", request.BranchGroupID))
	}

	if c, ok := ctx.(*utils.HttpContext); ok {
		username := c.GetUserName()
		branchGroup.LastUpdateBy = username
	}

	if request.Application != nil {
		branchGroup.Application = *request.Application
	}

	if request.BranchGroupName != nil {
		branchGroup.BranchGroupName = *request.BranchGroupName
	}

	if request.SyncTimePoints != nil {
		var tmp = []*branch_group.SyncTimePoint{}
		for _, v := range *request.SyncTimePoints {
			var syncDate uint32
			for _, vv := range branchGroup.SyncTimePoints {
				if vv.SyncTime == v {
					syncDate = vv.LastSyncDate
				}
			}

			tmp = append(tmp, &branch_group.SyncTimePoint{
				SyncTime:     v,
				LastSyncDate: syncDate,
			})
		}
		branchGroup.SyncTimePoints = branch_group.SyncTimePointSlice(tmp)
	}

	if c, ok := ctx.(*utils.HttpContext); ok {
		username := c.GetUserName()
		branchGroup.LastUpdateBy = username
	}

	if err := b.branchGroupDao.UpdateBranchGroup(ctx, branchGroup); err != nil {
		return nil, err
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LogisticBranchGroupNamespace)
	return branchGroup, nil
}

func (b *branchService) GetBranch(ctx utils.LCOSContext, branchID uint64) (*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError) {
	branchInfo, err := b.branchDao.GetBranchByBranchID(ctx, branchID, ctx.GetCountry())
	if err != nil {
		return nil, err
	}

	if branchInfo == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchError, fmt.Sprintf("Not Found Branch[%d]", branchID))
	}
	allBranchGroup, err := b.branchGroupDao.AllBranchGroupInfo(ctx)
	if err != nil {
		return nil, err
	}

	branchInfo.BranchGroupIDs = []uint32{}
	for _, v := range allBranchGroup {
		if utils.InUint32Slice(branchInfo.SupplyType, v.SupplyTypes) {
			branchInfo.BranchGroupIDs = append(branchInfo.BranchGroupIDs, v.BranchGroupID)
		}
	}

	return branchInfo, nil
}

func (b *branchService) GetBranchGroupInfoByGroupId(ctx utils.LCOSContext, branchGroupID uint32) (*branch_group.LogisticBranchGroupTab, *lcos_error.LCOSError) {
	branchGroupInfo, err := b.branchGroupDao.GetBranchGroupByGroupID(ctx, branchGroupID)
	if err != nil {
		return nil, err
	}

	if branchGroupInfo == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchError, fmt.Sprintf("Not Found BranchGroupID[%d]", branchGroupID))
	}
	return branchGroupInfo, nil
}

func (b *branchService) GetAllBranchesByBranchGroupID(ctx utils.LCOSContext, branchGroupID uint64) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError) {
	branchGroupInfos, _, err := b.branchGroupDao.SearchBranchGroup(ctx, 0, 0, 1, map[string]interface{}{"branch_group_id": branchGroupID})
	if err != nil {
		return nil, err
	}

	if len(branchGroupInfos) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchGroupError, fmt.Sprintf("Not Found Branch Group[%d]", branchGroupID))
	}
	return b.branchDao.SearchAllBranches(ctx, map[string]interface{}{"supply_type in": branchGroupInfos[0].SupplyTypes})
}

// pis迁移后，和之前的lcs相比使用了不同的s3服务器，所以需要指明access key id
func (b *branchService) SyncBranchInfoFromChannel(ctx utils.LCOSContext, branchSupplyType uint32, region, remoteFilePath, requestID string, endpoint, accessKeyID, bucketName string, isIncremental bool) *lcos_error.LCOSError {
	go func() {
		logger.SetLogId("|" + requestID)
		defer func() {
			logger.UnsetLogId()
			_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LogisticBranchInfoNamespace)
			if err := recover(); err != nil {
				logger.CtxLogErrorf(ctx, "Async save branch panic, err[%v]", err)
			}
		}()
		// 防止外层context done掉影响
		baseCtx := utils.NewLogContext(ctx, "|"+requestID)
		newCtx := utils.NewCommonCtx(baseCtx)

		originData, err := b.originBranchData(newCtx, remoteFilePath, endpoint, accessKeyID, bucketName)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncBranch, constant.SyncBranchEventNameGetBranchFail, monitor.StatusError, err.Msg)
			logger.CtxLogErrorf(ctx, "%s", err.Msg)
			return
		}

		_, err = b.asyncSaveBranchInfo(newCtx, region, branchSupplyType, originData, isIncremental, 0)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncBranch, constant.SyncBranchEventNameSaveBranchFail, monitor.StatusError, err.Msg)
			logger.CtxLogErrorf(ctx, "Async save branch failed, err[%v]", err)
			return
		}

		_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncBranch, constant.SyncBranchEventNameSyncBranchSuccess, monitor.StatusSuccess, "")
	}()

	return nil
}

func (b *branchService) asyncSaveBranchInfo(ctx utils.LCOSContext, region string, branchSupplyType uint32, originData []*OriginBranchData, isIncremental bool, operationTime uint32) (map[string]BranchResult, *lcos_error.LCOSError) {
	exist := map[string]struct{}{}
	branchIdMap := make(map[string]BranchResult, 0)

	branchInfos, err := b.basicBranchInfos(ctx, originData, branchSupplyType, region, operationTime, branchIdMap)
	if err != nil {
		return nil, err
	}
	logger.CtxLogInfof(ctx, "BranchInfo number after add extra data[%d]", len(branchInfos))

	for _, branchInfo := range branchInfos {
		exist[branchInfo.BranchRef] = struct{}{}
		key := getBranchUniqueKey(branchInfo.BranchRef, branchInfo.SupplyType)
		remoteBranch, err := b.spexService.GetBranchInfo(ctx, branchInfo.Region, branchInfo.SupplyType, branchInfo.BranchRef)

		if err == nil {
			branchInfo.BranchID = remoteBranch.BranchInfo.GetId()
			if _, err := b.spexService.UpdateBranchInfo(ctx, branchInfo); err != nil {
				logger.CtxLogErrorf(ctx, "%v", err.Msg)
				branchIdMap[key] = BranchResult{BranchId: remoteBranch.BranchInfo.GetId(), ErrorMsg: err.Msg}
				continue
			}
			// 可能存在core server存在数据, lls db和core server数据不一致, 所以双写时是update/create
			if operationTime > 0 {
				if err := b.branchDao.UpdateOrCreateBranchWithOperationTime(ctx, branchInfo, operationTime); err != nil {
					logger.CtxLogErrorf(ctx, "%v", err.Msg)
					branchIdMap[key] = BranchResult{BranchId: remoteBranch.BranchInfo.GetId(), ErrorMsg: err.Msg}
					continue
				}
			} else {
				if err := b.branchDao.UpdateOrCreateBranchByBranchID(ctx, branchInfo); err != nil {
					logger.CtxLogErrorf(ctx, "%v", err.Msg)
					branchIdMap[key] = BranchResult{BranchId: remoteBranch.BranchInfo.GetId(), ErrorMsg: err.Msg}
					continue
				}
			}

		} else {
			result, err := b.spexService.CreateBranchInfo(ctx, branchInfo)
			if err != nil {
				logger.CtxLogErrorf(ctx, "%v", err.Msg)
				branchIdMap[key] = BranchResult{ErrorMsg: err.Msg}
				continue
			}
			branchInfo.BranchID = *result.BranchInfo.Id
			// 可能存在core server存在数据, lls db和core server数据不一致, 所以双写时是update/create
			if operationTime > 0 {
				if err := b.branchDao.UpdateOrCreateBranchWithOperationTime(ctx, branchInfo, operationTime); err != nil {
					logger.CtxLogErrorf(ctx, "%v", err.Msg)
					branchIdMap[key] = BranchResult{BranchId: remoteBranch.BranchInfo.GetId(), ErrorMsg: err.Msg}
					continue
				}
			} else {
				if err := b.branchDao.UpdateOrCreateBranchByBranchID(ctx, branchInfo); err != nil {
					logger.CtxLogErrorf(ctx, "%v", err.Msg)
					branchIdMap[key] = BranchResult{BranchId: remoteBranch.BranchInfo.GetId(), ErrorMsg: err.Msg}
					continue
				}
			}
		}
		branchIdMap[key] = BranchResult{BranchId: branchInfo.BranchID}
	}

	// SPLN-23285, add flag to indicate whether is incrementally add. For incrementally add, no need to remove invalid data
	if !isIncremental {
		logger.CtxLogInfof(ctx, "branch supply type:[%d] is not incrementally add, ready to remove invalid data", branchSupplyType)
		b.removeInvalidData(ctx, region, exist, branchSupplyType)
	} else {
		logger.CtxLogInfof(ctx, "branch supply type:[%d] is incrementally add, no need to do remove invalid data", branchSupplyType)
	}

	return branchIdMap, nil
}

func (b *branchService) removeInvalidData(ctx utils.LCOSContext, region string, exist map[string]struct{}, branchSupplyType uint32) {
	allRemoteBranch, err := b.spexService.GetBranchListByType(ctx, region, int(branchSupplyType))
	if err != nil {
		logger.CtxLogErrorf(ctx, "%s", err.Msg)
	}

	var invalidBranchIDs []uint64
	zeroTime := uint32(0)
	invalidStatus := uint32(spex_constant.BranchInActive)
	for _, v := range allRemoteBranch {
		if _, ok := exist[v.GetBranchRef()]; ok {
			continue
		}
		v.CreateTime = &zeroTime
		v.UpdateTime = &zeroTime
		v.Status = &invalidStatus
		if _, err := b.spexService.UpdateBranchByRemoteBranch(ctx, region, v); err != nil {
			logger.CtxLogErrorf(ctx, "%s", err.Msg)
		}

		invalidBranchIDs = append(invalidBranchIDs, *v.Id)
	}

	for _, v := range utils.SplitUint64Slice(invalidBranchIDs, 500) {
		if err := b.branchDao.BatchUpdateBranchStatusAndSubStatus(ctx, branch_constant.Invalid, branch_constant.Unavailable, v); err != nil {
			logger.CtxLogErrorf(ctx, "%s", err.Msg)
		}
	}
}

func (b *branchService) originBranchData(ctx utils.LCOSContext, remoteFilePath string, endpoint, s3AccessKeyID, bucketName string) ([]*OriginBranchData, *lcos_error.LCOSError) {
	reader, err := b.s3Service.DownloadWithEndpoint(ctx, endpoint, s3AccessKeyID, bucketName, remoteFilePath, time.Second*10)
	if err != nil {
		return nil, err
	}

	//logger.LogInfof("file content:%s", data)

	var originBranchData []*OriginBranchData
	if strings.HasSuffix(remoteFilePath, "v2.json") {
		defer reader.Close()
		bufReader := bufio.NewReader(reader)
		maxLoopSize := config.GetMaxLoopSizeForFileRead(ctx)
		var loopIndex int
		for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
			line, _, err := bufReader.ReadLine()
			if err != nil {
				if err == io.EOF {
					break
				}
				return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
			}
			item := &OriginBranchData{}
			if err := json.Unmarshal(line, &item); err != nil {
				logger.CtxLogErrorf(ctx, "decode item failed, %v", err)
				continue
			}
			// SPLN-25563: check and fix originbranch opening hours with fallback logic
			checkAndFixOriginBranchOpeningHours(ctx, item)
			originBranchData = append(originBranchData, item)
		}
		if loopIndex >= maxLoopSize {
			return nil, lcos_error.NewLCOSError(lcos_error.LoopOverMaxSize, fmt.Sprintf("loop over max size, max_size=%d", loopIndex))
		}
	} else {
		data, e := ioutil.ReadAll(reader)
		if e != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, e.Error())
		}
		if e := json.Unmarshal(data, &originBranchData); e != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, e.Error())
		}
	}

	return originBranchData, nil
}

func (b *branchService) basicBranchInfos(ctx utils.LCOSContext, originBranchData []*OriginBranchData, supplyType uint32, region string, operationTime uint32, errMap map[string]BranchResult) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError) {
	var branchInfos []*branch_info.LogisticBranchInfoTab

	for _, v := range originBranchData {
		branchStatus := uint8(branch_constant.Valid)

		if v.BranchStatus == 0 {
			branchStatus = uint8(branch_constant.Invalid)
		}
		branchSubStatus := getSubStatus(branchStatus, v.SubStatus)

		branch := &branch_info.LogisticBranchInfoTab{
			SupplyType:            supplyType,
			BranchRef:             v.BranchRef,
			BranchName:            v.BranchName,
			BranchType:            v.BranchType,
			BranchStatus:          branchStatus,
			Postalcode:            v.Postalcode,
			Region:                v.Region,
			Longitude:             v.Longitude,
			Latitude:              v.Latitude,
			BranchPhone:           v.BranchPhone,
			MaxParcelStayDuration: v.MaxParcelStayDuration,
			DetailAddress:         v.DetailAddress,
			SubDistrict:           v.SubDistrict,
			SpexExtraData:         v.SpexExtraData,
			ExtraData:             v.ExtraData,
			BranchCode:            v.BranchCode,
			OpsType:               branch_info.GenerateOpsTypeString(v.OpsType),
			LocationDescription:   v.LocationDescription,
			BranchSubStatus:       branchSubStatus,
			OperationTime:         operationTime,
		}

		for _, openingHour := range v.OpeningHours {
			branch.OpeningHours = append(branch.OpeningHours, &branch_info.OpeningHour{
				StartTime: openingHour.StartTime,
				EndTime:   openingHour.EndTime,
				DayOfWeek: openingHour.DayOfWeek,
				TimeZone:  openingHour.TimeZone,
			})
		}

		// 补充location信息
		var locationList []string
		for _, locationName := range []string{v.State, v.City, v.District, v.Street} {
			if len(locationName) == 0 {
				break
			}
			locationList = append(locationList, locationName)
		}
		locationRes, err := ops_service.GetUpLocationInfoByName(ctx, &ops_service.GetLocationInfoByNameRequest{
			Country:      region,
			LocationName: locationList,
		})
		key := getBranchUniqueKey(v.BranchRef, supplyType)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncBranch, fmt.Sprintf("location_id_%d_%s", supplyType, branch.BranchRef),
				constant.StatusError, fmt.Sprintf("%s_%v", err.Msg, locationList))
			logger.LogErrorf("Get location id failed, location_info[%v], err[%v], branch_ref[%s]", locationList, err, branch.BranchRef)
			errMap[key] = BranchResult{ErrorMsg: err.Msg}
			continue
		}
		for k, locationItem := range []*uint64{&branch.StateLocationID, &branch.CityLocationID, &branch.DistrictLocationID, &branch.StreetLocationID} {
			if k >= len(locationRes) {
				break
			}
			*locationItem = uint64(locationRes[k].LocationId)
			branch.LocationID = *locationItem
		}
		// 补充location_division_id
		locationDevisionID, err := b.spexService.SearchLocationDivisionID(ctx, v.Region, v.State, v.City, v.District, v.Street)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncBranch, fmt.Sprintf("division_location_id_%d_%s", supplyType, branch.BranchRef),
				constant.StatusError, fmt.Sprintf("%s_%v", err.Msg, locationList))
			logger.LogErrorf("Get location_devistion_id failed, region[%s], state[%s], city[%s], district[%s], err[%v], branch_ref[%s]", v.Region, v.State, v.City, v.District, err, branch.BranchRef)
			errMap[key] = BranchResult{ErrorMsg: err.Msg}
			continue
		}

		branch.LocationDivisionID = locationDevisionID

		branchInfos = append(branchInfos, branch)
	}

	return branchInfos, nil
}

func (b *branchService) GetSyncBranchSupplyType(ctx utils.LCOSContext, region string) ([]uint32, *lcos_error.LCOSError) {
	branchGroups, _, err := b.branchGroupDao.SearchBranchGroup(ctx, 0, 0, 1, map[string]interface{}{
		"region": region})
	if err != nil {
		return nil, err
	}

	now := utils.GetTimestamp(ctx)
	dateBegin := utils.GetDateBeginTimestamp(ctx, region)
	logger.LogInfof("now:[%d] dateBegin[%d]", now, dateBegin)
	branchSupplyTypes := []uint32{}
	for _, branchGroup := range branchGroups {
		for _, syncPoints := range branchGroup.SyncTimePoints {
			if syncPoints.LastSyncDate < dateBegin && now > dateBegin+syncPoints.SyncTime {
				branchSupplyTypes = append(branchSupplyTypes, branchGroup.SupplyTypes...)
				syncPoints.LastSyncDate = dateBegin
				b.branchGroupDao.UpdateBranchGroup(ctx, branchGroup)
				break
			}
		}
	}

	return utils.UniqUint32Slice(branchSupplyTypes), nil
}

// GetSyncBranchSupplyTypeForLCOSSync
// SPLN-26133
// for lcos sync to use, will not update last sync date, use now to check whether to sync
func (b *branchService) GetSyncBranchSupplyTypeForLCOSSync(ctx utils.LCOSContext, region string) ([]uint32, *lcos_error.LCOSError) {
	branchGroups, _, err := b.branchGroupDao.SearchBranchGroup(ctx, 0, 0, 1, map[string]interface{}{
		"region": region})
	if err != nil {
		return nil, err
	}

	now := utils.GetTimestamp(ctx)
	dateBegin := utils.GetDateBeginTimestamp(ctx, region)
	logger.CtxLogInfof(ctx, "now:[%d] dateBegin[%d]", now, dateBegin)
	branchSupplyTypes := []uint32{}
	for _, branchGroup := range branchGroups {
		for _, syncPoints := range branchGroup.SyncTimePoints {
			if now >= dateBegin+syncPoints.SyncTime && now < dateBegin+syncPoints.SyncTime+3600 { // now time is within one hour between sync point, this need sync task to run every one hour
				branchSupplyTypes = append(branchSupplyTypes, branchGroup.SupplyTypes...)
				break
			}
		}
	}

	return utils.UniqUint32Slice(branchSupplyTypes), nil
}

func (b *branchService) fillBranchGroupExtraData(ctx utils.LCOSContext, branchGroups []*branch_group.LogisticBranchGroupTab) ([]map[string]interface{}, *lcos_error.LCOSError) {
	results := []map[string]interface{}{}
	if len(branchGroups) == 0 {
		return results, nil
	}
	var branchGroupIds, branchSupplyTypes []uint32
	for _, v := range branchGroups {
		branchGroupIds = append(branchGroupIds, v.BranchGroupID)
		branchSupplyTypes = append(branchSupplyTypes, v.SupplyTypes...)
	}

	// fill lineids
	//lines, _, _, _, err := b.lineDao.SearchLineBaseList(ctx, 1, 100000, searchLineCondition, false)
	lines, err := lls_service.GetLineInfoByBranchGroupIDs(ctx, branchGroupIds)
	if err != nil {
		return nil, err
	}

	// fill shipping_channel_id
	shippingChannels, err := b.lcsService.GetShippingChannels(ctx, branchSupplyTypes)
	if err != nil {
		return nil, err
	}

	// fill branch_type name
	var branchTypeInfo []*dto.BranchTypeInfo
	branchTypeInfo, err = b.getBranchTypeInfo(ctx, true)
	if err != nil {
		return nil, err
	}

	for _, v := range branchGroups {
		item, _ := utils.Struct2map(v)
		lineIds := []string{}
		shippingChannelIds := []uint32{}
		branchSupplyInfo := []map[string]interface{}{}
		for _, line := range lines {
			if line.BranchGroupID == v.BranchGroupID {
				lineIds = append(lineIds, line.LineId)
			}
		}

		for _, shippingChannel := range shippingChannels {
			if utils.InUint32Slice(shippingChannel.BranchSupplyType, v.SupplyTypes) {
				shippingChannelIds = append(shippingChannelIds, shippingChannel.ShippingChannelID)
			}
		}

		for _, b := range branchTypeInfo {
			if utils.InUint32Slice(b.BranchSupplyType, v.SupplyTypes) {
				branchSupplyInfo = append(branchSupplyInfo, map[string]interface{}{
					"branch_supply_name": b.BranchTypeName,
					"branch_supply_type": b.BranchSupplyType,
				})
			}
		}

		item["line_ids"] = lineIds
		item["shipping_channel_id"] = shippingChannelIds
		item["product_ids"] = []string{}
		item["branch_supply_types"] = branchSupplyInfo

		results = append(results, item)
	}

	return results, nil
}

func (b *branchService) SendSyncBranchCommand(ctx utils.LCOSContext, branchGroupID uint32) *lcos_error.LCOSError {
	branchGroup, err := b.branchGroupDao.GetBranchGroupByGroupID(ctx, branchGroupID)
	if err != nil {
		return nil
	}

	if branchGroup == nil {
		return lcos_error.NewLCOSError(lcos_error.NotFoundBranchGroupError, fmt.Sprintf("Not Found branch group[%d]", branchGroupID))
	}

	logger.CtxLogInfof(ctx, "ready to sync branch by PIS")
	pisService := pis_service.NewPISService(ctx, branchGroup.Region)
	err = pisService.SendPisSyncCommand(ctx, branchGroup.SupplyTypes)

	if err != nil {
		return err
	}
	return nil
}

func (b *branchService) GetBranchFromCache(ctx utils.LCOSContext, branchID uint64, branchGroupIDs []uint32) (*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError) {
	branchInfo, err := b.branchDao.GetBranchFromCache(ctx, branchID)
	if err != nil {
		return nil, err
	}

	if branchInfo == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchError, fmt.Sprintf("Not Found branch[%d]", branchID))
	}

	if len(branchGroupIDs) == 0 {
		return branchInfo, nil
	}

	for _, v := range branchGroupIDs {
		branchGroup, err := b.branchGroupDao.GetBranchGroupFromCache(ctx, v)
		if err != nil {
			return nil, err
		}
		if branchGroup == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchGroupError, fmt.Sprintf("Not Found branch group[%d]", v))
		}
		if utils.InUint32Slice(branchInfo.SupplyType, branchGroup.SupplyTypes) {
			return branchInfo, nil
		}
	}
	return nil, lcos_error.NewLCOSError(lcos_error.BranchNotMatchGroupError, fmt.Sprintf("Branch group not match branch[%d]", branchID))
}

func (b *branchService) AllBranchSupply(ctx utils.LCOSContext, region *string) ([]map[string]interface{}, *lcos_error.LCOSError) {

	branchTypes, err := b.getBranchTypeInfo(ctx, true)
	if err != nil {
		return nil, err
	}

	// 获取所有的group信息，忽略报错。并将其置为 supply_type->branch_group的列表
	allBranchGroups, _ := b.branchGroupDao.AllBranchGroupInfo(ctx)
	supplyTypeToBranchGroupMap := map[uint32][]*branch_group.LogisticBranchGroupTab{}
	for _, branchGroup := range allBranchGroups {
		for _, singleSupplyType := range branchGroup.SupplyTypes {
			supplyTypeToBranchGroupMap[singleSupplyType] = append(supplyTypeToBranchGroupMap[singleSupplyType], branchGroup)
		}
	}

	result := make([]map[string]interface{}, 0, len(branchTypes))

	for _, v := range branchTypes {
		if region != nil && v.Region != *region {
			continue
		}

		tmpResult := map[string]interface{}{
			"branch_supply_name": v.BranchTypeName,
			"branch_supply_type": v.BranchSupplyType,
			"region":             v.Region,
		}

		// 补充branch group信息
		var addedBranchGroupInfo []map[string]interface{}
		if _, ok := supplyTypeToBranchGroupMap[v.BranchSupplyType]; ok {
			for _, tmp := range supplyTypeToBranchGroupMap[v.BranchSupplyType] {
				addedBranchGroupInfo = append(addedBranchGroupInfo, map[string]interface{}{
					"branch_group_id":     tmp.BranchGroupID,
					"branch_group_name":   tmp.BranchGroupName,
					"default_branch_type": tmp.DefaultBranchType,
				})
			}
		}
		tmpResult["branch_groups"] = addedBranchGroupInfo

		result = append(result, tmpResult)
	}

	return result, nil
}

func (b *branchService) GetBranchByGroupIdAndLocationId(ctx utils.LCOSContext, branchGroupID uint32, locationId uint64, subStatus []pb.SubStatusEnum) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError) {
	branchGroup, err := b.branchGroupDao.GetBranchGroupFromCache(ctx, branchGroupID)
	if err != nil {
		return nil, err
	}
	if branchGroup == nil {
		return nil, err
	}
	logger.LogInfof("GetBranchByGroupIdAndLocationId SupplyTypes[%v]", branchGroup.SupplyTypes)
	branchInfos, err := b.branchDao.GetBranchesBySupplyTypesAndLocationIdAndSubStatus(ctx, branchGroup.SupplyTypes, locationId, subStatus)
	if err != nil {
		return nil, err
	}
	return branchInfos, nil
}

func (b *branchService) GetSubLocationsByGroupIdAndLocationId(ctx utils.LCOSContext, branchGroupID uint32, locationId uint64) (map[string][]uint64, *lcos_error.LCOSError) {
	branchGroup, err := b.branchGroupDao.GetBranchGroupFromCache(ctx, branchGroupID)
	if err != nil {
		return nil, err
	}
	if branchGroup == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchGroupError, fmt.Sprintf("Not Found branch group[%d]", branchGroupID))
	}
	params := make(map[string]interface{})
	var supplyTypes []uint32
	for _, value := range branchGroup.SupplyTypes {
		supplyTypes = append(supplyTypes, value)
	}
	params["supply_types"] = supplyTypes

	stateLocationsId := []uint64{}
	cityLocationsId := []uint64{}
	districtLocationsId := []uint64{}
	streetLocationsId := []uint64{}

	subLocationList, locationLeve, err := b.branchDao.GetSubLocationsBySupplyTypesAndLocationId(ctx, supplyTypes, locationId)
	if err != nil {
		return nil, err
	}

	switch locationLeve {
	case 0:
		stateLocationsId = subLocationList
	case 1:
		cityLocationsId = subLocationList
	case 2:
		districtLocationsId = subLocationList
	case 3:
		streetLocationsId = subLocationList
	}

	result := make(map[string][]uint64)
	result["stateLocationsId"] = stateLocationsId
	result["cityLocationsId"] = cityLocationsId
	result["districtLocationsId"] = districtLocationsId
	result["streetLocationsId"] = streetLocationsId
	return result, nil
}

func (b *branchService) FindBranchGeneral(ctx utils.LCOSContext, req *SearchBranchReq) ([]*branch_info.LogisticBranchInfoTab, []float64, *lcos_error.LCOSError) {
	branchGroup, err := b.branchGroupDao.GetBranchGroupFromCache(ctx, req.BranchGroupID)
	if err != nil {
		return nil, nil, err
	}
	if branchGroup == nil {
		return nil, nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchGroupError, fmt.Sprintf("Not Found Branch Group[%d]", req.BranchGroupID))
	}
	logger.CtxLogInfof(ctx, "branch_group_id:%d, branch_supply_types:%v", branchGroup.BranchGroupID, branchGroup.SupplyTypes)
	size := int(req.Size)
	if req.FindType == branch_constant.SearchByDistance {
		var lng, lat float64
		var err1 error
		if req.Latitude != "" && req.Longitude != "" {
			lng, err1 = strconv.ParseFloat(req.Longitude, 64)
			if err1 != nil {
				return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("longitude is not valid|err=%s", err1.Error()))
			}
			lat, err1 = strconv.ParseFloat(req.Latitude, 64)
			if err1 != nil {
				return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("latitude is not valid|err=%s", err1.Error()))
			}
		} else {
			return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "latitude and longitude are required when find type is search by distance")
		}
		return b.findNearest(ctx, lng, lat, branchGroup, req.LocationSortType, size, int(req.Distance), nil, nil)
	} else if req.FindType == branch_constant.SearchByKeyword {
		if req.Keyword == "" {
			return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "keyword is required when find type is search by keyword")
		}
		results, err := b.fuzzyFindForGoogleMaps(ctx, req.Keyword, branchGroup.Region, branchGroup.SupplyTypes, size)
		return results, nil, err
	}
	return nil, nil, nil
}

func (b *branchService) FindBranch(ctx utils.LCOSContext, req *pb.SearchBranchRequest) ([]*branch_info.LogisticBranchInfoTab, []float64, *lcos_error.LCOSError) {
	// get config for search branch
	searchBranchConfig := config.GetSearchBranchConfig(ctx, req.GetRegion())
	distanceLimit := req.GetDistance()
	if distanceLimit == 0 {
		distanceLimit = uint32(searchBranchConfig.DistanceLimit)
	}
	// 最大搜索距离限制
	if searchBranchConfig.MaxDistanceLimit != 0 {
		if distanceLimit > uint32(searchBranchConfig.MaxDistanceLimit) {
			distanceLimit = uint32(searchBranchConfig.MaxDistanceLimit)
		}
	}

	size := int(req.GetSize())
	if size == 0 {
		size = searchBranchConfig.NumberLimit
	} else {
		// find the smaller size
		if size > searchBranchConfig.NumberLimit {
			size = searchBranchConfig.NumberLimit
		}
	}

	var sortType uint32
	if req.LocationSortType == nil { // when upstream not set any sort type, use config
		sortType = uint32(searchBranchConfig.LocationSortType)
	} else {
		sortType = req.GetLocationSortType()
	}

	// form extra query
	extraQueryMap := make(map[string]interface{})
	if len(req.GetOpsTypeList()) > 0 {

		var opsTypeList [][]string
		for _, singleOpsType := range req.GetOpsTypeList() {
			opsTypeList = append(opsTypeList, singleOpsType.GetOpsType())
		}

		extraQueryMap["ops_type"] = opsTypeList
	}

	var subStatusList []uint8
	for _, subStatus := range req.SubStatus {
		subStatusList = append(subStatusList, uint8(subStatus))
	}

	branchGroup, err := b.branchGroupDao.GetBranchGroupFromCache(ctx, req.GetBranchGroupId())
	if err != nil {
		return nil, nil, err
	}
	if branchGroup == nil {
		return nil, nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchGroupError, fmt.Sprintf("Not Found Branch Group[%d]", req.GetBranchGroupId()))
	}
	logger.CtxLogInfof(ctx, "branch_group_id:%d, branch_supply_types:%v", branchGroup.BranchGroupID, branchGroup.SupplyTypes)
	if req.GetFindType() == branch_constant.SearchByDistance {
		var lng, lat float64
		if req.Latitude != nil && req.Longitude != nil {
			lng, _ = strconv.ParseFloat(req.GetLongitude(), 64)
			lat, _ = strconv.ParseFloat(req.GetLatitude(), 64)
		} else {
			zipcode := req.GetZipcode()
			if len(zipcode) == 0 {
				zipcode = req.GetKeyword()
			}
			lng, lat, err = b.getLngAndLat(ctx, zipcode)
			if err != nil {
				logger.CtxLogErrorf(ctx, "find lng and lat by zipcode fail, zipcode:%s, return empty branch", zipcode)
				if err.RetCode == lcos_error.FindLngLatFromZipcodeFail {
					// 通过zipcode转换经纬度失败的情况，直接返回空列表（目前只有sg能转换成功）
					return nil, nil, nil
				}
				return nil, nil, err
			}
			logger.CtxLogInfof(ctx, "zipcode:%s, lng:%f, lat:%f", zipcode, lng, lat)
		}
		return b.findNearest(ctx, lng, lat, branchGroup, sortType, size, int(distanceLimit), extraQueryMap, subStatusList)
	} else if req.GetFindType() == branch_constant.SearchByKeyword {
		results, err := b.fuzzyFind(ctx, req.GetKeyword(), branchGroup.Region, branchGroup.SupplyTypes, size, extraQueryMap, subStatusList)
		return results, nil, err
	} else if req.GetFindType() == branch_constant.DistanceFirst {
		// SPLN-22783 新增逻辑
		// 如果有Longitude、Latitude参数，按照经纬度搜索2km内branch
		// 如果keyword可以按照zipcode转经纬度，按照经纬度搜索2km内branch（只有sg可以转）
		// 否则按照keyword搜索
		var lng, lat float64
		hasLngLat := false
		if req.Latitude != nil && req.Longitude != nil {
			lng, _ = strconv.ParseFloat(req.GetLongitude(), 64)
			lat, _ = strconv.ParseFloat(req.GetLatitude(), 64)
			hasLngLat = true
		} else {
			// 只有sg可以转成功
			lng, lat, err = b.getLngAndLat(ctx, *req.Keyword)
			hasLngLat = true
			if err != nil {
				logger.LogInfof("getLngAndLat fail, zipcode:%s, er:%v", *req.Keyword, err)
				hasLngLat = false
			}
		}
		if hasLngLat {
			logger.CtxLogInfof(ctx, "try search by location, lng:%f, lat:%f", lng, lat)
			return b.findNearest(ctx, lng, lat, branchGroup, sortType, size, int(distanceLimit), extraQueryMap, subStatusList)
		} else {
			results, err := b.fuzzyFind(ctx, req.GetKeyword(), branchGroup.Region, branchGroup.SupplyTypes, size, extraQueryMap, subStatusList)
			return results, nil, err
		}
	}

	return nil, nil, nil
}

func (b *branchService) BatchGetBranchInfoByBranchId(ctx utils.LCOSContext, batchBranchReq []*pb.GetBranchInfoByBranchIdRequest) []*pb.SingleBranchInfo {
	var branchInfoList []*pb.SingleBranchInfo
	for _, singleReq := range batchBranchReq {
		branchInfo, err := b.getSingleBranchInfoByBranchId(ctx, singleReq)
		// Err Case:
		if err != nil {
			tempBranchInfo := &pb.SingleBranchInfo{
				UniqueId: singleReq.UniqueId,
				ErrCode:  utils.NewInt32(err.RetCode),
				Message:  utils.NewString(err.Msg),
			}
			branchInfoList = append(branchInfoList, tempBranchInfo)
			// skip
			continue
		}
		// Normal Case:
		branchInfoList = append(branchInfoList, branchInfo)
	}
	return branchInfoList
}

func (b *branchService) getSingleBranchInfoByBranchId(ctx utils.LCOSContext, singleBranchReq *pb.GetBranchInfoByBranchIdRequest) (*pb.SingleBranchInfo, *lcos_error.LCOSError) {
	// get branch info
	branchGroupList := []uint32{singleBranchReq.GetBranchGroup()}
	if singleBranchReq.GetBranchGroup() == 0 {
		branchGroupList = []uint32{}
	}
	branchInfo, brErr := b.GetBranchFromCache(ctx, singleBranchReq.GetBranchId(), branchGroupList)
	if brErr != nil {
		return nil, brErr
	}
	// get location info
	locationInfo, locErr := address_service.LocationServer.GetLocationInfoById(ctx, int(branchInfo.LocationID))
	if locErr != nil {
		return nil, locErr
	}
	// normal
	singleBranchRsp := &pb.SingleBranchInfo{
		UniqueId:      singleBranchReq.UniqueId,
		Id:            utils.NewUint64(branchInfo.BranchID),
		BranchGroup:   utils.NewUint32(branchInfo.SupplyType),
		BranchType:    utils.NewUint32(branchInfo.BranchType),
		BranchRef:     utils.NewString(branchInfo.BranchRef),
		BranchName:    utils.NewString(branchInfo.BranchName),
		BranchAddress: utils.NewString(branchInfo.DetailAddress),
		BranchPhone:   utils.NewString(branchInfo.BranchPhone),
		Status:        utils.NewUint32(uint32(branchInfo.BranchStatus)),
		ExtraData:     utils.NewString(parseOpeningHoursToExtraData(branchInfo.OpeningHours)),
		PostalCode:    utils.NewString(branchInfo.Postalcode),
		Latitude:      utils.NewString(branchInfo.Latitude),
		Longitude:     utils.NewString(branchInfo.Longitude),
		CreateTime:    utils.NewUint32(branchInfo.Ctime),
		UpdateTime:    utils.NewUint32(branchInfo.Mtime),
		Country:       utils.NewString(branch_constant.CountryMapper[branchInfo.Region]),
		State:         utils.NewString(locationInfo.State),
		City:          utils.NewString(locationInfo.City),
		District:      utils.NewString(locationInfo.District),
		Street:        utils.NewString(locationInfo.Street),
		SubStatus:     pb.SubStatusEnum(branchInfo.BranchSubStatus).Enum(),
	}
	// Special logic for "SingaPore"
	// SLS State = SingaPore
	// Map State = Singapore
	if singleBranchRsp.GetState() == "SingaPore" {
		singleBranchRsp.State = utils.NewString("Singapore")
	}
	return singleBranchRsp, nil
}

func (b *branchService) BatchGetBranchInfoByBranchRef(ctx utils.LCOSContext, batchBranchReq []*pb.GetBranchInfoByBranchRefRequest) []*pb.SingleBranchInfo {
	var branchInfoList []*pb.SingleBranchInfo
	for _, singleReq := range batchBranchReq {
		branchInfo, err := b.getSingleBranchInfoByBranchRef(ctx, singleReq)
		// Err Case:
		if err != nil {
			tempBranchInfo := &pb.SingleBranchInfo{
				UniqueId: singleReq.UniqueId,
				ErrCode:  utils.NewInt32(err.RetCode),
				Message:  utils.NewString(err.Msg),
			}
			branchInfoList = append(branchInfoList, tempBranchInfo)
			// skip
			continue
		}
		// Normal Case:
		branchInfoList = append(branchInfoList, branchInfo)
	}
	return branchInfoList
}

func (b *branchService) getSingleBranchInfoByBranchRef(ctx utils.LCOSContext, singleBranchReq *pb.GetBranchInfoByBranchRefRequest) (*pb.SingleBranchInfo, *lcos_error.LCOSError) {
	// get branch info
	branchInfo, brErr := b.branchDao.GetBranchByRefFromCache(ctx, singleBranchReq.GetBranchGroup(), singleBranchReq.GetBranchRef())
	if brErr != nil {
		return nil, brErr
	}
	// get location info
	locationInfo, locErr := address_service.LocationServer.GetLocationInfoById(ctx, int(branchInfo.LocationID))
	if locErr != nil {
		return nil, locErr
	}
	// normal
	singleBranchRsp := &pb.SingleBranchInfo{
		UniqueId:      singleBranchReq.UniqueId,
		Id:            utils.NewUint64(branchInfo.BranchID),
		BranchGroup:   utils.NewUint32(branchInfo.SupplyType),
		BranchType:    utils.NewUint32(branchInfo.BranchType),
		BranchRef:     utils.NewString(branchInfo.BranchRef),
		BranchName:    utils.NewString(branchInfo.BranchName),
		BranchAddress: utils.NewString(branchInfo.DetailAddress),
		BranchPhone:   utils.NewString(branchInfo.BranchPhone),
		Status:        utils.NewUint32(uint32(branchInfo.BranchStatus)),
		ExtraData:     utils.NewString(parseOpeningHoursToExtraData(branchInfo.OpeningHours)),
		PostalCode:    utils.NewString(branchInfo.Postalcode),
		Latitude:      utils.NewString(branchInfo.Latitude),
		Longitude:     utils.NewString(branchInfo.Longitude),
		CreateTime:    utils.NewUint32(branchInfo.Ctime),
		UpdateTime:    utils.NewUint32(branchInfo.Mtime),
		Country:       utils.NewString(branch_constant.CountryMapper[branchInfo.Region]),
		State:         utils.NewString(locationInfo.State),
		City:          utils.NewString(locationInfo.City),
		District:      utils.NewString(locationInfo.District),
		Street:        utils.NewString(locationInfo.Street),
		SubStatus:     pb.SubStatusEnum(branchInfo.BranchSubStatus).Enum(),
	}
	// Special logic for "SingaPore"
	// SLS State = SingaPore
	// Map State = Singapore
	if singleBranchRsp.GetState() == "SingaPore" {
		singleBranchRsp.State = utils.NewString("Singapore")
	}
	return singleBranchRsp, nil
}

func (b *branchService) fuzzyFind(ctx utils.LCOSContext, keyword, region string, supplyTypes []uint32, size int, extraQueryMap map[string]interface{}, subStatusList []uint8) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError) {
	keywords := strings.Split(keyword, " ")
	var results []*branch_info.LogisticBranchInfoTab
	for _, k := range keywords {
		k = strings.TrimSpace(k)
		if utils.IsPostalcode(ctx, k, region) {
			logger.LogInfof("try search in postalcode %s", k)
			for _, supplyType := range supplyTypes {
				tmp, err := b.branchDao.FindByPostalcode(ctx, supplyType, k, size, extraQueryMap, subStatusList)
				if err != nil {
					return nil, err
				}
				if len(tmp) > 0 {
					results = append(results, tmp...)
				}
			}
			if len(results) > 0 {
				if len(results) > size {
					return results[:size], nil
				}
				return results, nil
			}
		}
	}

	for _, supplyType := range supplyTypes {
		logger.LogInfof("try search in branch name %s, supplyType:%d, size:%d", keyword, supplyType, size)
		var tmp []*branch_info.LogisticBranchInfoTab
		var err *lcos_error.LCOSError
		if stringutil.StringInSlice(strings.ToUpper(region), config.GetMutableConf(ctx).FuzzyFindFromCacheRegions) {
			logger.LogInfof("try search from cache in branch name %s,region:%s, supplyType:%d, size:%d", keyword, region, supplyType, size)
			tmp, err = b.fuzzyFindFromCache(ctx, supplyType, keyword, size, subStatusList)
		} else {
			tmp, err = b.branchDao.FindByBranchNameAndAddress(ctx, supplyType, keyword, size, extraQueryMap, subStatusList)
		}

		if err != nil {
			return nil, err
		}
		if len(tmp) > 0 {
			results = append(results, tmp...)
		}
	}
	if len(results) > size {
		return results[:size], nil
	}
	return results, nil
}

func (b *branchService) fuzzyFindFromCache(ctx utils.LCOSContext, supplyType uint32, keyword string, size int, subStatusList []uint8) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError) {
	results := make([]*branch_info.LogisticBranchInfoTab, 0)
	data, _ := b.branchDao.GetBranchInfoBySupplyTypeUsingCache(ctx, supplyType)
	var expectedList []uint8
	for _, subStatus := range subStatusList {
		if subStatus != branch_constant.Unavailable {
			expectedList = append(expectedList, subStatus)
		}
	}
	for _, v := range data {
		for _, item := range v {
			// 开关没切，sub status的数据未同步，保持老逻辑
			if !config.GetSubStatusIndexSwitchConfig() && item.BranchStatus != branch_constant.Valid {
				continue
			}
			// 开关切了，不包含sub status的过滤掉
			if config.GetSubStatusIndexSwitchConfig() {
				if len(expectedList) == 0 && item.BranchSubStatus == branch_constant.Unavailable {
					continue
				}
				if len(expectedList) > 0 && !utils.CheckInUint8(item.BranchSubStatus, expectedList) {
					continue
				}
			}

			if strings.Contains(item.BranchName, keyword) {
				results = append(results, item)
				continue
			}
			if strings.Contains(item.DetailAddress, keyword) {
				results = append(results, item)
				continue
			}
		}
	}

	if len(results) > size {
		return results[:size], nil
	}
	return results, nil
}

func (b *branchService) fuzzyFindForGoogleMaps(ctx utils.LCOSContext, keyword, region string, supplyTypes []uint32, size int) ([]*branch_info.LogisticBranchInfoTab, *lcos_error.LCOSError) {
	keywords := strings.Split(keyword, " ")
	var results []*branch_info.LogisticBranchInfoTab
	for _, k := range keywords {
		k = strings.TrimSpace(k)
		if utils.IsPostalcode(ctx, k, region) {
			logger.LogInfof("try search in postalcode %s", k)
			for _, supplyType := range supplyTypes {
				tmp, err := b.branchDao.FindByPostalcode(ctx, supplyType, k, size, nil, nil)
				if err != nil {
					return nil, err
				}
				if len(tmp) > 0 {
					results = append(results, tmp...)
				}
			}
			if len(results) > 0 {
				if len(results) > size {
					return results[:size], nil
				}
				return results, nil
			}
		}
	}

	for _, supplyType := range supplyTypes {
		logger.LogInfof("try search in branch name %s, supplyType:%d, size:%d", keyword, supplyType, size)
		tmp, err := b.branchDao.FindByBranchNameAndAddressForGoogleMaps(ctx, supplyType, keyword, size)
		if err != nil {
			return nil, err
		}
		if len(tmp) > 0 {
			results = append(results, tmp...)
		}
	}
	if len(results) > size {
		return results[:size], nil
	}
	return results, nil
}

func (b *branchService) findNearest(ctx utils.LCOSContext, lng, lat float64, branchGroup *branch_group.LogisticBranchGroupTab, sortType uint32, size int, maxDistance int, extraQueryMap map[string]interface{}, subStatusList []uint8) ([]*branch_info.LogisticBranchInfoTab, []float64, *lcos_error.LCOSError) {
	var results []*branch_info.LogisticBranchInfoTab
	for _, supplyType := range branchGroup.SupplyTypes {
		tmp, _ := b.branchDao.FindNearest(ctx, supplyType, lng, lat, size, maxDistance, extraQueryMap, subStatusList)
		results = append(results, tmp...)
	}

	if len(results) == 0 {
		return []*branch_info.LogisticBranchInfoTab{}, []float64{}, nil
	}

	var s *sortBranches
	if sortType == branch_constant.SortByStraightLine {
		s = newSortByStraightLineBranches(results, lng, lat)
	} else if sortType == branch_constant.SortByCar {
		distance, err := b.spexService.RoutingMatrix(ctx, lat, lng, branchGroup.Region, results, false)
		if err != nil {
			return nil, nil, err
		}
		s = newSortByCar(results, distance)
	} else {
		// 兼容上游传惨不符合预期的情况，默认按距离排序
		s = newSortByStraightLineBranches(results, lng, lat)
	}
	sort.Sort(s)
	results = s.list
	if len(results) > size {
		return results[:size], s.distance[:size], nil
	}
	return results, s.distance, nil
}

func (b *branchService) getLngAndLat(ctx utils.LCOSContext, zipcode string) (float64, float64, *lcos_error.LCOSError) {
	result, err := b.postalCodeToGeoDao.GetGeoByZipcode(ctx, zipcode)
	if err != nil {
		return 0, 0, err
	}
	if result == nil {
		return 0, 0, lcos_error.NewLCOSError(lcos_error.FindLngLatFromZipcodeFail, fmt.Sprintf("not found postalcode[%s] lat and lng", zipcode))
	}
	return result.Lng, result.Lat, nil
}

func (b *branchService) DeleteBranchGroup(ctx utils.LCOSContext, request *protocol.DeleteBranchGroupRequest) *lcos_error.LCOSError {
	logger.LogInfof("try delete branch group! branchGroupId:%d, user:%s", request.BranchGroupID, ctx.GetUserEmail())
	return b.branchGroupDao.DeleteBranchGroup(ctx, request.BranchGroupID)
}

func (b *branchService) getBranchTypeInfo(ctx utils.LCOSContext, allRegion bool) ([]*dto.BranchTypeInfo, *lcos_error.LCOSError) {
	// SPLN-23126 pis migration
	var branchTypeInfo []*dto.BranchTypeInfo
	var err *lcos_error.LCOSError
	branchTypeInfo, err = GetBranchSupplyTypes(ctx, allRegion)

	if err != nil {
		logger.CtxLogErrorf(ctx, "requesting lcos for branch type error|error=%s", err.Msg)
		return nil, err
	}

	if !config.GetPisBranchSwitch(ctx) {
		// if switch is false, have to merge all branch type data from lcs
		lcsBranchTypeInfos, lcsErr := b.lcsService.GetBranchTypeInfo(ctx, allRegion)

		if lcsErr == nil {
			// make branch Type info into map
			branchTypeInfoMap := make(map[uint32]bool)
			for _, singleBranchType := range branchTypeInfo {
				branchTypeInfoMap[singleBranchType.BranchSupplyType] = true
			}

			for _, singleLcsBranchType := range lcsBranchTypeInfos {
				if _, ok := branchTypeInfoMap[singleLcsBranchType.BranchSupplyType]; !ok {
					branchTypeInfo = append(branchTypeInfo, singleLcsBranchType)
					branchTypeInfoMap[singleLcsBranchType.BranchSupplyType] = true
				}
			}
		} else {
			logger.CtxLogErrorf(ctx, "requesting lcs for branch type error|error=%s", lcsErr.Msg)
		}
	}
	return branchTypeInfo, err
}

// 增量新增branch信息
func (b *branchService) SyncBranchData(ctx utils.LCOSContext, branchSupplyType uint32, region, requestID string, data []pis_protocol.BranchData, operationTime uint32) (*pis_protocol.SyncBranchResponseData, *lcos_error.LCOSError) {
	logger.SetLogId("|" + requestID)

	//配置单次增量更新的数量
	limit := config.GetBranchIncrementUpdateSize()
	if limit == 0 {
		limit = 10
	}
	if len(data) > limit {
		return nil, lcos_error.NewLCOSError(lcos_error.LoopOverMaxSize, fmt.Sprintf("loop over max size,branch list should not more than %d,req size:%d", limit, len(data)))
	}
	// 判断请求的unique_id和branch_ref的唯一性
	err := checkBranchUniqueness(data)
	if err != nil {
		logger.CtxLogErrorf(ctx, "branch uniqueness check fail, err[%v]", err)
		return nil, err
	}

	var originDataV2 []*OriginBranchData
	// 转换数据类型
	originData := convertedToOriginData(region, data)
	// SPLN-25563: check and fix originbranch opening hours with fallback logic
	for _, od := range originData {
		checkAndFixOriginBranchOpeningHours(ctx, od)
		originDataV2 = append(originDataV2, od)
	}

	// 同步branch信息
	syncResultMap, err := b.asyncSaveBranchInfo(ctx, region, branchSupplyType, originDataV2, true, operationTime)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Async save branch failed, err[%v]", err)
	}

	// 获取branch信息，返回给上游
	if len(syncResultMap) != len(data) {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "sync result map is not eqaul with branch list")
	}
	failList, successList := getResultList(data, syncResultMap, branchSupplyType)
	resp := &pis_protocol.SyncBranchResponseData{
		FailList:    failList,
		SuccessList: successList,
	}
	for _, failData := range failList {
		if failData == nil {
			continue
		}
		_ = monitor.AwesomeReportEvent(ctx, constant.SyncBranchData, fmt.Sprintf("supplyType:[%d]", branchSupplyType), constant.StatusError, fmt.Sprintf("branch_ref:[%s],reason:%s", failData.BranchRef, failData.FailedReason))
	}

	for _, successData := range successList {
		if successData == nil {
			continue
		}
		_ = monitor.AwesomeReportEvent(ctx, constant.SyncBranchData, fmt.Sprintf("supplyType:[%d]", branchSupplyType), constant.StatusSuccess, "")
	}

	if len(failList) == len(data) {
		resp.Result = pis_protocol.FailCode
	} else if len(successList) == len(data) {
		resp.Result = pis_protocol.SuccessCode
	} else {
		resp.Result = pis_protocol.PartialSuccessCode
	}

	// 更新版本号
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LogisticBranchInfoNamespace)

	return resp, nil
}

func checkBranchUniqueness(dataList []pis_protocol.BranchData) *lcos_error.LCOSError {
	uniqueIdMap := make(map[string]string, 0)
	branchRefMap := make(map[string]string, 0)
	for _, data := range dataList {
		_, ok := uniqueIdMap[data.UniqueId]
		if ok {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("unique id are repeated，id:%s", data.UniqueId))
		}
		uniqueIdMap[data.UniqueId] = data.UniqueId
		_, ok = branchRefMap[data.BranchRef]
		if ok {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("branch ref are repeated，ref:%s", data.BranchRef))
		}
		branchRefMap[data.BranchRef] = data.BranchRef
	}
	return nil
}

func convertedToOriginData(region string, dataList []pis_protocol.BranchData) []*OriginBranchData {
	originBranchDataList := make([]*OriginBranchData, 0, len(dataList))
	for _, data := range dataList {
		OpeningHourList := make([]*OpeningHour, 0, len(data.OpeningHours))
		for _, openingHour := range data.OpeningHours {
			OpeningHourList = append(OpeningHourList, &OpeningHour{
				StartTime: openingHour.StartTime,
				EndTime:   openingHour.EndTime,
				TimeZone:  openingHour.TimeZone,
				DayOfWeek: openingHour.DayOfWeek,
			})
		}
		originBranchData := &OriginBranchData{
			BranchRef:             data.BranchRef,
			BranchName:            data.BranchName,
			BranchStatus:          data.BranchStatus,
			BranchType:            data.BranchType,
			Region:                region,
			DetailAddress:         data.DetailAddress,
			Postalcode:            data.Postalcode,
			BranchPhone:           data.BranchPhone,
			SubDistrict:           data.SubDistrict,
			Longitude:             data.Longitude,
			Latitude:              data.Latitude,
			MaxParcelStayDuration: data.MaxParcelStayDuration,
			OpeningHours:          OpeningHourList,
			State:                 data.State,
			City:                  data.City,
			District:              data.District,
			Street:                data.Street,
			SpexExtraData:         data.SpexExtraData,
			ExtraData:             convertedToExtraData(data.ExtraData),
			BranchCode:            data.BranchCode,
			OpsType:               data.OpsType,
			LocationDescription:   data.LocationDescription,
			SubStatus:             data.SubStatus,
		}
		originBranchDataList = append(originBranchDataList, originBranchData)
	}

	return originBranchDataList
}

func convertedToExtraData(extraData pis_protocol.ExtraData) branch_info.ExtraData {
	return branch_info.ExtraData{
		DCCODE: extraData.DCCODE,
		DCNAME: extraData.DCNAME,
	}
}

func getBranchUniqueKey(branchRef string, supplyType uint32) string {
	return fmt.Sprintf("%s.%d", branchRef, supplyType)
}

func getResultList(BranchDataList []pis_protocol.BranchData, resultMap map[string]BranchResult, branchSupplyType uint32) ([]*pis_protocol.FailData, []*pis_protocol.SuccessData) {
	failList := make([]*pis_protocol.FailData, 0)
	successList := make([]*pis_protocol.SuccessData, 0)
	for _, branch := range BranchDataList {
		key := getBranchUniqueKey(branch.BranchRef, branchSupplyType)
		result, ok := resultMap[key]
		if !ok {
			failList = append(failList, &pis_protocol.FailData{UniqueId: branch.UniqueId, BranchRef: branch.BranchRef, FailedReason: "can not get sync result"})
			continue
		}
		if result.ErrorMsg != "" {
			failList = append(failList, &pis_protocol.FailData{UniqueId: branch.UniqueId, BranchRef: branch.BranchRef, FailedReason: result.ErrorMsg})
			continue
		}
		if result.BranchId == 0 {
			failList = append(failList, &pis_protocol.FailData{UniqueId: branch.UniqueId, BranchRef: branch.BranchRef, FailedReason: "branch id is 0"})
			continue
		}
		successList = append(successList, &pis_protocol.SuccessData{
			UniqueId:     branch.UniqueId,
			BranchId:     result.BranchId,
			BranchRef:    branch.BranchRef,
			BranchStatus: branch.BranchStatus,
		})
	}
	return failList, successList
}

func (b *branchService) FindDropoffBranch(ctx utils.LCOSContext, req *pb.SearchBranchRequest) ([]*branch_info.LogisticBranchInfoTab, []float64, *lcos_error.LCOSError) {
	// get config for search branch
	searchBranchConfig := config.GetSearchBranchConfig(ctx, req.GetRegion())
	distanceLimit := req.GetDistance()
	if distanceLimit == 0 {
		distanceLimit = uint32(searchBranchConfig.DistanceLimit)
	}
	size := int(req.GetSize())
	if size == 0 {
		size = searchBranchConfig.NumberLimit
	}
	var subStatusList []uint8
	for _, subStatus := range req.SubStatus {
		subStatusList = append(subStatusList, uint8(subStatus))
	}
	branchGroup, err := b.branchGroupDao.GetBranchGroupFromCache(ctx, req.GetBranchGroupId())
	if err != nil {
		return nil, nil, err
	}
	if branchGroup == nil {
		return nil, nil, lcos_error.NewLCOSError(lcos_error.NotFoundBranchGroupError, fmt.Sprintf("Not Found Branch Group[%d]", req.GetBranchGroupId()))
	}
	logger.CtxLogInfof(ctx, "branch_group_id:%d, branch_supply_types:%v", branchGroup.BranchGroupID, branchGroup.SupplyTypes)

	// req.GetFindType() == branch_constant.SearchByDistance
	var lng, lat float64
	if req.Latitude != nil && req.Longitude != nil {
		lng, _ = strconv.ParseFloat(req.GetLongitude(), 64)
		lat, _ = strconv.ParseFloat(req.GetLatitude(), 64)
	} else {
		zipcode := req.GetZipcode()
		if len(zipcode) == 0 {
			zipcode = req.GetKeyword()
		}
		lng, lat, err = b.getLngAndLat(ctx, zipcode)
		if err != nil {
			if err.RetCode == lcos_error.FindLngLatFromZipcodeFail {
				// 通过zipcode转换经纬度失败的情况，直接返回空列表（目前只有sg能转换成功）
				logger.CtxLogInfof(ctx, "find lng and lat by zipcode fail, zipcode:%s, return empty branch", zipcode)
				return nil, nil, nil
			}
			return nil, nil, err
		}
		logger.CtxLogInfof(ctx, "zipcode:%s, lng:%f, lat:%f", zipcode, lng, lat)
	}

	// findNearest
	var results []*branch_info.LogisticBranchInfoTab
	for _, supplyType := range branchGroup.SupplyTypes {
		tmp, _ := b.branchDao.FindNearest(ctx, supplyType, lng, lat, size, int(distanceLimit), nil, subStatusList)
		results = append(results, tmp...)
	}
	if len(results) == 0 {
		return []*branch_info.LogisticBranchInfoTab{}, []float64{}, nil
	}

	// sortType == branch_constant.SortByCar
	distance, err := b.spexService.RoutingMatrix(ctx, lat, lng, branchGroup.Region, results, true)
	if err != nil {
		return nil, nil, err
	}
	s := newSortByCar(results, distance)
	sort.Sort(s)
	results = s.list
	if len(results) > size {
		return results[:size], s.distance[:size], nil
	}
	return results, s.distance, nil
}

var _ BranchService = (*branchService)(nil)
