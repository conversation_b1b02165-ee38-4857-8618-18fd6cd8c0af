package edt_rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/edt_rule"
	edt_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
	"git.garena.com/shopee/platform/service-governance/viewercontext/attr"
	"log"
	"os"
	"path"
	"reflect"
	"testing"
)

func TestConvertRequest(t *testing.T) {
	type args struct {
		ctx     utils.LCOSContext
		request *edt_rule.CreateEdtRuleRequest
	}
	tests := []struct {
		name string
		args args
		want *edt_rule2.CreateEntity
	}{
		////TODO: Add test cases.
		//// region
		//{
		//	args: args{
		//		ctx: utils.NewCommonCtx(context.Background()),
		//		request: &edt_rule.CreateEdtRuleRequest{
		//			MatchType: edt_rule2.EdtMatchByRegion,
		//			//EffectiveImmediately: true,
		//			EffectiveStartTime: 1717066802,
		//			EdtFormulaList: []edt_rule.EdtFormula{
		//				{
		//					SellerGroupRouteEdt: true,
		//					EdtType:                  edt_rule.EdtMinPlusMax,
		//					MinEdtFormula: &edt_rule.Formula{
		//						Apt: &edt_rule2.APT{
		//							ItemEstimatedDaysAsPreOrder: false,
		//							DTSAsAPTFallback:            true,
		//							AggregatedByOrderCreateTime: true,
		//						},
		//						AptExtension: &edt_rule2.APTNonWorkingDay{
		//							Local: &edt_rule2.SingleAPTNonWorkingDay{
		//								LPSChannelHolidayAndWeekend: &edt_rule2.LPSChannelHolidayAndWeekend{
		//									CBSpecial: true,
		//								},
		//								TPLPickupWindowHolidayAndWeekend: false,
		//								SellerHolidayAndWeekend:          true,
		//								EstimateSellerWeekend:            false,
		//							},
		//							CB: &edt_rule2.SingleAPTNonWorkingDay{
		//								LPSChannelHolidayAndWeekend: &edt_rule2.LPSChannelHolidayAndWeekend{
		//									CBSpecial: true,
		//								},
		//								TPLPickupWindowHolidayAndWeekend: false,
		//								SellerHolidayAndWeekend:          true,
		//								EstimateSellerWeekend:            false,
		//							},
		//						},
		//						Cdt: &edt_rule2.CDTMin{
		//							EstimatedSellerShipmentMethod: false,
		//						},
		//						CdtExtension: &edt_rule2.CDTMinNonWorkingDay{
		//							LPSChannelHolidayAndWeekend: true,
		//						},
		//						FixedEdt: &edt_rule2.FixedEDT{
		//							FixedEDT:      111,
		//							CutoffTime:    222,
		//							SellerHoliday: true,
		//						},
		//						SBD: &edt_rule2.SBD{
		//							Enable: true,
		//						},
		//						EDTNWDAdjustment: &edt_rule2.EDTNWDAdjustment{
		//							Enable: false,
		//						},
		//					},
		//					MaxEdtFormula: &edt_rule.Formula{
		//						Apt: &edt_rule2.APT{
		//							ItemEstimatedDaysAsPreOrder: false,
		//							DTSAsAPTFallback:            true,
		//							AggregatedByOrderCreateTime: true,
		//						},
		//					},
		//				},
		//			},
		//		},
		//	},
		//	want: nil,
		//},
		////// product
		//{
		//	args: args{
		//		ctx: utils.NewCommonCtx(context.Background()),
		//		request: &edt_rule.CreateEdtRuleRequest{
		//			MatchType: edt_rule2.EdtMatchByProduct,
		//			//EffectiveImmediately: true,
		//			EffectiveStartTime: 1717066802,
		//			SpecialChannel:     1000,
		//			EdtFormulaList: []edt_rule.EdtFormula{
		//				{
		//					SellerGroupRouteEdt: true,
		//					EdtType:                  edt_rule.EdtMinPlusMax,
		//					MinEdtFormula: &edt_rule.Formula{
		//						Apt: &edt_rule2.APT{
		//							ItemEstimatedDaysAsPreOrder: false,
		//							DTSAsAPTFallback:            true,
		//							AggregatedByOrderCreateTime: true,
		//						},
		//						AptExtension: &edt_rule2.APTNonWorkingDay{
		//							Local: &edt_rule2.SingleAPTNonWorkingDay{
		//								LPSChannelHolidayAndWeekend: &edt_rule2.LPSChannelHolidayAndWeekend{
		//									CBSpecial: true,
		//								},
		//								TPLPickupWindowHolidayAndWeekend: false,
		//								SellerHolidayAndWeekend:          true,
		//								EstimateSellerWeekend:            false,
		//							},
		//							CB: &edt_rule2.SingleAPTNonWorkingDay{
		//								LPSChannelHolidayAndWeekend: &edt_rule2.LPSChannelHolidayAndWeekend{
		//									CBSpecial: true,
		//								},
		//								TPLPickupWindowHolidayAndWeekend: false,
		//								SellerHolidayAndWeekend:          true,
		//								EstimateSellerWeekend:            false,
		//							},
		//						},
		//						Cdt: &edt_rule2.CDTMin{
		//							EstimatedSellerShipmentMethod: false,
		//						},
		//						CdtExtension: &edt_rule2.CDTMinNonWorkingDay{
		//							LPSChannelHolidayAndWeekend: true,
		//						},
		//						FixedEdt: &edt_rule2.FixedEDT{
		//							FixedEDT:      111,
		//							CutoffTime:    222,
		//							SellerHoliday: true,
		//						},
		//						SBD: &edt_rule2.SBD{
		//							Enable: true,
		//						},
		//						EDTNWDAdjustment: &edt_rule2.EDTNWDAdjustment{
		//							Enable: false,
		//						},
		//					},
		//					MaxEdtFormula: &edt_rule.Formula{
		//						Apt: &edt_rule2.APT{
		//							ItemEstimatedDaysAsPreOrder: false,
		//							DTSAsAPTFallback:            true,
		//							AggregatedByOrderCreateTime: true,
		//						},
		//					},
		//				},
		//			},
		//		},
		//	},
		//	want: nil,
		//},
		// ab test group
		{
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edt_rule.CreateEdtRuleRequest{
					MatchType: edt_rule2.EdtMatchByABTestGroup,
					//EffectiveImmediately: true,
					EffectiveStartTime: 1717066802,
					AbChannelList:      []int{90022},
					Region:             "BR",
					BuyerIdGroupList: []edt_rule2.BuyerIdGroup{
						edt_rule2.BuyerIdGroup{
							BuyerIdMin: 1,
							BuyerIdMax: 9,
						},
						edt_rule2.BuyerIdGroup{
							BuyerIdMin: 91,
							BuyerIdMax: 99,
						},
					},
					EdtFormulaList: []edt_rule.EdtFormula{
						{
							SellerGroupRouteEdt: true,
							EdtType:             edt_rule.EdtMinPlusMax,
							MinEdtFormula: &edt_rule.Formula{
								Apt: &edt_rule2.APT{
									ItemEstimatedDaysAsPreOrder: false,
									DTSAsAPTFallback:            true,
									AggregatedByOrderCreateTime: true,
								},
								AptExtension: &edt_rule2.NewAPTNonWorkingDay{
									Local: &edt_rule2.NewSingleAPTNonWorkingDay{
										NewLPSChannelHolidayAndWeekend:   true,
										TPLPickupWindowHolidayAndWeekend: false,
										SellerHolidayAndWeekend:          true,
										EstimateSellerWeekend:            false,
									},
									CB: &edt_rule2.NewSingleAPTNonWorkingDay{
										NewLPSChannelHolidayAndWeekend:   true,
										TPLPickupWindowHolidayAndWeekend: false,
										SellerHolidayAndWeekend:          true,
										EstimateSellerWeekend:            false,
									},
								},
								Cdt: &edt_rule2.CDTMin{
									EstimatedSellerShipmentMethod: false,
								},
								CdtExtension: &edt_rule2.CDTMinNonWorkingDay{
									LPSChannelHolidayAndWeekend: true,
								},
								FixedEdt: &edt_rule2.FixedEDT{
									FixedEDT:      111,
									CutoffTime:    222,
									SellerHoliday: true,
								},
								SBD: &edt_rule2.SBD{
									Enable: true,
								},
								EDTNWDAdjustment: &edt_rule2.EDTNWDAdjustment{
									Enable: false,
								},
							},
							MaxEdtFormula: &edt_rule.Formula{
								Apt: &edt_rule2.APT{
									ItemEstimatedDaysAsPreOrder: false,
									DTSAsAPTFallback:            true,
									AggregatedByOrderCreateTime: true,
								},
							},
						},
					},
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ConvertRequest(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ConvertRequest() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEdtRuleServiceImpl_createFormulas1(t *testing.T) {
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("CID", "sg")
	//_ = os.Setenv("PFB_NAME", "PFB_NAME=pfb-dms-dev-spln-31383-public")

	ctxWithPfb, _ := viewercontext.Start(context.WithValue(context.Background(), "logid", "111111dawdawd111111111111111"), attr.WithPFB("pfb-dms-dev-spln-31383-public"))
	ctx := utils.NewCommonCtx(ctxWithPfb)

	startup.InitSSCEnv()
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		log.Fatalf("getConfig %v", err)
	}
	err = startup.InitLibs(c)

	if err != nil {
		log.Fatalf("InitLibs Error: %v", err)
	}

	//test ab test group create
	//request := &edt_rule.CreateEdtRuleRequest{
	//	MatchType:            edt_rule2.EdtMatchByABTestGroup,
	//	EffectiveImmediately: true,
	//	EffectiveStartTime:   1717066802,
	//	AbChannelList:        []int{90022},
	//	Region:               "BR",
	//	Operator:             "xiaolin",
	//	BuyerIdGroupList: []edt_rule2.BuyerIdGroup{
	//		edt_rule2.BuyerIdGroup{
	//			BuyerIdMin: 1,
	//			BuyerIdMax: 9,
	//		},
	//		edt_rule2.BuyerIdGroup{
	//			BuyerIdMin: 91,
	//			BuyerIdMax: 99,
	//		},
	//	},
	//	EdtFormulaList: []edt_rule.EdtFormula{
	//		{
	//			SellerGroupRouteEdt: true,
	//			EdtType:                  edt_rule.EdtMinPlusMax,
	//			MinEdtFormula: &edt_rule.Formula{
	//				Apt: &edt_rule2.APT{
	//					ItemEstimatedDaysAsPreOrder: false,
	//					DTSAsAPTFallback:            true,
	//					AggregatedByOrderCreateTime: true,
	//				},
	//				AptExtension: &edt_rule2.NewAPTNonWorkingDay{
	//					Local: &edt_rule2.NewSingleAPTNonWorkingDay{
	//						NewLPSChannelHolidayAndWeekend:   false,
	//						TPLPickupWindowHolidayAndWeekend: false,
	//						SellerHolidayAndWeekend:          true,
	//						EstimateSellerWeekend:            false,
	//					},
	//					CB: &edt_rule2.NewSingleAPTNonWorkingDay{
	//						NewLPSChannelHolidayAndWeekend:   true,
	//						TPLPickupWindowHolidayAndWeekend: false,
	//						SellerHolidayAndWeekend:          true,
	//						EstimateSellerWeekend:            false,
	//					},
	//				},
	//				Cdt: &edt_rule2.CDTMin{
	//					EstimatedSellerShipmentMethod: false,
	//				},
	//				CdtExtension: &edt_rule2.CDTMinNonWorkingDay{
	//					LPSChannelHolidayAndWeekend: true,
	//				},
	//				FixedEdt: &edt_rule2.FixedEDT{
	//					FixedEDT:      111,
	//					CutoffTime:    222,
	//					SellerHoliday: true,
	//				},
	//				SBD: &edt_rule2.SBD{
	//					Enable: true,
	//				},
	//				EDTNWDAdjustment: &edt_rule2.EDTNWDAdjustment{
	//					Enable: false,
	//				},
	//			},
	//			MaxEdtFormula: &edt_rule.Formula{
	//				Apt: &edt_rule2.APT{
	//					ItemEstimatedDaysAsPreOrder: false,
	//					DTSAsAPTFallback:            true,
	//					AggregatedByOrderCreateTime: true,
	//				},
	//			},
	//		},
	//	},
	//}

	//// test channel upcoming create
	//request := &edt_rule.CreateEdtRuleRequest{
	//	MatchType: edt_rule2.EdtMatchByProduct,
	//	EdtType:     edt_rule.EdtMinPlusMax,
	//	//EffectiveImmediately: true,
	//	EffectiveStartTime: 1717066802,
	//	SpecialChannel:     1000,
	//	SellerGroupRouteEdt: true,
	//	EdtFormulas: edt_rule.EdtFormulas{
	//		FirstFormulaList: []edt_rule.Formula{
	//			edt_rule.Formula{
	//				SubEdtType: edt_rule2.EdtMinType,
	//				Apt: &edt_rule2.APT{
	//					ItemEstimatedDaysAsPreOrder: false,
	//					DTSAsAPTFallback:            true,
	//					AggregatedByOrderCreateTime: true,
	//				},
	//				AptExtension: &edt_rule2.APTNonWorkingDay{
	//					Local: &edt_rule2.SingleAPTNonWorkingDay{
	//						LPSChannelHolidayAndWeekend: &edt_rule2.LPSChannelHolidayAndWeekend{
	//							CBSpecial: true,
	//						},
	//						TPLPickupWindowHolidayAndWeekend: false,
	//						SellerHolidayAndWeekend:          true,
	//						EstimateSellerWeekend:            false,
	//					},
	//					CB: &edt_rule2.SingleAPTNonWorkingDay{
	//						LPSChannelHolidayAndWeekend: &edt_rule2.LPSChannelHolidayAndWeekend{
	//							CBSpecial: true,
	//						},
	//						TPLPickupWindowHolidayAndWeekend: false,
	//						SellerHolidayAndWeekend:          true,
	//						EstimateSellerWeekend:            false,
	//					},
	//				},
	//				Cdt: &edt_rule2.CDTMin{
	//					EstimatedSellerShipmentMethod: false,
	//				},
	//				CdtExtension: &edt_rule2.CDTMinNonWorkingDay{
	//					LPSChannelHolidayAndWeekend: true,
	//				},
	//				FixedEdt: &edt_rule2.FixedEDT{
	//					FixedEDT:      111,
	//					CutoffTime:    222,
	//					SellerHoliday: true,
	//				},
	//				SBD: &edt_rule2.SBD{
	//					Enable: true,
	//				},
	//				EDTNWDAdjustment: &edt_rule2.EDTNWDAdjustment{
	//					Enable: false,
	//				},
	//			},
	//			edt_rule.Formula{
	//				SubEdtType: edt_rule2.EdtMaxType,
	//				Apt: &edt_rule2.APT{
	//					ItemEstimatedDaysAsPreOrder: false,
	//					DTSAsAPTFallback:            true,
	//					AggregatedByOrderCreateTime: true,
	//				},
	//				AptExtension: &edt_rule2.APTNonWorkingDay{
	//					Local: &edt_rule2.SingleAPTNonWorkingDay{
	//						LPSChannelHolidayAndWeekend: &edt_rule2.LPSChannelHolidayAndWeekend{
	//							CBSpecial: true,
	//						},
	//						TPLPickupWindowHolidayAndWeekend: false,
	//						SellerHolidayAndWeekend:          true,
	//						EstimateSellerWeekend:            false,
	//					},
	//					CB: &edt_rule2.SingleAPTNonWorkingDay{
	//						LPSChannelHolidayAndWeekend: &edt_rule2.LPSChannelHolidayAndWeekend{
	//							CBSpecial: true,
	//						},
	//						TPLPickupWindowHolidayAndWeekend: false,
	//						SellerHolidayAndWeekend:          true,
	//						EstimateSellerWeekend:            false,
	//					},
	//				},
	//				Cdt: &edt_rule2.CDTMin{
	//					EstimatedSellerShipmentMethod: false,
	//				},
	//				CdtExtension: &edt_rule2.CDTMinNonWorkingDay{
	//					LPSChannelHolidayAndWeekend: true,
	//				},
	//				FixedEdt: &edt_rule2.FixedEDT{
	//					FixedEDT:      111,
	//					CutoffTime:    222,
	//					SellerHoliday: true,
	//				},
	//				SBD: &edt_rule2.SBD{
	//					Enable: true,
	//				},
	//				EDTNWDAdjustment: &edt_rule2.EDTNWDAdjustment{
	//					Enable: false,
	//				},
	//			},
	//		},
	//	},
	//}

	//test channel active create
	request := &edt_rule.CreateEdtRuleRequest{
		MatchType:            edt_rule2.EdtMatchByProduct,
		EffectiveImmediately: true,
		EffectiveStartTime:   1717559428,
		SpecialChannel:       90022,
		Region:               "BR",
		Operator:             "xiaolin",
		EdtFormulaList: []edt_rule.EdtFormula{
			{
				SellerGroupRouteEdt: true,
				EdtType:             edt_rule.EdtMinPlusMax,
				MinEdtFormula: &edt_rule.Formula{
					Apt: &edt_rule2.APT{
						ItemEstimatedDaysAsPreOrder: false,
						DTSAsAPTFallback:            true,
						AggregatedByOrderCreateTime: true,
					},
					AptExtension: &edt_rule2.NewAPTNonWorkingDay{
						Local: &edt_rule2.NewSingleAPTNonWorkingDay{
							NewLPSChannelHolidayAndWeekend:   true,
							TPLPickupWindowHolidayAndWeekend: false,
							SellerHolidayAndWeekend:          true,
							EstimateSellerWeekend:            false,
						},
						CB: &edt_rule2.NewSingleAPTNonWorkingDay{
							NewLPSChannelHolidayAndWeekend:   true,
							TPLPickupWindowHolidayAndWeekend: false,
							SellerHolidayAndWeekend:          true,
							EstimateSellerWeekend:            false,
						},
					},
					Cdt: &edt_rule2.CDTMin{
						EstimatedSellerShipmentMethod: false,
					},
					CdtExtension: &edt_rule2.CDTMinNonWorkingDay{
						LPSChannelHolidayAndWeekend: true,
					},
					FixedEdt: &edt_rule2.FixedEDT{
						FixedEDT:      111,
						CutoffTime:    222,
						SellerHoliday: true,
					},
					SBD: &edt_rule2.SBD{
						Enable: true,
					},
					EDTNWDAdjustment: &edt_rule2.EDTNWDAdjustment{
						Enable: false,
					},
				},
				MaxEdtFormula: &edt_rule.Formula{
					Apt: &edt_rule2.APT{
						ItemEstimatedDaysAsPreOrder: false,
						DTSAsAPTFallback:            true,
						AggregatedByOrderCreateTime: true,
					},
				},
			},
		},
	}

	entity := ConvertRequest(ctx, request)

	type fields struct {
		EdtRuleRepo      edt_rule2.EdtRuleRepo
		ValidatorManager ValidatorManager
	}
	type args struct {
		c            utils.LCOSContext
		effectiveNow bool
		entity       *edt_rule2.CreateEntity
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			fields: fields{
				EdtRuleRepo: edt_rule2.NewEdtRuleRepoImpl(),
			},
			args: args{
				c:            ctx,
				effectiveNow: request.EffectiveImmediately,
				entity:       entity,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &EdtRuleServiceImpl{
				EdtRuleRepo:      tt.fields.EdtRuleRepo,
				ValidatorManager: tt.fields.ValidatorManager,
			}
			//tt.args.entity.AbTestGroup.Id = 18
			//tt.args.entity.Formulas[0].Id = 118
			//tt.args.entity.Formulas[1].Id = 119
			if got := i.CreateFormulas(tt.args.c, tt.args.effectiveNow, tt.args.entity); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("createFormulas() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEdtRuleServiceImpl_EdtRuleSchedule(t *testing.T) {
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("CID", "sg")
	//_ = os.Setenv("PFB_NAME", "PFB_NAME=pfb-dms-dev-spln-31383-public")

	ctxWithPfb, _ := viewercontext.Start(context.WithValue(context.Background(), "logid", "111111dawdawd111111111111111"), attr.WithPFB("pfb-dms-dev-spln-31383-public"))
	ctx := utils.NewCommonCtx(ctxWithPfb)

	startup.InitSSCEnv()
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		log.Fatalf("getConfig %v", err)
	}

	_, err = cf.InitMutableConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	err = startup.InitLibs(c)

	if err != nil {
		log.Fatalf("InitLibs Error: %v", err)
	}

	type fields struct {
		EdtRuleRepo      edt_rule2.EdtRuleRepo
		ValidatorManager ValidatorManager
	}
	type args struct {
		ctx utils.LCOSContext
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			fields: fields{
				EdtRuleRepo:      edt_rule2.NewEdtRuleRepoImpl(),
				ValidatorManager: NewValidatorManagerImpl(edt_rule2.NewEdtRuleRepoImpl()),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &EdtRuleServiceImpl{
				EdtRuleRepo:      tt.fields.EdtRuleRepo,
				ValidatorManager: tt.fields.ValidatorManager,
			}
			i.EdtRuleSchedule(tt.args.ctx)
		})
	}
}

func TestEdtRuleServiceImpl_UploadCutoffTime(t *testing.T) {
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("CID", "sg")
	//_ = os.Setenv("PFB_NAME", "PFB_NAME=pfb-dms-dev-spln-31383-public")

	ctxWithPfb, _ := viewercontext.Start(context.WithValue(context.Background(), "logid", "111111dawdawd111111111111111"), attr.WithPFB("pfb-dms-dev-spln-31383-public"))
	ctx := utils.NewCommonCtx(ctxWithPfb)

	startup.InitSSCEnv()
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		log.Fatalf("getConfig %v", err)
	}

	_, err = cf.InitMutableConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	err = startup.InitLibs(c)

	if err != nil {
		log.Fatalf("InitLibs Error: %v", err)
	}

	type fields struct {
		EdtRuleRepo      edt_rule2.EdtRuleRepo
		ValidatorManager ValidatorManager
	}
	type args struct {
		c       utils.LCOSContext
		request *edt_rule.UploadCutoffTimeReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			fields: fields{
				EdtRuleRepo:      edt_rule2.NewEdtRuleRepoImpl(),
				ValidatorManager: NewValidatorManagerImpl(edt_rule2.NewEdtRuleRepoImpl()),
			},
			args: args{
				c: utils.NewCommonCtx(context.Background()),
				request: &edt_rule.UploadCutoffTimeReq{
					// add
					//FileUrl: "https://proxy.uss.s3.test.shopee.io/shopee_slsopsrecon_sg_test/ssc-ops/20240607/283612231d47179c78dc45d59ef592f5/EDT_hardcoding_Template.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=60034057%2F20240607%2Fdefault%2Fs3%2Faws4_request&X-Amz-Date=20240607T034234Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=0599ebe1a68f84a8e0b53e9ea83d4c212232c600270e83c2d7b2d5bd949bc449",
					// delete
					FileUrl: "https://proxy.uss.s3.test.shopee.io/shopee_slsopsrecon_sg_test/ssc-ops/20240612/d7a9a46f0b7f756d9366bf7d7a00637b/EDT_hardcoding_Template.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256\u0026X-Amz-Credential=60034057%2F20240612%2Fdefault%2Fs3%2Faws4_request\u0026X-Amz-Date=20240612T023345Z\u0026X-Amz-Expires=604800\u0026X-Amz-SignedHeaders=host\u0026X-Amz-Signature=70634cda1b2d00745d4270f113475912a2ba2d2b9044df2609cc1492c393fabe",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &EdtRuleServiceImpl{
				EdtRuleRepo:      tt.fields.EdtRuleRepo,
				ValidatorManager: tt.fields.ValidatorManager,
			}
			if got := i.UploadCutoffTime(tt.args.c, tt.args.request, "VN", "xiaolin"); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UploadCutoffTime() = %v, want %v", got, tt.want)
			}
		})
	}
}
