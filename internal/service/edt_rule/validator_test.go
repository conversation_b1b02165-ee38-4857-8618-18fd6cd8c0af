package edt_rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	edt_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
	"git.garena.com/shopee/platform/service-governance/viewercontext/attr"
	"log"
	"os"
	"path"
	"reflect"
	"testing"
)

func TestRegionValidatorImpl_CheckRequest(t *testing.T) {
	//_ = os.Setenv("ENV", "TEST")
	//_ = os.Setenv("CID", "sg")
	////_ = os.Setenv("PFB_NAME", "PFB_NAME=pfb-dms-dev-spln-31383-public")
	//
	//ctxWithPfb, _ := viewercontext.Start(context.WithValue(context.Background(), "logid", "111111dawdawd111111111111111"), attr.WithPFB("pfb-dms-dev-spln-31383-public"))
	//ctx := utils.NewCommonCtx(ctxWithPfb)
	//
	//startup.InitSSCEnv()
	//_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	//
	//err := chassis.Init()
	//if err != nil {
	//	t.Fatalf(err.Error())
	//}
	//
	//c, err := cf.InitConfig(ctx)
	//if err != nil {
	//	log.Fatalf("getConfig %v", err)
	//}
	//err = startup.InitLibs(c)
	//
	//if err != nil {
	//	log.Fatalf("InitLibs Error: %v", err)
	//}

	type fields struct {
		EdtRuleRepo edt_rule.EdtRuleRepo
	}
	type args struct {
		ctx     utils.LCOSContext
		request *edt_rule2.CreateEdtRuleRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		// region type Formula
		//{
		//	args: args{
		//		ctx: utils.NewCommonCtx(context.Background()),
		//		request: &edt_rule2.CreateEdtRuleRequest{
		//			MatchType:          edt_rule.EdtMatchByRegion,
		//			EdtType:              edt_rule2.EdtMinAndMax,
		//			EffectiveImmediately: true,
		//			EffectiveStartTime:   time.Now().Unix(),
		//			EdtFormulas: edt_rule2.EdtFormulas{
		//				FirstFormulaList: []edt_rule2.Formula{
		//					edt_rule2.Formula{
		//						SubEdtType: edt_rule.EdtMinType,
		//					},
		//					//edt_rule2.Formula{
		//					//	SubEdtType: edt_rule.EdtMinAndEdtMax,
		//					//},
		//				},
		//			},
		//		},
		//	},
		//	want: nil,
		//},

		//region type formula
		{
			fields: fields{
				EdtRuleRepo: edt_rule.NewEdtRuleRepoImpl(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edt_rule2.CreateEdtRuleRequest{
					MatchType: edt_rule.EdtMatchByRegion,
					//EffectiveImmediately: true,
					EffectiveStartTime: 1717066802,
					//EnableMultiFormula: true,
					EdtFormulaList: []edt_rule2.EdtFormula{
						//{
						//	MinEdtFormula: &edt_rule2.Formula{},
						//	MaxEdtFormula: &edt_rule2.Formula{},
						//},
						{
							SellerGroupRouteEdt: true,
							EdtType:             edt_rule2.EdtMinPlusMax,
							MinEdtFormula:       &edt_rule2.Formula{},
							MaxEdtFormula:       &edt_rule2.Formula{},
							GeneralEdtFormula:   &edt_rule2.Formula{},
						},
					},
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &RegionValidatorImpl{
				EdtRuleRepo: tt.fields.EdtRuleRepo,
			}
			if got := i.CheckRequest(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckRequest() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelValidatorImpl_CheckRequest(t *testing.T) {
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("CID", "sg")
	//_ = os.Setenv("PFB_NAME", "PFB_NAME=pfb-dms-dev-spln-31383-public")

	ctxWithPfb, _ := viewercontext.Start(context.WithValue(context.Background(), "logid", "111111dawdawd111111111111111"), attr.WithPFB("pfb-dms-dev-spln-31383-public"))
	ctx := utils.NewCommonCtx(ctxWithPfb)

	startup.InitSSCEnv()
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		log.Fatalf("getConfig %v", err)
	}
	err = startup.InitLibs(c)

	if err != nil {
		log.Fatalf("InitLibs Error: %v", err)
	}

	type fields struct {
		EdtRuleRepo edt_rule.EdtRuleRepo
	}
	type args struct {
		ctx     utils.LCOSContext
		request *edt_rule2.CreateEdtRuleRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			fields: fields{
				EdtRuleRepo: edt_rule.NewEdtRuleRepoImpl(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edt_rule2.CreateEdtRuleRequest{
					MatchType: edt_rule.EdtMatchByProduct,
					//EffectiveImmediately: true,
					EffectiveStartTime: 1717066802,
					EdtFormulaList: []edt_rule2.EdtFormula{
						{
							SellerGroupRouteEdt: true,
							EdtType:             edt_rule2.EdtMinPlusMax,
							MinEdtFormula:       &edt_rule2.Formula{},
						},
					},
					SpecialChannel: 1004,
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &ChannelValidatorImpl{
				EdtRuleRepo: tt.fields.EdtRuleRepo,
			}
			if got := i.CheckRequest(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckRequest() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAbTestGroupValidatorImpl_CheckRequest(t *testing.T) {
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("CID", "sg")
	//_ = os.Setenv("PFB_NAME", "PFB_NAME=pfb-dms-dev-spln-31383-public")

	ctxWithPfb, _ := viewercontext.Start(context.WithValue(context.Background(), "logid", "111111dawdawd111111111111111"), attr.WithPFB("pfb-dms-dev-spln-31383-public"))
	ctx := utils.NewCommonCtx(ctxWithPfb)

	startup.InitSSCEnv()
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		log.Fatalf("getConfig %v", err)
	}
	err = startup.InitLibs(c)

	if err != nil {
		log.Fatalf("InitLibs Error: %v", err)
	}

	type fields struct {
		EdtRuleRepo edt_rule.EdtRuleRepo
	}
	type args struct {
		ctx     utils.LCOSContext
		request *edt_rule2.CreateEdtRuleRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			fields: fields{
				EdtRuleRepo: edt_rule.NewEdtRuleRepoImpl(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edt_rule2.CreateEdtRuleRequest{
					MatchType: edt_rule.EdtMatchByABTestGroup,
					//EffectiveImmediately: true,
					EffectiveStartTime: 1717066802,
					EdtFormulaList: []edt_rule2.EdtFormula{
						{
							SellerGroupRouteEdt: true,
							EdtType:             edt_rule2.EdtMinPlusMax,
							MinEdtFormula:       &edt_rule2.Formula{},
							MaxEdtFormula:       &edt_rule2.Formula{},
						},
					},
					AbChannelList: []int{10088, 10099},
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &AbTestGroupValidatorImpl{
				EdtRuleRepo: tt.fields.EdtRuleRepo,
			}
			if got := i.CheckRequest(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckRequest() = %v, want %v", got, tt.want)
			}
		})
	}
}
