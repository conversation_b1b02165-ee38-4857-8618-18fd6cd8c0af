package edt_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	commonConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/ceprange_util"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/edt_rule"
	edt_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"os"
	"strconv"
	"strings"
	"time"
)

type EdtRuleService interface {
	CreateEdtRule(c *utils.HttpContext, request *edt_rule.CreateEdtRuleRequest) *lcos_error.LCOSError
	ListEdtRule(c *utils.HttpContext, request *edt_rule.ListEdtRuleRequest) (*edt_rule.ListEdtRuleResponse, *lcos_error.LCOSError)
	GetRelationFormulaByRuleId(c *utils.HttpContext, ruleIds []uint64) (map[uint64][]edt_rule2.EDTFormulaTab, *lcos_error.LCOSError)
	GetEdtRule(c *utils.HttpContext, request *edt_rule.GetRuleDetailRequest) (*edt_rule.GetRuleDetailResponse, *lcos_error.LCOSError)
	DisableEdtRule(c *utils.HttpContext, request *edt_rule.GetRuleDetailRequest) *lcos_error.LCOSError
	EdtRuleSchedule(ctx utils.LCOSContext)
	//内部方法
	CreateFormulas(c utils.LCOSContext, effectiveNow bool, entity *edt_rule2.CreateEntity) *lcos_error.LCOSError
	// route fixed edt
	UploadRouteFixedEdt(c utils.LCOSContext, request *edt_rule.UploadRouteFixedEdtReq, region, operator string) *lcos_error.LCOSError
	ListRouteFixedEdt(c utils.LCOSContext, request *edt_rule.ListRouteFixedEdtReq, region string, page, size uint32) ([]edt_rule2.LogisticRouteFixedGroupEdtTab, uint32, *lcos_error.LCOSError)
	SearchRouteFixedEdt(c utils.LCOSContext, request *edt_rule.ListRouteFixedEdtReq, region string) ([]edt_rule2.LogisticRouteFixedGroupEdtTab, *lcos_error.LCOSError)
	// cep route fixed edt
	UploadCepRouteFixedEdt(c utils.LCOSContext, request *edt_rule.UploadCepRouteFixedEdtReq, region, operator string) *lcos_error.LCOSError
	ListCepRouteFixedEdt(c utils.LCOSContext, request *edt_rule.ListCepRouteFixedEdtReq, region string, page, size uint32) ([]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, uint32, *lcos_error.LCOSError)
	SearchCepRouteFixedEdt(c utils.LCOSContext, request *edt_rule.ListCepRouteFixedEdtReq, region string) ([]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, *lcos_error.LCOSError)
}

type EdtRuleServiceImpl struct {
	EdtRuleRepo      edt_rule2.EdtRuleRepo
	ValidatorManager ValidatorManager
}

func NewEdtRuleServiceImpl(EdtRuleRepo edt_rule2.EdtRuleRepo,
	ValidatorManager ValidatorManager) *EdtRuleServiceImpl {
	return &EdtRuleServiceImpl{
		EdtRuleRepo:      EdtRuleRepo,
		ValidatorManager: ValidatorManager,
	}
}

func (i *EdtRuleServiceImpl) CreateEdtRule(c *utils.HttpContext, request *edt_rule.CreateEdtRuleRequest) *lcos_error.LCOSError {
	// 1.转换请求，生成upcoming tab
	// 1.1 check request 是否合法
	request.Region = strings.ToUpper(c.GetCountry())
	validator := i.ValidatorManager.GetValidator(request.MatchType)
	if err := validator.CheckRequest(c, request); err != nil {
		logger.CtxLogErrorf(c, "check request err:%v", err)
		return err
	}

	operator := c.GetUserName()
	request.Operator = operator

	// 1.2 转换数据模型
	entity := ConvertRequest(c, request)

	// 2.判断是否要立即生效；若要立即生效，则检索并替换active Rule；否则直接落DB
	if err := i.CreateFormulas(c, request.EffectiveImmediately, entity); err != nil {
		logger.CtxLogErrorf(c, "insert request into db err:%v", err)
		return err
	}

	return nil
}
func (i *EdtRuleServiceImpl) ListEdtRule(c *utils.HttpContext, request *edt_rule.ListEdtRuleRequest) (*edt_rule.ListEdtRuleResponse, *lcos_error.LCOSError) {
	// 1 组装请求参数
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	request.Region = strings.ToUpper(c.GetCountry())
	formulaCondition, _ := utils.Struct2map(request)

	// 去除algo公式，目前只会在region维度
	if request.MatchType == edt_rule2.EdtMatchByRegion {
		formulaCondition["match_value"] = edt_rule2.EmptyMatchValue
	}

	// abtest公式
	if request.MatchType == edt_rule2.EdtMatchByABTestGroup {
		return i.ListABTestFormula(c, request, pageNo, count, formulaCondition)
	}

	if request.MatchType == edt_rule2.EdtMatchByProduct && request.ProductId != nil && *request.ProductId != 0 {
		formulaCondition["match_value"] = *request.ProductId
	}

	// 2 检索数据（channel and region公式）
	result, total, err := i.EdtRuleRepo.SearchEdtRule(c, pageNo, count, formulaCondition)
	if err != nil {
		logger.CtxLogErrorf(c, "search edt rule err:%v", err)
		return nil, err
	}

	// 2.1需要根据结果重新查一遍数据库，查找改rule下所有的formula
	var (
		ruleIdList []uint64
	)

	for _, tab := range result {
		ruleIdList = append(ruleIdList, tab.RuleId)
	}
	ruleMap, gErr := i.GetRelationFormulaByRuleId(c, ruleIdList)
	if gErr != nil {
		return nil, gErr
	}

	// 3 组装返回结果
	respList, cErr := ConvertFormulaListResponse(c, result, ruleMap)
	if cErr != nil {
		return nil, cErr
	}
	// 4 兼容channel返回
	if request.MatchType == edt_rule2.EdtMatchByProduct {
		respList, err = i.GetListSpecialChannelFormulaResponse(c, result, respList)
		if err != nil {
			logger.CtxLogErrorf(c, "get special channel edt rule fail,err:%v", err)
			return nil, err
		}
	}

	return &edt_rule.ListEdtRuleResponse{
		Total:  total,
		PageNo: pageNo,
		Count:  count,
		List:   respList,
	}, nil
}

func (i *EdtRuleServiceImpl) GetRelationFormulaByRuleId(c *utils.HttpContext, ruleIds []uint64) (map[uint64][]edt_rule2.EDTFormulaTab, *lcos_error.LCOSError) {
	var (
		ruleFormulaMap = make(map[uint64][]edt_rule2.EDTFormulaTab)
	)

	// 1 参数校验
	if len(ruleIds) == 0 {
		return ruleFormulaMap, nil
	}

	// 2 检索数据
	formulaCondition := map[string]interface{}{
		"rule_id in": ruleIds,
	}
	results, err := i.EdtRuleRepo.ListEdtRule(c, formulaCondition)
	if err != nil {
		logger.CtxLogErrorf(c, "get relation formula by rule id err:%v", err)
		return nil, err
	}

	for _, result := range results {
		formulaList, ok := ruleFormulaMap[result.RuleId]
		if !ok {
			formulaList = []edt_rule2.EDTFormulaTab{}
		}
		formulaList = append(formulaList, result)
		ruleFormulaMap[result.RuleId] = formulaList
	}

	return ruleFormulaMap, nil
}

func (i *EdtRuleServiceImpl) GetEdtRule(c *utils.HttpContext, request *edt_rule.GetRuleDetailRequest) (*edt_rule.GetRuleDetailResponse, *lcos_error.LCOSError) {
	// 处理请求参数
	request.Region = strings.ToUpper(c.GetCountry())
	formulaCondition, _ := utils.Struct2map(request)

	// 2 检索数据
	// 2.1 检索出与该rule相关的所有数据
	results, err := i.EdtRuleRepo.ListEdtRule(c, formulaCondition)
	if err != nil {
		logger.CtxLogErrorf(c, "search edt rule err:%v", err)
		return nil, err
	}

	// 2.2 将数据结构进行转化
	resp, err := ConvertFormulaDetailResponse(c, results)
	if err != nil {
		logger.CtxLogErrorf(c, "convert formula detail response err ,result:%v ,err:%v", results, err)
		return nil, err
	}

	// 3 兼容channel返回
	if request.MatchType == edt_rule2.EdtMatchByProduct {
		resp, err = i.GetDetailSpecialChannelFormulaResponse(c, results, resp)
		if err != nil {
			logger.CtxLogErrorf(c, "get special channel edt rule fail,err:%v", err)
			return nil, err
		}
	}
	// 4 兼容AB Test的返回
	if request.MatchType == edt_rule2.EdtMatchByABTestGroup {
		resp, err = i.GetDetailABTestFormulaResponse(c, results[0], results, resp)
		if err != nil {
			logger.CtxLogErrorf(c, "get ab test group edt rule fail err:%v", err)
			return nil, err
		}
	}

	return resp, nil
}

func (i *EdtRuleServiceImpl) DisableEdtRule(c *utils.HttpContext, request *edt_rule.GetRuleDetailRequest) *lcos_error.LCOSError {
	// 处理请求参数
	request.Region = strings.ToUpper(c.GetCountry())
	formulaCondition, _ := utils.Struct2map(request)

	switch request.MatchType {
	case edt_rule2.EdtMatchByRegion:
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "not support region rule")
	case edt_rule2.EdtMatchByProduct:
		return i.specialChannelFormulaDisable(c, formulaCondition)
	case edt_rule2.EdtMatchByABTestGroup:
		return i.abTestFormulaDisable(c, formulaCondition)
	default:
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "edt match type not support")
	}
}

func (i *EdtRuleServiceImpl) EdtRuleSchedule(ctx utils.LCOSContext) {
	// 1.获取effective time 小于当前Local时间的upcoming Rule
	var (
		upcomingTypeList = []edt_rule2.EdtMatchType{edt_rule2.EdtMatchByRegion, edt_rule2.EdtMatchByProduct, edt_rule2.EdtMatchByABTestGroup}
	)
	for _, upcomingType := range upcomingTypeList {
		for _, region := range config.GetRegionList(ctx) {
			var (
				entity = &edt_rule2.CreateEntity{
					FormulaType: upcomingType,
					Region:      region,
				}
				upcomingABIds []uint64
				nowTime       = utils.GetCurrentLocalTimeStampByRegion(ctx, region)
				// 非ab test，不需要通过ab test group来检索Formula，直接按照常规条件检索即可
				formulaCondition = map[string]interface{}{
					"match_type =":            upcomingType,
					"status_id =":             edt_rule2.StatusUpcoming,
					"region =":                region,
					"effective_start_time <=": nowTime,
				}
			)
			// 1. 先判断需不需要获取ab test group
			if upcomingType == edt_rule2.EdtMatchByABTestGroup {
				condition := map[string]interface{}{
					"region =":                region,
					"enable_status =":         edt_rule2.StatusUpcoming,
					"effective_start_time <=": nowTime,
				}
				abTestGroupList, err := i.EdtRuleRepo.ListAbTestGroup(ctx, condition)
				if err != nil {
					msg := fmt.Sprintf("formula type:%v, condition:%v, get ab test group err:%v", upcomingType, condition, err)
					logger.CtxLogErrorf(ctx, msg)
					// 上报cat
					_ = monitor.ReportEvent(commonConstant.EDTError, commonConstant.EdtRuleScheduleFail, commonConstant.StatusError, msg)
					continue
				}
				// 装填需要生效的ab test group
				entity.AbTestGroups = abTestGroupList

				// 装填Formula检索条件
				for _, abTestGroup := range abTestGroupList {
					upcomingABIds = append(upcomingABIds, abTestGroup.Id)
				}
				formulaCondition["match_value in"] = upcomingABIds
			}
			// 2. 获取upcoming Formulas
			formulas, err := i.EdtRuleRepo.ListEdtRule(ctx, formulaCondition)
			if err != nil {
				msg := fmt.Sprintf("formula type:%v, condition:%v, select err:%v", upcomingType, formulaCondition, err)
				logger.CtxLogErrorf(ctx, msg)
				// 上报cat
				_ = monitor.ReportEvent(commonConstant.EDTError, commonConstant.EdtRuleScheduleFail, commonConstant.StatusError, msg)
				continue
			}

			if len(formulas) == 0 {
				logger.CtxLogInfof(ctx, "match type:%v|region:%s|empty upcoming formula, continue", upcomingType, region)
				continue
			}

			entity.Formulas = formulas //这里的match value非空

			// fill entity
			fillEntity(entity, nowTime)

			// 2.替换生效
			if err := i.CreateFormulas(ctx, true, entity); err != nil {
				msg := fmt.Sprintf("formula type:%v, create formulas err:%v", upcomingType, err)
				logger.CtxLogErrorf(ctx, msg)
				// 上报cat
				_ = monitor.ReportEvent(commonConstant.EDTError, commonConstant.EdtRuleScheduleFail, commonConstant.StatusError, msg)
			}
		}
	}
}

// ConvertRequest 将request转换为db模型
func ConvertRequest(ctx utils.LCOSContext, request *edt_rule.CreateEdtRuleRequest) *edt_rule2.CreateEntity {
	var (
		entity = &edt_rule2.CreateEntity{
			FormulaType: request.MatchType,
			ChannelIds:  []int64{request.SpecialChannel},
			Region:      request.Region,
		}
		formulas    []edt_rule2.EDTFormulaTab
		abTestGroup edt_rule2.EDTABTestGroupTab
	)
	// region\channel 只需要Formula tab
	if request.MatchType != edt_rule2.EdtMatchByABTestGroup {
		nowTime := utils.GetCurrentLocalTimeStampByRegion(ctx, request.Region)
		formulas = onlyConvertFormula(ctx, request, nowTime)
	} else { //ab test group, 需要Formula tab 和 ab test group tab
		formulas, abTestGroup = convertFormulaAndAb(ctx, request)
	}
	entity.Formulas = formulas
	entity.AbTestGroups = []edt_rule2.EDTABTestGroupTab{abTestGroup}

	return entity
}

// onlyConvertFormula 将request转换为Formula db模型
func onlyConvertFormula(ctx utils.LCOSContext, request *edt_rule.CreateEdtRuleRequest, nowTime int64) []edt_rule2.EDTFormulaTab {
	var (
		multiFormulaStrategy = request.MultiFormulaPriorityType
		result               = make([]edt_rule2.EDTFormulaTab, 0)
	)

	// 循环转换多公式，展平成无状态的Formula
	for _, formula := range request.EdtFormulaList {
		// basic info
		var (
			tab = edt_rule2.EDTFormulaTab{
				Region:             request.Region,
				StatusID:           getStatus(request.EffectiveImmediately),
				MatchType:          request.MatchType,
				EffectiveStartTime: getEffectiveStartTime(request.EffectiveImmediately, nowTime, request.EffectiveStartTime),
				Ctime:              nowTime,
				Mtime:              nowTime,
				Operator:           request.Operator,
				// MatchValue : region-不需要装填，默认为0； ab_test_group-需要match ab test group,因此等到实际创建时再赋值
			}
			typeFormulaMap = make(map[edt_rule2.EdtFormulaType]*edt_rule.Formula)
		)
		if formula.IsVerifyAvailableChannel && len(formula.VerifyAvailableChannelList) > 0 {
			tab.AvailableFChannelList = formula.VerifyAvailableChannelList
		}

		// 装填Channel match value, shop-route toggle
		if request.MatchType == edt_rule2.EdtMatchByProduct {
			tab.MatchValue = uint64(request.SpecialChannel)
		}

		// 获取Formula信息
		if formula.MinEdtFormula != nil {
			typeFormulaMap[edt_rule2.EdtMinType] = formula.MinEdtFormula
		}
		if formula.MaxEdtFormula != nil {
			typeFormulaMap[edt_rule2.EdtMaxType] = formula.MaxEdtFormula
		}
		if formula.GeneralEdtFormula != nil {
			typeFormulaMap[edt_rule2.EdtMinAndEdtMax] = formula.GeneralEdtFormula
		}

		// 装填min tab细节
		for edtFormulaType, tempFormula := range typeFormulaMap {
			tempTab := tab
			tempTab.EdtType = edtFormulaType
			newFixedEdt := tempFormula.FillNewFixedEdt()
			// 装填Formula细节
			edtConfig := edt_rule2.EDTConfig{
				APT:              tempFormula.Apt,
				APTNonWorkingDay: tempFormula.ConvertAptExtension(),
				// cdt min/max公用
				CDTMin: tempFormula.Cdt,
				CDTMax: tempFormula.CopyToCdtMax(),
				// cdt nwd min/max公用
				CDTMinNonWorkingDay: tempFormula.CdtExtension,
				CDTMaxNonWorkingDay: tempFormula.CopyToCdtMaxNWD(),
				NewFixedEDT:         newFixedEdt,
				SBD:                 tempFormula.SBD,
				EDTNWDAdjustment:    tempFormula.ConvertEDTNWDAdjustment(),
				FixedShopEDT:        tempFormula.ConvertFixedShopEdt(),
				AlgoControl:         tempFormula.ConvertAlgoControlEdt(),
			}
			tempTab.Formula = edtConfig
			tempTab.EnableShopRouteEdt = uint8(formula.SellerRouteEdt)
			tempTab.Priority = uint8(formula.Priority)
			tempTab.MultiFormulaStrategy = uint8(multiFormulaStrategy)
			result = append(result, tempTab)
		}
	}

	return result
}

// convertFormulaAndAb 转换为Formula和ab test group模型
func convertFormulaAndAb(ctx utils.LCOSContext, request *edt_rule.CreateEdtRuleRequest) ([]edt_rule2.EDTFormulaTab, edt_rule2.EDTABTestGroupTab) {
	// 转换Formula模型
	nowTime := utils.GetCurrentLocalTimeStampByRegion(ctx, request.Region)
	formulas := onlyConvertFormula(ctx, request, nowTime)

	// 转换ab test group模型
	abTestGroupTab := edt_rule2.EDTABTestGroupTab{
		Region:             request.Region,
		EnableStatus:       getStatus(request.EffectiveImmediately),
		ProductIDList:      request.AbChannelList,
		Ctime:              nowTime,
		Mtime:              nowTime,
		BuyerIdGroupList:   request.BuyerIdGroupList,
		EffectiveStartTime: getEffectiveStartTime(request.EffectiveImmediately, nowTime, request.EffectiveStartTime),
	}

	return formulas, abTestGroupTab
}

// getStatus 根据开关决定Formula 和ab test group的状态
func getStatus(effectiveImmediately bool) uint8 {
	if effectiveImmediately {
		return edt_rule2.StatusActive
	}

	return edt_rule2.StatusUpcoming
}

// getStatus 根据开关决定Formula 和ab test group的状态
func getEffectiveStartTime(effectiveImmediately bool, now, reqEffectiveStartTime int64) int64 {
	if effectiveImmediately {
		return now
	}

	return reqEffectiveStartTime
}

// CreateFormulas 底层公共方法，用来admin立即创建，or 定时任务生效时调用
func (i *EdtRuleServiceImpl) CreateFormulas(c utils.LCOSContext, effectiveNow bool, entity *edt_rule2.CreateEntity) *lcos_error.LCOSError {
	var (
		needExpiredAbIds, needExpiredFormulaIds []uint64
		err                                     *lcos_error.LCOSError
	)

	if effectiveNow {
		// 1 获取需要expired的active ab test group
		needExpiredAbIds, err = i.getNeedExpiredAbIds(c, entity)
		if err != nil {
			return err
		}
		// 2. 获取需要expired的active Formula
		needExpiredFormulaIds, err = i.getNeedExpiredFormulaIds(c, needExpiredAbIds, entity)
		if err != nil {
			return err
		}
	}

	// 立即生效，需要将active Rule检索出来，设置成expired，再把本次请求中的Rule设置为active
	err = c.Transaction(func() *lcos_error.LCOSError {
		// 3. 写入新的ab test group
		if entity.FormulaType == edt_rule2.EdtMatchByABTestGroup {
			// upcoming定时生效模式，按id更新即可，否则写入新数据
			if err := i.EdtRuleRepo.BatchCreateOrUpdateAbTestGroup(c, entity.AbTestGroups); err != nil {
				logger.CtxLogErrorf(c, "insert active ab test group, err:%v", err)
				return err
			}
			// 装填match_value
			for idx := 0; idx < len(entity.Formulas); idx++ {
				// match value != 0, 说明是upcoming定时生效任务；只有立即生效场景需要装填match value
				// 由于外层根据match type作了区分，所以match value只能同时为空，或同时非空
				if entity.Formulas[idx].MatchValue != edt_rule2.EmptyMatchValue {
					break
				}
				entity.Formulas[idx].MatchValue = entity.AbTestGroups[0].Id
			}
		}
		// 4.获取最新的rule id
		maxRuleId, lErr := i.EdtRuleRepo.GetMaxEdtRuleId(c)
		if lErr != nil {
			logger.CtxLogErrorf(c, "get last edt rule err:%v", lErr)
			return lErr
		}
		var formulaList []edt_rule2.EDTFormulaTab
		for _, formula := range entity.Formulas {
			// 当rule id为0 的时候需要生成最新的rule id
			if formula.RuleId == 0 {
				formula.RuleId = maxRuleId + 1
			}
			formulaList = append(formulaList, formula)
		}

		// 5. 写入新的Formula
		if err := i.EdtRuleRepo.BatchCreateOrUpdateEdtRule(c, formulaList); err != nil {
			logger.CtxLogErrorf(c, "insert active formulas, err:%v", err)
			return err
		}

		// 6. 令need expired转换状态
		if len(needExpiredFormulaIds) != 0 {
			if err := i.EdtRuleRepo.UpdateEdtRuleByCondition(c, map[string]interface{}{
				"id in": needExpiredFormulaIds,
			}, map[string]interface{}{
				"status_id": edt_rule2.StatusExpired,
			}); err != nil {
				logger.CtxLogErrorf(c, "update active rule to expired, err:%v", err)
				return err
			}
		}

		// 7. 令need expired ab test转换状态
		if len(needExpiredAbIds) != 0 {
			if err := i.EdtRuleRepo.UpdateAbTestGroupByCondition(c, map[string]interface{}{
				"id in": needExpiredAbIds,
			}, map[string]interface{}{
				"enable_status": edt_rule2.StatusExpired,
			}); err != nil {
				logger.CtxLogErrorf(c, "update active rule to expired, err:%v", err)
				return err
			}
		}

		return nil
	})
	if err != nil {
		logger.CtxLogErrorf(c, "insert upcoming formulas err:%v", err)
		return err
	}

	logger.CtxLogInfof(c, "finish create formulas")

	return nil
}

func (i *EdtRuleServiceImpl) getNeedExpiredAbIds(c utils.LCOSContext, entity *edt_rule2.CreateEntity) ([]uint64, *lcos_error.LCOSError) {
	var (
		effectiveStartTime = entity.GetMaxEffectiveStartTime()
	)

	if entity.FormulaType != edt_rule2.EdtMatchByABTestGroup {
		logger.CtxLogInfof(c, "empty ab test group, no need check")
		return nil, nil
	}

	needExpiredAbId := make([]uint64, 0)
	// 1. 获取所有可用的ab test group
	condition := map[string]interface{}{
		"enable_status =": edt_rule2.StatusActive,
		"region =":        entity.Region,
	}
	activeAbTestGroups, err := i.EdtRuleRepo.ListAbTestGroup(c, condition)
	if err != nil {
		logger.CtxLogErrorf(c, "get ab test group by condition:%v, err:%v", condition, err)
		return nil, err
	}
	// 2. 获取所有upcoming且生效时间小等于当前时间的ab test group
	upcomingCondition := map[string]interface{}{
		"enable_status =":        edt_rule2.StatusUpcoming,
		"region =":               entity.Region,
		"effective_start_time <": effectiveStartTime,
	}
	upComingAbTestGroups, err := i.EdtRuleRepo.ListAbTestGroup(c, upcomingCondition)
	if err != nil {
		logger.CtxLogErrorf(c, "get ab test group by condition:%v, err:%v", upcomingCondition, err)
		return nil, err
	}

	activeAbTestGroups = append(activeAbTestGroups, upComingAbTestGroups...)

	// 3. 判断是否有交集
	tabs := entity.AbTestGroups
	for _, activeTab := range activeAbTestGroups {
		for _, tab := range tabs {
			if tab.IsProductOverLap(activeTab.ProductIDList) {
				needExpiredAbId = append(needExpiredAbId, activeTab.Id)
			}
		}
	}

	logger.CtxLogInfof(c, "need expired id:%v", needExpiredAbId)
	return needExpiredAbId, nil
}

// getNeedExpiredFormulaIds
/*
	1.region 条件：region + active + match_type = 0
	2.channel: region + active + match_type = 1 + match_value = channel_id
	3.ab test: region + active + match_type = 2 + match_value = ab_test_group_id
*/

func (i *EdtRuleServiceImpl) getNeedExpiredFormulaIds(c utils.LCOSContext, activeABIds []uint64, entity *edt_rule2.CreateEntity) ([]uint64, *lcos_error.LCOSError) {
	var (
		effectiveStartTime = entity.GetMaxEffectiveStartTime()
	)

	needExpiredFormulaIds := make([]uint64, 0)
	condition := map[string]interface{}{
		"region =":     entity.Region,
		"status_id =":  edt_rule2.StatusActive,
		"match_type =": entity.FormulaType,
	}
	// ab test group类型，需要根据active ab id去检索
	if entity.FormulaType == edt_rule2.EdtMatchByABTestGroup {
		if len(activeABIds) == 0 {
			logger.CtxLogInfof(c, "empty duplicate active ab test group, no need check formula, return")
			return needExpiredFormulaIds, nil
		}
		condition["match_value in"] = activeABIds
	}
	// channel类型, match channel id
	if entity.FormulaType == edt_rule2.EdtMatchByProduct {
		if len(entity.ChannelIds) == 0 {
			logger.CtxLogInfof(c, "empty duplicate active channel formula, no need check formula, return")
			return needExpiredFormulaIds, nil
		}
		condition["match_value in"] = entity.ChannelIds
	}
	// region类型，避开算法公式
	if entity.FormulaType == edt_rule2.EdtMatchByRegion {
		condition["match_value ="] = edt_rule2.EmptyMatchValue
	}

	formulas, err := i.EdtRuleRepo.ListEdtRule(c, condition)
	if err != nil {
		logger.CtxLogErrorf(c, "list edt rules by condition:%v, err:%v", condition, err)
		return nil, err
	}
	for _, formula := range formulas {
		needExpiredFormulaIds = append(needExpiredFormulaIds, formula.Id)
	}

	// 2. 获取所有upcoming且生效时间小等于当前时间的ab test group
	condition["status_id ="] = edt_rule2.StatusUpcoming
	condition["effective_start_time <"] = effectiveStartTime

	upComingFormulas, err := i.EdtRuleRepo.ListEdtRule(c, condition)
	if err != nil {
		logger.CtxLogErrorf(c, "list edt rules by condition:%v, err:%v", condition, err)
		return nil, err
	}

	for _, formula := range upComingFormulas {
		needExpiredFormulaIds = append(needExpiredFormulaIds, formula.Id)
	}

	return needExpiredFormulaIds, nil
}

func fillEntity(entity *edt_rule2.CreateEntity, nowTime int64) {
	for i := 0; i < len(entity.AbTestGroups); i++ {
		entity.AbTestGroups[i].EnableStatus = edt_rule2.StatusActive
		entity.AbTestGroups[i].Mtime = nowTime
	}

	for i := 0; i < len(entity.Formulas); i++ {
		entity.Formulas[i].StatusID = edt_rule2.StatusActive
		entity.Formulas[i].Mtime = nowTime

		// 装填Channel id
		if entity.Formulas[i].MatchType == edt_rule2.EdtMatchByProduct {
			entity.ChannelIds = append(entity.ChannelIds, int64(entity.Formulas[i].MatchValue))
		}
	}
	entity.ChannelIds = utils.RemoveInt64Slice(entity.ChannelIds)
}

func ConvertFormulaListResponse(c utils.LCOSContext, results []edt_rule2.EDTFormulaTab, ruleFormulaMap map[uint64][]edt_rule2.EDTFormulaTab) ([]edt_rule.ListEdtRuleData, *lcos_error.LCOSError) {
	var respDataList []edt_rule.ListEdtRuleData
	if len(results) == 0 {
		return respDataList, nil
	}
	for _, edtFormula := range results {
		relateFormulas, ok := ruleFormulaMap[edtFormula.RuleId]
		if !ok {
			logger.CtxLogErrorf(c, "relate formula not found , ruleId : %d", edtFormula.RuleId)
			return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "relate formula not found")
		}

		edtFormulaStr := ""
		// 拼装公式返回
		edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.APT, "APT", edtFormulaStr)
		edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.APTNonWorkingDay, "APT Extension", edtFormulaStr)
		// cdt体现在cdt min 和 cdt max,其中一个有值就需要展示
		if edtFormula.Formula.CDTMin != nil || edtFormula.Formula.CDTMax != nil {
			edtFormulaStr = assemblyFormulaParam("CDT", "CDT", edtFormulaStr)
		}
		if edtFormula.Formula.CDTMinNonWorkingDay != nil || edtFormula.Formula.CDTMaxNonWorkingDay != nil {
			edtFormulaStr = assemblyFormulaParam("CDT Extension", "CDT Extension", edtFormulaStr)
		}
		edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.SBD, "SBD Extension", edtFormulaStr)
		if edtFormula.Formula.FixedEDT != nil || edtFormula.Formula.NewFixedEDT != nil {
			edtFormulaStr = assemblyFormulaParam("Fixed EDT", "Fixed EDT", edtFormulaStr)
		}
		edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.EDTNWDAdjustment, "EDT NWD Adjustment", edtFormulaStr)

		if edtFormula.Formula.FixedShopEDT != nil {
			edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.FixedShopEDT, "Fixed Shop EDT", edtFormulaStr)
		}

		respDataList = append(respDataList, edt_rule.ListEdtRuleData{
			RuleId:             edtFormula.RuleId,
			EdtType:            getEdtTypeList(relateFormulas),
			EdtFormula:         edtFormulaStr,
			Status:             edtFormula.StatusID,
			Operator:           edtFormula.Operator,
			EffectiveStartTime: edtFormula.EffectiveStartTime,
			EnableMultiFormula: getEnableMultiFormulaFromMultiFormulaStrategy(edtFormula.MultiFormulaStrategy),
		})
	}

	return respDataList, nil
}

func convertFormulaListResponseForAbtestRule(c utils.LCOSContext, results []edt_rule2.EDTABTestGroupTab, ruleFormulaMap map[uint64][]edt_rule2.EDTFormulaTab) ([]edt_rule.ListEdtRuleData, *lcos_error.LCOSError) {
	var respDataList []edt_rule.ListEdtRuleData
	if len(results) == 0 {
		return respDataList, nil
	}
	for _, abTestRule := range results {
		relateFormulas, ok := ruleFormulaMap[abTestRule.Id]
		if !ok || len(relateFormulas) == 0 {
			logger.CtxLogErrorf(c, "relate formula not found ,abtest ruleId : %d", abTestRule.Id)
			return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "relate formula not found")
		}
		edtFormula := relateFormulas[0]

		edtFormulaStr := ""
		// 拼装公式返回
		edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.APT, "APT", edtFormulaStr)
		edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.APTNonWorkingDay, "APT Extension", edtFormulaStr)
		// cdt体现在cdt min 和 cdt max,其中一个有值就需要展示
		if edtFormula.Formula.CDTMin != nil || edtFormula.Formula.CDTMax != nil {
			edtFormulaStr = assemblyFormulaParam("CDT", "CDT", edtFormulaStr)
		}
		if edtFormula.Formula.CDTMinNonWorkingDay != nil || edtFormula.Formula.CDTMaxNonWorkingDay != nil {
			edtFormulaStr = assemblyFormulaParam("CDT Extension", "CDT Extension", edtFormulaStr)
		}
		edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.SBD, "SBD Extension", edtFormulaStr)
		if edtFormula.Formula.FixedEDT != nil || edtFormula.Formula.NewFixedEDT != nil {
			edtFormulaStr = assemblyFormulaParam("Fixed EDT", "Fixed EDT", edtFormulaStr)
		}
		edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.EDTNWDAdjustment, "EDT NWD Adjustment", edtFormulaStr)

		if edtFormula.Formula.FixedShopEDT != nil {
			edtFormulaStr = assemblyFormulaParam(edtFormula.Formula.FixedShopEDT, "Fixed Shop EDT", edtFormulaStr)
		}

		respDataList = append(respDataList, edt_rule.ListEdtRuleData{
			RuleId:             edtFormula.RuleId,
			EdtType:            getEdtTypeList(relateFormulas),
			EdtFormula:         edtFormulaStr,
			Status:             edtFormula.StatusID,
			Operator:           edtFormula.Operator,
			EffectiveStartTime: edtFormula.EffectiveStartTime,
			EnableMultiFormula: getEnableMultiFormulaFromMultiFormulaStrategy(edtFormula.MultiFormulaStrategy),
			ABChannelList:      abTestRule.ProductIDList,
			BuyerIdGroupList:   abTestRule.BuyerIdGroupList,
		})
	}

	return respDataList, nil
}

func assemblyFormulaParam(param interface{}, namespace string, formula string) string {
	if param == nil {
		return formula
	}
	if formula == "" {
		return namespace
	}
	return fmt.Sprintf("%s + %s", formula, namespace)
}

// 将策略转化成multi formula标识位
func getEdtTypeList(results []edt_rule2.EDTFormulaTab) []edt_rule.EdtType {
	var (
		edtTypes []edt_rule.EdtType
	)

	// 处理异常
	if len(results) == 0 {
		return edtTypes
	}

	// 聚合rule的formula规则
	edtFormulaComposes := generateEdtFormulaCompose(results)
	for _, compose := range edtFormulaComposes {
		edtType := generateEdtType(compose.EdtFormulas)
		edtTypes = append(edtTypes, edtType)
	}
	return edtTypes
}

// 转化成返回的edt type类型
func generateEdtType(results []edt_rule2.EDTFormulaTab) edt_rule.EdtType {
	var minExist, maxExist, generalExist bool
	for _, result := range results {
		switch result.EdtType {
		case edt_rule2.EdtMinType:
			minExist = true
			continue
		case edt_rule2.EdtMaxType:
			maxExist = true
			continue
		case edt_rule2.EdtMinAndEdtMax:
			generalExist = true
			continue
		default:
			continue
		}
	}
	if generalExist {
		return edt_rule.EdtMinAndMax
	}

	if minExist && maxExist {
		return edt_rule.EdtMinPlusMax
	}

	if minExist {
		return edt_rule.EdtMin
	}

	return edt_rule.EdtMax
}

// 将策略转化成multi formula标识位
func getEnableMultiFormulaFromMultiFormulaStrategy(strategy uint8) bool {
	return strategy > 0
}

func (i *EdtRuleServiceImpl) GetListSpecialChannelFormulaResponse(c utils.LCOSContext, results []edt_rule2.EDTFormulaTab, respList []edt_rule.ListEdtRuleData) ([]edt_rule.ListEdtRuleData, *lcos_error.LCOSError) {
	var (
		respDataList []edt_rule.ListEdtRuleData
	)
	resultMap := make(map[uint64]edt_rule2.EDTFormulaTab)
	if len(results) == 0 {
		return respList, nil
	}
	for _, result := range results {
		resultMap[result.RuleId] = result
	}

	// 返回special channel
	for _, resp := range respList {
		edtFormula, ok := resultMap[resp.RuleId]
		if !ok {
			logger.CtxLogErrorf(c, "edtFormula not found, ruleId: %d", resp.RuleId)
			return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "edt formula not found")
		}
		resp.SpecialChannel = &edtFormula.MatchValue
		respDataList = append(respDataList, resp)
	}

	return respDataList, nil
}

func (i *EdtRuleServiceImpl) ListABTestFormula(c utils.LCOSContext, request *edt_rule.ListEdtRuleRequest, pageNo uint32, count uint32, formulaCondition map[string]interface{}) (*edt_rule.ListEdtRuleResponse, *lcos_error.LCOSError) {
	// 1 检索abtest数据
	abtestCondition := make(map[string]interface{})
	if request.Status != nil {
		abtestCondition["enable_status"] = request.Status
	}
	abtestCondition["region"] = request.Region

	// 2 检索ab数据
	abTestResult, total, err := i.EdtRuleRepo.SearchAbtestEdtRule(c, pageNo, count, abtestCondition, request.ProductId)
	if err != nil {
		logger.CtxLogErrorf(c, "search edt rule err:%v", err)
		return nil, err
	}

	// 3.检索rule数据
	var (
		matchValueList []uint64
		abTestGroupMap = make(map[uint64]edt_rule2.EDTABTestGroupTab)
	)
	for _, tab := range abTestResult {
		matchValueList = append(matchValueList, tab.Id)
		abTestGroupMap[tab.Id] = tab
	}
	formulaCondition["match_value in"] = matchValueList
	results, lErr := i.EdtRuleRepo.ListEdtRule(c, formulaCondition)
	if lErr != nil {
		logger.CtxLogErrorf(c, "list edt rule fail,err:%v,condition:%v", lErr, formulaCondition)
		return nil, lErr
	}

	var (
		ruleMap = make(map[uint64][]edt_rule2.EDTFormulaTab)
	)

	for _, result := range results {
		formulaList, ok := ruleMap[result.MatchValue]
		if !ok {
			formulaList = []edt_rule2.EDTFormulaTab{}
		}
		formulaList = append(formulaList, result)
		ruleMap[result.MatchValue] = formulaList
	}

	// 3 组装返回结果
	respList, cErr := convertFormulaListResponseForAbtestRule(c, abTestResult, ruleMap)
	if cErr != nil {
		return nil, cErr
	}

	return &edt_rule.ListEdtRuleResponse{
		Total:  total,
		PageNo: pageNo,
		Count:  count,
		List:   respList,
	}, nil
}

func (i *EdtRuleServiceImpl) GetListABTestFormulaResponse(c utils.LCOSContext, results []edt_rule2.EDTFormulaTab, respList []edt_rule.ListEdtRuleData) ([]edt_rule.ListEdtRuleData, *lcos_error.LCOSError) {
	var (
		respDataList      []edt_rule.ListEdtRuleData
		abTestGroupIdList []uint64
	)
	resultMap := make(map[uint64]edt_rule2.EDTFormulaTab)
	abTestGroupMap := make(map[uint64]edt_rule2.EDTABTestGroupTab)

	if len(results) == 0 {
		return respList, nil
	}
	for _, result := range results {
		resultMap[result.RuleId] = result
		abTestGroupIdList = append(abTestGroupIdList, result.MatchValue)
	}

	// 获取ab test 规则
	condition := map[string]interface{}{
		"id in":    abTestGroupIdList,
		"region =": results[0].Region,
	}
	abTestList, err := i.EdtRuleRepo.ListAbTestGroup(c, condition)
	if err != nil {
		logger.CtxLogErrorf(c, "ListAbTestGroup err: %v", err)
		return respDataList, err
	}
	for _, abTestGroup := range abTestList {
		abTestGroupMap[abTestGroup.Id] = abTestGroup
	}

	for _, resp := range respList {
		edtFormula, ok := resultMap[resp.RuleId]
		if !ok {
			logger.CtxLogErrorf(c, "edtFormula not found, ruleId: %d", resp.RuleId)
			return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "edt formula not found")
		}
		abTestGroup, ok := abTestGroupMap[edtFormula.MatchValue]
		if !ok {
			logger.CtxLogErrorf(c, "abTestGroup not found, ruleId: %d, ab test rule:%d", resp.RuleId, edtFormula.MatchValue)
			return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "ab test group not found")
		}
		resp.ABChannelList = abTestGroup.ProductIDList
		resp.BuyerIdGroupList = abTestGroup.BuyerIdGroupList
		respDataList = append(respDataList, resp)
	}

	return respDataList, nil
}

func getCutoffTimeSeconds(cutoffTime string, lineNum int) (int64, string) {
	// 拆分，获取Hour，minute
	strList := strings.Split(cutoffTime, ":")
	if len(strList) != 2 {
		return 0, fmt.Sprintf("[%dth Row]  Formatting error in 'Cutoff Time':%s", lineNum, cutoffTime)
	}
	hour, err := strconv.ParseInt(strList[0], 10, 64)
	if err != nil {
		return 0, fmt.Sprintf("[%dth Row]  Formatting error in 'Cutoff Time':%s", lineNum, cutoffTime)
	}
	if !(hour >= 0 && hour <= 23) {
		return 0, fmt.Sprintf("[%dth Row]  Formatting error in 'Cutoff Time':%s", lineNum, cutoffTime)
	}
	minute, err := strconv.ParseInt(strList[1], 10, 64)
	if err != nil {
		return 0, fmt.Sprintf("[%dth Row]  Formatting error in 'Cutoff Time':%s", lineNum, cutoffTime)
	}
	if !(minute >= 0 && minute <= 59) {
		return 0, fmt.Sprintf("[%dth Row]  Formatting error in 'Cutoff Time':%s", lineNum, cutoffTime)
	}

	// 转换成秒数
	return hour*60*60 + minute*60, ""
}

// ConvertFormulaDetailResponse 将多个公式聚合到一个返回给前端展示，这里只处理edt min&min和edt min+edt max处理，其他结果为异常
func ConvertFormulaDetailResponse(c utils.LCOSContext, results []edt_rule2.EDTFormulaTab) (*edt_rule.GetRuleDetailResponse, *lcos_error.LCOSError) {
	var (
		formulaList []edt_rule.EdtFormula
		response    *edt_rule.GetRuleDetailResponse
	)
	// 处理异常
	if len(results) == 0 {
		logger.CtxLogErrorf(c, "edt formula length is 0, results: %v", results)
		return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "edt formula data anomaly")
	}

	//  将数据按priority 聚合
	edtFormulaComposes := generateEdtFormulaCompose(results)
	// 处理诚返回的formula
	for _, compose := range edtFormulaComposes {
		formula, gErr := genFormulaComposeToEdtFormula(c, compose.EdtFormulas)
		if gErr != nil {
			return nil, gErr
		}
		formulaList = append(formulaList, formula)
	}

	response = &edt_rule.GetRuleDetailResponse{
		EdtFormulaList:           formulaList,
		MatchType:                results[0].MatchType,
		EffectiveStartTime:       results[0].EffectiveStartTime,
		Operator:                 results[0].Operator,
		EnableMultiFormula:       getEnableMultiFormulaFromMultiFormulaStrategy(results[0].MultiFormulaStrategy),
		MultiFormulaPriorityType: edt_rule.MultiFormulaStrategyType(results[0].MultiFormulaStrategy),
	}

	return response, nil
}

func generateEdtFormulaCompose(results []edt_rule2.EDTFormulaTab) []EdtFormulaCompose {
	// 处理多公式，组装多公式的值，返回具有优先级的列表
	var (
		formulaConfigMap      = make(map[string][]edt_rule2.EDTFormulaTab)
		edtFormulaComposeList []EdtFormulaCompose
		keyList               []string // map是无序的，切片用来保持返回结果有序
	)
	for _, result := range results {
		key := result.GenEDTFormulaUniqueKey()
		tempList, ok := formulaConfigMap[key]
		if !ok {
			keyList = append(keyList, key)
			tempList = []edt_rule2.EDTFormulaTab{}
		}
		tempList = append(tempList, result)
		formulaConfigMap[key] = tempList
	}

	// 将map转成list
	for _, k := range keyList {
		v, _ := formulaConfigMap[k]
		edtFormulaComposeList = append(edtFormulaComposeList, EdtFormulaCompose{
			EdtFormulas:          v,
			Priority:             v[0].Priority,
			MultiFormulaStrategy: v[0].Priority,
		})
	}

	return edtFormulaComposeList
}

func genFormulaComposeToEdtFormula(c utils.LCOSContext, results []edt_rule2.EDTFormulaTab) (edt_rule.EdtFormula, *lcos_error.LCOSError) {
	var minExist, maxExist, generalExist bool
	for _, result := range results {
		if result.EdtType == edt_rule2.EdtMinType {
			minExist = true
		} else if result.EdtType == edt_rule2.EdtMaxType {
			maxExist = true
		} else {
			generalExist = true
		}
	}
	// edt min + edt max 或者edt min&max只能选一个
	if (minExist || maxExist) && generalExist {
		logger.CtxLogErrorf(c, "edt formula data anomaly,edt min + edt max data and edt min&max concurrent exist, results: %v", results)
		return edt_rule.EdtFormula{}, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "edt formula data anomaly,edt min + edt max data and edt min&max concurrent exist ")
	}

	// 处理edt min + edt min情况 异常，排除edt min或者edt max type数据重复情况
	if len(results) > 1 && (!minExist || !maxExist) && !generalExist {
		logger.CtxLogErrorf(c, "edt formula data anomaly,should contain edt min and edt max data, results: %v", results)
		return edt_rule.EdtFormula{}, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "edt formula data anomaly,edt min or edt max data is empty")
	}

	return transferToEdtFormula(results), nil
}

func transferToEdtFormula(edtFormulaList []edt_rule2.EDTFormulaTab) edt_rule.EdtFormula {
	var edtFormula edt_rule.EdtFormula
	if len(edtFormulaList) == 0 {
		return edtFormula
	}
	edtFormulaMap := make(map[edt_rule2.EdtFormulaType]*edt_rule.Formula)
	for _, formula := range edtFormulaList {
		edtFormulaMap[formula.EdtType] = transferToFormula(formula)
		if formula.EnableShopRouteEdt > 0 {
			edtFormula.SellerRouteEdt = edt_rule.RouteType(formula.EnableShopRouteEdt)
		}
		if len(formula.AvailableFChannelList) > 0 {
			edtFormula.VerifyAvailableChannelList = formula.AvailableFChannelList
			edtFormula.IsVerifyAvailableChannel = true
		}
	}
	edtFormula.Priority = int(edtFormulaList[0].Priority)
	for formulaType, formula := range edtFormulaMap {
		switch formulaType {
		case edt_rule2.EdtMinType:
			edtFormula.MinEdtFormula = formula
			edtFormula.EdtType = edt_rule.EdtMinPlusMax
		case edt_rule2.EdtMaxType:
			edtFormula.MaxEdtFormula = formula
			edtFormula.EdtType = edt_rule.EdtMinPlusMax
		default:
			edtFormula.GeneralEdtFormula = formula
			edtFormula.EdtType = edt_rule.EdtMinAndMax
		}
	}

	return edtFormula
}

func transferToFormula(result edt_rule2.EDTFormulaTab) *edt_rule.Formula {
	fixEdt := ConvertNewFixedEdt(result.Formula.NewFixedEDT)
	return &edt_rule.Formula{
		Apt:              result.Formula.APT,
		AptExtension:     ConvertAPTNonWorkingDay(result.Formula.APTNonWorkingDay),
		Cdt:              result.Formula.CDTMin,
		CdtExtension:     result.Formula.CDTMinNonWorkingDay,
		FixedEdt:         fixEdt,
		SBD:              result.Formula.SBD,
		EDTNWDAdjustment: result.Formula.EDTNWDAdjustment,
		FixedShopEDT:     result.Formula.FixedShopEDT,
		AlgoEdt:          convertAlgoEdt(result.Formula.AlgoControl),
	}
}

func (i *EdtRuleServiceImpl) GetDetailSpecialChannelFormulaResponse(c utils.LCOSContext, results []edt_rule2.EDTFormulaTab, resp *edt_rule.GetRuleDetailResponse) (*edt_rule.GetRuleDetailResponse, *lcos_error.LCOSError) {
	if len(results) == 0 || resp == nil {
		return resp, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "edt formula is empty")
	}
	specialChannel := results[0].MatchValue
	resp.SpecialChannel = specialChannel

	return resp, nil
}

func (i *EdtRuleServiceImpl) GetDetailABTestFormulaResponse(c utils.LCOSContext, ruleResult edt_rule2.EDTFormulaTab, results []edt_rule2.EDTFormulaTab, resp *edt_rule.GetRuleDetailResponse) (*edt_rule.GetRuleDetailResponse, *lcos_error.LCOSError) {
	var (
		abChannelList     edt_rule2.ProductListType
		buyerIdGroupList  edt_rule2.BuyerIdGroupList
		abTestGroupIdList []uint64
	)
	abTestGroupMap := make(map[string][]edt_rule2.EDTABTestGroupTab)

	if len(results) == 0 {
		return resp, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "edt formula is empty")
	}
	for _, result := range results {
		abTestGroupIdList = append(abTestGroupIdList, result.MatchValue)
	}

	// 获取ab test 规则，是为了兼容老数据
	condition := map[string]interface{}{
		"id in":    abTestGroupIdList,
		"region =": results[0].Region,
	}
	abTestList, err := i.EdtRuleRepo.ListAbTestGroup(c, condition)
	if err != nil {
		logger.CtxLogErrorf(c, "ListAbTestGroup err: %v", err)
		return nil, err
	}
	for _, abTestGroup := range abTestList {
		uniqueBuyerListKey := genUniqueProductListKey(abTestGroup.ProductIDList)
		abTestGroupList, ok := abTestGroupMap[uniqueBuyerListKey]
		if !ok {
			abTestGroupList = []edt_rule2.EDTABTestGroupTab{}
		}
		abTestGroupList = append(abTestGroupList, abTestGroup)
		abTestGroupMap[uniqueBuyerListKey] = abTestGroupList
	}

	// 根据rule id获取最初的rule规则对应的ab规则
	condition = map[string]interface{}{
		"id =":     ruleResult.MatchValue,
		"region =": results[0].Region,
	}
	abTestGroup, err := i.EdtRuleRepo.ListAbTestGroup(c, condition)
	if err != nil || len(abTestGroup) == 0 {
		logger.CtxLogErrorf(c, "get abTestGroup fail, ListAbTestGroup err: %v,abTestGroup:%v", err, abTestGroup)
		return nil, err
	}
	uniqueKey := genUniqueProductListKey(abTestGroup[0].ProductIDList)
	abChannelList = abTestGroup[0].ProductIDList

	abTestGroupList, ok := abTestGroupMap[uniqueKey]
	if !ok {
		logger.CtxLogErrorf(c, "get abTestGroup fail, abTestGroupList is nil,uniqueKey:%v,abTestGroupMap:%+v", uniqueKey, abTestGroupMap)
		return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "abTestGroup list is nil")
	}
	// 获取最终的ab test参数字段
	for _, abTestGroupData := range abTestGroupList {
		if abTestGroupData.BuyerIdGroupList != nil {
			buyerIdGroupList = append(buyerIdGroupList, abTestGroupData.BuyerIdGroupList...)
		} else {
			buyerIdGroupList = append(buyerIdGroupList, edt_rule2.BuyerIdGroup{
				BuyerIdMax: abTestGroupData.BuyerIDMax,
				BuyerIdMin: abTestGroupData.BuyerIDMin,
			})
		}
	}

	resp.BuyerIdGroupList = buyerIdGroupList
	resp.AbChannelList = abChannelList

	return resp, nil
}

func genUniqueProductListKey(productList edt_rule2.ProductListType) string {
	var key string
	for i, product := range productList {
		if i != 0 {
			key += "+"
		}
		key += fmt.Sprintf("%d", product)
	}
	return key
}

func ConvertAPTNonWorkingDay(d *edt_rule2.APTNonWorkingDay) *edt_rule2.NewAPTNonWorkingDay {
	if d == nil {
		return nil
	}
	var (
		cb, local *edt_rule2.NewSingleAPTNonWorkingDay
	)
	if d.CB != nil {
		cb = &edt_rule2.NewSingleAPTNonWorkingDay{
			TPLPickupWindowHolidayAndWeekend: d.CB.TPLPickupWindowHolidayAndWeekend,
			SellerHolidayAndWeekend:          d.CB.SellerHolidayAndWeekend,
			EstimateSellerWeekend:            d.CB.EstimateSellerWeekend,
		}
		if d.CB.LPSChannelHolidayAndWeekend != nil {
			cb.NewLPSChannelHolidayAndWeekend = d.CB.LPSChannelHolidayAndWeekend.CBSpecial
		}
	}
	if d.Local != nil {
		local = &edt_rule2.NewSingleAPTNonWorkingDay{
			TPLPickupWindowHolidayAndWeekend: d.Local.TPLPickupWindowHolidayAndWeekend,
			SellerHolidayAndWeekend:          d.Local.SellerHolidayAndWeekend,
			EstimateSellerWeekend:            d.Local.EstimateSellerWeekend,
		}
		if d.Local.LPSChannelHolidayAndWeekend != nil {
			local.NewLPSChannelHolidayAndWeekend = d.Local.LPSChannelHolidayAndWeekend.CBSpecial
		}
	}
	return &edt_rule2.NewAPTNonWorkingDay{
		CB:    cb,
		Local: local,
	}
}

// 将新的fixed edt结构转换为旧的fixed edt兼容结构
func ConvertNewFixedEdt(d *edt_rule2.NewFixedEDT) *edt_rule2.NewFixedEDT {
	if d == nil {
		return nil
	}
	fixEdt := &edt_rule2.NewFixedEDT{
		AptFixedEdt:                  d.AptFixedEdt,
		CdtFixedEdt:                  d.CdtFixedEdt,
		SLAType:                      d.SLAType,
		SamedayCutoffTime:            d.SamedayCutoffTime,
		ExtendHoursAfterCutoffTime:   d.ExtendHoursAfterCutoffTime,
		CutoffTime:                   d.CutoffTime,
		EnableSLAMode:                d.EnableSLAMode,
		DefaultInstantOperatingHours: d.DefaultInstantOperatingHours,
	}
	if fixEdt.SLAType == edt_rule2.SLATypeDay {
		fixEdt.ExtendHoursAfterCutoffTime = int(fixEdt.CutoffTime)
		fixEdt.CutoffTime = 0
	}

	return fixEdt
}

func convertAlgoEdt(a *edt_rule2.AlgoControl) *edt_rule2.AlgoEdt {
	if a != nil {
		return &edt_rule2.AlgoEdt{
			AlgoEdtEnable: a.AlgoEdtEnable,
		}
	}
	return nil
}

func (i *EdtRuleServiceImpl) specialChannelFormulaDisable(c utils.LCOSContext, formulaCondition map[string]interface{}) *lcos_error.LCOSError {
	// 1 检索数据
	//  检索出与该rule相关的所有数据
	results, err := i.EdtRuleRepo.ListEdtRule(c, formulaCondition)
	if err != nil {
		logger.CtxLogErrorf(c, "search edt rule err:%v", err)
		return err
	}
	if len(results) == 0 {
		return lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "edt formula is empty")
	}
	if results[0].StatusID != edt_rule2.StatusActive && results[0].StatusID != edt_rule2.StatusUpcoming {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "edt formula is not enable, no need disable")
	}

	// 2.按id让数据失效
	for _, result := range results {
		if eErr := i.EdtRuleRepo.UpdateEdtRuleByCondition(c, map[string]interface{}{
			"id": result.Id,
		}, map[string]interface{}{
			"status_id": edt_rule2.StatusInActive,
		}); eErr != nil {
			logger.CtxLogErrorf(c, "update active rule to inactive, err:%v", eErr)
			return eErr
		}
	}

	return nil
}

func (i *EdtRuleServiceImpl) abTestFormulaDisable(c utils.LCOSContext, formulaCondition map[string]interface{}) *lcos_error.LCOSError {
	// 1 检索数据
	//  检索出与该rule相关的所有数据
	results, err := i.EdtRuleRepo.ListEdtRule(c, formulaCondition)
	if err != nil {
		logger.CtxLogErrorf(c, "search edt rule err:%v", err)
		return err
	}
	if len(results) == 0 {
		return lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, "edt formula is empty")
	}

	// 按id让数据失效
	err = c.Transaction(func() *lcos_error.LCOSError {
		var needExpiredAbIds []uint64
		needExpiredAbIdMap := make(map[uint64]struct{})
		for _, result := range results {
			if eErr := i.EdtRuleRepo.UpdateEdtRuleByCondition(c, map[string]interface{}{
				"id": result.Id,
			}, map[string]interface{}{
				"status_id": edt_rule2.StatusInActive,
			}); eErr != nil {
				logger.CtxLogErrorf(c, "update active rule to inactive, err:%v", eErr)
				return eErr
			}
			needExpiredAbIdMap[result.MatchValue] = struct{}{}
		}

		for id := range needExpiredAbIdMap {
			needExpiredAbIds = append(needExpiredAbIds, id)
		}

		// 7. 令need expired ab test转换状态
		if len(needExpiredAbIds) != 0 {
			if err := i.EdtRuleRepo.UpdateAbTestGroupByCondition(c, map[string]interface{}{
				"id in": needExpiredAbIds,
			}, map[string]interface{}{
				"enable_status": edt_rule2.StatusInActive,
			}); err != nil {
				logger.CtxLogErrorf(c, "update abtest rule active rule to inactive, err:%v", err)
				return err
			}
		}
		return nil
	})
	if err != nil {
		logger.CtxLogErrorf(c, "diable formulas err:%v", err)
		return err
	}

	return nil
}

func (i *EdtRuleServiceImpl) UploadRouteFixedEdt(c utils.LCOSContext, request *edt_rule.UploadRouteFixedEdtReq, region, operator string) *lcos_error.LCOSError {
	// 解析Excel
	filePath, err := serviceable_util.DownloadFileFromS3(c, request.FileUrl)
	if err != nil {
		return err
	}
	defer func(name string) {
		_ = os.Remove(name)
	}(filePath)

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(c)
	defer func() {
		logger.CtxLogInfof(c, "Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}

	sheet1 := file.WorkBook.Sheets.Sheet[0].Name
	rows, rowErr := file.Rows(sheet1)
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}

	// 定义一个左右 range 的数组
	var (
		createOrUpdateList []edt_rule2.LogisticRouteFixedGroupEdtTab
	)
	deleteMap := make(map[int]edt_rule2.LogisticRouteFixedGroupEdtTab)
	// 记录唯一键，防止文件出现重复数据
	keyMap := make(map[string]struct{})
	nowTime := utils.GetCurrentLocalTimeStampByRegion(c, region)

	// 获取Client group
	resp, gErr := product_service.GetAllShopGroupsByTag(c, region, product_service.ClientTagFixedEdt)
	if gErr != nil {
		logger.CtxLogErrorf(c, "get shop groups err:%v\n", gErr)
		return gErr
	}
	shopGroupMap := product_service.ConvertToShopGroupMap(resp)

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}

		// skip header
		if lineNum == 1 {
			continue
		}

		// 跳过表头和空白行
		if serviceable_util.IsBlankRow(row) {
			continue
		}
		// 需要
		if lineNum > config.GetUploadRouteMaxUploadSize(c) {
			return lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, fmt.Sprintf("line num is %d the size of upload file should be not more than %d lines", lineNum, config.GetMutableConf(c).RouteFixedEdtMaxUploadSize))
		}
		routeModels, isDelete, errMsg := transferRowToRouteFixedEdtStruct(c, lineNum, row, region, nowTime, operator, shopGroupMap)
		if len(errMsg) > 0 {
			return lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, fmt.Sprintf("row=%d|error=[%s]", lineNum, errMsg))
		}

		// 是否存在重复数据
		key := getRouteFixedGroupUniqueKey(routeModels)
		_, ok := keyMap[key]
		if ok {
			logger.CtxLogErrorf(c, "row=%d|error=[%s]|data=%v", lineNum, "data is duplicated", routeModels)
			return lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, fmt.Sprintf("row=%d|error=[%s]", lineNum, "data is duplicated"))
		} else {
			keyMap[key] = struct{}{}
		}

		// 处理需要删除的数据
		if isDelete {
			// 删除
			deleteMap[lineNum] = routeModels
		} else {
			// 更新或插入
			createOrUpdateList = append(createOrUpdateList, routeModels)
		}
	}

	err = c.Transaction(func() *lcos_error.LCOSError {
		var tErr *lcos_error.LCOSError
		// Batch create
		if len(createOrUpdateList) != 0 {
			tErr = i.EdtRuleRepo.BatchCreateOrUpdateRouteFixedEdt(c, createOrUpdateList)
			if tErr != nil {
				return tErr
			}
		}
		if len(deleteMap) != 0 {
			tErr = i.EdtRuleRepo.DeleteRouteFixedEdtAndCheckAffected(c, deleteMap)
			if tErr != nil {
				return tErr
			}
		}
		return nil
	})
	if err != nil {
		logger.CtxLogErrorf(c, "batch create or delete err:%v", err)
		return err
	}

	return nil
}

func (i *EdtRuleServiceImpl) ListRouteFixedEdt(c utils.LCOSContext, request *edt_rule.ListRouteFixedEdtReq, region string, page, size uint32) ([]edt_rule2.LogisticRouteFixedGroupEdtTab, uint32, *lcos_error.LCOSError) {
	condition := make(map[string]interface{})
	if request.ProductID != 0 {
		condition["product_id ="] = request.ProductID
	}
	if request.ClientGroupID != "" {
		condition["client_group_id ="] = request.ClientGroupID
	}
	condition["region ="] = region
	condition["status_id ="] = edt_rule2.StatusActive
	if request.SenderRegion != "" {
		condition["sender_region"] = request.SenderRegion
	}
	if request.ReceiverRegion != "" {
		condition["receiver_region"] = request.ReceiverRegion
	}

	if request.SenderState != 0 {
		condition["sender_state_id"] = request.SenderState
	}
	if request.ReceiverState != 0 {
		condition["receiver_state_id"] = request.ReceiverState
	}

	if request.SenderState != 0 {
		condition["sender_state_id"] = request.SenderState
	}
	if request.ReceiverState != 0 {
		condition["receiver_state_id"] = request.ReceiverState
	}

	if request.SenderCity != 0 {
		condition["sender_city_id"] = request.SenderCity
	}
	if request.ReceiverCity != 0 {
		condition["receiver_city_id"] = request.ReceiverCity
	}

	if request.SenderDistrict != 0 {
		condition["sender_district_id"] = request.SenderDistrict
	}
	if request.ReceiverDistrict != 0 {
		condition["receiver_district_id"] = request.ReceiverDistrict
	}

	if request.SenderStreet != 0 {
		condition["sender_street_id"] = request.SenderStreet
	}
	if request.ReceiverStreet != 0 {
		condition["receiver_street_id"] = request.ReceiverStreet
	}

	if request.Operator != "" {
		condition["operator ="] = request.Operator
	}

	tabs, total, err := i.EdtRuleRepo.ListRouteFixedEdt(c, page, size, condition)
	if err != nil {
		logger.CtxLogErrorf(c, "list rout fix edt by condition:%v, err:%v", condition, err)
		return nil, 0, err
	}
	return tabs, total, nil
}

func (i *EdtRuleServiceImpl) SearchRouteFixedEdt(c utils.LCOSContext, request *edt_rule.ListRouteFixedEdtReq, region string) ([]edt_rule2.LogisticRouteFixedGroupEdtTab, *lcos_error.LCOSError) {
	condition := make(map[string]interface{})
	if request.ProductID != 0 {
		condition["product_id ="] = request.ProductID
	}
	if request.ClientGroupID != "" {
		condition["client_group_id ="] = request.ClientGroupID
	}
	condition["region ="] = region
	condition["status_id ="] = edt_rule2.StatusActive
	if request.SenderRegion != "" {
		condition["sender_region"] = request.SenderRegion
	}
	if request.ReceiverRegion != "" {
		condition["receiver_region"] = request.ReceiverRegion
	}

	if request.SenderState != 0 {
		condition["sender_state_id"] = request.SenderState
	}
	if request.SenderState != 0 {
		condition["receiver_state_id"] = request.ReceiverState
	}

	if request.SenderState != 0 {
		condition["sender_state_id"] = request.SenderState
	}
	if request.ReceiverState != 0 {
		condition["receiver_state_id"] = request.ReceiverState
	}

	if request.SenderCity != 0 {
		condition["sender_city_id"] = request.SenderCity
	}
	if request.ReceiverCity != 0 {
		condition["receiver_city_id"] = request.ReceiverCity
	}

	if request.SenderDistrict != 0 {
		condition["sender_district_id"] = request.SenderDistrict
	}
	if request.ReceiverDistrict != 0 {
		condition["receiver_district_id"] = request.ReceiverDistrict
	}

	if request.SenderStreet != 0 {
		condition["sender_street_id"] = request.SenderStreet
	}
	if request.ReceiverStreet != 0 {
		condition["receiver_street_id"] = request.ReceiverStreet
	}
	if request.Operator != "" {
		condition["operator ="] = request.Operator
	}

	tabs, err := i.EdtRuleRepo.SearchAllRouteFixedEdt(c, condition)
	if err != nil {
		logger.CtxLogErrorf(c, "list rout fix edt by condition:%v, err:%v", condition, err)
		return nil, err
	}
	return tabs, nil
}

func transferRowToRouteFixedEdtStruct(ctx utils.LCOSContext, lineNum int, row []string, region string, nowTime int64, operator string, shopGroupMap map[string]struct{}) (edt_rule2.LogisticRouteFixedGroupEdtTab, bool, string) {
	// row: Product ID、Client Group ID、Sender Region、Sender State、Sender City、Sender District、Sender Street、
	// Receiver Region 、Receiver State、Receiver City、Receiver District、Receiver Street、Fixed APT 、Fixed CDT(hours)、Action Code
	var isDelete bool
	if len(row) < 16 {
		return edt_rule2.LogisticRouteFixedGroupEdtTab{}, isDelete, fmt.Sprintf("[%dth Row] Missing mandatory date, data row is less than 16 ", lineNum)
	}
	// 解析row
	rowStruct := fillStructWithRowData(row)

	// 基础数据校验
	rowStruct.IsValid(region)

	addRouteFixedEdtInfoForUploadExcelData(lineNum, rowStruct, region, nowTime, operator, shopGroupMap)
	addLocationInfo(ctx, rowStruct)
	if len(rowStruct.ErrorMsg) > 0 {
		return edt_rule2.LogisticRouteFixedGroupEdtTab{}, isDelete, rowStruct.ErrorMsg
	}
	if rowStruct.CleanData == nil {
		return edt_rule2.LogisticRouteFixedGroupEdtTab{}, isDelete, fmt.Sprintf("[%dth Row] Missing mandatory date, get data for fb fail ", lineNum)
	}
	if rowStruct.ActionCode == edt_rule2.ActionDelete {
		isDelete = true
	}
	return rowStruct.GetCleanData(), isDelete, ""
}

func addRouteFixedEdtInfoForUploadExcelData(lineNum int, rawUploadedData *RawUploadRouteFixedGroupEdtData, region string, nowTime int64, operator string, shopGroupMap map[string]struct{}) {
	if len(rawUploadedData.ErrorMsg) == 0 {
		productId, err := strconv.Atoi(strings.Trim(rawUploadedData.ProductId, " "))
		if err != nil {
			rawUploadedData.ErrorMsg = fmt.Sprintf("[%dth Row] Invalid product id:%v", lineNum, rawUploadedData.ProductId)
			return
		}
		// 支持client group 非必传
		clientGroupId := strings.Trim(rawUploadedData.ClientGroupId, "")
		if _, ok := shopGroupMap[product_service.FormatShopGroupKey(clientGroupId, strconv.Itoa(productId))]; !ok && clientGroupId != "" {
			rawUploadedData.ErrorMsg = fmt.Sprintf("[%dth Row] Invalid client shop group id:%v", lineNum, rawUploadedData.ClientGroupId)
			return
		}
		fixApt, fErr := strconv.Atoi(strings.Trim(rawUploadedData.FixApt, " "))
		if fErr != nil {
			rawUploadedData.ErrorMsg = fmt.Sprintf("“Fixed EDT” can only be configured with positive integers,[%dth Row] Invalid fix apt:%v", lineNum, rawUploadedData.FixApt)
			return
		}
		fixCdt, fErr := strconv.Atoi(strings.Trim(rawUploadedData.FixCdt, " "))
		if fErr != nil {
			rawUploadedData.ErrorMsg = fmt.Sprintf("“Fixed CDT” can only be configured with positive integers,[%dth Row] Invalid fix apt:%v", lineNum, rawUploadedData.FixCdt)
			return
		}
		// cutoff time
		cutoffTime, errMsg := getCutoffTimeSeconds(strings.Trim(rawUploadedData.CutoffTime, " "), lineNum)
		if errMsg != "" {
			rawUploadedData.ErrorMsg = errMsg
			return
		}

		rawUploadedData.CleanData = &edt_rule2.LogisticRouteFixedGroupEdtTab{
			Region:             region,
			ProductId:          productId,
			ClientGroupId:      clientGroupId, // client shop group id
			FixApt:             int64(fixApt),
			FixCdt:             int64(fixCdt),
			CutoffTime:         cutoffTime,
			SenderRegionId:     0,
			SenderStateId:      0,
			SenderCityId:       0,
			SenderDistrictId:   0,
			SenderStreetId:     0,
			SenderLocationID:   0,
			ReceiverRegionId:   0,
			ReceiverStateId:    0,
			ReceiverCityId:     0,
			ReceiverDistrictId: 0,
			ReceiverStreetId:   0,
			ReceiverLocationID: 0,
			SenderRegion:       strings.Trim(rawUploadedData.SenderRegion, " "),
			SenderState:        strings.Trim(rawUploadedData.SenderState, " "),    // sender state
			SenderCity:         strings.Trim(rawUploadedData.SenderCity, " "),     // sender city
			SenderDistrict:     strings.Trim(rawUploadedData.SenderDistrict, " "), // sender district
			SenderStreet:       strings.Trim(rawUploadedData.SenderStreet, " "),
			ReceiverRegion:     strings.Trim(rawUploadedData.ReceiverRegion, " "),
			ReceiverState:      strings.Trim(rawUploadedData.ReceiverState, " "),
			ReceiverCity:       strings.Trim(rawUploadedData.ReceiverCity, " "),
			ReceiverDistrict:   strings.Trim(rawUploadedData.ReceiverDistrict, " "),
			ReceiverStreet:     strings.Trim(rawUploadedData.ReceiverStreet, " "),
			StatusId:           edt_rule2.StatusActive,
			Operator:           operator,
			Ctime:              nowTime,
			Mtime:              nowTime,
		}
	}
}

// 增加location id信息
func addLocationInfo(ctx utils.LCOSContext, rawUploadedData *RawUploadRouteFixedGroupEdtData) {
	if len(rawUploadedData.ErrorMsg) == 0 && rawUploadedData.CleanData != nil {
		// sender
		// 支持sender只为region,也就是location id为0
		if rawUploadedData.CleanData.SenderState != "" {
			senderLocationInfo, lcosErr := address_service.LocationServer.GetLocationInfoByName(ctx, rawUploadedData.CleanData.SenderRegion,
				rawUploadedData.CleanData.SenderState, rawUploadedData.CleanData.SenderCity, rawUploadedData.CleanData.SenderDistrict, rawUploadedData.CleanData.SenderStreet)
			if lcosErr != nil {
				errMsg := fmt.Sprintf("cannot get location info,|location=[%s|%s|%s|%s|%s], error=%s", rawUploadedData.CleanData.SenderRegion, rawUploadedData.CleanData.SenderState, rawUploadedData.CleanData.SenderCity, rawUploadedData.CleanData.SenderDistrict, rawUploadedData.CleanData.SenderStreet, lcosErr.Msg)
				logger.CtxLogErrorf(ctx, errMsg)
				rawUploadedData.ErrorMsg = errMsg
				return
			}

			rawUploadedData.CleanData.SenderStateId = uint64(senderLocationInfo.StateLocationId)
			rawUploadedData.CleanData.SenderCityId = uint64(senderLocationInfo.CityLocationId)
			rawUploadedData.CleanData.SenderDistrictId = uint64(senderLocationInfo.DistrictLocationId)
			rawUploadedData.CleanData.SenderStreetId = uint64(senderLocationInfo.StreetLocationId)
			if senderLocationInfo.StateLocationId != 0 {
				rawUploadedData.CleanData.SenderLocationID = uint64(senderLocationInfo.StateLocationId)
			}
			if senderLocationInfo.CityLocationId != 0 {
				rawUploadedData.CleanData.SenderLocationID = uint64(senderLocationInfo.CityLocationId)
			}
			if senderLocationInfo.DistrictLocationId != 0 {
				rawUploadedData.CleanData.SenderLocationID = uint64(senderLocationInfo.DistrictLocationId)
			}
			if senderLocationInfo.StreetLocationId != 0 {
				rawUploadedData.CleanData.SenderLocationID = uint64(senderLocationInfo.StreetLocationId)
			}
		}

		// receiver
		// 支持receiver只为region,也就是location id为0
		if rawUploadedData.CleanData.ReceiverState != "" {
			receiverLocationInfo, lcosErr := address_service.LocationServer.GetLocationInfoByName(ctx, rawUploadedData.CleanData.ReceiverRegion,
				rawUploadedData.CleanData.ReceiverState, rawUploadedData.CleanData.ReceiverCity, rawUploadedData.CleanData.ReceiverDistrict, rawUploadedData.CleanData.ReceiverStreet)
			if lcosErr != nil {
				errMsg := fmt.Sprintf("cannot get location info,|location=[%s|%s|%s|%s|%s], error=%s", rawUploadedData.CleanData.ReceiverRegion, rawUploadedData.CleanData.ReceiverState,
					rawUploadedData.CleanData.ReceiverCity, rawUploadedData.CleanData.ReceiverDistrict, rawUploadedData.CleanData.ReceiverStreet, lcosErr.Msg)
				logger.CtxLogErrorf(ctx, errMsg)
				rawUploadedData.ErrorMsg = errMsg
				return
			}

			rawUploadedData.CleanData.ReceiverStateId = uint64(receiverLocationInfo.StateLocationId)
			rawUploadedData.CleanData.ReceiverCityId = uint64(receiverLocationInfo.CityLocationId)
			rawUploadedData.CleanData.ReceiverDistrictId = uint64(receiverLocationInfo.DistrictLocationId)
			rawUploadedData.CleanData.ReceiverStreetId = uint64(receiverLocationInfo.StreetLocationId)
			if receiverLocationInfo.StateLocationId != 0 {
				rawUploadedData.CleanData.ReceiverLocationID = uint64(receiverLocationInfo.StateLocationId)
			}
			if receiverLocationInfo.CityLocationId != 0 {
				rawUploadedData.CleanData.ReceiverLocationID = uint64(receiverLocationInfo.CityLocationId)
			}
			if receiverLocationInfo.DistrictLocationId != 0 {
				rawUploadedData.CleanData.ReceiverLocationID = uint64(receiverLocationInfo.DistrictLocationId)
			}
			if receiverLocationInfo.StreetLocationId != 0 {
				rawUploadedData.CleanData.ReceiverLocationID = uint64(receiverLocationInfo.StreetLocationId)
			}
		}
	}
}

func getRouteFixedGroupUniqueKey(data edt_rule2.LogisticRouteFixedGroupEdtTab) string {
	return fmt.Sprintf("%d_%s_%d_%d", data.ProductId, data.ClientGroupId, data.SenderLocationID, data.ReceiverLocationID)
}

func (i *EdtRuleServiceImpl) UploadCepRouteFixedEdt(c utils.LCOSContext, request *edt_rule.UploadCepRouteFixedEdtReq, region, operator string) *lcos_error.LCOSError {
	// 解析Excel
	filePath, err := serviceable_util.DownloadFileFromS3(c, request.FileUrl)
	if err != nil {
		return err
	}
	defer func(name string) {
		_ = os.Remove(name)
	}(filePath)

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(c)
	defer func() {
		logger.CtxLogInfof(c, "Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}

	sheet1 := file.WorkBook.Sheets.Sheet[0].Name
	rows, rowErr := file.Rows(sheet1)
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}

	// 1.excel数据解析+数据格式合法性校验
	var (
		addCepGroupList = make([]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, 0)
		// map是为了记录表格行数与数据的对应关系，方便输出删除数据不存在时的报错信息
		deleteCepGroupMap = make(map[int]*edt_rule2.LogisticCepRouteFixedGroupEdtTab)
		nowTime           = utils.GetCurrentLocalTimeStampByRegion(c, region)
	)

	// 获取Client group
	resp, gErr := product_service.GetAllShopGroupsByTag(c, region, product_service.ClientTagFixedEdt)
	if gErr != nil {
		logger.CtxLogErrorf(c, "get shop groups err:%v\n", gErr)
		return gErr
	}
	shopGroupMap := product_service.ConvertToShopGroupMap(resp)

	var (
		beforeRowAction bool
	)

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}

		// skip header
		if lineNum == 1 {
			continue
		}

		// 跳过表头和空白行
		if serviceable_util.IsBlankRow(row) {
			continue
		}
		// 限制上传行数
		if lineNum > config.GetUploadRouteMaxUploadSize(c) {
			return lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, fmt.Sprintf("line num is %d the size of upload file should be not more than %d lines", lineNum, config.GetMutableConf(c).RouteFixedEdtMaxUploadSize))
		}
		routeModels, isDelete, tErr := transferAndValidateCepData(c, lineNum, row, region, nowTime, operator, shopGroupMap)
		if tErr != nil {
			return tErr
		}

		// 一个文件上传只能是add操作或只能是delete操作，不能同时add和delete
		if lineNum != 2 && isDelete != beforeRowAction {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "[%dth Row] %s", lineNum, "action code in the same file should be same")
		}
		beforeRowAction = isDelete

		// 处理需要删除的数据
		if isDelete {
			// 删除
			deleteCepGroupMap[lineNum] = routeModels
		} else {
			// 插入
			addCepGroupList = append(addCepGroupList, routeModels)
		}
	}

	// 3.校验上传数据与DB里已有数据是否有overlap
	// 根据addCepGroupList 中的productId + clientGroup + senderRegion + receiverRegion 查找db中数据
	dbData, aErr := i.EdtRuleRepo.GetAllCepRouteFixedEdt(c, generateUniqueKeySet(addCepGroupList))
	if aErr != nil {
		logger.CtxLogErrorf(c, "get all cep route data err:%v\n", aErr)
		return aErr
	}

	if err := i.validateCepOverlap(c, region, dbData, addCepGroupList); err != nil {
		return err
	}

	err = c.Transaction(func() *lcos_error.LCOSError {
		var tErr *lcos_error.LCOSError
		// Batch create
		if len(addCepGroupList) != 0 {
			tErr = i.EdtRuleRepo.BatchCreateOrUpdateCepRouteFixedEdt(c, addCepGroupList)
			if tErr != nil {
				return tErr
			}
		}
		if len(deleteCepGroupMap) != 0 {
			tErr = i.EdtRuleRepo.BatchDeleteCepRouteFixedEdt(c, deleteCepGroupMap)
			if tErr != nil {
				return tErr
			}
		}
		return nil
	})
	if err != nil {
		logger.CtxLogErrorf(c, "batch create or delete err:%v", err)
		return err
	}

	return nil
}

func generateUniqueKeySet(input []*edt_rule2.LogisticCepRouteFixedGroupEdtTab) []edt_rule2.CepRouteFixedGroupEdtKeyStruct {
	uniqueKeys := make(map[edt_rule2.CepRouteFixedGroupEdtKeyStruct]struct{})

	for _, item := range input {
		key := edt_rule2.CepRouteFixedGroupEdtKeyStruct{
			Region:         item.Region,
			ProductId:      item.ProductId,
			ClientGroupId:  item.ClientGroupId,
			SenderRegion:   item.SenderRegion,
			ReceiverRegion: item.ReceiverRegion,
		}

		uniqueKeys[key] = struct{}{}
	}

	result := make([]edt_rule2.CepRouteFixedGroupEdtKeyStruct, 0, len(uniqueKeys))
	for key := range uniqueKeys {
		result = append(result, key)
	}

	return result
}

func mergeData(dbData []*edt_rule2.LogisticCepRouteFixedGroupEdtTab, excelData []*edt_rule2.LogisticCepRouteFixedGroupEdtTab) []*edt_rule2.LogisticCepRouteFixedGroupEdtTab {
	mergedData := make([]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, 0, len(dbData)+len(excelData))
	uniqueKeys := make(map[string]struct{})

	// 将excelData中的数据添加到mergedData和uniqueKeys中
	for _, item := range excelData {
		key := item.GenerateKey()
		uniqueKeys[key] = struct{}{}
		mergedData = append(mergedData, item)
	}

	// 将dbData中的数据添加到mergedData中，但仅当其对应的key在uniqueKeys中不存在时
	for _, item := range dbData {
		key := item.GenerateKey()
		if _, exists := uniqueKeys[key]; !exists {
			uniqueKeys[key] = struct{}{}
			mergedData = append(mergedData, item)
		}
	}

	return mergedData
}

func (i *EdtRuleServiceImpl) validateCepOverlap(c utils.LCOSContext, region string, dbData []*edt_rule2.LogisticCepRouteFixedGroupEdtTab, excelData []*edt_rule2.LogisticCepRouteFixedGroupEdtTab) *lcos_error.LCOSError {

	if len(excelData) == 0 {
		return nil
	}
	// 若dbData中有key和excelData重复时，只将excelData中数据添加到数组汇总，dbData中数据不添加
	// 原因：兼容edit场景，业务添加数据若与db中重复，认为是需要edit这条数据
	allData := mergeData(dbData, excelData)

	// 生成映射：map[productId+clientGroupId+senderRegion+receiverRegion] []tab
	dataMap := make(map[string][]*edt_rule2.LogisticCepRouteFixedGroupEdtTab)
	for i := range allData {
		data := allData[i]
		key := fmt.Sprintf("%d+%s+%s+%s", data.ProductId, data.ClientGroupId, data.SenderRegion, data.ReceiverRegion)
		dataMap[key] = append(dataMap[key], data)
	}

	// 数据量限制,防止业务上传太多数据
	if config.GetCepFixedEdtConfigEnable(c, region) {
		maxSize := config.GetCepFixedEdtConfigProductClientGroupMaxSize(c, region)
		for _, dataSlice := range dataMap {
			if len(dataSlice) > maxSize {
				return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "The maximum number of cep with the same productId, clientGroupId, "+
					"senderRegion, and receiverRegion is exceeded,limit = %d ,productId = %d, clientGroupId = %s, senderRegion = %s, receiverRegion = %s",
					maxSize, dataSlice[0].ProductId, dataSlice[0].ClientGroupId, dataSlice[0].SenderRegion, dataSlice[0].ReceiverRegion)
			}
		}
	}

	// 在相同的 productId+clientGroupId+senderRegion+receiverRegion 下，检查重叠规则
	for _, dataSlice := range dataMap {

		sellerCepList, buyerCepList := generateCepSliceForBuyerAndSeller(dataSlice)

		// 检查相同的 senderCep 和 receiverCep 是否有重叠规则
		if err := checkCepRangeDuplicates(sellerCepList, checkSeller); err != nil {
			return err
		}

		if err := checkCepRangeDuplicates(buyerCepList, checkBuyer); err != nil {
			return err
		}
	}

	return nil
}

// 分别根据seller overlap 和 buyer overlap的数据分组
func generateCepSliceForBuyerAndSeller(dataList []*edt_rule2.LogisticCepRouteFixedGroupEdtTab) ([][]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, [][]*edt_rule2.LogisticCepRouteFixedGroupEdtTab) {
	if len(dataList) == 0 {
		return nil, nil
	}

	var (
		sellerCepList = make([][]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, 0)
		buyerCepList  = make([][]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, 0)
	)

	// 生成sellerCepList
	for i := 0; i < len(dataList); i++ {
		currentSellerGroup := []*edt_rule2.LogisticCepRouteFixedGroupEdtTab{dataList[i]}
		sellerCepInitial := dataList[i].SenderCepInitial
		sellerCepFinal := dataList[i].SenderCepFinal

		for j := i + 1; j < len(dataList); j++ {
			if ceprange_util.OverlapDirectly(dataList[j].SenderCepInitial, dataList[j].SenderCepFinal, sellerCepInitial, sellerCepFinal) {
				currentSellerGroup = append(currentSellerGroup, dataList[j])
			}
		}

		sellerCepList = append(sellerCepList, currentSellerGroup)
	}

	// 生成buyerCepList
	for i := 0; i < len(dataList); i++ {
		currentBuyerGroup := []*edt_rule2.LogisticCepRouteFixedGroupEdtTab{dataList[i]}
		buyerCepInitial := dataList[i].ReceiverCepInitial
		buyerCepFinal := dataList[i].ReceiverCepFinal

		for j := i + 1; j < len(dataList); j++ {
			if ceprange_util.OverlapDirectly(dataList[j].ReceiverCepInitial, dataList[j].ReceiverCepFinal, buyerCepInitial, buyerCepFinal) {
				currentBuyerGroup = append(currentBuyerGroup, dataList[j])
			}
		}

		buyerCepList = append(buyerCepList, currentBuyerGroup)
	}

	return sellerCepList, buyerCepList
}

type checkType int

var (
	checkSeller checkType = 1
	checkBuyer  checkType = 2
)

func checkCepRangeDuplicates(dataList [][]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, checkType checkType) *lcos_error.LCOSError {
	for i := 0; i < len(dataList); i++ {
		var (
			compareCepData                     = dataList[i][0]
			compareCepInitial, compareCepFinal int
		)

		if checkType == checkSeller {
			compareCepInitial = compareCepData.ReceiverCepInitial
			compareCepFinal = compareCepData.ReceiverCepFinal
		} else {
			//checkType == checkBuyer
			compareCepInitial = compareCepData.SenderCepInitial
			compareCepFinal = compareCepData.SenderCepFinal
		}

		for j := 1; j < len(dataList[i]); j++ {
			var (
				tmpData                        = dataList[i][j]
				checkCepInitial, checkCepFinal int
			)

			if checkType == checkSeller {
				checkCepInitial = tmpData.ReceiverCepInitial
				checkCepFinal = tmpData.ReceiverCepFinal
			} else {
				// checkType == checkBuyer
				checkCepInitial = tmpData.SenderCepInitial
				checkCepFinal = tmpData.SenderCepFinal
			}

			// 两者之间必然有一个是excel中的数据，若rowId=0 ,说明是data中的数据。
			if ceprange_util.OverlapDirectly(compareCepInitial, compareCepFinal, checkCepInitial, checkCepFinal) {
				var (
					compareRowId = compareCepData.RowId
					checkRowId   = tmpData.RowId
				)

				if checkType == checkSeller {
					if compareRowId == 0 {
						return lcos_error.NewLCOSErrorf(lcos_error.CepRangeDuplicate, "[%dth Row] Duplicated Receiver CEP range with DB data,db data:senderInitial:%d,"+
							"senderFinal:%d,receiverInitial:%d,receiverFinal:%d", checkRowId, compareCepData.SenderCepInitial, compareCepData.SenderCepFinal, compareCepData.ReceiverCepInitial, compareCepData.ReceiverCepFinal)
					}
					if checkRowId == 0 {
						return lcos_error.NewLCOSErrorf(lcos_error.CepRangeDuplicate, "[%dth Row] Duplicated Receiver CEP range with DB data,db data:senderInitial:%d,"+
							"senderFinal:%d,receiverInitial:%d,receiverFinal:%d", compareRowId, tmpData.SenderCepInitial, tmpData.SenderCepFinal, tmpData.ReceiverCepInitial, tmpData.ReceiverCepFinal)
					}
					return lcos_error.NewLCOSErrorf(lcos_error.CepRangeDuplicate, "[%dth Row] Duplicated Receiver CEP range with [%dth Row]", compareRowId, checkRowId)
				} else {
					if compareRowId == 0 {
						return lcos_error.NewLCOSErrorf(lcos_error.CepRangeDuplicate, "[%dth Row] Duplicated Sender CEP range with DB data,db data:senderInitial:%d,"+
							"senderFinal:%d,receiverInitial:%d,receiverFinal:%d", checkRowId, compareCepData.SenderCepInitial, compareCepData.SenderCepFinal, compareCepData.ReceiverCepInitial, compareCepData.ReceiverCepFinal)
					}
					if checkRowId == 0 {
						return lcos_error.NewLCOSErrorf(lcos_error.CepRangeDuplicate, "[%dth Row] Duplicated Sender CEP range with DB data,db data:senderInitial:%d,"+
							"senderFinal:%d,receiverInitial:%d,receiverFinal:%d", compareRowId, tmpData.SenderCepInitial, tmpData.SenderCepFinal, tmpData.ReceiverCepInitial, tmpData.ReceiverCepFinal)
					}
					return lcos_error.NewLCOSErrorf(lcos_error.CepRangeDuplicate, "[%dth Row] Duplicated Sender CEP range with [%dth Row]", compareRowId, checkRowId)
				}
			}

		}
	}

	return nil
}

func (i *EdtRuleServiceImpl) ListCepRouteFixedEdt(c utils.LCOSContext, request *edt_rule.ListCepRouteFixedEdtReq, region string, page, size uint32) ([]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, uint32, *lcos_error.LCOSError) {
	condition := make(map[string]interface{})
	condition["region ="] = region
	if request.ProductID != 0 {
		condition["product_id ="] = request.ProductID
	}
	if request.ClientGroupID != "" {
		condition["client_group_id ="] = request.ClientGroupID
	}
	if request.SenderRegion != "" {
		condition["sender_region"] = request.SenderRegion
	}
	if request.ReceiverRegion != "" {
		condition["receiver_region"] = request.ReceiverRegion
	}

	tabs, total, err := i.EdtRuleRepo.ListCepRouteFixedEdt(c, page, size, condition)
	if err != nil {
		logger.CtxLogErrorf(c, "list cep rout fix edt by condition:%v, err:%v", condition, err)
		return nil, 0, err
	}
	return tabs, total, nil
}

func (i *EdtRuleServiceImpl) SearchCepRouteFixedEdt(c utils.LCOSContext, request *edt_rule.ListCepRouteFixedEdtReq, region string) ([]*edt_rule2.LogisticCepRouteFixedGroupEdtTab, *lcos_error.LCOSError) {
	condition := make(map[string]interface{})
	condition["region ="] = region
	if request.ProductID != 0 {
		condition["product_id ="] = request.ProductID
	}
	if request.ClientGroupID != "" {
		condition["client_group_id ="] = request.ClientGroupID
	}
	if request.SenderRegion != "" {
		condition["sender_region"] = request.SenderRegion
	}
	if request.ReceiverRegion != "" {
		condition["receiver_region"] = request.ReceiverRegion
	}

	tabs, err := i.EdtRuleRepo.SearchAllCepRouteFixedEdt(c, condition)
	if err != nil {
		logger.CtxLogErrorf(c, "list rout fix edt by condition:%v, err:%v", condition, err)
		return nil, err
	}
	return tabs, nil
}

// excel数据转换+数据合法性校验（格式的合法性）
func transferAndValidateCepData(ctx utils.LCOSContext, lineNum int, row []string, region string, nowTime int64, operator string,
	shopGroupMap map[string]struct{}) (*edt_rule2.LogisticCepRouteFixedGroupEdtTab, bool, *lcos_error.LCOSError) {
	var isDelete bool
	if len(row) < 12 {
		return nil, false, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "[%dth Row] Missing mandatory date, data row is less than 12", lineNum)
	}

	// 解析row
	rowData := &RawUploadCepRouteFixedGroupEdtData{}
	if err := excel.ParseRowDataWithHeader(lineNum, row, serviceable_util.FixedEdtCepGroupHeader, rowData); err != nil {
		return nil, false, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "[%dth Row] %s", lineNum, err.Msg)
	}

	// Sender cep initial+Sender cep final+Receiver cep initial+Receiver cep final 校验逻辑：要不都不填，要不都填，其余情况认为上传不合理，上传校验卡住
	if !((rowData.SenderCepInitial != 0 && rowData.SenderCepFinal != 0 && rowData.ReceiverCepInitial != 0 && rowData.ReceiverCepFinal != 0) ||
		(rowData.SenderCepInitial == 0 && rowData.SenderCepFinal == 0 && rowData.ReceiverCepInitial == 0 && rowData.ReceiverCepFinal == 0)) {
		return nil, false, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "[%dth Row] %s", lineNum, "cep Either fill in all or none")
	}

	// 校验 cep final 是否 >= cep initial
	if rowData.SenderCepFinal < rowData.SenderCepInitial || rowData.ReceiverCepFinal < rowData.ReceiverCepInitial {
		return nil, false, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "[%dth Row] %s", lineNum, "cep final should >= cep initial")
	}

	// 校验clientGroup是否存在
	if _, ok := shopGroupMap[product_service.FormatShopGroupKey(rowData.ClientGroupId, strconv.Itoa(rowData.ProductId))]; !ok && rowData.ClientGroupId != "" {
		return nil, false, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "[%dth Row] Invalid client shop group id:%s", lineNum, rowData.ClientGroupId)
	}

	if rowData.ActionCode == edt_rule2.ActionDelete {
		isDelete = true
	}
	return &edt_rule2.LogisticCepRouteFixedGroupEdtTab{
		Region:             region,
		RowId:              rowData.RowId,
		ProductId:          rowData.ProductId,
		ClientGroupId:      rowData.ClientGroupId,
		FixApt:             int64(rowData.FixApt),
		FixCdt:             int64(rowData.FixCdt),
		CutoffTime:         int64(rowData.CutoffTime),
		SenderRegion:       rowData.SenderRegion,
		SenderCepInitial:   rowData.SenderCepInitial,
		SenderCepFinal:     rowData.SenderCepFinal,
		ReceiverRegion:     rowData.ReceiverRegion,
		ReceiverCepInitial: rowData.ReceiverCepInitial,
		ReceiverCepFinal:   rowData.ReceiverCepFinal,
		Operator:           operator,
		Ctime:              nowTime,
		Mtime:              nowTime,
	}, isDelete, nil
}
