package product_serviceable_zone

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/zone_serviceable_protocol"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/zone_serviceable/product_serviceable_zone"
	"github.com/tealeg/xlsx"
)

type ProductServiceableZoneController interface {
	ImportProductServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListProductServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListProductServiceableZoneWithCepGroup(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListProductServiceableZoneAsGeoJson(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	ImportProductServiceableCepGroup(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportProductServiceableCepGroup(ctx *utils.HttpContext, rawRequest interface{})
}

func NewProductServiceableZoneController(svc service.ProductSerivceableZoneService) *productServiceableZoneController {
	return &productServiceableZoneController{
		svc: svc,
	}
}

type productServiceableZoneController struct {
	svc service.ProductSerivceableZoneService
}

func (p *productServiceableZoneController) ImportProductServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ImportProductServiceableZoneRequest)
	if err := p.svc.ImportProductServiceableZone(ctx, req.ProductId, req.CityName, req.UploadFileUrl); err != nil {
		return nil, err
	}
	return nil, nil
}

func (p *productServiceableZoneController) ListProductServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ListProductServiceableZoneRequest)

	// 部分zone的state name和metro region可能不一致，上游使用state name筛选zone，需要将state name映射为metro region
	originReqCityName := req.CityName
	metroRegion, ok := config.GetMutableConf(ctx).DirectDeliveryConfig.State2MetroRegionMap[req.CityName]
	if ok {
		req.CityName = metroRegion
	}

	dataList, err := p.svc.ListProductServiceableZone(ctx, req.ProductId, req.CityName)
	if err != nil {
		return nil, err
	}
	for _, data := range dataList {
		if metroRegion != "" && data.CityName == metroRegion {
			data.CityName = originReqCityName // zone对应的metro region和state name不一致，对外返回替换为state name
		}
	}

	return &zone_serviceable_protocol.ListProductServiceableZoneResponse{
		ZoneList: dataList,
	}, nil
}

func (p *productServiceableZoneController) ListProductServiceableZoneWithCepGroup(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ListProductServiceableZoneRequest)
	dataList, err := p.svc.ListProductServiceableZoneWithCepGroup(ctx, req.ProductId, "")
	if err != nil {
		return nil, err
	}

	zoneNameList := make([]string, 0, len(dataList))
	zoneList := make([]*zone_serviceable_protocol.ProductServiceableZone, 0, len(dataList))
	for _, data := range dataList {
		zoneNameList = append(zoneNameList, data.ZoneName)
		zoneList = append(zoneList, &zone_serviceable_protocol.ProductServiceableZone{
			ProductId: data.ProductId,
			StateName: data.CityName, // zone表存储的city_name字段，对外返回为state_name
			ZoneName:  data.ZoneName,
		})
	}

	return &zone_serviceable_protocol.ListProductServiceableZoneNameResponse{
		ZoneNameList: zoneNameList,
		ZoneList:     zoneList,
	}, nil
}

func (p *productServiceableZoneController) ListProductServiceableZoneAsGeoJson(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ListProductServiceableZoneRequest)
	dataList, err := p.svc.ListProductServiceableZone(ctx, req.ProductId, req.CityName)
	if err != nil {
		return nil, err
	}
	polygonList := make([]model.GeoPolygon, 0, len(dataList))
	for _, data := range dataList {
		polygonList = append(polygonList, data.Polygon)
	}
	return map[string]interface{}{
		"type":     "FeatureCollection",
		"features": polygonList,
	}, nil
}

func (p *productServiceableZoneController) ImportProductServiceableCepGroup(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ImportProductServiceableCepGroupRequest)
	if err := p.svc.ImportProductServiceableCepGroup(ctx, req.UploadFileUrl); err != nil {
		return nil, err
	}
	return nil, nil
}

func (p *productServiceableZoneController) ExportProductServiceableCepGroup(ctx *utils.HttpContext, rawRequest interface{}) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ExportProductServiceableCepGroupRequest)
	dataList, lcosErr := p.svc.ListProductServiceableCepGroup(ctx, req.ProductId)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(ctx.Context, lcosErr.RetCode, nil, lcosErr.Msg)
		return
	}

	rows := make([]interface{}, 0, len(dataList))
	for _, data := range dataList {
		rows = append(rows, &productServiceableCepGroupExcelRow{
			ChannelId:  data.ProductId,
			CepInitial: data.CepInitial,
			CepFinal:   data.CepFinal,
			GroupName:  data.ZoneName,
		})
	}

	file := xlsx.NewFile()
	if err := excel.WriteTitleAndStruct(file, "Sheet1", productServiceableCepGroupExcelTitles, rows); err != nil {
		http.GenErrorResponseWithParam(ctx.Context, lcos_error.ServerErrorCode, nil, fmt.Sprintf("generate export file error, cause: %s", err.Error()))
		return
	}
	http.WriteXlsxFileResponse(ctx.Context, file, "cep_group.xlsx")
}
