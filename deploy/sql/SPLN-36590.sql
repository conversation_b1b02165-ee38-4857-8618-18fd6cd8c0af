ALTER TABLE logistic_line_basic_serviceable_conf_tab ADD COLUMN (
	`is_check_predefined_route` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '0-disable, 1-enable'
);

CREATE TABLE `logistic_line_predefined_route_tab` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
    `region` varchar(8) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'region',
    `line_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'line id',
    `group_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'collect deliver group id',
    `from_area_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT 'from area id',
    `from_area_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'from area name',
    `to_area_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT 'to area id',
    `to_area_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'to area name',
    `route_code` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'route code',
    `ctime` int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'create time',
    `mtime` int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'modify time',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_region_line_group_from_id_to_id` (`region`, `line_id`,`group_id`,`from_area_id`,`to_area_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;