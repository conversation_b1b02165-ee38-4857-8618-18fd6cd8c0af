package main

import (
	"fmt"
	"log"

	"golang.org/x/net/context"

	"git.garena.com/shopee/bg-logistics/go/auth_detector"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/chassis/handler"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/api_router"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/saturnprovider"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/middleware"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/sls_location_sdk"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
)

func main() {
	startup.InitSSCEnv()
	startup.SetChassisConfDir(startup.ServiceAPI)
	ctx := context.Background()
	// 注册handler
	// 1、配置变更上报
	if err := change_report.InitChangeReport(); err != nil {
		log.Fatalf("init change report failed %+v", err)
	}
	if err := change_report.RegisterHttpChangeReportHandler(); err != nil {
		log.Fatalf("registry change report failed %+v", err)
	}
	middleware.RegisterRecovery()
	middleware.RegisterRateLimit()
	middleware.RegisterJwt()
	middleware.RegisterLogger()
	middleware.RegisterLogRequest()
	// 注册prometheus handler
	handler.RegisterPrometheusMetricHandler()
	handler.RegisterReplayerHandler()
	handler.RegisterRecorderHandler(handler.RecorderHandlerOptions{})
	if err := chassis.Init(
		chassis.WithDefaultProviderHandlerChain(
			handler.RepalyerProviderName,
			handler.RecorderProviderName,
			middleware.RateLimitKey,
			middleware.JwtKey,
			middleware.LogRequestKey,
			middleware.LoggerKey,
			change_report.ChangeReportHandlerName,
			middleware.RecoveryKey,
			handler.NameOfPrometheusMetricProvider,
			auth_detector.AuthDetectorHandlerName,
		), chassis.WithDefaultConsumerHandlerChain(
			handler.NameOfPrometheusMetricConsumer,
		)); err != nil {
		log.Fatalf("init chassis: %v", err)
	}
	startup.InitApiMonitorMetrics()
	c, err := cf.InitConfig(ctx)
	if err != nil {
		log.Fatalf("getConfig %v", err)
	}
	_, err = cf.InitMutableConfig(ctx)
	if err != nil {
		log.Fatal(err)
	}
	cf.InitChassisConfig()
	cf.InitLruConfig()

	if err = startup.InitLogger(c); err != nil {
		log.Fatalf("InitLogger Error:%v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		log.Fatalf("InitLibs Error: %v", err)
	}

	if err := startup.InitBranchAdminLibs(c); err != nil {
		log.Fatalf("InitBranchLibs Error: %v", err)
	}

	if err := cache.InitRefreshCache(c); err != nil {
		log.Fatalf("init refresh cache %v", err)
	}

	if err := startup.InitRedisCache(c); err != nil {
		log.Fatalf("init redis cache error: %s", err.Error())
	}

	if err := startup.InitRedisCacheForApi(c); err != nil {
		log.Fatalf("init redis cache error: %s", err.Error())
	}

	if err := startup.InitHDStationRedis(c); err != nil {
		log.Fatalf("init hd station redis cache error:%v", err)
	}

	if err := startup.InitLayeredCache(c); err != nil {
		log.Fatalf("init layered cache error:%v", err)
	}

	// 初始化一下数据版本的service，用于更新数据版本
	localcache.InitDataVersionInfo()

	// 初始化saturn
	saturnprovider.InitSaturnProvider(c.Saturn.DomainName)

	chassis.RegisterSchema("rest", api_router.InitApiResource())
	chassis.RegisterSchema("rest", admin_router.InitAdminResource())

	// SLS-Location SDK初始化
	sls_location_sdk.Init()

	err = config.RegisterListener(&cf.ApplicationConfigListener{})
	if err != nil {
		log.Fatalf("RegisterListener %v", err)
	}
	err = config.RegisterListener(&cf.MutableApplicationConfigListener{})
	if err != nil {
		log.Fatalf("RegisterListener %v", err)
	}
	err = config.RegisterListener(&cf.LRUConfigListener{})
	if err != nil {
		log.Fatalf("Register LRUConfigListener %v", err)
	}

	fmt.Println("chassis.Run() begin")
	if err := chassis.Run(); err != nil {
		log.Fatalf("run failed, %v", err)
	}
}
