package product_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/region_tools"
)

type ClientTag int

const (
	//ClientTagPickupWindow       ClientTag = 1
	//ClientTag3PLMasking         ClientTag = 2
	//ClientTag3PLMaskingForecast ClientTag = 3
	//ClientTagShippingFee        ClientTag = 4
	//ClientTagCBLM               ClientTag = 5
	ClientTagFixedEdt ClientTag = 6
)

var (
	account = "lps-admin-api"
	hosts   = map[string]string{
		"LOCAL":   "https://admin.lps.test.shopee.%s",
		"TEST":    "https://admin.lps.test.shopee.%s",
		"UAT":     "https://admin.lps.uat.shopee.%s",
		"STAGING": "https://admin.lps.staging.shopee.%s",
		"LIVEISH": "https://admin.lps.shopee.%s",
		"LIVE":    "https://admin.lps.shopee.%s",
	}
	apiHosts = map[string]string{
		"LOCAL":   "https://api.lps.test.shopee.%s",
		"TEST":    "https://api.lps.test.shopee.%s",
		"UAT":     "https://api.lps.uat.shopee.%s",
		"STAGING": "https://api.lps.staging.shopee.%s",
		"LIVEISH": "https://api.lps.shopee.%s",
		"LIVE":    "https://api.lps.shopee.%s",
	}

	DraftStatusProduct  = 1
	ActiveStatusProduct = 11

	GETLINELISTAPI            = "/api/admin/product/get_line_list"
	LISTALLPRODUCTAPI         = "/api/admin/product/list_all"
	GETSCENARIO               = "/api/admin/scenario/list"
	GETSITELINEINFO           = "/api/admin/product/site_line_info"
	GETLANECODES              = "/api/logistics/channels/get_lane_codes/"
	GetClientGroupByClientTag = "/api/admin/client_mode/list_client_group_by_client_tag"
	CHANNELSGETAPI    = "/api/v3/logistics/channels/get"
)

func GetUrl(env string, region string) string {
	return fmt.Sprintf(hosts[env], region_tools.GetCountrySuffix(region))
}

func GetApiUrl(env string, region string) string {
	return fmt.Sprintf(apiHosts[env], region_tools.GetCountrySuffix(region))
}
