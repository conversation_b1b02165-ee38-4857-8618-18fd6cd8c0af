syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";
import "lcos_parcel_lib.proto";

service LcosSceneServiceable {
  rpc BatchCheckProductServiceable(BatchCheckProductServiceableReq) returns (BatchCheckProductServiceableRsp) {}
  rpc BatchCheckProductServiceableV2(BatchCheckProductServiceableReq) returns (BatchCheckProductServiceableRsp) {}
  rpc BatchCheckProductServiceableFulfillment(BatchCheckProductServiceableReq) returns (BatchCheckProductServiceableRsp) {}
  rpc BatchCheckProductServiceableForItemScene(BatchCheckProductServiceableForItemSceneReq) returns (BatchCheckProductServiceableForItemSceneResp) {}
  rpc BatchCheckLaneServiceableReroute(RerouteLaneServiceableReq) returns (RerouteLaneServiceableRsp) {}
  rpc BatchCheckShopServiceable(BatchCheckShopServiceableReq) returns (BatchCheckShopServiceableResp) {}
  rpc SearchProductServiceableZone(SearchProductServiceableZoneReq) returns (SearchProductServiceableZoneResp) {}
  rpc BatchGetProductServiceableRouteCode(BatchGetProductServiceableRouteCodeReq) returns (BatchGetProductServiceableRouteCodeResp) {}
}

// serviceable context, directives for how to check serviceable
message ServiceableBasis {
  required uint32 check_operation = 1;   // 是否进行运营层校验
  required uint32 check_basic = 2;    //  是否进行基础层校验
  optional CollectTypeEnum collect_type = 3;  // 揽收类型 1-pickup，2-dropoff, 4-btc
  optional DeliveryTypeEnum deliver_type = 4; // 派送类型 8-to_home，16-to_site，32-to_tws，64-to_3pl，128-to_branch
  optional uint32 check_postal_code = 5;    // 是否跳过postcode校验
  optional PaymentMethodEnum payment_method = 6;  // 支付类型，1-standard，2-cod
  optional LocationCheckLevelEnum sender_check_level = 7;  // 发送方需要check的location level，1-state，2-city，3-district，4-street
  optional LocationCheckLevelEnum receiver_check_level = 8;  // 接收方需要check的location level，1-state，2-city，3-district，4-street
  optional uint32 check_sender = 9; // 是否需要校验发送方地址
  optional uint32 check_receiver = 10; // 是否需要校验接收方地址
  optional uint32 skip_zone_route = 11; // 是否跳过zone和route的校验
  optional bool skip_electric_fence = 12; // 是否跳过电子围栏校验【废弃】
  optional bool use_electric_fence = 13; // 是否启用电子围栏校验
  optional int32 is_check_trade_in = 14;
  optional bool check_predefined_route = 15;
  repeated string predefined_route_codes = 16;
}

message AreaServiceability {
  optional uint32 can_pickup = 1;
  optional uint32 can_cod_pickup = 2;
  optional uint32 can_deliver = 3;
  optional uint32 can_cod_deliver = 4;
  optional uint32 pickup_in_e_fence = 5;
  optional uint32 deliver_in_e_fence = 6;
  optional uint32 support_trade_in = 7;
}

message ServiceableAddress {
  optional uint32 state_location_id = 1;
  optional uint32 city_location_id = 2;
  optional uint32 district_location_id = 3;
  optional uint32 street_location_id = 4;
  optional string postal_code = 5;
  optional string longitude = 6;
  optional string latitude = 7;
  optional string region = 8;
}
message AreaServiceable {
  required int32 code = 1;
  required string message = 2;
  optional AreaServiceability ability = 3;
  repeated ActualPoint actual_points = 4;
}

message ActualPoint {
  required string site_id = 1;
  required string point_id = 2;
  required int32 site_sub_type = 3;
  required int32 point_type = 4;
}

message LaneAreaServiceable {
  required string lane_code = 1;
  required AreaServiceable serviceable = 2;
  repeated string lane_code_group = 3;
}

message LaneAreaServiceabilityReq {
  repeated string lane_codes = 1;
  required string unique_id = 2;    // 必填且不为空
  required int32 product_id = 3;
  required string region = 4;    // 指定region，用于调用lfs获取到lane信息
  required ServiceableBasis basis = 5;
  optional ServiceableAddress pickup_addr = 6;
  optional ServiceableAddress deliver_addr = 7;
  optional uint64 item_id = 8;
  optional ServiceableAddress handover_point_addr = 9;
}

message LaneInfo {
    required int32 sequence = 1;
    required string resource_id = 2;
    required int32 resource_type = 3;
    required int32 resource_sub_type = 4;
    required int32 need_check = 5;
    repeated string actual_point_id = 6;
}

message BatchCheckProductServiceableReq {
  required ReqHeader req_header = 1;
  repeated LaneAreaServiceabilityReq product_list = 2;
}

message ProductServiceableRsp {
  required int32 product_id = 1;
  required string unique_id = 2;
  repeated LaneAreaServiceable lane_list = 3;
  optional uint64 item_id = 4;
}

message BatchCheckProductServiceableRsp {
  required RespHeader resp_header = 1;
  repeated ProductServiceableRsp serviceable_list = 2;
}

message RerouteLaneServiceableReq {
    required ReqHeader req_header = 1;
    repeated LaneServiceableReq lane_serviceable_list = 2;
}

message RerouteLaneServiceableRsp {
    required RespHeader resp_header = 1;
    repeated RerouteLaneServiceable serviceable_rsp = 2;
}

message LaneServiceableReq {
  repeated string lane_codes = 1;
  required string unique_id = 2;    // 必填且不为空
  required int32 product_id = 3;
  required string region = 4;    // 指定region，用于调用lfs获取到lane信息
  required RerouteServiceableBasis basis = 5;
  optional ServiceableAddress pickup_addr = 6;
  optional ServiceableAddress deliver_addr = 7;
  optional ServiceableAddress handover_point_addr = 8;
}

// only for reroute check, some params different from the normal one
message RerouteServiceableBasis {
  required uint32 check_operation = 1;   // 是否进行运营层校验
  required uint32 check_basic = 2;    //  是否进行基础层校验
  optional CollectTypeEnum collect_type = 3;  // 揽收类型 1-pickup，2-dropoff, 4-btc
  optional DeliveryTypeEnum deliver_type = 4; // 派送类型 8-to_home，16-to_site，32-to_tws，64-to_3pl，128-to_branch
  optional uint32 skip_postal_code = 5;    // 是否跳过postcode校验, 0 - 不跳过， 1 - 跳过
  optional PaymentMethodEnum payment_method = 6;  // 支付类型，1-standard，2-cod
  optional LocationCheckLevelEnum sender_check_level = 7;  // 发送方需要check的location level，1-state，2-city，3-district，4-street
  optional LocationCheckLevelEnum receiver_check_level = 8;  // 接收方需要check的location level，1-state，2-city，3-district，4-street
  optional uint32 check_sender = 9; // 是否需要校验发送方地址
  optional uint32 check_receiver = 10; // 是否需要校验接收方地址
  optional uint32 skip_zone_route = 11; // 是否跳过zone和route的校验
}

message RerouteLaneServiceable {
    required int32 product_id = 1;
    required string unique_id = 2;
    repeated RerouteServiceableArea serviceable_list = 3;
}

message RerouteServiceableArea {
  required int32 code = 1;
  required string message = 2;
  required string lane_code = 3;
  repeated Serviceable serviceable = 4;
  repeated string lane_code_group = 5;
}

message Serviceable {
    required int32 code = 1;
    required string message = 2;
    required string resource_id = 3;
    required uint32 sequence = 4;
    optional uint32 can_pickup = 5;
    optional uint32 can_cod_pickup = 6;
    optional uint32 can_deliver = 7;
    optional uint32 can_cod_deliver = 8;
    repeated ActualPoint  actual_points = 9;
    optional uint32 pickup_in_e_fence = 10;
    optional uint32 deliver_in_e_fence = 11;
}

message BatchCheckProductServiceableForItemSceneReq {
    required ReqHeader req_header = 1;
    repeated ItemSceneProductServiceableReq serviceable_list = 2;
    repeated ParcelLibraryQuery parcel_library_queries = 3;
}

message ItemSceneProductServiceableReq {
    required string region = 1;
    repeated ItemSceneProductInfo product_list = 2;
    optional ServiceableAddress pickup_addr = 3;
    optional ServiceableAddress deliver_addr = 4;
    optional ServiceableAddress handover_point_addr = 5;
}

message ItemSceneProductInfo {
    required string unique_id = 1;
    required int32 product_id = 2;
    repeated string lane_codes = 3;
    required ServiceableBasis basis = 4;
}

message BatchCheckProductServiceableForItemSceneResp {
    required RespHeader resp_header = 1;
    repeated ItemSceneProductServiceableResp serviceable_list = 2;
    map<string, ParcelLibraryInfo> parcel_library_info_map = 3;
}

message ItemSceneProductServiceableResp {
    required string unique_id = 1;
    required int32 product_id = 2;
    repeated LaneAreaServiceable lane_list = 3;
    optional uint64 item_id = 4;
}

message BatchCheckShopServiceableReq {
  required ReqHeader req_header = 1;
  repeated CheckShopServiceableReq check_list = 2;
}

message ShopServiceableBasis {
  optional uint32 check_sender = 1;
  optional uint32 check_receiver = 2;
}

message CheckShopServiceableReq {
  optional string unique_id = 1;
  optional string region = 2;
  optional int32 shop_id = 3;
  optional int32 product_id = 4;
  optional ShopServiceableBasis basis = 5;
  optional ServiceableAddress pickup_addr = 6;
  optional ServiceableAddress deliver_addr = 7;
}

message BatchCheckShopServiceableResp {
  required RespHeader resp_header = 1;
  repeated CheckShopServiceableResp serviceable_list = 2;
}

message CheckShopServiceableResp {
  optional string unique_id = 1;
  optional int32 product_id = 2;
  optional int32 shop_id = 3;
  optional bool can_pickup = 4;
  optional string pickup_zone = 5;
  optional bool can_delvier = 6;
  optional string deliver_zone= 7;
}

message SearchProductServiceableZoneReq {
  required ReqHeader req_header = 1;
  repeated SearchProductServiceableZoneAddress address_list = 2;
}

message SearchProductServiceableZoneAddress {
  optional string unique_id = 1;
  optional int32 product_id = 2;
  optional ServiceableAddress address = 3;
}

message SearchProductServiceableZoneResp {
  required RespHeader resp_header = 1;
  repeated SearchProductServiceableZoneResult zone_list = 2;
}

message SearchProductServiceableZoneResult {
  optional string unique_id = 1;
  optional string zone_name = 2;
  optional int32 retcode = 3;
  optional string message = 4;
}

message BatchGetProductServiceableRouteCodeReq {
  required ReqHeader req_header = 1;
  repeated GetProductServiceableRouteCodeReq req_list = 2;
}

message GetProductServiceableRouteCodeReq {
  required string unique_id = 1;
  required int32  product_id = 2;
  repeated string lane_codes = 3;
  required string region = 4;
}

message BatchGetProductServiceableRouteCodeResp {
  required RespHeader resp_header = 1;
  repeated GetProductServiceableRouteCodeResp resp_list = 2;
}

message GetProductServiceableRouteCodeResp {
  required string unique_id = 1;
  required int32  product_id = 2;
  repeated LaneServiceableRouteCodeInfo lane_serviceable_route_codes = 3;
}

message LaneServiceableRouteCodeInfo {
  required string lane_code = 1;
  repeated string route_codes = 2;
}