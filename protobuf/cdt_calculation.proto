syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service LcosCdtCalculationService {
  // 批量获取cdt信息
  rpc BatchGetCdtInfo(QueryCdtInfoRequest) returns (QueryCdtInfoRequestResponse) {}
  rpc GetCdtInfoForTracking(QueryCdtInfoForTrackingRequest) returns (QueryCdtInfoForTrackingResponse) {} // 计算lm的cdt数据，并且返回对应3pl的holiday和action status
  rpc GetEventCodeList(QueryEventCodeRequest) returns (QueryEventCodeResponse) {} // 获取event code list
  // SPLN-23295 return extra info for calculation
  rpc GetEddInfo(QueryCdtInfoForTrackingRequest) returns (GetEddInfoResponse) {}

  // SPLN-24285 calculate edd with extension
  rpc GetExtendedEDD(GetExtendedEDDRequest) returns (GetExtendedEDDResponse) {}

  // SPLN-29072 return edd calculation info, including edd info, ddl info
  rpc GetEddCalculationInfo(GetEddCalculationInfoRequest) returns (GetEddCalculationInfoResponse) {}

  // SPLN-33284 通过algo模型计算edd
  rpc GetEddInfoByAlgo(GetEddInfoByAlgoRequest) returns (GetEddInfoByAlgoResponse) {}

  // SPLN-30606 get edd by sls tn, service name: sls-timeservice-offline-{cid}
  rpc BatchQueryEDDInfo(QueryEDDInfoRequest) returns (QueryEDDInfoResponse) {}

  rpc BatchGetCdtInfoForItemScene(QueryCdtInfoRequest) returns (QueryCdtInfoRequestResponse) {} // SPLN-32027, disply EDT on item card

  // SPLN-31544 get Aggregate rule by region
  rpc ListMChannelRuleByRegion(ListMChannelRuleByRegionRequest) returns (MChannelRuleByRegionResponse) {}

  rpc ListABTestRuleByRegion(ListABTestRuleByRegionRequest) returns (ListABTestRuleByRegionResponse) {}

  // SPLN-31544 get Aggregate channel grey rule by region
  rpc ListMChannelGreyConfigByRegion(ListMChannelGreyConfigByRegionRequest) returns (MChannelGreyConfigByRegionResponse) {}

  // SPLN-33865 增加Algo Model获取修正规则的逻辑
  // SPLN-34535 新增对于历史修正规则的查询逻辑
  rpc GetEDTManualManipulationForAlgoModel(QueryManualManipulationRequest) returns (QueryManualManipulationResponse) {}

  rpc GetFallbackEddInfo(GetFallbackEddInfoRequest) returns (GetFallbackEddInfoResponse) {}
}

message AddressInfo {
  optional int32 state_location_id = 1;
  optional int32 city_location_id = 2;
  optional int32 district_location_id = 3;
  optional string postcode = 4;
}

message CdtSkus {
  required string shop_id = 1;
  repeated SkuInfo sku_items = 2;
}

message SimpleLineInfo {
  required string line_id = 1;
  required uint32 line_sub_type = 2;
}

message ProductInfo {
  required string query_id = 1;   // 唯一表示query id
  required string product_id = 2;
  required int32 is_cb = 3;
  required int32 is_site_line = 4;
  required int32 is_lm = 5;
  required string region = 6;
  optional string tpl_unique_key = 7;
  required AddressInfo seller_addr = 8;
  required AddressInfo buyer_addr = 9;
  optional CdtSkus sku_info = 10; //add sku
  optional string lane_code = 11;  // SPLN-24104, add lane code for EDD Calculation
  repeated SimpleLineInfo line_list = 12;  // SPLN-24104, add line list for EDD Calculation
  optional uint32 update_event = 13;   // SPLN-26162, add update event for EDD
  optional uint32 next_event = 14; // SPLN-29072, next update event, to get cdt for ddl
  optional uint64 item_id = 15; // SPLN-32027, display EDT on item card
  optional uint64 buyer_id = 16; // SPLN-31544, for a/b test
  optional CdtScenarioEnum scenario = 17; // SPLN-31544, 1-PDP，2-Checkout
  repeated FChannelInfo available_fulfillment_channel_ids = 18; // SPLN-31544, f-channel info
  optional int32 need_volume = 19; // SPLN-31544, need volume
  optional uint32 start_day = 20;  // SPLN-32248 新增start_day时间
  optional uint32 start_hour = 21; // SPLN-32248 新增start_hour时间
  optional uint32 end_day = 22;  // SPLN-32248 新增end_day时间
  optional uint32 end_hour = 23;  // SPLN-32248 新增end_hour时间
  optional uint32 group_tag = 24;
  optional uint32 request_time = 25; // SPLN-35764 offline 新增字段，用于匹配请求时间生效的 cdt 手动规则
  optional uint32 start_fcode_type = 26; //SPLN-36163 增加F000 cdt
  optional CdtScene scene = 27; // SPLN-35764 offline 新增字段，用于区分 live, simulation, offline 场景
}

message FChannelInfo {
  required string fulfillment_channel_id = 1;
  required int32 is_cb = 2;
  required int32 is_site_line = 3;
}

message Error {
  required int32 retcode = 1;
  optional string message = 2;
}

message CdtReply {
  required string query_id = 1;
  optional double cdt_min = 2;
  optional double cdt_max = 3;
  optional double cb_lm_max = 4;
  optional string override_type = 5;
  optional Error error = 6;
  optional uint64 item_id = 7;
  repeated FChannelCdtAndVolumeInfo fchannel_cdt_and_volume = 8;
  repeated DayAndTime day_and_time = 9; // 不同时间段的CDT结果，MChannel维度，在没有FChannel信息的时候会用到这个
  optional uint32 request_update_event = 10;
  optional uint32 start_day = 20;  // SPLN-32248 新增start_day时间
  optional uint32 start_hour = 21; // SPLN-32248 新增start_hour时间
  optional uint32 end_day = 22;  // SPLN-32248 新增end_day时间
  optional uint32 end_hour = 23;  // SPLN-32248 新增end_hour时间
  optional uint32 group_tag = 24;
}

message FChannelCdtAndVolumeInfo {
  required int32 retcode = 1;
  optional string message = 2;
  optional string fulfillment_channel_id = 3;
  repeated AllLevelCdtInfoWithLocationLevel cdt_infos = 4;
  repeated AllLevelVolumeInfoWithLocationLevel volume_infos = 5;
}

message AllLevelCdtInfoWithLocationLevel {
  optional double cdt_min = 1;
  optional double cdt_max = 2;
  optional CdtLocationLevelEnum origin_location_level = 3;
  optional CdtLocationLevelEnum dest_location_level = 4;
  repeated DayAndTime day_and_time_list = 5; // 不同时间段的CDT结果，FChannel维度
}

message AllLevelVolumeInfoWithLocationLevel {
  optional uint64 volume = 1;
  optional CdtLocationLevelEnum origin_location_level = 2;
  optional CdtLocationLevelEnum dest_location_level = 3;
}

message DayAndTime {
  required uint32 day = 1; // 表示这个day_group包含了哪些天，[1,2,3]表示周一周二周三
  required uint32 start_hour = 4; // 24小时制，0表示0点，22表示22点
  required uint32 end_hour = 5;
  required double cdt_min = 6;
  required double cdt_max = 7;
}

message QueryCdtInfoRequest {
  required ReqHeader  req_header = 1;
  repeated ProductInfo product_info = 2;
  optional uint32 object_type = 3;
}

message QueryCdtInfoForTrackingRequest {
  required ReqHeader   req_header = 1;
  required ProductInfo product_info = 2;
  required uint32      cb_lm_inbound_date = 3;
  optional uint64      forder_id = 4;
}

message QueryCdtInfoRequestResponse {
  required RespHeader  resp_header = 1;
  repeated CdtReply    cdt_reply_list = 2;
}

message QueryCdtInfoForTrackingResponse {
  required RespHeader  resp_header = 1;
  optional double      cb_lm_cb_max = 2;
  optional double      cb_lm_holiday_ext = 3;
}

message QueryEventCodeRequest {
  required ReqHeader  req_header = 1;
  required uint32     is_site_line = 2;
}

message QueryEventCodeResponse {
  required RespHeader  resp_header = 1;
  repeated string      action_code_list = 2;
}

message CdtQueryInfo {
  optional string product_id = 1;
  optional string tpl_unique_key = 2;
  optional int32 is_lm = 3;
  optional int32 is_site_line = 4;
  optional int32 origin_location_id = 5;
  optional int32 destination_location_id = 6;
  optional int32 destination_ceprange_postcode = 7;
  optional string destination_postcode = 8;
  optional string query_type = 9; // location ceprange or postcode
  optional string lane_code = 10; // SPLN-24104 lane code
  optional string update_event = 11;  // SPLN-26162
  optional int32 origin_location_level = 12;
  optional int32 destination_location_level = 13;
}

// mid result for auto update rule
message AutoUpdateProcess {
  optional uint64 auto_update_rule_id = 1;
  optional double cdt_min = 2;
  optional double cdt_max = 3;
  optional double lm_cdt_max = 4;
  optional int64 cdt_version = 5;
}

// mid result for manual update rule
message ManualUpdateProcess {
  optional double cdt_min = 1;
  optional double cdt_max = 2;
  optional double lm_cdt_max = 3;
}

message ManualManipulationProcess {
  optional double cdt_min_delta = 1;
  optional double cdt_max_delta = 2;
  optional double cb_lm_delta = 3;
  optional int64  record_id = 4;
}

message CDTProcess {
  optional CdtQueryInfo cdt_query_info = 1;
  optional CdtQueryInfo manual_manipulation_query_info = 2;
  optional AutoUpdateProcess auto_update_process = 3;
  optional ManualUpdateProcess manual_update_process = 4;
  optional ManualManipulationProcess manual_manipulation_process = 5;
  optional uint32 group_tag = 6;
}

message NonWorkingDayProcess {
  repeated string edd_min_nwd_list = 1;
  repeated string edd_max_nwd_list = 2;
  repeated string line_id_list = 3;
}

// mid info when calculating edd
message EDDProcess {
  optional CDTProcess cdt_process = 1;
  repeated string non_working_day_list = 2;
  optional NonWorkingDayProcess non_working_day_process = 3;
}

message GetEddInfoResponse {
  required RespHeader  resp_header = 1;
  optional int64       edd = 2;
  optional EDDProcess  edd_process = 3;
  optional int64       edd_min = 4;
}

message SingleEDDExtension {
  required string unique_id = 1;
  required string product_id = 2;
  optional int32 state_location_id = 3;
  required string region = 4;
  required int32 adjustment = 5;
  required int64 edd = 6;
  required int32 is_lm = 7;
  required int32 is_sit_line = 8;
  repeated SimpleLineInfo line_id_list = 9;
  optional string sls_tn = 10;
  required int32 edd_min_adjustment = 11;
  required int64 edd_min = 12;
}

message GetExtendedEDDRequest {
  required ReqHeader req_header = 1;
  repeated SingleEDDExtension edd_extension_list = 2;
}

message SingleEDDResponse {
  required int64 extended_edd = 1;                      // extended edd result
  repeated string extended_non_working_day_list = 2;    // holiday between edd and extended edd
  optional int32 extended_days = 3;                     // days between edd and extended edd
  optional string sls_tn = 4;                           // only for record
  required int64 extended_edd_min = 5;                      // extended edd result
  optional NonWorkingDayProcess non_working_day_process = 6;
  optional int32 edd_min_extended_days = 7;
}

message GetExtendedEDDResponse {
  required RespHeader  resp_header = 1;
  map<string, SingleEDDResponse> extended_edd_response_map = 2; // key is unique id
}

message GetEddCalculationInfoRequest {
  required ReqHeader   req_header = 1;
  required ProductInfo product_info = 2;
  required uint32      event_time = 3;
  optional uint64      forder_id = 4;
}

message EDDCalculationInfo {
  optional bool edd_min_available = 1;
  optional int64 edd_range_limit = 2;
  optional double cdt_min = 3;
  optional double cdt_max = 4;
  repeated string holiday = 5;
  repeated int32 weekend = 6;
}

message DDLCalculationInfo {
  optional double forward_cdt = 1;
  optional double backward_cdt = 2;
  optional double next_event_forward_cdt = 3;
  optional double next_event_backward_cdt = 4;
}

message GetEddCalculationInfoResponse {
  required RespHeader  resp_header = 1;
  optional EDDCalculationInfo edd_info = 2;
  optional DDLCalculationInfo ddl_info = 3;
  optional EDDProcess edd_process = 4;
}

message GetEddInfoByAlgoRequest {
  required ReqHeader   req_header = 1;
  required ProductInfo product_info = 2;
  required uint32      event_time = 3;
  required string      event_tracking_code = 4;
  required string      sls_tn = 5;
}

message AlgoResult {
  required string edd_min = 1;
  required string edd_max = 2;
  required string event_type = 3;
  required int64 event_time = 4;
}

message GetEddInfoByAlgoResponse {
  required RespHeader  resp_header = 1;
  optional int64       edd_min = 2;
  optional int64       edd_max = 3;
  optional EDDProcess  edd_process = 4;
  optional AlgoResult  algo_result = 5;
}


// for possible new query params, put it here
message QueryInfo {
  required string query_id = 1;   // has to be unique
  optional string sls_tn = 2;    // sls tn
  optional string region = 3;    // region
}

message QueryEDDInfoRequest {
  required ReqHeader req_header = 1;
  repeated QueryInfo query_info_list = 2;
}

message EDDInfo {
  optional string query_id = 1;
  optional int64 edd_max = 2;              // timestamp of edd max, use this as edd
  optional int64 edd_min = 3;              // timestamp of edd min
  optional int64 ctime = 4;                // the timestamp when edd was created
  optional string product_id = 5;          // product id
  optional string sls_tn = 6;              // sls tn
  optional int32 retcode = 7;              // retcode
  optional string error_message = 8;       // error message
}

message QueryEDDInfoResponse {
  required RespHeader  resp_header = 1;  // will report error when any sls tn timeout
  map<string, EDDInfo> edd_info_map = 2; // key is query id
}

// --- 获取Region维度可用的mcahnnel规则---
message ListMChannelRuleByRegionRequest {
  required ReqHeader     req_header = 1;
  required string        region = 2;
}

message MChannelRuleByRegionResponse {
  required RespHeader              resp_header = 1;
  repeated MChannelRuleList mchannel_rule_list = 2;
}

message MChannelRuleList {
  required string        product_id = 1;
  optional MChannelRuleEnum        max_cdt_rule = 2;
  optional MChannelRuleEnum        min_cdt_rule = 3;
}

// --- 获取Region维度可用的mcahnnel 灰度开关规则---
message ListMChannelGreyConfigByRegionRequest {
  required ReqHeader     req_header = 1;
  required string        region = 2;
}

message MChannelGreyConfigByRegionResponse {
  required RespHeader              resp_header = 1;
  repeated MChannelGreyConfigList mchannel_grey_config_list = 2;
}

message MChannelGreyConfigList {
  required string        product_id = 1;
  required uint32        percentage = 2;
  repeated uint32        buyer_ids_list = 3;
  repeated uint32        state_location_id_list = 4;
  repeated uint32        city_location_id_list = 5;
  repeated uint32        scenario_list = 6;
  repeated uint32        f_channel_nums_list = 7;
}

message ListABTestRuleByRegionRequest {
  required ReqHeader     req_header = 1;
  required string        region = 2;
}

message ListABTestRuleByRegionResponse {
  required RespHeader              resp_header = 1;
  repeated ABTestRuleList     ab_test_rule_list = 2;
}

message ABTestRuleList {
  required string        product_id = 1;
  required uint32        active_time = 2;
  repeated ABTestRuleDetails        ab_test_rule_details = 3;
}

message ABTestRuleDetails {
  required uint32 group_tag = 1;
  repeated uint32 groups = 2;
}


// SPLN-33865 用于根据请求入参计算修正规则的简单product信息
message SimpleProductInfo {
  required string query_id = 1;     // 唯一表示query id
  required string product_id = 2;
  required int32 is_cb = 3;
  required int32 is_site_line = 4;
  required int32 is_lm = 5;
  required string region = 6;
  required AddressInfo seller_addr = 7;
  required AddressInfo buyer_addr = 8;
  optional uint32 update_event = 9;
  optional uint32 request_time = 10; // SPLN-34535 新增request time，用于区分
  optional CdtScene scene = 11; // SPLN-35764 offline 新增字段，用于区分 live, simulation, offline 场景
}

message QueryManualManipulationRequest {
  required ReqHeader         req_header = 1;
  repeated SimpleProductInfo product_info = 2;
}

// SPLN-33865 单个修正规则返回
message DeltaReply {
  required string query_id = 1;
  optional int32  retcode = 2;
  optional string message = 3;
  optional double delta_min = 4;
  optional double delta_max = 5;
  optional uint32 effect_time = 6;
  optional uint32 expire_time = 7;
}

message QueryManualManipulationResponse {
  required RespHeader           resp_header = 1;
  map<string, DeltaReply>       delta_reply_map = 2; // key is unique id
}

message GetFallbackEddInfoRequest {
  required ReqHeader req_header = 1;
  required string region = 2;
  required uint64 forder_id = 3;
  required uint32 days_to_delivery = 4;
  optional uint32 request_time = 5;
}

message GetFallbackEddInfoResponse {
  required RespHeader resp_header = 1;
  optional uint32 edd_min = 2;
  optional uint32 edd_max = 3;
}