syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service LcosPickupWindowService {
  // 给LFS点线调用，用于获取揽收时间
  rpc GetArrangedPickupDays(GetArrangedPickupDaysRequest) returns (GetPickupDaysResponse) {}

  // 给开发物流调用，用于获取揽收时间
  rpc GetArrangedPickupDaysOpenLogistic(GetArrangedPickupDaysOpenLogisticRequest) returns (GetPickupDaysResponse) {}

  // 给LFS掉线调用，用于获取退货揽收时间
  rpc GetReturnPickupDays(GetReturnPickupDaysRequest) returns (GetPickupDaysResponse) {}

  // 目前给lcs调用，用于校验pickup_time是否合法
  rpc CheckPickupTime(CheckPickupTimeRequest) returns (CheckPickupTimeResponse) {}

  // 给lcs调用，用于获取timeslot
  rpc GetPickupTimeslot(GetPickupTimeslotRequest) returns (GetPickupTimeslotsResponse) {}

  // ！！！请勿使用。仅用给sls兼容期间调用，不提供给其他服务调用
  rpc QueryPickupTimeslots(QueryPickupTimeslotsRequest) returns (QueryPickupTimeslotsResponse) {}

  // ！！！请勿使用。仅用给sls兼容期间调用，不提供给其他服务调用
  rpc GetValidPickupConf(GetValidPickupConfRequest) returns (GetValidPickupConfResponse) {}

  // 用于获取指定line的未来days的假期
  rpc GetHolidays(GetHolidaysRequest) returns (GetHolidaysResponse) {}

  // 目前仅给LFS调用，用于获取pickup group和line的映射关系，用于单量统计
  rpc GetAllPickupGroups(GetAllPickupGroupsRequest) returns (GetAllPickupGroupsResponse) {}

  // 给lps调用，用于获取pickup noworking days
  rpc GetProductPickupDays(GetProductPickupDaysRequest) returns (GetProductPickupDaysResponse) {}
}

message GetArrangedPickupDaysRequest {
  required ReqHeader  req_header = 1;
  required PickupInfo pickup_info = 2;
}

message GetArrangedPickupDaysOpenLogisticRequest {
  required ReqHeader              req_header = 1;
  required OpenLogisticPickupInfo pickup_info = 2;
}

message GetReturnPickupDaysRequest {
  required ReqHeader        req_header = 1;
  required ReturnPickupInfo pickup_info = 2;
}

message CheckPickupTimeRequest {
  required ReqHeader           req_header = 1;
  required CheckPickupTimeInfo pickup_info = 2;
}

message GetPickupTimeslotRequest {
  required ReqHeader req_header = 1;
  repeated string    line_id_list = 2;            // 传入的first mile line id列表。如果传入的line列表不属于同一个group，或者不满足pickup条件，则报错
  required uint32    pickup_time_range_id = 3;    // timeslot的pickup time range id
  optional uint32    start_time = 4;              // 当前不需要
  optional uint32    merchant_type = 5;           // merchant_type，当merchant_type和account_group同时传入时，走开放物流逻辑。只要有一个没传入，则走点线逻辑
  optional uint32    account_group = 6;           // account_group，不必填
}

message QueryPickupTimeslotsRequest {
  required ReqHeader req_header = 1;
  repeated string    line_id_list = 2;
  optional uint32    pickup_time_range_id = 3;
}

message GetValidPickupConfRequest {
  required ReqHeader req_header = 1;
  repeated string    line_id_list = 2;
}

message GetHolidaysRequest {
  required ReqHeader req_header = 1;
  repeated string    line_id_list = 2;                    // 传入的line id列表。如果传入的line列表不属于同一个group，或者不满足pickup条件，则报错
  optional uint32    days = 3;                            // 返回多少天内的假期。如果不传入，默认填充为30
  optional int64     state_location_id = 4;               // 具体的stateLocationID，不传入表示为0，此时表示取line列表的origin region所在的国家假期列表。
  optional string    zipcode = 5;                         // SPLN-18095，新增postcode用于获取weekends
}


message GetAllPickupGroupsRequest {
  required ReqHeader req_header = 1;
}

message Volume {
  required string date = 1;
  required uint32 volume = 2;
}

message PickupInfo {
  required uint32     ship_by_datetime = 1;    // 时间戳，order传入
  repeated string     line_id_list = 2;        // first mile的line id列表
  optional uint32     release_time = 3;        // 时间戳，order传入
  optional uint32     pay_time = 4;            // 时间戳，order传入
  optional int32      days_to_ship = 5;        // 整体的天数，order传入
  required uint32     state_location_id = 6;   // 揽收地点的stateLocationID
  repeated Volume     volumes = 7;             // 单量按照日期的统计
  optional uint32     buyer_timeslot_id = 8;   // 针对uparcel的修改
  optional string     zipcode = 9;             // SPLN-18095，新增postcode用于获取weekends
  repeated string     client_group_id_list = 10;    // SPLN-18171，shop所属的group，由SLS Client 定义
  optional bool       is_change_pickup_date = 11;      // SPLN-19855, 针对 Exceed Pickup Time场景，为true则不使用extend逻辑
  optional bool       is_b2c = 12;             // 是否为仓库单
  optional string     region = 13;             // 地区，当仓库单没有匹配到pickup_group的时候，用这个字段决定地区
  optional uint32     acl2_time = 14;          // SPLN-34806新增，ACL2时间戳，order传入。用于在SBD前无法获取pickup day时的extend逻辑
  optional bool       is_rapid_sla = 15;       // SPLN-36499 是否是Rapid SLA渠道。Rapid SLA渠道ACL2为小时维度
}

message OpenLogisticPickupInfo {
  repeated string     line_id_list = 1;                 // first mile的line id列表
  required uint32     merchant_type = 2;                // merchant type 当前必填
  optional uint32     account_group = 3;                // account group 当前不必填
  optional int64      pickup_location_id = 4;           // location id，当前不必填，默认为0，如果需要根据location id区分节假日则需要填入
  optional uint32     start_time = 5;                   // 开始时间，目前直接传入当前的时间戳即可
}

message ReturnPickupInfo {
  required uint32     ship_by_days = 1;            // 天数
  repeated string     line_id_list = 2;            // 逆向物流的first mile的line id列表
  required uint32     state_location_id = 3;       // 揽收地点的stateLocationID
  optional string     zipcode = 4;                 // SPLN-18095，新增postcode用于获取weekends
}

message CheckPickupTimeInfo {
  repeated string line_id_list = 1;               // first mile的line id列表
  required string country = 2;                    // 揽收地区的国家信息，country用于转换pickup time时间戳为country时区的datetime
  required int64  state_location_id = 3;          // 揽收地点的stateLocationID
  required uint32 pickup_time = 4;                // 时间戳，揽收时间
  optional uint32 start_time = 5;                 // 从何时开始，目前不需要
  optional uint32 merchant_type = 6;              // merchant_type，当merchant_type和account_group同时传入时，走开放物流逻辑。只要有一个没传入，则走点线逻辑
  optional uint32 account_group = 7;              // account_group
  optional uint32 pick_time_range_id = 8;         // 只给one api使用，其他不需要
  optional string zipcode = 9;                    // SPLN-18095，增加postcode用于获取weekends
  repeated string client_group_id_list = 10;           // SPLN-18171，由sls client定义，表示shop所属的group
}


message Day {
  required string date = 1;                      // 日期的字符串
  required uint32 value = 2;                     // 日期的时间戳
  required uint32 volume_limit = 3;              // 当天是否容量限制，1表示限制，0表示不限制
}

message Slot {
  required uint32 value = 1;              // 当前timeslot的pickup_time_range_id
  required string time = 2;               // slot mark，具体的timeslot描述，如2:00pm~4:00pm
  optional uint32 start_time = 3;         // timeslot开始时间
  optional uint32 end_time = 4;           // timeslot结束时间
  optional uint32 slot_start_hour = 5;    // timeslot开始时间-小时
  optional uint32 slot_cutoff_hour = 6;   // timeslot截止时间-小时
  optional uint32 slot_cutoff_minute = 7; // timeslot截止时间-分钟
}

message Timeslot {
  required string date = 1;
  required uint32 value = 2;
  repeated Slot   slots = 3;
}

message PickupDays {
  repeated Day      days = 1;
  repeated Timeslot timeslots = 2;
  required string   pickup_group_id = 3;
  optional uint32   pickup_cutoff_hour = 4;
}

message GetPickupDaysResponse {
  required RespHeader resp_header = 1;
  required PickupDays pickup_days = 2;
}

message CheckPickupTimeResponse {
  required RespHeader resp_header = 1;
}

message GetPickupTimeslotsResponse {
  required RespHeader   resp_header = 1;
  required TimeslotInfo timeslot = 2;
}

message QueryPickupTimeslotsResponse {
  required RespHeader       resp_header = 1;
  repeated FullTimeslotInfo timeslots = 2;
}

message GetValidPickupConfResponse {
  required RespHeader       resp_header = 1;
  optional PickupConf       pickup_conf = 2;
}

message GetHolidaysResponse {
  required RespHeader       resp_header = 1;
  repeated string           holidays = 2;
}

message GetAllPickupGroupsResponse {
  required RespHeader   resp_header = 1;
  repeated PickupGroup  pickup_groups = 2;
}

message TimeslotInfo {
  required uint32     start_hour = 1;
  required uint32     start_minute = 2;
  required uint32     start_second = 3;
  required uint32     end_hour = 4;
  required uint32     end_minute = 5;
  required uint32     end_second = 6;
}

message FullTimeslotInfo {
  required uint64      id = 1;
  required uint32      type = 2;
  required string      pickup_country = 3;
  required string      deliver_country = 4;
  required uint32      value = 5;
  required uint32      start_time_hour = 6;
  required uint32      start_time_min = 7;
  required uint32      start_time_second = 8;
  required uint32      end_time_hour = 9;
  required uint32      end_time_min = 10;
  required uint32      end_time_second = 11;
  required string      time_slot_remark = 12;
  required uint32      status = 13;
  required uint32      ctime = 14;
  required uint32      mtime = 15;
}

message PickupConf {
  required uint64 id = 1;
  required string pickup_group_id = 2;
  required string destination_region = 3;
  required uint32 is_extend = 4;
  required uint32 extend_days = 5;
  required uint32 use_release_time = 6;
  required uint32 is_non_working_day_pickup_allowed = 7;
  required uint32 return_days = 8;
  required uint32 advance_days = 9;
  required uint32 return_cutoff_hour = 10;
  required uint32 return_cutoff_hour_days = 11;
  required string pickup_date_format = 12;
  required uint32 pickup_cutoff_hour = 13;
  required uint32 is_have_time_slot = 14;
  required uint32 slot_num = 15;
  required uint32 extend_slot_num = 16;
  required uint32 effective_time = 17;
  required uint32 enable_status = 18;
  required uint32 daily_max_volume = 19;
  required uint32 daily_control_status = 20;
  required uint32 daily_min_days_extend = 21;
  required uint32 daily_control_begin_time = 22;
  required uint32 daily_control_end_time = 23;
  required uint32 ctime = 24;
  required uint32 mtime = 25;
}

message PickupGroup {
  required string     pickup_group_id = 1;
  required string     pickup_group_name = 2;
  required string     origin_region = 3;
  required string     destination_region = 4;
  required uint32     pickup_type = 5;
  repeated string     line_id_list = 6;
  required uint32     ctime = 7;
  required uint32     mtime = 8;
}

message GetProductPickupDaysRequest {
  required ReqHeader  req_header = 1;
  required PickupInfoReq pickup_info = 2;
}

message PickupInfoReq {
  optional string region = 1;               //
  required int32 product_id = 2;
  optional string zipcode = 3;
  optional uint32 state_id = 4;
  repeated string fm_lines = 5;           // fm 的line信息
}

message GetProductPickupDaysResponse {
  required RespHeader resp_header = 1;
  required ProductPickupDays pickup_days = 2;
}


message ProductPickupDays {
  repeated uint32     pickup_recurring_holidays = 1;        // first mile的line id列表
  repeated string     pickup_holidays = 2;
  required int32  status = 3;
}
