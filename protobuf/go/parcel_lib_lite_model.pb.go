// Code generated by protoc-gen-go. DO NOT EDIT.
// source: parcel_lib_lite_model.proto

package lcos_protobuf

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ParcelLibraryDataLite struct {
	AccurateLength        *float64 `protobuf:"fixed64,1,req,name=accurate_length,json=accurateLength" json:"accurate_length,omitempty"`
	AccurateWidth         *float64 `protobuf:"fixed64,2,req,name=accurate_width,json=accurateWidth" json:"accurate_width,omitempty"`
	AccurateHeight        *float64 `protobuf:"fixed64,3,req,name=accurate_height,json=accurateHeight" json:"accurate_height,omitempty"`
	AccurateWeight        *float64 `protobuf:"fixed64,4,req,name=accurate_weight,json=accurateWeight" json:"accurate_weight,omitempty"`
	Version               *uint64  `protobuf:"varint,5,req,name=version" json:"version,omitempty"`
	EffectiveTime         *uint32  `protobuf:"varint,6,req,name=effective_time,json=effectiveTime" json:"effective_time,omitempty"`
	LengthAccuracy        *float64 `protobuf:"fixed64,7,req,name=length_accuracy,json=lengthAccuracy" json:"length_accuracy,omitempty"`
	WidthAccuracy         *float64 `protobuf:"fixed64,8,req,name=width_accuracy,json=widthAccuracy" json:"width_accuracy,omitempty"`
	HeightAccuracy        *float64 `protobuf:"fixed64,9,req,name=height_accuracy,json=heightAccuracy" json:"height_accuracy,omitempty"`
	WeightAccuracy        *float64 `protobuf:"fixed64,10,req,name=weight_accuracy,json=weightAccuracy" json:"weight_accuracy,omitempty"`
	WeightFinAccuracy     *float64 `protobuf:"fixed64,11,req,name=weight_fin_accuracy,json=weightFinAccuracy" json:"weight_fin_accuracy,omitempty"`
	VolumetricFinAccuracy *float64 `protobuf:"fixed64,12,req,name=volumetric_fin_accuracy,json=volumetricFinAccuracy" json:"volumetric_fin_accuracy,omitempty"`
	IsBulkyParcel         *int32   `protobuf:"varint,13,opt,name=is_bulky_parcel,json=isBulkyParcel,def=-1" json:"is_bulky_parcel,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *ParcelLibraryDataLite) Reset()         { *m = ParcelLibraryDataLite{} }
func (m *ParcelLibraryDataLite) String() string { return proto.CompactTextString(m) }
func (*ParcelLibraryDataLite) ProtoMessage()    {}
func (*ParcelLibraryDataLite) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2475affee630028, []int{0}
}

func (m *ParcelLibraryDataLite) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ParcelLibraryDataLite.Unmarshal(m, b)
}
func (m *ParcelLibraryDataLite) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ParcelLibraryDataLite.Marshal(b, m, deterministic)
}
func (m *ParcelLibraryDataLite) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ParcelLibraryDataLite.Merge(m, src)
}
func (m *ParcelLibraryDataLite) XXX_Size() int {
	return xxx_messageInfo_ParcelLibraryDataLite.Size(m)
}
func (m *ParcelLibraryDataLite) XXX_DiscardUnknown() {
	xxx_messageInfo_ParcelLibraryDataLite.DiscardUnknown(m)
}

var xxx_messageInfo_ParcelLibraryDataLite proto.InternalMessageInfo

const Default_ParcelLibraryDataLite_IsBulkyParcel int32 = -1

func (m *ParcelLibraryDataLite) GetAccurateLength() float64 {
	if m != nil && m.AccurateLength != nil {
		return *m.AccurateLength
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetAccurateWidth() float64 {
	if m != nil && m.AccurateWidth != nil {
		return *m.AccurateWidth
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetAccurateHeight() float64 {
	if m != nil && m.AccurateHeight != nil {
		return *m.AccurateHeight
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetAccurateWeight() float64 {
	if m != nil && m.AccurateWeight != nil {
		return *m.AccurateWeight
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetVersion() uint64 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetEffectiveTime() uint32 {
	if m != nil && m.EffectiveTime != nil {
		return *m.EffectiveTime
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetLengthAccuracy() float64 {
	if m != nil && m.LengthAccuracy != nil {
		return *m.LengthAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetWidthAccuracy() float64 {
	if m != nil && m.WidthAccuracy != nil {
		return *m.WidthAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetHeightAccuracy() float64 {
	if m != nil && m.HeightAccuracy != nil {
		return *m.HeightAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetWeightAccuracy() float64 {
	if m != nil && m.WeightAccuracy != nil {
		return *m.WeightAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetWeightFinAccuracy() float64 {
	if m != nil && m.WeightFinAccuracy != nil {
		return *m.WeightFinAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetVolumetricFinAccuracy() float64 {
	if m != nil && m.VolumetricFinAccuracy != nil {
		return *m.VolumetricFinAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetIsBulkyParcel() int32 {
	if m != nil && m.IsBulkyParcel != nil {
		return *m.IsBulkyParcel
	}
	return Default_ParcelLibraryDataLite_IsBulkyParcel
}

func init() {
	proto.RegisterType((*ParcelLibraryDataLite)(nil), "lcos_protobuf.ParcelLibraryDataLite")
}

func init() {
	proto.RegisterFile("parcel_lib_lite_model.proto", fileDescriptor_f2475affee630028)
}

var fileDescriptor_f2475affee630028 = []byte{
	// 326 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x5c, 0x92, 0x41, 0x4b, 0xf3, 0x40,
	0x10, 0x86, 0x69, 0xbe, 0xf6, 0xab, 0xae, 0xc6, 0x62, 0xa5, 0xb8, 0xe0, 0x25, 0x08, 0xd2, 0x20,
	0x58, 0xf0, 0xe2, 0xc1, 0x9b, 0x22, 0xe2, 0xa1, 0x07, 0x09, 0x42, 0x8f, 0x4b, 0xb2, 0x9d, 0x34,
	0x83, 0x9b, 0xa4, 0x6c, 0x36, 0x0d, 0xfd, 0x57, 0xfe, 0x44, 0xc9, 0x4e, 0x93, 0x34, 0xde, 0x92,
	0x67, 0x9e, 0x79, 0x93, 0x17, 0x86, 0xdd, 0x6c, 0x43, 0x2d, 0x41, 0x09, 0x85, 0x91, 0x50, 0x68,
	0x40, 0xa4, 0xf9, 0x1a, 0xd4, 0x62, 0xab, 0x73, 0x93, 0x4f, 0x5d, 0x25, 0xf3, 0x42, 0xd8, 0xe7,
	0xa8, 0x8c, 0x6f, 0x7f, 0x86, 0x6c, 0xf6, 0x69, 0xf5, 0x25, 0x46, 0x3a, 0xd4, 0xfb, 0xb7, 0xd0,
	0x84, 0x4b, 0x34, 0x30, 0x9d, 0xb3, 0x49, 0x28, 0x65, 0xa9, 0x43, 0x03, 0x42, 0x41, 0xb6, 0x31,
	0x09, 0x1f, 0x78, 0x8e, 0x3f, 0x08, 0x2e, 0x1a, 0xbc, 0xb4, 0x74, 0x7a, 0xc7, 0x5a, 0x22, 0x2a,
	0x5c, 0x9b, 0x84, 0x3b, 0xd6, 0x73, 0x1b, 0xba, 0xaa, 0x61, 0x2f, 0x2f, 0x01, 0xdc, 0x24, 0x86,
	0xff, 0xeb, 0xe7, 0x7d, 0x58, 0xda, 0x13, 0x2b, 0x12, 0x87, 0x7d, 0x71, 0x45, 0x22, 0x67, 0xe3,
	0x1d, 0xe8, 0x02, 0xf3, 0x8c, 0x8f, 0x3c, 0xc7, 0x1f, 0x06, 0xcd, 0x6b, 0xfd, 0x4b, 0x10, 0xc7,
	0x20, 0x0d, 0xee, 0x40, 0x18, 0x4c, 0x81, 0xff, 0xf7, 0x1c, 0xdf, 0x0d, 0xdc, 0x96, 0x7e, 0x61,
	0x6a, 0x2b, 0x52, 0x33, 0x41, 0xc9, 0x72, 0xcf, 0xc7, 0xf4, 0x25, 0xc2, 0x2f, 0x07, 0x5a, 0xe7,
	0xd9, 0x66, 0x9d, 0x77, 0x42, 0x15, 0x2d, 0x6d, 0xb5, 0x39, 0x9b, 0x50, 0xb3, 0xce, 0x3b, 0xa5,
	0x3c, 0xc2, 0xc7, 0x62, 0xf5, 0x47, 0x64, 0x24, 0x56, 0x7d, 0x71, 0xc1, 0xae, 0x0e, 0x62, 0x8c,
	0x59, 0x27, 0x9f, 0x59, 0xf9, 0x92, 0x46, 0xef, 0x98, 0xb5, 0xfe, 0x13, 0xbb, 0xde, 0xe5, 0xaa,
	0x4c, 0xc1, 0x68, 0x94, 0xfd, 0x9d, 0x73, 0xbb, 0x33, 0xeb, 0xc6, 0xc7, 0x7b, 0xf7, 0x6c, 0x82,
	0x85, 0x88, 0x4a, 0xf5, 0xbd, 0x17, 0x74, 0x3d, 0xdc, 0xf5, 0x06, 0xfe, 0xe8, 0xd9, 0x79, 0x78,
	0x0c, 0x5c, 0x2c, 0x5e, 0xeb, 0x09, 0xdd, 0xc9, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xe3, 0x94,
	0x3d, 0xbe, 0x5f, 0x02, 0x00, 0x00,
}
