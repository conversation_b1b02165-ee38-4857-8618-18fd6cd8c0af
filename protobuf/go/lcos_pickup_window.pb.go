// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_pickup_window.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GetArrangedPickupDaysRequest struct {
	ReqHeader            *ReqHeader  `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	PickupInfo           *PickupInfo `protobuf:"bytes,2,req,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetArrangedPickupDaysRequest) Reset()         { *m = GetArrangedPickupDaysRequest{} }
func (m *GetArrangedPickupDaysRequest) String() string { return proto.CompactTextString(m) }
func (*GetArrangedPickupDaysRequest) ProtoMessage()    {}
func (*GetArrangedPickupDaysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{0}
}

func (m *GetArrangedPickupDaysRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetArrangedPickupDaysRequest.Unmarshal(m, b)
}
func (m *GetArrangedPickupDaysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetArrangedPickupDaysRequest.Marshal(b, m, deterministic)
}
func (m *GetArrangedPickupDaysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetArrangedPickupDaysRequest.Merge(m, src)
}
func (m *GetArrangedPickupDaysRequest) XXX_Size() int {
	return xxx_messageInfo_GetArrangedPickupDaysRequest.Size(m)
}
func (m *GetArrangedPickupDaysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetArrangedPickupDaysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetArrangedPickupDaysRequest proto.InternalMessageInfo

func (m *GetArrangedPickupDaysRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetArrangedPickupDaysRequest) GetPickupInfo() *PickupInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

type GetArrangedPickupDaysOpenLogisticRequest struct {
	ReqHeader            *ReqHeader              `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	PickupInfo           *OpenLogisticPickupInfo `protobuf:"bytes,2,req,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetArrangedPickupDaysOpenLogisticRequest) Reset() {
	*m = GetArrangedPickupDaysOpenLogisticRequest{}
}
func (m *GetArrangedPickupDaysOpenLogisticRequest) String() string { return proto.CompactTextString(m) }
func (*GetArrangedPickupDaysOpenLogisticRequest) ProtoMessage()    {}
func (*GetArrangedPickupDaysOpenLogisticRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{1}
}

func (m *GetArrangedPickupDaysOpenLogisticRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetArrangedPickupDaysOpenLogisticRequest.Unmarshal(m, b)
}
func (m *GetArrangedPickupDaysOpenLogisticRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetArrangedPickupDaysOpenLogisticRequest.Marshal(b, m, deterministic)
}
func (m *GetArrangedPickupDaysOpenLogisticRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetArrangedPickupDaysOpenLogisticRequest.Merge(m, src)
}
func (m *GetArrangedPickupDaysOpenLogisticRequest) XXX_Size() int {
	return xxx_messageInfo_GetArrangedPickupDaysOpenLogisticRequest.Size(m)
}
func (m *GetArrangedPickupDaysOpenLogisticRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetArrangedPickupDaysOpenLogisticRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetArrangedPickupDaysOpenLogisticRequest proto.InternalMessageInfo

func (m *GetArrangedPickupDaysOpenLogisticRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetArrangedPickupDaysOpenLogisticRequest) GetPickupInfo() *OpenLogisticPickupInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

type GetReturnPickupDaysRequest struct {
	ReqHeader            *ReqHeader        `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	PickupInfo           *ReturnPickupInfo `protobuf:"bytes,2,req,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetReturnPickupDaysRequest) Reset()         { *m = GetReturnPickupDaysRequest{} }
func (m *GetReturnPickupDaysRequest) String() string { return proto.CompactTextString(m) }
func (*GetReturnPickupDaysRequest) ProtoMessage()    {}
func (*GetReturnPickupDaysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{2}
}

func (m *GetReturnPickupDaysRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReturnPickupDaysRequest.Unmarshal(m, b)
}
func (m *GetReturnPickupDaysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReturnPickupDaysRequest.Marshal(b, m, deterministic)
}
func (m *GetReturnPickupDaysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReturnPickupDaysRequest.Merge(m, src)
}
func (m *GetReturnPickupDaysRequest) XXX_Size() int {
	return xxx_messageInfo_GetReturnPickupDaysRequest.Size(m)
}
func (m *GetReturnPickupDaysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReturnPickupDaysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetReturnPickupDaysRequest proto.InternalMessageInfo

func (m *GetReturnPickupDaysRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetReturnPickupDaysRequest) GetPickupInfo() *ReturnPickupInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

type CheckPickupTimeRequest struct {
	ReqHeader            *ReqHeader           `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	PickupInfo           *CheckPickupTimeInfo `protobuf:"bytes,2,req,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CheckPickupTimeRequest) Reset()         { *m = CheckPickupTimeRequest{} }
func (m *CheckPickupTimeRequest) String() string { return proto.CompactTextString(m) }
func (*CheckPickupTimeRequest) ProtoMessage()    {}
func (*CheckPickupTimeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{3}
}

func (m *CheckPickupTimeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPickupTimeRequest.Unmarshal(m, b)
}
func (m *CheckPickupTimeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPickupTimeRequest.Marshal(b, m, deterministic)
}
func (m *CheckPickupTimeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPickupTimeRequest.Merge(m, src)
}
func (m *CheckPickupTimeRequest) XXX_Size() int {
	return xxx_messageInfo_CheckPickupTimeRequest.Size(m)
}
func (m *CheckPickupTimeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPickupTimeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPickupTimeRequest proto.InternalMessageInfo

func (m *CheckPickupTimeRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *CheckPickupTimeRequest) GetPickupInfo() *CheckPickupTimeInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

type GetPickupTimeslotRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineIdList           []string   `protobuf:"bytes,2,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	PickupTimeRangeId    *uint32    `protobuf:"varint,3,req,name=pickup_time_range_id,json=pickupTimeRangeId" json:"pickup_time_range_id,omitempty"`
	StartTime            *uint32    `protobuf:"varint,4,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	MerchantType         *uint32    `protobuf:"varint,5,opt,name=merchant_type,json=merchantType" json:"merchant_type,omitempty"`
	AccountGroup         *uint32    `protobuf:"varint,6,opt,name=account_group,json=accountGroup" json:"account_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetPickupTimeslotRequest) Reset()         { *m = GetPickupTimeslotRequest{} }
func (m *GetPickupTimeslotRequest) String() string { return proto.CompactTextString(m) }
func (*GetPickupTimeslotRequest) ProtoMessage()    {}
func (*GetPickupTimeslotRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{4}
}

func (m *GetPickupTimeslotRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPickupTimeslotRequest.Unmarshal(m, b)
}
func (m *GetPickupTimeslotRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPickupTimeslotRequest.Marshal(b, m, deterministic)
}
func (m *GetPickupTimeslotRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPickupTimeslotRequest.Merge(m, src)
}
func (m *GetPickupTimeslotRequest) XXX_Size() int {
	return xxx_messageInfo_GetPickupTimeslotRequest.Size(m)
}
func (m *GetPickupTimeslotRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPickupTimeslotRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPickupTimeslotRequest proto.InternalMessageInfo

func (m *GetPickupTimeslotRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetPickupTimeslotRequest) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

func (m *GetPickupTimeslotRequest) GetPickupTimeRangeId() uint32 {
	if m != nil && m.PickupTimeRangeId != nil {
		return *m.PickupTimeRangeId
	}
	return 0
}

func (m *GetPickupTimeslotRequest) GetStartTime() uint32 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *GetPickupTimeslotRequest) GetMerchantType() uint32 {
	if m != nil && m.MerchantType != nil {
		return *m.MerchantType
	}
	return 0
}

func (m *GetPickupTimeslotRequest) GetAccountGroup() uint32 {
	if m != nil && m.AccountGroup != nil {
		return *m.AccountGroup
	}
	return 0
}

type QueryPickupTimeslotsRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineIdList           []string   `protobuf:"bytes,2,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	PickupTimeRangeId    *uint32    `protobuf:"varint,3,opt,name=pickup_time_range_id,json=pickupTimeRangeId" json:"pickup_time_range_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *QueryPickupTimeslotsRequest) Reset()         { *m = QueryPickupTimeslotsRequest{} }
func (m *QueryPickupTimeslotsRequest) String() string { return proto.CompactTextString(m) }
func (*QueryPickupTimeslotsRequest) ProtoMessage()    {}
func (*QueryPickupTimeslotsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{5}
}

func (m *QueryPickupTimeslotsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPickupTimeslotsRequest.Unmarshal(m, b)
}
func (m *QueryPickupTimeslotsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPickupTimeslotsRequest.Marshal(b, m, deterministic)
}
func (m *QueryPickupTimeslotsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPickupTimeslotsRequest.Merge(m, src)
}
func (m *QueryPickupTimeslotsRequest) XXX_Size() int {
	return xxx_messageInfo_QueryPickupTimeslotsRequest.Size(m)
}
func (m *QueryPickupTimeslotsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPickupTimeslotsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPickupTimeslotsRequest proto.InternalMessageInfo

func (m *QueryPickupTimeslotsRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *QueryPickupTimeslotsRequest) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

func (m *QueryPickupTimeslotsRequest) GetPickupTimeRangeId() uint32 {
	if m != nil && m.PickupTimeRangeId != nil {
		return *m.PickupTimeRangeId
	}
	return 0
}

type GetValidPickupConfRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineIdList           []string   `protobuf:"bytes,2,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetValidPickupConfRequest) Reset()         { *m = GetValidPickupConfRequest{} }
func (m *GetValidPickupConfRequest) String() string { return proto.CompactTextString(m) }
func (*GetValidPickupConfRequest) ProtoMessage()    {}
func (*GetValidPickupConfRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{6}
}

func (m *GetValidPickupConfRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidPickupConfRequest.Unmarshal(m, b)
}
func (m *GetValidPickupConfRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidPickupConfRequest.Marshal(b, m, deterministic)
}
func (m *GetValidPickupConfRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidPickupConfRequest.Merge(m, src)
}
func (m *GetValidPickupConfRequest) XXX_Size() int {
	return xxx_messageInfo_GetValidPickupConfRequest.Size(m)
}
func (m *GetValidPickupConfRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidPickupConfRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidPickupConfRequest proto.InternalMessageInfo

func (m *GetValidPickupConfRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetValidPickupConfRequest) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

type GetHolidaysRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineIdList           []string   `protobuf:"bytes,2,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	Days                 *uint32    `protobuf:"varint,3,opt,name=days" json:"days,omitempty"`
	StateLocationId      *int64     `protobuf:"varint,4,opt,name=state_location_id,json=stateLocationId" json:"state_location_id,omitempty"`
	Zipcode              *string    `protobuf:"bytes,5,opt,name=zipcode" json:"zipcode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetHolidaysRequest) Reset()         { *m = GetHolidaysRequest{} }
func (m *GetHolidaysRequest) String() string { return proto.CompactTextString(m) }
func (*GetHolidaysRequest) ProtoMessage()    {}
func (*GetHolidaysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{7}
}

func (m *GetHolidaysRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHolidaysRequest.Unmarshal(m, b)
}
func (m *GetHolidaysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHolidaysRequest.Marshal(b, m, deterministic)
}
func (m *GetHolidaysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHolidaysRequest.Merge(m, src)
}
func (m *GetHolidaysRequest) XXX_Size() int {
	return xxx_messageInfo_GetHolidaysRequest.Size(m)
}
func (m *GetHolidaysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHolidaysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHolidaysRequest proto.InternalMessageInfo

func (m *GetHolidaysRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetHolidaysRequest) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

func (m *GetHolidaysRequest) GetDays() uint32 {
	if m != nil && m.Days != nil {
		return *m.Days
	}
	return 0
}

func (m *GetHolidaysRequest) GetStateLocationId() int64 {
	if m != nil && m.StateLocationId != nil {
		return *m.StateLocationId
	}
	return 0
}

func (m *GetHolidaysRequest) GetZipcode() string {
	if m != nil && m.Zipcode != nil {
		return *m.Zipcode
	}
	return ""
}

type GetAllPickupGroupsRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAllPickupGroupsRequest) Reset()         { *m = GetAllPickupGroupsRequest{} }
func (m *GetAllPickupGroupsRequest) String() string { return proto.CompactTextString(m) }
func (*GetAllPickupGroupsRequest) ProtoMessage()    {}
func (*GetAllPickupGroupsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{8}
}

func (m *GetAllPickupGroupsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllPickupGroupsRequest.Unmarshal(m, b)
}
func (m *GetAllPickupGroupsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllPickupGroupsRequest.Marshal(b, m, deterministic)
}
func (m *GetAllPickupGroupsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllPickupGroupsRequest.Merge(m, src)
}
func (m *GetAllPickupGroupsRequest) XXX_Size() int {
	return xxx_messageInfo_GetAllPickupGroupsRequest.Size(m)
}
func (m *GetAllPickupGroupsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllPickupGroupsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllPickupGroupsRequest proto.InternalMessageInfo

func (m *GetAllPickupGroupsRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

type Volume struct {
	Date                 *string  `protobuf:"bytes,1,req,name=date" json:"date,omitempty"`
	Volume               *uint32  `protobuf:"varint,2,req,name=volume" json:"volume,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Volume) Reset()         { *m = Volume{} }
func (m *Volume) String() string { return proto.CompactTextString(m) }
func (*Volume) ProtoMessage()    {}
func (*Volume) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{9}
}

func (m *Volume) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Volume.Unmarshal(m, b)
}
func (m *Volume) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Volume.Marshal(b, m, deterministic)
}
func (m *Volume) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Volume.Merge(m, src)
}
func (m *Volume) XXX_Size() int {
	return xxx_messageInfo_Volume.Size(m)
}
func (m *Volume) XXX_DiscardUnknown() {
	xxx_messageInfo_Volume.DiscardUnknown(m)
}

var xxx_messageInfo_Volume proto.InternalMessageInfo

func (m *Volume) GetDate() string {
	if m != nil && m.Date != nil {
		return *m.Date
	}
	return ""
}

func (m *Volume) GetVolume() uint32 {
	if m != nil && m.Volume != nil {
		return *m.Volume
	}
	return 0
}

type PickupInfo struct {
	ShipByDatetime       *uint32   `protobuf:"varint,1,req,name=ship_by_datetime,json=shipByDatetime" json:"ship_by_datetime,omitempty"`
	LineIdList           []string  `protobuf:"bytes,2,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	ReleaseTime          *uint32   `protobuf:"varint,3,opt,name=release_time,json=releaseTime" json:"release_time,omitempty"`
	PayTime              *uint32   `protobuf:"varint,4,opt,name=pay_time,json=payTime" json:"pay_time,omitempty"`
	DaysToShip           *int32    `protobuf:"varint,5,opt,name=days_to_ship,json=daysToShip" json:"days_to_ship,omitempty"`
	StateLocationId      *uint32   `protobuf:"varint,6,req,name=state_location_id,json=stateLocationId" json:"state_location_id,omitempty"`
	Volumes              []*Volume `protobuf:"bytes,7,rep,name=volumes" json:"volumes,omitempty"`
	BuyerTimeslotId      *uint32   `protobuf:"varint,8,opt,name=buyer_timeslot_id,json=buyerTimeslotId" json:"buyer_timeslot_id,omitempty"`
	Zipcode              *string   `protobuf:"bytes,9,opt,name=zipcode" json:"zipcode,omitempty"`
	ClientGroupIdList    []string  `protobuf:"bytes,10,rep,name=client_group_id_list,json=clientGroupIdList" json:"client_group_id_list,omitempty"`
	IsChangePickupDate   *bool     `protobuf:"varint,11,opt,name=is_change_pickup_date,json=isChangePickupDate" json:"is_change_pickup_date,omitempty"`
	IsB2C                *bool     `protobuf:"varint,12,opt,name=is_b2c,json=isB2c" json:"is_b2c,omitempty"`
	Region               *string   `protobuf:"bytes,13,opt,name=region" json:"region,omitempty"`
	Acl2Time             *uint32   `protobuf:"varint,14,opt,name=acl2_time,json=acl2Time" json:"acl2_time,omitempty"`
	IsRapidSla           *bool     `protobuf:"varint,15,opt,name=is_rapid_sla,json=isRapidSla" json:"is_rapid_sla,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *PickupInfo) Reset()         { *m = PickupInfo{} }
func (m *PickupInfo) String() string { return proto.CompactTextString(m) }
func (*PickupInfo) ProtoMessage()    {}
func (*PickupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{10}
}

func (m *PickupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupInfo.Unmarshal(m, b)
}
func (m *PickupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupInfo.Marshal(b, m, deterministic)
}
func (m *PickupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupInfo.Merge(m, src)
}
func (m *PickupInfo) XXX_Size() int {
	return xxx_messageInfo_PickupInfo.Size(m)
}
func (m *PickupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PickupInfo proto.InternalMessageInfo

func (m *PickupInfo) GetShipByDatetime() uint32 {
	if m != nil && m.ShipByDatetime != nil {
		return *m.ShipByDatetime
	}
	return 0
}

func (m *PickupInfo) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

func (m *PickupInfo) GetReleaseTime() uint32 {
	if m != nil && m.ReleaseTime != nil {
		return *m.ReleaseTime
	}
	return 0
}

func (m *PickupInfo) GetPayTime() uint32 {
	if m != nil && m.PayTime != nil {
		return *m.PayTime
	}
	return 0
}

func (m *PickupInfo) GetDaysToShip() int32 {
	if m != nil && m.DaysToShip != nil {
		return *m.DaysToShip
	}
	return 0
}

func (m *PickupInfo) GetStateLocationId() uint32 {
	if m != nil && m.StateLocationId != nil {
		return *m.StateLocationId
	}
	return 0
}

func (m *PickupInfo) GetVolumes() []*Volume {
	if m != nil {
		return m.Volumes
	}
	return nil
}

func (m *PickupInfo) GetBuyerTimeslotId() uint32 {
	if m != nil && m.BuyerTimeslotId != nil {
		return *m.BuyerTimeslotId
	}
	return 0
}

func (m *PickupInfo) GetZipcode() string {
	if m != nil && m.Zipcode != nil {
		return *m.Zipcode
	}
	return ""
}

func (m *PickupInfo) GetClientGroupIdList() []string {
	if m != nil {
		return m.ClientGroupIdList
	}
	return nil
}

func (m *PickupInfo) GetIsChangePickupDate() bool {
	if m != nil && m.IsChangePickupDate != nil {
		return *m.IsChangePickupDate
	}
	return false
}

func (m *PickupInfo) GetIsB2C() bool {
	if m != nil && m.IsB2C != nil {
		return *m.IsB2C
	}
	return false
}

func (m *PickupInfo) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *PickupInfo) GetAcl2Time() uint32 {
	if m != nil && m.Acl2Time != nil {
		return *m.Acl2Time
	}
	return 0
}

func (m *PickupInfo) GetIsRapidSla() bool {
	if m != nil && m.IsRapidSla != nil {
		return *m.IsRapidSla
	}
	return false
}

type OpenLogisticPickupInfo struct {
	LineIdList           []string `protobuf:"bytes,1,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	MerchantType         *uint32  `protobuf:"varint,2,req,name=merchant_type,json=merchantType" json:"merchant_type,omitempty"`
	AccountGroup         *uint32  `protobuf:"varint,3,opt,name=account_group,json=accountGroup" json:"account_group,omitempty"`
	PickupLocationId     *int64   `protobuf:"varint,4,opt,name=pickup_location_id,json=pickupLocationId" json:"pickup_location_id,omitempty"`
	StartTime            *uint32  `protobuf:"varint,5,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenLogisticPickupInfo) Reset()         { *m = OpenLogisticPickupInfo{} }
func (m *OpenLogisticPickupInfo) String() string { return proto.CompactTextString(m) }
func (*OpenLogisticPickupInfo) ProtoMessage()    {}
func (*OpenLogisticPickupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{11}
}

func (m *OpenLogisticPickupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenLogisticPickupInfo.Unmarshal(m, b)
}
func (m *OpenLogisticPickupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenLogisticPickupInfo.Marshal(b, m, deterministic)
}
func (m *OpenLogisticPickupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenLogisticPickupInfo.Merge(m, src)
}
func (m *OpenLogisticPickupInfo) XXX_Size() int {
	return xxx_messageInfo_OpenLogisticPickupInfo.Size(m)
}
func (m *OpenLogisticPickupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenLogisticPickupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OpenLogisticPickupInfo proto.InternalMessageInfo

func (m *OpenLogisticPickupInfo) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

func (m *OpenLogisticPickupInfo) GetMerchantType() uint32 {
	if m != nil && m.MerchantType != nil {
		return *m.MerchantType
	}
	return 0
}

func (m *OpenLogisticPickupInfo) GetAccountGroup() uint32 {
	if m != nil && m.AccountGroup != nil {
		return *m.AccountGroup
	}
	return 0
}

func (m *OpenLogisticPickupInfo) GetPickupLocationId() int64 {
	if m != nil && m.PickupLocationId != nil {
		return *m.PickupLocationId
	}
	return 0
}

func (m *OpenLogisticPickupInfo) GetStartTime() uint32 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

type ReturnPickupInfo struct {
	ShipByDays           *uint32  `protobuf:"varint,1,req,name=ship_by_days,json=shipByDays" json:"ship_by_days,omitempty"`
	LineIdList           []string `protobuf:"bytes,2,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	StateLocationId      *uint32  `protobuf:"varint,3,req,name=state_location_id,json=stateLocationId" json:"state_location_id,omitempty"`
	Zipcode              *string  `protobuf:"bytes,4,opt,name=zipcode" json:"zipcode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReturnPickupInfo) Reset()         { *m = ReturnPickupInfo{} }
func (m *ReturnPickupInfo) String() string { return proto.CompactTextString(m) }
func (*ReturnPickupInfo) ProtoMessage()    {}
func (*ReturnPickupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{12}
}

func (m *ReturnPickupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReturnPickupInfo.Unmarshal(m, b)
}
func (m *ReturnPickupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReturnPickupInfo.Marshal(b, m, deterministic)
}
func (m *ReturnPickupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReturnPickupInfo.Merge(m, src)
}
func (m *ReturnPickupInfo) XXX_Size() int {
	return xxx_messageInfo_ReturnPickupInfo.Size(m)
}
func (m *ReturnPickupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReturnPickupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReturnPickupInfo proto.InternalMessageInfo

func (m *ReturnPickupInfo) GetShipByDays() uint32 {
	if m != nil && m.ShipByDays != nil {
		return *m.ShipByDays
	}
	return 0
}

func (m *ReturnPickupInfo) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

func (m *ReturnPickupInfo) GetStateLocationId() uint32 {
	if m != nil && m.StateLocationId != nil {
		return *m.StateLocationId
	}
	return 0
}

func (m *ReturnPickupInfo) GetZipcode() string {
	if m != nil && m.Zipcode != nil {
		return *m.Zipcode
	}
	return ""
}

type CheckPickupTimeInfo struct {
	LineIdList           []string `protobuf:"bytes,1,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	Country              *string  `protobuf:"bytes,2,req,name=country" json:"country,omitempty"`
	StateLocationId      *int64   `protobuf:"varint,3,req,name=state_location_id,json=stateLocationId" json:"state_location_id,omitempty"`
	PickupTime           *uint32  `protobuf:"varint,4,req,name=pickup_time,json=pickupTime" json:"pickup_time,omitempty"`
	StartTime            *uint32  `protobuf:"varint,5,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	MerchantType         *uint32  `protobuf:"varint,6,opt,name=merchant_type,json=merchantType" json:"merchant_type,omitempty"`
	AccountGroup         *uint32  `protobuf:"varint,7,opt,name=account_group,json=accountGroup" json:"account_group,omitempty"`
	PickTimeRangeId      *uint32  `protobuf:"varint,8,opt,name=pick_time_range_id,json=pickTimeRangeId" json:"pick_time_range_id,omitempty"`
	Zipcode              *string  `protobuf:"bytes,9,opt,name=zipcode" json:"zipcode,omitempty"`
	ClientGroupIdList    []string `protobuf:"bytes,10,rep,name=client_group_id_list,json=clientGroupIdList" json:"client_group_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPickupTimeInfo) Reset()         { *m = CheckPickupTimeInfo{} }
func (m *CheckPickupTimeInfo) String() string { return proto.CompactTextString(m) }
func (*CheckPickupTimeInfo) ProtoMessage()    {}
func (*CheckPickupTimeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{13}
}

func (m *CheckPickupTimeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPickupTimeInfo.Unmarshal(m, b)
}
func (m *CheckPickupTimeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPickupTimeInfo.Marshal(b, m, deterministic)
}
func (m *CheckPickupTimeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPickupTimeInfo.Merge(m, src)
}
func (m *CheckPickupTimeInfo) XXX_Size() int {
	return xxx_messageInfo_CheckPickupTimeInfo.Size(m)
}
func (m *CheckPickupTimeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPickupTimeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPickupTimeInfo proto.InternalMessageInfo

func (m *CheckPickupTimeInfo) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

func (m *CheckPickupTimeInfo) GetCountry() string {
	if m != nil && m.Country != nil {
		return *m.Country
	}
	return ""
}

func (m *CheckPickupTimeInfo) GetStateLocationId() int64 {
	if m != nil && m.StateLocationId != nil {
		return *m.StateLocationId
	}
	return 0
}

func (m *CheckPickupTimeInfo) GetPickupTime() uint32 {
	if m != nil && m.PickupTime != nil {
		return *m.PickupTime
	}
	return 0
}

func (m *CheckPickupTimeInfo) GetStartTime() uint32 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *CheckPickupTimeInfo) GetMerchantType() uint32 {
	if m != nil && m.MerchantType != nil {
		return *m.MerchantType
	}
	return 0
}

func (m *CheckPickupTimeInfo) GetAccountGroup() uint32 {
	if m != nil && m.AccountGroup != nil {
		return *m.AccountGroup
	}
	return 0
}

func (m *CheckPickupTimeInfo) GetPickTimeRangeId() uint32 {
	if m != nil && m.PickTimeRangeId != nil {
		return *m.PickTimeRangeId
	}
	return 0
}

func (m *CheckPickupTimeInfo) GetZipcode() string {
	if m != nil && m.Zipcode != nil {
		return *m.Zipcode
	}
	return ""
}

func (m *CheckPickupTimeInfo) GetClientGroupIdList() []string {
	if m != nil {
		return m.ClientGroupIdList
	}
	return nil
}

type Day struct {
	Date                 *string  `protobuf:"bytes,1,req,name=date" json:"date,omitempty"`
	Value                *uint32  `protobuf:"varint,2,req,name=value" json:"value,omitempty"`
	VolumeLimit          *uint32  `protobuf:"varint,3,req,name=volume_limit,json=volumeLimit" json:"volume_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Day) Reset()         { *m = Day{} }
func (m *Day) String() string { return proto.CompactTextString(m) }
func (*Day) ProtoMessage()    {}
func (*Day) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{14}
}

func (m *Day) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Day.Unmarshal(m, b)
}
func (m *Day) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Day.Marshal(b, m, deterministic)
}
func (m *Day) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Day.Merge(m, src)
}
func (m *Day) XXX_Size() int {
	return xxx_messageInfo_Day.Size(m)
}
func (m *Day) XXX_DiscardUnknown() {
	xxx_messageInfo_Day.DiscardUnknown(m)
}

var xxx_messageInfo_Day proto.InternalMessageInfo

func (m *Day) GetDate() string {
	if m != nil && m.Date != nil {
		return *m.Date
	}
	return ""
}

func (m *Day) GetValue() uint32 {
	if m != nil && m.Value != nil {
		return *m.Value
	}
	return 0
}

func (m *Day) GetVolumeLimit() uint32 {
	if m != nil && m.VolumeLimit != nil {
		return *m.VolumeLimit
	}
	return 0
}

type Slot struct {
	Value                *uint32  `protobuf:"varint,1,req,name=value" json:"value,omitempty"`
	Time                 *string  `protobuf:"bytes,2,req,name=time" json:"time,omitempty"`
	StartTime            *uint32  `protobuf:"varint,3,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime              *uint32  `protobuf:"varint,4,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
	SlotStartHour        *uint32  `protobuf:"varint,5,opt,name=slot_start_hour,json=slotStartHour" json:"slot_start_hour,omitempty"`
	SlotCutoffHour       *uint32  `protobuf:"varint,6,opt,name=slot_cutoff_hour,json=slotCutoffHour" json:"slot_cutoff_hour,omitempty"`
	SlotCutoffMinute     *uint32  `protobuf:"varint,7,opt,name=slot_cutoff_minute,json=slotCutoffMinute" json:"slot_cutoff_minute,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Slot) Reset()         { *m = Slot{} }
func (m *Slot) String() string { return proto.CompactTextString(m) }
func (*Slot) ProtoMessage()    {}
func (*Slot) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{15}
}

func (m *Slot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Slot.Unmarshal(m, b)
}
func (m *Slot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Slot.Marshal(b, m, deterministic)
}
func (m *Slot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Slot.Merge(m, src)
}
func (m *Slot) XXX_Size() int {
	return xxx_messageInfo_Slot.Size(m)
}
func (m *Slot) XXX_DiscardUnknown() {
	xxx_messageInfo_Slot.DiscardUnknown(m)
}

var xxx_messageInfo_Slot proto.InternalMessageInfo

func (m *Slot) GetValue() uint32 {
	if m != nil && m.Value != nil {
		return *m.Value
	}
	return 0
}

func (m *Slot) GetTime() string {
	if m != nil && m.Time != nil {
		return *m.Time
	}
	return ""
}

func (m *Slot) GetStartTime() uint32 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *Slot) GetEndTime() uint32 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *Slot) GetSlotStartHour() uint32 {
	if m != nil && m.SlotStartHour != nil {
		return *m.SlotStartHour
	}
	return 0
}

func (m *Slot) GetSlotCutoffHour() uint32 {
	if m != nil && m.SlotCutoffHour != nil {
		return *m.SlotCutoffHour
	}
	return 0
}

func (m *Slot) GetSlotCutoffMinute() uint32 {
	if m != nil && m.SlotCutoffMinute != nil {
		return *m.SlotCutoffMinute
	}
	return 0
}

type Timeslot struct {
	Date                 *string  `protobuf:"bytes,1,req,name=date" json:"date,omitempty"`
	Value                *uint32  `protobuf:"varint,2,req,name=value" json:"value,omitempty"`
	Slots                []*Slot  `protobuf:"bytes,3,rep,name=slots" json:"slots,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Timeslot) Reset()         { *m = Timeslot{} }
func (m *Timeslot) String() string { return proto.CompactTextString(m) }
func (*Timeslot) ProtoMessage()    {}
func (*Timeslot) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{16}
}

func (m *Timeslot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Timeslot.Unmarshal(m, b)
}
func (m *Timeslot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Timeslot.Marshal(b, m, deterministic)
}
func (m *Timeslot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Timeslot.Merge(m, src)
}
func (m *Timeslot) XXX_Size() int {
	return xxx_messageInfo_Timeslot.Size(m)
}
func (m *Timeslot) XXX_DiscardUnknown() {
	xxx_messageInfo_Timeslot.DiscardUnknown(m)
}

var xxx_messageInfo_Timeslot proto.InternalMessageInfo

func (m *Timeslot) GetDate() string {
	if m != nil && m.Date != nil {
		return *m.Date
	}
	return ""
}

func (m *Timeslot) GetValue() uint32 {
	if m != nil && m.Value != nil {
		return *m.Value
	}
	return 0
}

func (m *Timeslot) GetSlots() []*Slot {
	if m != nil {
		return m.Slots
	}
	return nil
}

type PickupDays struct {
	Days                 []*Day      `protobuf:"bytes,1,rep,name=days" json:"days,omitempty"`
	Timeslots            []*Timeslot `protobuf:"bytes,2,rep,name=timeslots" json:"timeslots,omitempty"`
	PickupGroupId        *string     `protobuf:"bytes,3,req,name=pickup_group_id,json=pickupGroupId" json:"pickup_group_id,omitempty"`
	PickupCutoffHour     *uint32     `protobuf:"varint,4,opt,name=pickup_cutoff_hour,json=pickupCutoffHour" json:"pickup_cutoff_hour,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *PickupDays) Reset()         { *m = PickupDays{} }
func (m *PickupDays) String() string { return proto.CompactTextString(m) }
func (*PickupDays) ProtoMessage()    {}
func (*PickupDays) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{17}
}

func (m *PickupDays) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupDays.Unmarshal(m, b)
}
func (m *PickupDays) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupDays.Marshal(b, m, deterministic)
}
func (m *PickupDays) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupDays.Merge(m, src)
}
func (m *PickupDays) XXX_Size() int {
	return xxx_messageInfo_PickupDays.Size(m)
}
func (m *PickupDays) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupDays.DiscardUnknown(m)
}

var xxx_messageInfo_PickupDays proto.InternalMessageInfo

func (m *PickupDays) GetDays() []*Day {
	if m != nil {
		return m.Days
	}
	return nil
}

func (m *PickupDays) GetTimeslots() []*Timeslot {
	if m != nil {
		return m.Timeslots
	}
	return nil
}

func (m *PickupDays) GetPickupGroupId() string {
	if m != nil && m.PickupGroupId != nil {
		return *m.PickupGroupId
	}
	return ""
}

func (m *PickupDays) GetPickupCutoffHour() uint32 {
	if m != nil && m.PickupCutoffHour != nil {
		return *m.PickupCutoffHour
	}
	return 0
}

type GetPickupDaysResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	PickupDays           *PickupDays `protobuf:"bytes,2,req,name=pickup_days,json=pickupDays" json:"pickup_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPickupDaysResponse) Reset()         { *m = GetPickupDaysResponse{} }
func (m *GetPickupDaysResponse) String() string { return proto.CompactTextString(m) }
func (*GetPickupDaysResponse) ProtoMessage()    {}
func (*GetPickupDaysResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{18}
}

func (m *GetPickupDaysResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPickupDaysResponse.Unmarshal(m, b)
}
func (m *GetPickupDaysResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPickupDaysResponse.Marshal(b, m, deterministic)
}
func (m *GetPickupDaysResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPickupDaysResponse.Merge(m, src)
}
func (m *GetPickupDaysResponse) XXX_Size() int {
	return xxx_messageInfo_GetPickupDaysResponse.Size(m)
}
func (m *GetPickupDaysResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPickupDaysResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPickupDaysResponse proto.InternalMessageInfo

func (m *GetPickupDaysResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetPickupDaysResponse) GetPickupDays() *PickupDays {
	if m != nil {
		return m.PickupDays
	}
	return nil
}

type CheckPickupTimeResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CheckPickupTimeResponse) Reset()         { *m = CheckPickupTimeResponse{} }
func (m *CheckPickupTimeResponse) String() string { return proto.CompactTextString(m) }
func (*CheckPickupTimeResponse) ProtoMessage()    {}
func (*CheckPickupTimeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{19}
}

func (m *CheckPickupTimeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPickupTimeResponse.Unmarshal(m, b)
}
func (m *CheckPickupTimeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPickupTimeResponse.Marshal(b, m, deterministic)
}
func (m *CheckPickupTimeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPickupTimeResponse.Merge(m, src)
}
func (m *CheckPickupTimeResponse) XXX_Size() int {
	return xxx_messageInfo_CheckPickupTimeResponse.Size(m)
}
func (m *CheckPickupTimeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPickupTimeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPickupTimeResponse proto.InternalMessageInfo

func (m *CheckPickupTimeResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

type GetPickupTimeslotsResponse struct {
	RespHeader           *RespHeader   `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Timeslot             *TimeslotInfo `protobuf:"bytes,2,req,name=timeslot" json:"timeslot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPickupTimeslotsResponse) Reset()         { *m = GetPickupTimeslotsResponse{} }
func (m *GetPickupTimeslotsResponse) String() string { return proto.CompactTextString(m) }
func (*GetPickupTimeslotsResponse) ProtoMessage()    {}
func (*GetPickupTimeslotsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{20}
}

func (m *GetPickupTimeslotsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPickupTimeslotsResponse.Unmarshal(m, b)
}
func (m *GetPickupTimeslotsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPickupTimeslotsResponse.Marshal(b, m, deterministic)
}
func (m *GetPickupTimeslotsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPickupTimeslotsResponse.Merge(m, src)
}
func (m *GetPickupTimeslotsResponse) XXX_Size() int {
	return xxx_messageInfo_GetPickupTimeslotsResponse.Size(m)
}
func (m *GetPickupTimeslotsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPickupTimeslotsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPickupTimeslotsResponse proto.InternalMessageInfo

func (m *GetPickupTimeslotsResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetPickupTimeslotsResponse) GetTimeslot() *TimeslotInfo {
	if m != nil {
		return m.Timeslot
	}
	return nil
}

type QueryPickupTimeslotsResponse struct {
	RespHeader           *RespHeader         `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Timeslots            []*FullTimeslotInfo `protobuf:"bytes,2,rep,name=timeslots" json:"timeslots,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *QueryPickupTimeslotsResponse) Reset()         { *m = QueryPickupTimeslotsResponse{} }
func (m *QueryPickupTimeslotsResponse) String() string { return proto.CompactTextString(m) }
func (*QueryPickupTimeslotsResponse) ProtoMessage()    {}
func (*QueryPickupTimeslotsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{21}
}

func (m *QueryPickupTimeslotsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPickupTimeslotsResponse.Unmarshal(m, b)
}
func (m *QueryPickupTimeslotsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPickupTimeslotsResponse.Marshal(b, m, deterministic)
}
func (m *QueryPickupTimeslotsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPickupTimeslotsResponse.Merge(m, src)
}
func (m *QueryPickupTimeslotsResponse) XXX_Size() int {
	return xxx_messageInfo_QueryPickupTimeslotsResponse.Size(m)
}
func (m *QueryPickupTimeslotsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPickupTimeslotsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPickupTimeslotsResponse proto.InternalMessageInfo

func (m *QueryPickupTimeslotsResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *QueryPickupTimeslotsResponse) GetTimeslots() []*FullTimeslotInfo {
	if m != nil {
		return m.Timeslots
	}
	return nil
}

type GetValidPickupConfResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	PickupConf           *PickupConf `protobuf:"bytes,2,opt,name=pickup_conf,json=pickupConf" json:"pickup_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetValidPickupConfResponse) Reset()         { *m = GetValidPickupConfResponse{} }
func (m *GetValidPickupConfResponse) String() string { return proto.CompactTextString(m) }
func (*GetValidPickupConfResponse) ProtoMessage()    {}
func (*GetValidPickupConfResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{22}
}

func (m *GetValidPickupConfResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidPickupConfResponse.Unmarshal(m, b)
}
func (m *GetValidPickupConfResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidPickupConfResponse.Marshal(b, m, deterministic)
}
func (m *GetValidPickupConfResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidPickupConfResponse.Merge(m, src)
}
func (m *GetValidPickupConfResponse) XXX_Size() int {
	return xxx_messageInfo_GetValidPickupConfResponse.Size(m)
}
func (m *GetValidPickupConfResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidPickupConfResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidPickupConfResponse proto.InternalMessageInfo

func (m *GetValidPickupConfResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetValidPickupConfResponse) GetPickupConf() *PickupConf {
	if m != nil {
		return m.PickupConf
	}
	return nil
}

type GetHolidaysResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Holidays             []string    `protobuf:"bytes,2,rep,name=holidays" json:"holidays,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetHolidaysResponse) Reset()         { *m = GetHolidaysResponse{} }
func (m *GetHolidaysResponse) String() string { return proto.CompactTextString(m) }
func (*GetHolidaysResponse) ProtoMessage()    {}
func (*GetHolidaysResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{23}
}

func (m *GetHolidaysResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHolidaysResponse.Unmarshal(m, b)
}
func (m *GetHolidaysResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHolidaysResponse.Marshal(b, m, deterministic)
}
func (m *GetHolidaysResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHolidaysResponse.Merge(m, src)
}
func (m *GetHolidaysResponse) XXX_Size() int {
	return xxx_messageInfo_GetHolidaysResponse.Size(m)
}
func (m *GetHolidaysResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHolidaysResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHolidaysResponse proto.InternalMessageInfo

func (m *GetHolidaysResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetHolidaysResponse) GetHolidays() []string {
	if m != nil {
		return m.Holidays
	}
	return nil
}

type GetAllPickupGroupsResponse struct {
	RespHeader           *RespHeader    `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	PickupGroups         []*PickupGroup `protobuf:"bytes,2,rep,name=pickup_groups,json=pickupGroups" json:"pickup_groups,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetAllPickupGroupsResponse) Reset()         { *m = GetAllPickupGroupsResponse{} }
func (m *GetAllPickupGroupsResponse) String() string { return proto.CompactTextString(m) }
func (*GetAllPickupGroupsResponse) ProtoMessage()    {}
func (*GetAllPickupGroupsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{24}
}

func (m *GetAllPickupGroupsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllPickupGroupsResponse.Unmarshal(m, b)
}
func (m *GetAllPickupGroupsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllPickupGroupsResponse.Marshal(b, m, deterministic)
}
func (m *GetAllPickupGroupsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllPickupGroupsResponse.Merge(m, src)
}
func (m *GetAllPickupGroupsResponse) XXX_Size() int {
	return xxx_messageInfo_GetAllPickupGroupsResponse.Size(m)
}
func (m *GetAllPickupGroupsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllPickupGroupsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllPickupGroupsResponse proto.InternalMessageInfo

func (m *GetAllPickupGroupsResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetAllPickupGroupsResponse) GetPickupGroups() []*PickupGroup {
	if m != nil {
		return m.PickupGroups
	}
	return nil
}

type TimeslotInfo struct {
	StartHour            *uint32  `protobuf:"varint,1,req,name=start_hour,json=startHour" json:"start_hour,omitempty"`
	StartMinute          *uint32  `protobuf:"varint,2,req,name=start_minute,json=startMinute" json:"start_minute,omitempty"`
	StartSecond          *uint32  `protobuf:"varint,3,req,name=start_second,json=startSecond" json:"start_second,omitempty"`
	EndHour              *uint32  `protobuf:"varint,4,req,name=end_hour,json=endHour" json:"end_hour,omitempty"`
	EndMinute            *uint32  `protobuf:"varint,5,req,name=end_minute,json=endMinute" json:"end_minute,omitempty"`
	EndSecond            *uint32  `protobuf:"varint,6,req,name=end_second,json=endSecond" json:"end_second,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeslotInfo) Reset()         { *m = TimeslotInfo{} }
func (m *TimeslotInfo) String() string { return proto.CompactTextString(m) }
func (*TimeslotInfo) ProtoMessage()    {}
func (*TimeslotInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{25}
}

func (m *TimeslotInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeslotInfo.Unmarshal(m, b)
}
func (m *TimeslotInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeslotInfo.Marshal(b, m, deterministic)
}
func (m *TimeslotInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeslotInfo.Merge(m, src)
}
func (m *TimeslotInfo) XXX_Size() int {
	return xxx_messageInfo_TimeslotInfo.Size(m)
}
func (m *TimeslotInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeslotInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TimeslotInfo proto.InternalMessageInfo

func (m *TimeslotInfo) GetStartHour() uint32 {
	if m != nil && m.StartHour != nil {
		return *m.StartHour
	}
	return 0
}

func (m *TimeslotInfo) GetStartMinute() uint32 {
	if m != nil && m.StartMinute != nil {
		return *m.StartMinute
	}
	return 0
}

func (m *TimeslotInfo) GetStartSecond() uint32 {
	if m != nil && m.StartSecond != nil {
		return *m.StartSecond
	}
	return 0
}

func (m *TimeslotInfo) GetEndHour() uint32 {
	if m != nil && m.EndHour != nil {
		return *m.EndHour
	}
	return 0
}

func (m *TimeslotInfo) GetEndMinute() uint32 {
	if m != nil && m.EndMinute != nil {
		return *m.EndMinute
	}
	return 0
}

func (m *TimeslotInfo) GetEndSecond() uint32 {
	if m != nil && m.EndSecond != nil {
		return *m.EndSecond
	}
	return 0
}

type FullTimeslotInfo struct {
	Id                   *uint64  `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	Type                 *uint32  `protobuf:"varint,2,req,name=type" json:"type,omitempty"`
	PickupCountry        *string  `protobuf:"bytes,3,req,name=pickup_country,json=pickupCountry" json:"pickup_country,omitempty"`
	DeliverCountry       *string  `protobuf:"bytes,4,req,name=deliver_country,json=deliverCountry" json:"deliver_country,omitempty"`
	Value                *uint32  `protobuf:"varint,5,req,name=value" json:"value,omitempty"`
	StartTimeHour        *uint32  `protobuf:"varint,6,req,name=start_time_hour,json=startTimeHour" json:"start_time_hour,omitempty"`
	StartTimeMin         *uint32  `protobuf:"varint,7,req,name=start_time_min,json=startTimeMin" json:"start_time_min,omitempty"`
	StartTimeSecond      *uint32  `protobuf:"varint,8,req,name=start_time_second,json=startTimeSecond" json:"start_time_second,omitempty"`
	EndTimeHour          *uint32  `protobuf:"varint,9,req,name=end_time_hour,json=endTimeHour" json:"end_time_hour,omitempty"`
	EndTimeMin           *uint32  `protobuf:"varint,10,req,name=end_time_min,json=endTimeMin" json:"end_time_min,omitempty"`
	EndTimeSecond        *uint32  `protobuf:"varint,11,req,name=end_time_second,json=endTimeSecond" json:"end_time_second,omitempty"`
	TimeSlotRemark       *string  `protobuf:"bytes,12,req,name=time_slot_remark,json=timeSlotRemark" json:"time_slot_remark,omitempty"`
	Status               *uint32  `protobuf:"varint,13,req,name=status" json:"status,omitempty"`
	Ctime                *uint32  `protobuf:"varint,14,req,name=ctime" json:"ctime,omitempty"`
	Mtime                *uint32  `protobuf:"varint,15,req,name=mtime" json:"mtime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FullTimeslotInfo) Reset()         { *m = FullTimeslotInfo{} }
func (m *FullTimeslotInfo) String() string { return proto.CompactTextString(m) }
func (*FullTimeslotInfo) ProtoMessage()    {}
func (*FullTimeslotInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{26}
}

func (m *FullTimeslotInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FullTimeslotInfo.Unmarshal(m, b)
}
func (m *FullTimeslotInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FullTimeslotInfo.Marshal(b, m, deterministic)
}
func (m *FullTimeslotInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FullTimeslotInfo.Merge(m, src)
}
func (m *FullTimeslotInfo) XXX_Size() int {
	return xxx_messageInfo_FullTimeslotInfo.Size(m)
}
func (m *FullTimeslotInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FullTimeslotInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FullTimeslotInfo proto.InternalMessageInfo

func (m *FullTimeslotInfo) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *FullTimeslotInfo) GetType() uint32 {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return 0
}

func (m *FullTimeslotInfo) GetPickupCountry() string {
	if m != nil && m.PickupCountry != nil {
		return *m.PickupCountry
	}
	return ""
}

func (m *FullTimeslotInfo) GetDeliverCountry() string {
	if m != nil && m.DeliverCountry != nil {
		return *m.DeliverCountry
	}
	return ""
}

func (m *FullTimeslotInfo) GetValue() uint32 {
	if m != nil && m.Value != nil {
		return *m.Value
	}
	return 0
}

func (m *FullTimeslotInfo) GetStartTimeHour() uint32 {
	if m != nil && m.StartTimeHour != nil {
		return *m.StartTimeHour
	}
	return 0
}

func (m *FullTimeslotInfo) GetStartTimeMin() uint32 {
	if m != nil && m.StartTimeMin != nil {
		return *m.StartTimeMin
	}
	return 0
}

func (m *FullTimeslotInfo) GetStartTimeSecond() uint32 {
	if m != nil && m.StartTimeSecond != nil {
		return *m.StartTimeSecond
	}
	return 0
}

func (m *FullTimeslotInfo) GetEndTimeHour() uint32 {
	if m != nil && m.EndTimeHour != nil {
		return *m.EndTimeHour
	}
	return 0
}

func (m *FullTimeslotInfo) GetEndTimeMin() uint32 {
	if m != nil && m.EndTimeMin != nil {
		return *m.EndTimeMin
	}
	return 0
}

func (m *FullTimeslotInfo) GetEndTimeSecond() uint32 {
	if m != nil && m.EndTimeSecond != nil {
		return *m.EndTimeSecond
	}
	return 0
}

func (m *FullTimeslotInfo) GetTimeSlotRemark() string {
	if m != nil && m.TimeSlotRemark != nil {
		return *m.TimeSlotRemark
	}
	return ""
}

func (m *FullTimeslotInfo) GetStatus() uint32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *FullTimeslotInfo) GetCtime() uint32 {
	if m != nil && m.Ctime != nil {
		return *m.Ctime
	}
	return 0
}

func (m *FullTimeslotInfo) GetMtime() uint32 {
	if m != nil && m.Mtime != nil {
		return *m.Mtime
	}
	return 0
}

type PickupConf struct {
	Id                           *uint64  `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	PickupGroupId                *string  `protobuf:"bytes,2,req,name=pickup_group_id,json=pickupGroupId" json:"pickup_group_id,omitempty"`
	DestinationRegion            *string  `protobuf:"bytes,3,req,name=destination_region,json=destinationRegion" json:"destination_region,omitempty"`
	IsExtend                     *uint32  `protobuf:"varint,4,req,name=is_extend,json=isExtend" json:"is_extend,omitempty"`
	ExtendDays                   *uint32  `protobuf:"varint,5,req,name=extend_days,json=extendDays" json:"extend_days,omitempty"`
	UseReleaseTime               *uint32  `protobuf:"varint,6,req,name=use_release_time,json=useReleaseTime" json:"use_release_time,omitempty"`
	IsNonWorkingDayPickupAllowed *uint32  `protobuf:"varint,7,req,name=is_non_working_day_pickup_allowed,json=isNonWorkingDayPickupAllowed" json:"is_non_working_day_pickup_allowed,omitempty"`
	ReturnDays                   *uint32  `protobuf:"varint,8,req,name=return_days,json=returnDays" json:"return_days,omitempty"`
	AdvanceDays                  *uint32  `protobuf:"varint,9,req,name=advance_days,json=advanceDays" json:"advance_days,omitempty"`
	ReturnCutoffHour             *uint32  `protobuf:"varint,10,req,name=return_cutoff_hour,json=returnCutoffHour" json:"return_cutoff_hour,omitempty"`
	ReturnCutoffHourDays         *uint32  `protobuf:"varint,11,req,name=return_cutoff_hour_days,json=returnCutoffHourDays" json:"return_cutoff_hour_days,omitempty"`
	PickupDateFormat             *string  `protobuf:"bytes,12,req,name=pickup_date_format,json=pickupDateFormat" json:"pickup_date_format,omitempty"`
	PickupCutoffHour             *uint32  `protobuf:"varint,13,req,name=pickup_cutoff_hour,json=pickupCutoffHour" json:"pickup_cutoff_hour,omitempty"`
	IsHaveTimeSlot               *uint32  `protobuf:"varint,14,req,name=is_have_time_slot,json=isHaveTimeSlot" json:"is_have_time_slot,omitempty"`
	SlotNum                      *uint32  `protobuf:"varint,15,req,name=slot_num,json=slotNum" json:"slot_num,omitempty"`
	ExtendSlotNum                *uint32  `protobuf:"varint,16,req,name=extend_slot_num,json=extendSlotNum" json:"extend_slot_num,omitempty"`
	EffectiveTime                *uint32  `protobuf:"varint,17,req,name=effective_time,json=effectiveTime" json:"effective_time,omitempty"`
	EnableStatus                 *uint32  `protobuf:"varint,18,req,name=enable_status,json=enableStatus" json:"enable_status,omitempty"`
	DailyMaxVolume               *uint32  `protobuf:"varint,19,req,name=daily_max_volume,json=dailyMaxVolume" json:"daily_max_volume,omitempty"`
	DailyControlStatus           *uint32  `protobuf:"varint,20,req,name=daily_control_status,json=dailyControlStatus" json:"daily_control_status,omitempty"`
	DailyMinDaysExtend           *uint32  `protobuf:"varint,21,req,name=daily_min_days_extend,json=dailyMinDaysExtend" json:"daily_min_days_extend,omitempty"`
	DailyControlBeginTime        *uint32  `protobuf:"varint,22,req,name=daily_control_begin_time,json=dailyControlBeginTime" json:"daily_control_begin_time,omitempty"`
	DailyControlEndTime          *uint32  `protobuf:"varint,23,req,name=daily_control_end_time,json=dailyControlEndTime" json:"daily_control_end_time,omitempty"`
	Ctime                        *uint32  `protobuf:"varint,24,req,name=ctime" json:"ctime,omitempty"`
	Mtime                        *uint32  `protobuf:"varint,25,req,name=mtime" json:"mtime,omitempty"`
	XXX_NoUnkeyedLiteral         struct{} `json:"-"`
	XXX_unrecognized             []byte   `json:"-"`
	XXX_sizecache                int32    `json:"-"`
}

func (m *PickupConf) Reset()         { *m = PickupConf{} }
func (m *PickupConf) String() string { return proto.CompactTextString(m) }
func (*PickupConf) ProtoMessage()    {}
func (*PickupConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{27}
}

func (m *PickupConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupConf.Unmarshal(m, b)
}
func (m *PickupConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupConf.Marshal(b, m, deterministic)
}
func (m *PickupConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupConf.Merge(m, src)
}
func (m *PickupConf) XXX_Size() int {
	return xxx_messageInfo_PickupConf.Size(m)
}
func (m *PickupConf) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupConf.DiscardUnknown(m)
}

var xxx_messageInfo_PickupConf proto.InternalMessageInfo

func (m *PickupConf) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *PickupConf) GetPickupGroupId() string {
	if m != nil && m.PickupGroupId != nil {
		return *m.PickupGroupId
	}
	return ""
}

func (m *PickupConf) GetDestinationRegion() string {
	if m != nil && m.DestinationRegion != nil {
		return *m.DestinationRegion
	}
	return ""
}

func (m *PickupConf) GetIsExtend() uint32 {
	if m != nil && m.IsExtend != nil {
		return *m.IsExtend
	}
	return 0
}

func (m *PickupConf) GetExtendDays() uint32 {
	if m != nil && m.ExtendDays != nil {
		return *m.ExtendDays
	}
	return 0
}

func (m *PickupConf) GetUseReleaseTime() uint32 {
	if m != nil && m.UseReleaseTime != nil {
		return *m.UseReleaseTime
	}
	return 0
}

func (m *PickupConf) GetIsNonWorkingDayPickupAllowed() uint32 {
	if m != nil && m.IsNonWorkingDayPickupAllowed != nil {
		return *m.IsNonWorkingDayPickupAllowed
	}
	return 0
}

func (m *PickupConf) GetReturnDays() uint32 {
	if m != nil && m.ReturnDays != nil {
		return *m.ReturnDays
	}
	return 0
}

func (m *PickupConf) GetAdvanceDays() uint32 {
	if m != nil && m.AdvanceDays != nil {
		return *m.AdvanceDays
	}
	return 0
}

func (m *PickupConf) GetReturnCutoffHour() uint32 {
	if m != nil && m.ReturnCutoffHour != nil {
		return *m.ReturnCutoffHour
	}
	return 0
}

func (m *PickupConf) GetReturnCutoffHourDays() uint32 {
	if m != nil && m.ReturnCutoffHourDays != nil {
		return *m.ReturnCutoffHourDays
	}
	return 0
}

func (m *PickupConf) GetPickupDateFormat() string {
	if m != nil && m.PickupDateFormat != nil {
		return *m.PickupDateFormat
	}
	return ""
}

func (m *PickupConf) GetPickupCutoffHour() uint32 {
	if m != nil && m.PickupCutoffHour != nil {
		return *m.PickupCutoffHour
	}
	return 0
}

func (m *PickupConf) GetIsHaveTimeSlot() uint32 {
	if m != nil && m.IsHaveTimeSlot != nil {
		return *m.IsHaveTimeSlot
	}
	return 0
}

func (m *PickupConf) GetSlotNum() uint32 {
	if m != nil && m.SlotNum != nil {
		return *m.SlotNum
	}
	return 0
}

func (m *PickupConf) GetExtendSlotNum() uint32 {
	if m != nil && m.ExtendSlotNum != nil {
		return *m.ExtendSlotNum
	}
	return 0
}

func (m *PickupConf) GetEffectiveTime() uint32 {
	if m != nil && m.EffectiveTime != nil {
		return *m.EffectiveTime
	}
	return 0
}

func (m *PickupConf) GetEnableStatus() uint32 {
	if m != nil && m.EnableStatus != nil {
		return *m.EnableStatus
	}
	return 0
}

func (m *PickupConf) GetDailyMaxVolume() uint32 {
	if m != nil && m.DailyMaxVolume != nil {
		return *m.DailyMaxVolume
	}
	return 0
}

func (m *PickupConf) GetDailyControlStatus() uint32 {
	if m != nil && m.DailyControlStatus != nil {
		return *m.DailyControlStatus
	}
	return 0
}

func (m *PickupConf) GetDailyMinDaysExtend() uint32 {
	if m != nil && m.DailyMinDaysExtend != nil {
		return *m.DailyMinDaysExtend
	}
	return 0
}

func (m *PickupConf) GetDailyControlBeginTime() uint32 {
	if m != nil && m.DailyControlBeginTime != nil {
		return *m.DailyControlBeginTime
	}
	return 0
}

func (m *PickupConf) GetDailyControlEndTime() uint32 {
	if m != nil && m.DailyControlEndTime != nil {
		return *m.DailyControlEndTime
	}
	return 0
}

func (m *PickupConf) GetCtime() uint32 {
	if m != nil && m.Ctime != nil {
		return *m.Ctime
	}
	return 0
}

func (m *PickupConf) GetMtime() uint32 {
	if m != nil && m.Mtime != nil {
		return *m.Mtime
	}
	return 0
}

type PickupGroup struct {
	PickupGroupId        *string  `protobuf:"bytes,1,req,name=pickup_group_id,json=pickupGroupId" json:"pickup_group_id,omitempty"`
	PickupGroupName      *string  `protobuf:"bytes,2,req,name=pickup_group_name,json=pickupGroupName" json:"pickup_group_name,omitempty"`
	OriginRegion         *string  `protobuf:"bytes,3,req,name=origin_region,json=originRegion" json:"origin_region,omitempty"`
	DestinationRegion    *string  `protobuf:"bytes,4,req,name=destination_region,json=destinationRegion" json:"destination_region,omitempty"`
	PickupType           *uint32  `protobuf:"varint,5,req,name=pickup_type,json=pickupType" json:"pickup_type,omitempty"`
	LineIdList           []string `protobuf:"bytes,6,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	Ctime                *uint32  `protobuf:"varint,7,req,name=ctime" json:"ctime,omitempty"`
	Mtime                *uint32  `protobuf:"varint,8,req,name=mtime" json:"mtime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PickupGroup) Reset()         { *m = PickupGroup{} }
func (m *PickupGroup) String() string { return proto.CompactTextString(m) }
func (*PickupGroup) ProtoMessage()    {}
func (*PickupGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{28}
}

func (m *PickupGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupGroup.Unmarshal(m, b)
}
func (m *PickupGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupGroup.Marshal(b, m, deterministic)
}
func (m *PickupGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupGroup.Merge(m, src)
}
func (m *PickupGroup) XXX_Size() int {
	return xxx_messageInfo_PickupGroup.Size(m)
}
func (m *PickupGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupGroup.DiscardUnknown(m)
}

var xxx_messageInfo_PickupGroup proto.InternalMessageInfo

func (m *PickupGroup) GetPickupGroupId() string {
	if m != nil && m.PickupGroupId != nil {
		return *m.PickupGroupId
	}
	return ""
}

func (m *PickupGroup) GetPickupGroupName() string {
	if m != nil && m.PickupGroupName != nil {
		return *m.PickupGroupName
	}
	return ""
}

func (m *PickupGroup) GetOriginRegion() string {
	if m != nil && m.OriginRegion != nil {
		return *m.OriginRegion
	}
	return ""
}

func (m *PickupGroup) GetDestinationRegion() string {
	if m != nil && m.DestinationRegion != nil {
		return *m.DestinationRegion
	}
	return ""
}

func (m *PickupGroup) GetPickupType() uint32 {
	if m != nil && m.PickupType != nil {
		return *m.PickupType
	}
	return 0
}

func (m *PickupGroup) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

func (m *PickupGroup) GetCtime() uint32 {
	if m != nil && m.Ctime != nil {
		return *m.Ctime
	}
	return 0
}

func (m *PickupGroup) GetMtime() uint32 {
	if m != nil && m.Mtime != nil {
		return *m.Mtime
	}
	return 0
}

type GetProductPickupDaysRequest struct {
	ReqHeader            *ReqHeader     `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	PickupInfo           *PickupInfoReq `protobuf:"bytes,2,req,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetProductPickupDaysRequest) Reset()         { *m = GetProductPickupDaysRequest{} }
func (m *GetProductPickupDaysRequest) String() string { return proto.CompactTextString(m) }
func (*GetProductPickupDaysRequest) ProtoMessage()    {}
func (*GetProductPickupDaysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{29}
}

func (m *GetProductPickupDaysRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductPickupDaysRequest.Unmarshal(m, b)
}
func (m *GetProductPickupDaysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductPickupDaysRequest.Marshal(b, m, deterministic)
}
func (m *GetProductPickupDaysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductPickupDaysRequest.Merge(m, src)
}
func (m *GetProductPickupDaysRequest) XXX_Size() int {
	return xxx_messageInfo_GetProductPickupDaysRequest.Size(m)
}
func (m *GetProductPickupDaysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductPickupDaysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductPickupDaysRequest proto.InternalMessageInfo

func (m *GetProductPickupDaysRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetProductPickupDaysRequest) GetPickupInfo() *PickupInfoReq {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

type PickupInfoReq struct {
	Region               *string  `protobuf:"bytes,1,opt,name=region" json:"region,omitempty"`
	ProductId            *int32   `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	Zipcode              *string  `protobuf:"bytes,3,opt,name=zipcode" json:"zipcode,omitempty"`
	StateId              *uint32  `protobuf:"varint,4,opt,name=state_id,json=stateId" json:"state_id,omitempty"`
	FmLines              []string `protobuf:"bytes,5,rep,name=fm_lines,json=fmLines" json:"fm_lines,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PickupInfoReq) Reset()         { *m = PickupInfoReq{} }
func (m *PickupInfoReq) String() string { return proto.CompactTextString(m) }
func (*PickupInfoReq) ProtoMessage()    {}
func (*PickupInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{30}
}

func (m *PickupInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickupInfoReq.Unmarshal(m, b)
}
func (m *PickupInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickupInfoReq.Marshal(b, m, deterministic)
}
func (m *PickupInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickupInfoReq.Merge(m, src)
}
func (m *PickupInfoReq) XXX_Size() int {
	return xxx_messageInfo_PickupInfoReq.Size(m)
}
func (m *PickupInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PickupInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_PickupInfoReq proto.InternalMessageInfo

func (m *PickupInfoReq) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *PickupInfoReq) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *PickupInfoReq) GetZipcode() string {
	if m != nil && m.Zipcode != nil {
		return *m.Zipcode
	}
	return ""
}

func (m *PickupInfoReq) GetStateId() uint32 {
	if m != nil && m.StateId != nil {
		return *m.StateId
	}
	return 0
}

func (m *PickupInfoReq) GetFmLines() []string {
	if m != nil {
		return m.FmLines
	}
	return nil
}

type GetProductPickupDaysResponse struct {
	RespHeader           *RespHeader        `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	PickupDays           *ProductPickupDays `protobuf:"bytes,2,req,name=pickup_days,json=pickupDays" json:"pickup_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetProductPickupDaysResponse) Reset()         { *m = GetProductPickupDaysResponse{} }
func (m *GetProductPickupDaysResponse) String() string { return proto.CompactTextString(m) }
func (*GetProductPickupDaysResponse) ProtoMessage()    {}
func (*GetProductPickupDaysResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{31}
}

func (m *GetProductPickupDaysResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductPickupDaysResponse.Unmarshal(m, b)
}
func (m *GetProductPickupDaysResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductPickupDaysResponse.Marshal(b, m, deterministic)
}
func (m *GetProductPickupDaysResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductPickupDaysResponse.Merge(m, src)
}
func (m *GetProductPickupDaysResponse) XXX_Size() int {
	return xxx_messageInfo_GetProductPickupDaysResponse.Size(m)
}
func (m *GetProductPickupDaysResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductPickupDaysResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductPickupDaysResponse proto.InternalMessageInfo

func (m *GetProductPickupDaysResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetProductPickupDaysResponse) GetPickupDays() *ProductPickupDays {
	if m != nil {
		return m.PickupDays
	}
	return nil
}

type ProductPickupDays struct {
	PickupRecurringHolidays []uint32 `protobuf:"varint,1,rep,name=pickup_recurring_holidays,json=pickupRecurringHolidays" json:"pickup_recurring_holidays,omitempty"`
	PickupHolidays          []string `protobuf:"bytes,2,rep,name=pickup_holidays,json=pickupHolidays" json:"pickup_holidays,omitempty"`
	Status                  *int32   `protobuf:"varint,3,req,name=status" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *ProductPickupDays) Reset()         { *m = ProductPickupDays{} }
func (m *ProductPickupDays) String() string { return proto.CompactTextString(m) }
func (*ProductPickupDays) ProtoMessage()    {}
func (*ProductPickupDays) Descriptor() ([]byte, []int) {
	return fileDescriptor_0460f6140c0be873, []int{32}
}

func (m *ProductPickupDays) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductPickupDays.Unmarshal(m, b)
}
func (m *ProductPickupDays) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductPickupDays.Marshal(b, m, deterministic)
}
func (m *ProductPickupDays) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductPickupDays.Merge(m, src)
}
func (m *ProductPickupDays) XXX_Size() int {
	return xxx_messageInfo_ProductPickupDays.Size(m)
}
func (m *ProductPickupDays) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductPickupDays.DiscardUnknown(m)
}

var xxx_messageInfo_ProductPickupDays proto.InternalMessageInfo

func (m *ProductPickupDays) GetPickupRecurringHolidays() []uint32 {
	if m != nil {
		return m.PickupRecurringHolidays
	}
	return nil
}

func (m *ProductPickupDays) GetPickupHolidays() []string {
	if m != nil {
		return m.PickupHolidays
	}
	return nil
}

func (m *ProductPickupDays) GetStatus() int32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func init() {
	proto.RegisterType((*GetArrangedPickupDaysRequest)(nil), "lcos_protobuf.GetArrangedPickupDaysRequest")
	proto.RegisterType((*GetArrangedPickupDaysOpenLogisticRequest)(nil), "lcos_protobuf.GetArrangedPickupDaysOpenLogisticRequest")
	proto.RegisterType((*GetReturnPickupDaysRequest)(nil), "lcos_protobuf.GetReturnPickupDaysRequest")
	proto.RegisterType((*CheckPickupTimeRequest)(nil), "lcos_protobuf.CheckPickupTimeRequest")
	proto.RegisterType((*GetPickupTimeslotRequest)(nil), "lcos_protobuf.GetPickupTimeslotRequest")
	proto.RegisterType((*QueryPickupTimeslotsRequest)(nil), "lcos_protobuf.QueryPickupTimeslotsRequest")
	proto.RegisterType((*GetValidPickupConfRequest)(nil), "lcos_protobuf.GetValidPickupConfRequest")
	proto.RegisterType((*GetHolidaysRequest)(nil), "lcos_protobuf.GetHolidaysRequest")
	proto.RegisterType((*GetAllPickupGroupsRequest)(nil), "lcos_protobuf.GetAllPickupGroupsRequest")
	proto.RegisterType((*Volume)(nil), "lcos_protobuf.Volume")
	proto.RegisterType((*PickupInfo)(nil), "lcos_protobuf.PickupInfo")
	proto.RegisterType((*OpenLogisticPickupInfo)(nil), "lcos_protobuf.OpenLogisticPickupInfo")
	proto.RegisterType((*ReturnPickupInfo)(nil), "lcos_protobuf.ReturnPickupInfo")
	proto.RegisterType((*CheckPickupTimeInfo)(nil), "lcos_protobuf.CheckPickupTimeInfo")
	proto.RegisterType((*Day)(nil), "lcos_protobuf.Day")
	proto.RegisterType((*Slot)(nil), "lcos_protobuf.Slot")
	proto.RegisterType((*Timeslot)(nil), "lcos_protobuf.Timeslot")
	proto.RegisterType((*PickupDays)(nil), "lcos_protobuf.PickupDays")
	proto.RegisterType((*GetPickupDaysResponse)(nil), "lcos_protobuf.GetPickupDaysResponse")
	proto.RegisterType((*CheckPickupTimeResponse)(nil), "lcos_protobuf.CheckPickupTimeResponse")
	proto.RegisterType((*GetPickupTimeslotsResponse)(nil), "lcos_protobuf.GetPickupTimeslotsResponse")
	proto.RegisterType((*QueryPickupTimeslotsResponse)(nil), "lcos_protobuf.QueryPickupTimeslotsResponse")
	proto.RegisterType((*GetValidPickupConfResponse)(nil), "lcos_protobuf.GetValidPickupConfResponse")
	proto.RegisterType((*GetHolidaysResponse)(nil), "lcos_protobuf.GetHolidaysResponse")
	proto.RegisterType((*GetAllPickupGroupsResponse)(nil), "lcos_protobuf.GetAllPickupGroupsResponse")
	proto.RegisterType((*TimeslotInfo)(nil), "lcos_protobuf.TimeslotInfo")
	proto.RegisterType((*FullTimeslotInfo)(nil), "lcos_protobuf.FullTimeslotInfo")
	proto.RegisterType((*PickupConf)(nil), "lcos_protobuf.PickupConf")
	proto.RegisterType((*PickupGroup)(nil), "lcos_protobuf.PickupGroup")
	proto.RegisterType((*GetProductPickupDaysRequest)(nil), "lcos_protobuf.GetProductPickupDaysRequest")
	proto.RegisterType((*PickupInfoReq)(nil), "lcos_protobuf.PickupInfoReq")
	proto.RegisterType((*GetProductPickupDaysResponse)(nil), "lcos_protobuf.GetProductPickupDaysResponse")
	proto.RegisterType((*ProductPickupDays)(nil), "lcos_protobuf.ProductPickupDays")
}

func init() {
	proto.RegisterFile("lcos_pickup_window.proto", fileDescriptor_0460f6140c0be873)
}

var fileDescriptor_0460f6140c0be873 = []byte{
	// 2393 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x19, 0xcb, 0x6e, 0xdc, 0xc8,
	0x71, 0x39, 0xa3, 0xc7, 0x4c, 0xcd, 0x8c, 0x34, 0xa2, 0x25, 0x8b, 0x92, 0x6d, 0xac, 0xc4, 0xac,
	0xbd, 0xe3, 0x47, 0xbc, 0x59, 0x25, 0x0b, 0x03, 0x0b, 0x2c, 0x12, 0x5b, 0x5e, 0xcb, 0x02, 0x64,
	0x27, 0xa1, 0x1c, 0xef, 0x91, 0xa0, 0xc8, 0x1e, 0xa9, 0x61, 0x0e, 0x49, 0xb3, 0x49, 0xd9, 0x93,
	0x73, 0x8e, 0x01, 0x36, 0x41, 0x5e, 0x08, 0x90, 0xdc, 0x72, 0x09, 0x82, 0x7c, 0x44, 0x8e, 0x01,
	0x72, 0xc9, 0x17, 0xe4, 0x9e, 0x63, 0x7e, 0x20, 0x41, 0x57, 0x35, 0x39, 0x1c, 0x92, 0x23, 0x29,
	0x90, 0x82, 0xdc, 0xd8, 0x55, 0xd5, 0x5d, 0xd5, 0xf5, 0x2e, 0x36, 0x18, 0xbe, 0x1b, 0x0a, 0x3b,
	0xe2, 0xee, 0x9b, 0x34, 0xb2, 0xdf, 0xf1, 0xc0, 0x0b, 0xdf, 0x3d, 0x8c, 0xe2, 0x30, 0x09, 0xf5,
	0x1e, 0x61, 0xe4, 0xf7, 0x51, 0x3a, 0xdc, 0x5c, 0xc6, 0xe5, 0x91, 0x23, 0x18, 0xe1, 0xcd, 0x5f,
	0x68, 0x70, 0x73, 0x8f, 0x25, 0x8f, 0xe3, 0xd8, 0x09, 0x8e, 0x99, 0xf7, 0x03, 0x3c, 0xe2, 0xa9,
	0x33, 0x16, 0x16, 0x7b, 0x9b, 0x32, 0x91, 0xe8, 0x8f, 0x00, 0x62, 0xf6, 0xd6, 0x3e, 0x61, 0x8e,
	0xc7, 0x62, 0x43, 0xdb, 0x6a, 0x0c, 0x3a, 0x3b, 0xc6, 0xc3, 0xa9, 0x53, 0x1f, 0x5a, 0xec, 0xed,
	0x73, 0xc4, 0x5b, 0xed, 0x38, 0xfb, 0xd4, 0x3f, 0x87, 0x8e, 0x12, 0x88, 0x07, 0xc3, 0xd0, 0x68,
	0xe0, 0xce, 0x8d, 0xd2, 0x4e, 0xe2, 0xb7, 0x1f, 0x0c, 0x43, 0x0b, 0xa2, 0xfc, 0xdb, 0xfc, 0x93,
	0x06, 0x83, 0x5a, 0xa9, 0xbe, 0x1f, 0xb1, 0xe0, 0x20, 0x3c, 0xe6, 0x22, 0xe1, 0xee, 0xa5, 0x25,
	0x7c, 0x56, 0x27, 0xe1, 0xed, 0xd2, 0xce, 0x22, 0xc7, 0x19, 0xd2, 0xfe, 0x46, 0x83, 0xcd, 0x3d,
	0x96, 0x58, 0x2c, 0x49, 0xe3, 0xe0, 0x0a, 0x35, 0xf8, 0xbd, 0x3a, 0xf9, 0x3e, 0xac, 0xec, 0x9c,
	0x70, 0xad, 0x48, 0xf6, 0x6b, 0x0d, 0xae, 0xef, 0x9e, 0x30, 0xf7, 0x0d, 0xe1, 0x5f, 0xf1, 0x11,
	0xbb, 0xb4, 0x54, 0xbb, 0x75, 0x52, 0x99, 0xa5, 0x9d, 0x25, 0xa6, 0x15, 0xc1, 0xbe, 0x6e, 0x80,
	0xb1, 0xc7, 0x92, 0x09, 0x85, 0xf0, 0xc3, 0xe4, 0xd2, 0xa2, 0x6d, 0x41, 0xd7, 0xe7, 0x01, 0xb3,
	0xb9, 0x67, 0xfb, 0x5c, 0x24, 0x46, 0x63, 0xab, 0x39, 0x68, 0x5b, 0x20, 0x61, 0xfb, 0xde, 0x01,
	0x17, 0x89, 0xfe, 0x09, 0xac, 0x2a, 0xe1, 0x13, 0x3e, 0x62, 0x36, 0xba, 0x97, 0xcd, 0x3d, 0xa3,
	0xb9, 0xd5, 0x18, 0xf4, 0xac, 0x95, 0x68, 0xa2, 0x26, 0x89, 0xd9, 0xf7, 0xf4, 0x5b, 0x00, 0x22,
	0x71, 0xe2, 0x04, 0xe9, 0x8d, 0xb9, 0x2d, 0x6d, 0xd0, 0xb3, 0xda, 0x08, 0x91, 0x54, 0xfa, 0x37,
	0xa0, 0x37, 0x62, 0xb1, 0x7b, 0xe2, 0x04, 0x89, 0x9d, 0x8c, 0x23, 0x66, 0xcc, 0x23, 0x45, 0x37,
	0x03, 0xbe, 0x1a, 0x47, 0x48, 0xe4, 0xb8, 0x6e, 0x98, 0x06, 0x89, 0x7d, 0x1c, 0x87, 0x69, 0x64,
	0x2c, 0x10, 0x91, 0x02, 0xee, 0x49, 0x98, 0xf9, 0x47, 0x0d, 0x6e, 0xfc, 0x30, 0x65, 0xf1, 0x78,
	0x5a, 0x27, 0xe2, 0xff, 0xaa, 0x14, 0xad, 0x56, 0x29, 0xe6, 0x29, 0x6c, 0xec, 0xb1, 0xe4, 0xb5,
	0xe3, 0x73, 0x15, 0x9a, 0xbb, 0x61, 0x30, 0xfc, 0xdf, 0x0b, 0x6a, 0xfe, 0x55, 0x03, 0x7d, 0x8f,
	0x25, 0xcf, 0x43, 0x9f, 0x7b, 0x57, 0x11, 0x60, 0xe7, 0xab, 0x46, 0x87, 0x39, 0xc9, 0x49, 0xa9,
	0x02, 0xbf, 0xf5, 0x7b, 0xb0, 0x22, 0x12, 0x27, 0x61, 0xb6, 0x1f, 0xba, 0x4e, 0xc2, 0xc3, 0x40,
	0xea, 0x4a, 0x7a, 0x46, 0xd3, 0x5a, 0x46, 0xc4, 0x81, 0x82, 0xef, 0x7b, 0xba, 0x01, 0x8b, 0x3f,
	0xe6, 0x91, 0x1b, 0x7a, 0xe4, 0x19, 0x6d, 0x2b, 0x5b, 0x9a, 0xaf, 0x50, 0x87, 0x8f, 0x7d, 0x9f,
	0x34, 0x88, 0x4e, 0x70, 0xe9, 0x1b, 0x99, 0xdf, 0x81, 0x85, 0xd7, 0xa1, 0x9f, 0x8e, 0x18, 0x49,
	0x9e, 0x30, 0xdc, 0xdc, 0xb6, 0xf0, 0x5b, 0xbf, 0x0e, 0x0b, 0xa7, 0x88, 0xc5, 0xa8, 0xed, 0x59,
	0x6a, 0x65, 0xfe, 0x79, 0x0e, 0x60, 0x92, 0x41, 0xf4, 0x01, 0xf4, 0xc5, 0x09, 0x8f, 0xec, 0xa3,
	0xb1, 0x2d, 0xb7, 0xa1, 0xe7, 0x6b, 0xb8, 0x61, 0x49, 0xc2, 0x9f, 0x8c, 0x9f, 0x2a, 0xe8, 0x05,
	0x14, 0xb8, 0x0d, 0xdd, 0x98, 0xf9, 0xcc, 0x11, 0x8c, 0x22, 0x88, 0x14, 0xd9, 0x51, 0x30, 0x8c,
	0xa1, 0x0d, 0x68, 0x45, 0xce, 0xb8, 0x18, 0x60, 0x8b, 0x91, 0x33, 0x7e, 0xa5, 0xce, 0x97, 0x2a,
	0xb7, 0x93, 0xd0, 0x96, 0x9c, 0x51, 0x87, 0xf3, 0x16, 0x48, 0xd8, 0xab, 0xf0, 0xf0, 0x84, 0x47,
	0xf5, 0xc6, 0x58, 0x40, 0x61, 0x2b, 0xc6, 0xf8, 0x04, 0x16, 0xe9, 0xc2, 0xc2, 0x58, 0xdc, 0x6a,
	0x0e, 0x3a, 0x3b, 0x6b, 0x25, 0x95, 0x92, 0xea, 0xac, 0x8c, 0x4a, 0x1e, 0x7e, 0x94, 0x8e, 0x59,
	0x8c, 0xb2, 0xc9, 0x68, 0x94, 0x87, 0xb7, 0x50, 0xc4, 0x65, 0x44, 0x64, 0x51, 0x3a, 0x6d, 0xe9,
	0xf6, 0x94, 0xa5, 0x65, 0x78, 0xb9, 0x3e, 0x67, 0x59, 0xf4, 0xe7, 0xca, 0x02, 0x54, 0xd6, 0x0a,
	0xe1, 0xd0, 0xfe, 0x4a, 0x67, 0x9f, 0xc2, 0x1a, 0x17, 0xb6, 0xcc, 0x1f, 0xc7, 0x2c, 0x2b, 0xea,
	0x68, 0xcb, 0xce, 0x96, 0x36, 0x68, 0x59, 0x3a, 0x17, 0xbb, 0x88, 0xcb, 0x4a, 0x4d, 0xc2, 0xf4,
	0x35, 0x58, 0xe0, 0xc2, 0x3e, 0xda, 0x71, 0x8d, 0x2e, 0xd2, 0xcc, 0x73, 0xf1, 0x64, 0xc7, 0x95,
	0x06, 0x8f, 0xd9, 0x31, 0x0f, 0x03, 0xa3, 0x87, 0x32, 0xa9, 0x95, 0x7e, 0x03, 0xda, 0x8e, 0xeb,
	0xef, 0x90, 0xce, 0x97, 0xf0, 0x42, 0x2d, 0x09, 0xc8, 0x94, 0xce, 0x85, 0x1d, 0x3b, 0x11, 0xf7,
	0x6c, 0xe1, 0x3b, 0xc6, 0x32, 0x9e, 0x08, 0x5c, 0x58, 0x12, 0x74, 0xe8, 0x3b, 0xe6, 0xdf, 0x35,
	0xb8, 0x5e, 0x5f, 0x17, 0x2b, 0x1e, 0xa1, 0x55, 0x3c, 0xa2, 0x92, 0x32, 0xc9, 0x17, 0xcf, 0x49,
	0x99, 0xcd, 0x6a, 0xca, 0xd4, 0x1f, 0x80, 0xae, 0xb4, 0x53, 0x8d, 0xc4, 0x3e, 0x61, 0x0a, 0xd6,
	0x9f, 0xce, 0xe4, 0xf3, 0xa5, 0x4c, 0x6e, 0xfe, 0x4e, 0x83, 0x7e, 0xb9, 0x96, 0xca, 0xdb, 0x4c,
	0x22, 0x61, 0x2c, 0x54, 0x14, 0x40, 0x16, 0x05, 0x63, 0x71, 0x81, 0x08, 0xa8, 0xf5, 0xd0, 0x66,
	0xbd, 0x87, 0x16, 0x9c, 0x68, 0x6e, 0x3a, 0x5d, 0xfc, 0xbb, 0x01, 0xd7, 0x6a, 0x8a, 0xea, 0x05,
	0xf4, 0x6d, 0xc0, 0x22, 0xea, 0x2c, 0x1e, 0xa3, 0xa6, 0xdb, 0x56, 0xb6, 0x9c, 0x2d, 0x59, 0x4d,
	0x22, 0xfb, 0x30, 0xaf, 0xfa, 0x2a, 0x4e, 0x51, 0x11, 0x93, 0xd2, 0x70, 0x8e, 0x7a, 0xab, 0x56,
	0x5f, 0xb8, 0x48, 0xa1, 0x5c, 0xac, 0xb1, 0xfa, 0x7d, 0xb2, 0x7a, 0xa9, 0x56, 0xa9, 0xa8, 0x94,
	0x98, 0x62, 0xf9, 0xbe, 0xba, 0xa8, 0x34, 0x2d, 0x68, 0x3e, 0x75, 0xc6, 0xb5, 0x79, 0x75, 0x15,
	0xe6, 0x4f, 0x1d, 0x3f, 0xcd, 0x5c, 0x99, 0x16, 0x32, 0xf5, 0x51, 0x22, 0xb1, 0x7d, 0x3e, 0xe2,
	0x89, 0xb2, 0x79, 0x87, 0x60, 0x07, 0x12, 0x64, 0xfe, 0x53, 0x83, 0xb9, 0x43, 0x3f, 0x4c, 0x26,
	0x27, 0x68, 0xc5, 0x13, 0x74, 0x98, 0x43, 0x6d, 0x92, 0xdd, 0xf0, 0xbb, 0xa4, 0xe7, 0x66, 0x59,
	0xcf, 0x1b, 0xd0, 0x62, 0x81, 0x37, 0x95, 0x4c, 0x59, 0xe0, 0x21, 0xea, 0x0e, 0x2c, 0x63, 0x0e,
	0xa3, 0xed, 0x27, 0x61, 0x1a, 0x2b, 0x33, 0xf5, 0x24, 0xf8, 0x50, 0x42, 0x9f, 0x87, 0x69, 0x8c,
	0xe9, 0x5f, 0xd2, 0xb9, 0x69, 0x12, 0x0e, 0x87, 0x44, 0x48, 0xd6, 0x5a, 0x92, 0xf0, 0x5d, 0x04,
	0x23, 0xe5, 0x03, 0xd0, 0x8b, 0x94, 0x23, 0x1e, 0xa4, 0x09, 0x53, 0x46, 0xeb, 0x4f, 0x68, 0x5f,
	0x20, 0xdc, 0xb4, 0xa1, 0x95, 0xe5, 0xcb, 0xff, 0x42, 0x8b, 0x77, 0x61, 0x1e, 0xfb, 0x20, 0xa3,
	0x89, 0x29, 0xfb, 0x5a, 0x29, 0x65, 0x4b, 0xed, 0x59, 0x44, 0x61, 0xfe, 0x45, 0xcb, 0xca, 0x18,
	0x86, 0xe6, 0x1d, 0x55, 0xbb, 0x35, 0xdc, 0xa8, 0x97, 0x36, 0x3e, 0x75, 0xc6, 0xaa, 0x9e, 0x7f,
	0x06, 0xed, 0x2c, 0xbf, 0x0b, 0x8c, 0xdf, 0xce, 0xce, 0x7a, 0x89, 0x38, 0xef, 0x50, 0x27, 0x94,
	0x52, 0x9d, 0x2a, 0x22, 0x32, 0x07, 0x42, 0x0b, 0xb7, 0xad, 0x5e, 0x34, 0xa9, 0xe8, 0xfb, 0x5e,
	0x21, 0x4b, 0x15, 0x15, 0x4a, 0xb6, 0x51, 0x59, 0x6a, 0xa2, 0x52, 0xf3, 0x6b, 0x0d, 0xd6, 0xf2,
	0xc6, 0x98, 0xa6, 0x08, 0x11, 0x85, 0x81, 0x60, 0x72, 0x9e, 0x8a, 0x99, 0x88, 0xa6, 0x9b, 0x82,
	0x8d, 0x4a, 0x53, 0x20, 0x22, 0xd5, 0x15, 0x40, 0x9c, 0x7f, 0x17, 0x66, 0x31, 0xd4, 0xc8, 0x59,
	0xb3, 0x18, 0xf2, 0x54, 0x81, 0x2d, 0xbf, 0xcd, 0x1f, 0xc1, 0x7a, 0x65, 0x84, 0xb8, 0xbc, 0x48,
	0xe6, 0xcf, 0x69, 0x68, 0xaa, 0x74, 0xbb, 0x57, 0x70, 0xdb, 0x47, 0xd0, 0xca, 0xcc, 0xa4, 0xae,
	0x7a, 0x63, 0x86, 0x3d, 0x71, 0x2e, 0xc9, 0x89, 0xcd, 0xdf, 0x6a, 0x70, 0xb3, 0xbe, 0x07, 0xbf,
	0x02, 0xa9, 0xbe, 0xa8, 0xba, 0x59, 0x79, 0x96, 0x7b, 0x96, 0xfa, 0xfe, 0x94, 0x68, 0x93, 0x1d,
	0xe6, 0x2f, 0x49, 0x5f, 0x95, 0xa6, 0xfb, 0x4a, 0xbd, 0xc3, 0x0d, 0x83, 0xa1, 0xd1, 0xd8, 0xd2,
	0x66, 0x7a, 0x07, 0xf2, 0x54, 0xde, 0x21, 0xbf, 0xcd, 0x11, 0x5c, 0x9b, 0xea, 0xc8, 0xaf, 0x40,
	0x9c, 0x4d, 0x68, 0x9d, 0xa8, 0xf3, 0x54, 0x39, 0xcd, 0xd7, 0xd2, 0x42, 0x9b, 0x75, 0x6d, 0xf3,
	0x15, 0xb0, 0xfd, 0x2e, 0xf4, 0x8a, 0xf1, 0x9c, 0xd9, 0x68, 0xb3, 0x56, 0x0f, 0xc8, 0xd7, 0xea,
	0x16, 0x22, 0x5d, 0x98, 0x7f, 0xd3, 0xa0, 0x5b, 0xb4, 0xde, 0x24, 0x55, 0x63, 0xc4, 0x53, 0x66,
	0xa7, 0x54, 0x8d, 0xd9, 0x73, 0x1b, 0xba, 0x84, 0x56, 0x79, 0x93, 0xd2, 0x5e, 0x07, 0x61, 0x94,
	0x32, 0x27, 0x24, 0x82, 0xb9, 0x61, 0x90, 0xb5, 0x0d, 0x44, 0x72, 0x88, 0xa0, 0x2c, 0xe1, 0xab,
	0xa4, 0xd2, 0x50, 0x09, 0x1f, 0x19, 0xdc, 0x02, 0x90, 0x28, 0x75, 0xfc, 0x3c, 0xf1, 0x67, 0x81,
	0xa7, 0x0e, 0x57, 0x68, 0x75, 0xf4, 0x42, 0x8e, 0xa6, 0x83, 0xcd, 0x7f, 0x35, 0xa1, 0x5f, 0x76,
	0x48, 0x7d, 0x09, 0x1a, 0xdc, 0xc3, 0xab, 0xcc, 0x59, 0x0d, 0xee, 0x61, 0x85, 0x9a, 0xf4, 0x70,
	0xf8, 0xad, 0xdf, 0x86, 0xa5, 0xdc, 0x9d, 0xa8, 0xef, 0x98, 0xca, 0x8b, 0xbb, 0xaa, 0xfb, 0xf8,
	0x18, 0x96, 0x3d, 0xe6, 0xf3, 0x53, 0x16, 0xe7, 0x74, 0x73, 0x48, 0xb7, 0xa4, 0xc0, 0x19, 0x61,
	0x5e, 0x17, 0xe6, 0x8b, 0x75, 0x41, 0x56, 0xb3, 0xbc, 0x0e, 0x66, 0x45, 0xaa, 0x81, 0xd5, 0x2c,
	0x2b, 0x86, 0xa8, 0x84, 0x8f, 0x60, 0xa9, 0x40, 0x37, 0xe2, 0x81, 0xb1, 0x48, 0xfd, 0x66, 0x4e,
	0xf6, 0x82, 0x07, 0xaa, 0x15, 0xca, 0xa8, 0x94, 0x4a, 0x5a, 0x79, 0x93, 0x46, 0x84, 0x4a, 0xe3,
	0x26, 0xf4, 0xb2, 0x12, 0x4b, 0x7c, 0xdb, 0x64, 0x15, 0x55, 0x67, 0x91, 0xeb, 0x16, 0x74, 0x73,
	0x1a, 0xc9, 0x13, 0xa8, 0x5f, 0x52, 0x24, 0x92, 0xe3, 0x1d, 0x58, 0xce, 0x29, 0x14, 0xbf, 0x0e,
	0xc9, 0xaf, 0x88, 0x14, 0xb7, 0x01, 0xf4, 0x89, 0x46, 0x16, 0xda, 0x98, 0x8d, 0x9c, 0xf8, 0x8d,
	0xd1, 0x25, 0x3d, 0x49, 0x38, 0xd6, 0x3f, 0x84, 0xca, 0x66, 0x5f, 0x76, 0x6d, 0xa9, 0x30, 0x7a,
	0x34, 0xdd, 0xd1, 0x4a, 0xea, 0xcf, 0x55, 0x8d, 0x3e, 0xea, 0x0f, 0x17, 0x12, 0x3a, 0x42, 0xe8,
	0x32, 0x41, 0x71, 0x61, 0xfe, 0xb4, 0x95, 0x95, 0x50, 0x19, 0xdd, 0x15, 0x73, 0xd7, 0xd4, 0xbc,
	0x46, 0x5d, 0xcd, 0xfb, 0x26, 0xe8, 0x1e, 0x13, 0x09, 0x0f, 0xa8, 0xad, 0x54, 0x33, 0x08, 0xb9,
	0xc1, 0x4a, 0x01, 0x63, 0xe5, 0xe3, 0x08, 0x17, 0x36, 0x7b, 0x9f, 0xb0, 0xc0, 0x53, 0x4e, 0xdc,
	0xe2, 0xe2, 0x4b, 0x5c, 0xcb, 0xce, 0x93, 0x30, 0x54, 0xbb, 0xe6, 0x95, 0x26, 0x11, 0x84, 0x75,
	0x7e, 0x00, 0xfd, 0x54, 0x30, 0x7b, 0x6a, 0xcc, 0x24, 0x57, 0x58, 0x4a, 0x05, 0xb3, 0x0a, 0x93,
	0xe6, 0x1e, 0x6c, 0x73, 0x61, 0x07, 0x61, 0x60, 0xbf, 0x0b, 0xe3, 0x37, 0x3c, 0x38, 0x96, 0x47,
	0x66, 0x13, 0x96, 0xe3, 0xfb, 0xe1, 0x3b, 0xe6, 0x29, 0xf7, 0xb8, 0xc9, 0xc5, 0xcb, 0x30, 0xf8,
	0x8a, 0xc8, 0x9e, 0x3a, 0xaa, 0x22, 0x3c, 0x26, 0x1a, 0x29, 0x53, 0x8c, 0xb3, 0x02, 0xc9, 0x44,
	0x8e, 0x02, 0x04, 0x42, 0x99, 0xb6, 0xa1, 0xeb, 0x78, 0xa7, 0x4e, 0xe0, 0x32, 0xa2, 0x50, 0x2e,
	0xa2, 0x60, 0x48, 0xf2, 0x00, 0x74, 0x75, 0x46, 0xb1, 0x2f, 0x20, 0x47, 0xe9, 0x13, 0xa6, 0xd0,
	0x6a, 0x7d, 0x06, 0xeb, 0x55, 0x6a, 0x3a, 0x9b, 0xdc, 0x66, 0xb5, 0xbc, 0x25, 0x63, 0x52, 0x18,
	0x20, 0xed, 0x61, 0x18, 0x8f, 0x9c, 0x44, 0xf9, 0x4f, 0x3f, 0xca, 0xe7, 0xc7, 0x67, 0x08, 0x9f,
	0xd1, 0xaa, 0x90, 0x37, 0x55, 0x5a, 0x15, 0xfd, 0x2e, 0xac, 0x70, 0x61, 0x9f, 0x38, 0xa7, 0xcc,
	0xce, 0x3d, 0x54, 0xf9, 0xd8, 0x12, 0x17, 0xcf, 0x9d, 0x53, 0x54, 0x3a, 0xb6, 0xb7, 0x1b, 0xd0,
	0x42, 0xff, 0x0d, 0xd2, 0x91, 0xf2, 0xb7, 0x45, 0xb9, 0x7e, 0x99, 0x8e, 0x30, 0x0e, 0xc8, 0xbc,
	0x39, 0x45, 0x5f, 0xc5, 0x01, 0x82, 0x0f, 0x15, 0xdd, 0x6d, 0x58, 0x62, 0xc3, 0x21, 0x73, 0x13,
	0xae, 0xf8, 0x19, 0x2b, 0x8a, 0x2c, 0x83, 0x66, 0x73, 0x06, 0x0b, 0x9c, 0x23, 0x9f, 0xd9, 0x2a,
	0x16, 0x74, 0x8a, 0x76, 0x02, 0x1e, 0x52, 0x44, 0x0c, 0xa0, 0xef, 0x39, 0xdc, 0x1f, 0xdb, 0x23,
	0xe7, 0xbd, 0xad, 0xfe, 0x88, 0x5c, 0x23, 0xc1, 0x11, 0xfe, 0xc2, 0x79, 0xaf, 0xfe, 0xa2, 0x7c,
	0x0b, 0x56, 0x89, 0xd2, 0x0d, 0x83, 0x24, 0x0e, 0xfd, 0xec, 0xd4, 0x55, 0xa4, 0xd6, 0x11, 0xb7,
	0x4b, 0x28, 0x75, 0xf6, 0xa7, 0xb0, 0xa6, 0xce, 0xe6, 0xe4, 0x1d, 0x99, 0x5f, 0xaf, 0x15, 0xb6,
	0xbc, 0xe0, 0xe8, 0x26, 0xca, 0xc3, 0x1f, 0x81, 0x31, 0xcd, 0xe4, 0x88, 0x1d, 0xf3, 0x80, 0x2e,
	0x79, 0x1d, 0x77, 0xad, 0x15, 0x19, 0x3d, 0x91, 0x58, 0xbc, 0xec, 0xb7, 0xe1, 0xfa, 0xf4, 0xc6,
	0xbc, 0xf5, 0x5f, 0xc7, 0x6d, 0xd7, 0x8a, 0xdb, 0xbe, 0x54, 0x63, 0x40, 0x9e, 0x0e, 0x8c, 0xda,
	0x74, 0xb0, 0x51, 0x4c, 0x07, 0x7f, 0x68, 0x40, 0xa7, 0x50, 0xf0, 0xea, 0xe2, 0x5f, 0xab, 0x8b,
	0xff, 0x7b, 0xb0, 0x32, 0x45, 0x17, 0x38, 0xf9, 0x14, 0xb3, 0x5c, 0xa0, 0x7c, 0xe9, 0x90, 0xc5,
	0xc2, 0x98, 0xcb, 0x0b, 0x4f, 0xa5, 0x89, 0x2e, 0x01, 0x55, 0x86, 0xa8, 0x4f, 0x28, 0x73, 0xb3,
	0x12, 0x4a, 0x61, 0x5a, 0xa5, 0x9f, 0xb2, 0xc5, 0x69, 0x55, 0xd6, 0xa8, 0xf2, 0xd8, 0xbc, 0x50,
	0x19, 0x9b, 0x73, 0x35, 0x2d, 0xd6, 0xaa, 0xa9, 0x55, 0x54, 0xd3, 0xaf, 0x34, 0xb8, 0x21, 0x7b,
	0xd9, 0x38, 0xf4, 0x52, 0x37, 0xb9, 0xc2, 0x17, 0x80, 0x2f, 0xea, 0xfe, 0xb5, 0xdf, 0x9c, 0xfd,
	0x86, 0xc2, 0xde, 0x96, 0x7f, 0xff, 0xf7, 0xa6, 0xb0, 0x85, 0x1f, 0x42, 0xda, 0xd4, 0x0f, 0xa1,
	0x5b, 0x00, 0x11, 0x49, 0x9f, 0xe5, 0xf4, 0x79, 0xab, 0xad, 0x20, 0xd3, 0x63, 0x74, 0x73, 0x7a,
	0x8c, 0x96, 0x91, 0x8d, 0xff, 0x10, 0xd4, 0x9f, 0x17, 0x19, 0xd9, 0x72, 0xbd, 0x8f, 0x9d, 0xc9,
	0x70, 0x64, 0x4b, 0x95, 0xca, 0xac, 0x2d, 0xf5, 0xbb, 0x38, 0x1c, 0x1d, 0xc8, 0xa5, 0xf9, 0x7b,
	0x7a, 0x75, 0xaa, 0x51, 0xd8, 0x15, 0x34, 0x72, 0x8f, 0xeb, 0x86, 0x9d, 0xad, 0xb2, 0xd2, 0x2a,
	0xac, 0x8b, 0x33, 0xcf, 0xcf, 0x34, 0x58, 0xa9, 0x50, 0xe8, 0x9f, 0xc3, 0x86, 0x3a, 0x38, 0x66,
	0x6e, 0x1a, 0xc7, 0xb2, 0x80, 0xe4, 0x9d, 0xaa, 0x9c, 0x32, 0x7b, 0xd6, 0x3a, 0x11, 0x58, 0x19,
	0x3e, 0x6b, 0x8c, 0x65, 0xb7, 0xa3, 0xf6, 0x96, 0x7a, 0x5b, 0xd5, 0x2b, 0xe5, 0x84, 0x93, 0x2a,
	0xde, 0x44, 0x2b, 0xa8, 0xd5, 0xce, 0x3f, 0x5a, 0xb0, 0x7e, 0xe0, 0x86, 0x82, 0xe4, 0xf9, 0x0a,
	0xdf, 0xf8, 0x0e, 0x59, 0x7c, 0xca, 0x5d, 0xa6, 0xfb, 0x38, 0x33, 0x56, 0x5f, 0xcb, 0xf4, 0xfb,
	0xa5, 0x5b, 0x9f, 0xf5, 0xd2, 0xb7, 0xf9, 0x51, 0x95, 0xb8, 0x6a, 0x19, 0xf3, 0x03, 0xfd, 0x27,
	0x1a, 0x6c, 0x9f, 0xfb, 0x38, 0xa7, 0x3f, 0xba, 0x08, 0xeb, 0x9a, 0xe7, 0xbc, 0x0b, 0x8b, 0x71,
	0x82, 0x93, 0x47, 0xf9, 0xd1, 0x4d, 0xbf, 0x5b, 0xdd, 0x3e, 0xe3, 0x61, 0xee, 0xc2, 0x9c, 0x8e,
	0x60, 0xb9, 0x34, 0x01, 0xeb, 0xb7, 0xcf, 0x7e, 0xef, 0xca, 0x38, 0xdc, 0x39, 0x8f, 0x2c, 0xe7,
	0xc1, 0x61, 0xa5, 0x32, 0x0d, 0xeb, 0x1f, 0xcf, 0x12, 0xb0, 0xf4, 0x62, 0xb6, 0x79, 0xf7, 0x3c,
	0xc2, 0xe2, 0x75, 0xde, 0xc2, 0x6a, 0xdd, 0x90, 0xab, 0xdf, 0x2b, 0x1d, 0x72, 0xc6, 0x6b, 0xd4,
	0xe6, 0xfd, 0x0b, 0xd1, 0xe6, 0x2c, 0xdf, 0xe0, 0xbb, 0x4d, 0x69, 0x76, 0xd5, 0x07, 0x55, 0xa9,
	0xeb, 0xdf, 0x94, 0xea, 0xee, 0x37, 0x63, 0x10, 0x36, 0x3f, 0xd0, 0x5f, 0x43, 0xa7, 0x30, 0x92,
	0xea, 0xdb, 0xd5, 0xbd, 0xa5, 0x07, 0xa4, 0x4d, 0xf3, 0x2c, 0x92, 0xd2, 0x25, 0x4a, 0xa3, 0x67,
	0xdd, 0x25, 0xea, 0x1f, 0x75, 0xea, 0x2e, 0x31, 0x63, 0x8e, 0x25, 0x23, 0xd5, 0x25, 0xc8, 0x8a,
	0x91, 0xce, 0x28, 0x3b, 0x9b, 0xf7, 0x2f, 0x44, 0x9b, 0xb1, 0xfc, 0x4f, 0x00, 0x00, 0x00, 0xff,
	0xff, 0xcf, 0x84, 0x9a, 0x4e, 0x45, 0x20, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosPickupWindowServiceClient is the client API for LcosPickupWindowService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosPickupWindowServiceClient interface {
	// 给LFS点线调用，用于获取揽收时间
	GetArrangedPickupDays(ctx context.Context, in *GetArrangedPickupDaysRequest, opts ...grpc.CallOption) (*GetPickupDaysResponse, error)
	// 给开发物流调用，用于获取揽收时间
	GetArrangedPickupDaysOpenLogistic(ctx context.Context, in *GetArrangedPickupDaysOpenLogisticRequest, opts ...grpc.CallOption) (*GetPickupDaysResponse, error)
	// 给LFS掉线调用，用于获取退货揽收时间
	GetReturnPickupDays(ctx context.Context, in *GetReturnPickupDaysRequest, opts ...grpc.CallOption) (*GetPickupDaysResponse, error)
	// 目前给lcs调用，用于校验pickup_time是否合法
	CheckPickupTime(ctx context.Context, in *CheckPickupTimeRequest, opts ...grpc.CallOption) (*CheckPickupTimeResponse, error)
	// 给lcs调用，用于获取timeslot
	GetPickupTimeslot(ctx context.Context, in *GetPickupTimeslotRequest, opts ...grpc.CallOption) (*GetPickupTimeslotsResponse, error)
	// ！！！请勿使用。仅用给sls兼容期间调用，不提供给其他服务调用
	QueryPickupTimeslots(ctx context.Context, in *QueryPickupTimeslotsRequest, opts ...grpc.CallOption) (*QueryPickupTimeslotsResponse, error)
	// ！！！请勿使用。仅用给sls兼容期间调用，不提供给其他服务调用
	GetValidPickupConf(ctx context.Context, in *GetValidPickupConfRequest, opts ...grpc.CallOption) (*GetValidPickupConfResponse, error)
	// 用于获取指定line的未来days的假期
	GetHolidays(ctx context.Context, in *GetHolidaysRequest, opts ...grpc.CallOption) (*GetHolidaysResponse, error)
	// 目前仅给LFS调用，用于获取pickup group和line的映射关系，用于单量统计
	GetAllPickupGroups(ctx context.Context, in *GetAllPickupGroupsRequest, opts ...grpc.CallOption) (*GetAllPickupGroupsResponse, error)
	// 给lps调用，用于获取pickup noworking days
	GetProductPickupDays(ctx context.Context, in *GetProductPickupDaysRequest, opts ...grpc.CallOption) (*GetProductPickupDaysResponse, error)
}

type lcosPickupWindowServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosPickupWindowServiceClient(cc grpc.ClientConnInterface) LcosPickupWindowServiceClient {
	return &lcosPickupWindowServiceClient{cc}
}

func (c *lcosPickupWindowServiceClient) GetArrangedPickupDays(ctx context.Context, in *GetArrangedPickupDaysRequest, opts ...grpc.CallOption) (*GetPickupDaysResponse, error) {
	out := new(GetPickupDaysResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/GetArrangedPickupDays", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosPickupWindowServiceClient) GetArrangedPickupDaysOpenLogistic(ctx context.Context, in *GetArrangedPickupDaysOpenLogisticRequest, opts ...grpc.CallOption) (*GetPickupDaysResponse, error) {
	out := new(GetPickupDaysResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/GetArrangedPickupDaysOpenLogistic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosPickupWindowServiceClient) GetReturnPickupDays(ctx context.Context, in *GetReturnPickupDaysRequest, opts ...grpc.CallOption) (*GetPickupDaysResponse, error) {
	out := new(GetPickupDaysResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/GetReturnPickupDays", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosPickupWindowServiceClient) CheckPickupTime(ctx context.Context, in *CheckPickupTimeRequest, opts ...grpc.CallOption) (*CheckPickupTimeResponse, error) {
	out := new(CheckPickupTimeResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/CheckPickupTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosPickupWindowServiceClient) GetPickupTimeslot(ctx context.Context, in *GetPickupTimeslotRequest, opts ...grpc.CallOption) (*GetPickupTimeslotsResponse, error) {
	out := new(GetPickupTimeslotsResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/GetPickupTimeslot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosPickupWindowServiceClient) QueryPickupTimeslots(ctx context.Context, in *QueryPickupTimeslotsRequest, opts ...grpc.CallOption) (*QueryPickupTimeslotsResponse, error) {
	out := new(QueryPickupTimeslotsResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/QueryPickupTimeslots", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosPickupWindowServiceClient) GetValidPickupConf(ctx context.Context, in *GetValidPickupConfRequest, opts ...grpc.CallOption) (*GetValidPickupConfResponse, error) {
	out := new(GetValidPickupConfResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/GetValidPickupConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosPickupWindowServiceClient) GetHolidays(ctx context.Context, in *GetHolidaysRequest, opts ...grpc.CallOption) (*GetHolidaysResponse, error) {
	out := new(GetHolidaysResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/GetHolidays", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosPickupWindowServiceClient) GetAllPickupGroups(ctx context.Context, in *GetAllPickupGroupsRequest, opts ...grpc.CallOption) (*GetAllPickupGroupsResponse, error) {
	out := new(GetAllPickupGroupsResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/GetAllPickupGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosPickupWindowServiceClient) GetProductPickupDays(ctx context.Context, in *GetProductPickupDaysRequest, opts ...grpc.CallOption) (*GetProductPickupDaysResponse, error) {
	out := new(GetProductPickupDaysResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosPickupWindowService/GetProductPickupDays", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosPickupWindowServiceServer is the server API for LcosPickupWindowService service.
type LcosPickupWindowServiceServer interface {
	// 给LFS点线调用，用于获取揽收时间
	GetArrangedPickupDays(context.Context, *GetArrangedPickupDaysRequest) (*GetPickupDaysResponse, error)
	// 给开发物流调用，用于获取揽收时间
	GetArrangedPickupDaysOpenLogistic(context.Context, *GetArrangedPickupDaysOpenLogisticRequest) (*GetPickupDaysResponse, error)
	// 给LFS掉线调用，用于获取退货揽收时间
	GetReturnPickupDays(context.Context, *GetReturnPickupDaysRequest) (*GetPickupDaysResponse, error)
	// 目前给lcs调用，用于校验pickup_time是否合法
	CheckPickupTime(context.Context, *CheckPickupTimeRequest) (*CheckPickupTimeResponse, error)
	// 给lcs调用，用于获取timeslot
	GetPickupTimeslot(context.Context, *GetPickupTimeslotRequest) (*GetPickupTimeslotsResponse, error)
	// ！！！请勿使用。仅用给sls兼容期间调用，不提供给其他服务调用
	QueryPickupTimeslots(context.Context, *QueryPickupTimeslotsRequest) (*QueryPickupTimeslotsResponse, error)
	// ！！！请勿使用。仅用给sls兼容期间调用，不提供给其他服务调用
	GetValidPickupConf(context.Context, *GetValidPickupConfRequest) (*GetValidPickupConfResponse, error)
	// 用于获取指定line的未来days的假期
	GetHolidays(context.Context, *GetHolidaysRequest) (*GetHolidaysResponse, error)
	// 目前仅给LFS调用，用于获取pickup group和line的映射关系，用于单量统计
	GetAllPickupGroups(context.Context, *GetAllPickupGroupsRequest) (*GetAllPickupGroupsResponse, error)
	// 给lps调用，用于获取pickup noworking days
	GetProductPickupDays(context.Context, *GetProductPickupDaysRequest) (*GetProductPickupDaysResponse, error)
}

// UnimplementedLcosPickupWindowServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLcosPickupWindowServiceServer struct {
}

func (*UnimplementedLcosPickupWindowServiceServer) GetArrangedPickupDays(ctx context.Context, req *GetArrangedPickupDaysRequest) (*GetPickupDaysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetArrangedPickupDays not implemented")
}
func (*UnimplementedLcosPickupWindowServiceServer) GetArrangedPickupDaysOpenLogistic(ctx context.Context, req *GetArrangedPickupDaysOpenLogisticRequest) (*GetPickupDaysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetArrangedPickupDaysOpenLogistic not implemented")
}
func (*UnimplementedLcosPickupWindowServiceServer) GetReturnPickupDays(ctx context.Context, req *GetReturnPickupDaysRequest) (*GetPickupDaysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReturnPickupDays not implemented")
}
func (*UnimplementedLcosPickupWindowServiceServer) CheckPickupTime(ctx context.Context, req *CheckPickupTimeRequest) (*CheckPickupTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPickupTime not implemented")
}
func (*UnimplementedLcosPickupWindowServiceServer) GetPickupTimeslot(ctx context.Context, req *GetPickupTimeslotRequest) (*GetPickupTimeslotsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPickupTimeslot not implemented")
}
func (*UnimplementedLcosPickupWindowServiceServer) QueryPickupTimeslots(ctx context.Context, req *QueryPickupTimeslotsRequest) (*QueryPickupTimeslotsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPickupTimeslots not implemented")
}
func (*UnimplementedLcosPickupWindowServiceServer) GetValidPickupConf(ctx context.Context, req *GetValidPickupConfRequest) (*GetValidPickupConfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetValidPickupConf not implemented")
}
func (*UnimplementedLcosPickupWindowServiceServer) GetHolidays(ctx context.Context, req *GetHolidaysRequest) (*GetHolidaysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHolidays not implemented")
}
func (*UnimplementedLcosPickupWindowServiceServer) GetAllPickupGroups(ctx context.Context, req *GetAllPickupGroupsRequest) (*GetAllPickupGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllPickupGroups not implemented")
}
func (*UnimplementedLcosPickupWindowServiceServer) GetProductPickupDays(ctx context.Context, req *GetProductPickupDaysRequest) (*GetProductPickupDaysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProductPickupDays not implemented")
}

func RegisterLcosPickupWindowServiceServer(s *grpc.Server, srv LcosPickupWindowServiceServer) {
	s.RegisterService(&_LcosPickupWindowService_serviceDesc, srv)
}

func _LcosPickupWindowService_GetArrangedPickupDays_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetArrangedPickupDaysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).GetArrangedPickupDays(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/GetArrangedPickupDays",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).GetArrangedPickupDays(ctx, req.(*GetArrangedPickupDaysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosPickupWindowService_GetArrangedPickupDaysOpenLogistic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetArrangedPickupDaysOpenLogisticRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).GetArrangedPickupDaysOpenLogistic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/GetArrangedPickupDaysOpenLogistic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).GetArrangedPickupDaysOpenLogistic(ctx, req.(*GetArrangedPickupDaysOpenLogisticRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosPickupWindowService_GetReturnPickupDays_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReturnPickupDaysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).GetReturnPickupDays(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/GetReturnPickupDays",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).GetReturnPickupDays(ctx, req.(*GetReturnPickupDaysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosPickupWindowService_CheckPickupTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPickupTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).CheckPickupTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/CheckPickupTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).CheckPickupTime(ctx, req.(*CheckPickupTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosPickupWindowService_GetPickupTimeslot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPickupTimeslotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).GetPickupTimeslot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/GetPickupTimeslot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).GetPickupTimeslot(ctx, req.(*GetPickupTimeslotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosPickupWindowService_QueryPickupTimeslots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPickupTimeslotsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).QueryPickupTimeslots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/QueryPickupTimeslots",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).QueryPickupTimeslots(ctx, req.(*QueryPickupTimeslotsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosPickupWindowService_GetValidPickupConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetValidPickupConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).GetValidPickupConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/GetValidPickupConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).GetValidPickupConf(ctx, req.(*GetValidPickupConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosPickupWindowService_GetHolidays_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHolidaysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).GetHolidays(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/GetHolidays",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).GetHolidays(ctx, req.(*GetHolidaysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosPickupWindowService_GetAllPickupGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllPickupGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).GetAllPickupGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/GetAllPickupGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).GetAllPickupGroups(ctx, req.(*GetAllPickupGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosPickupWindowService_GetProductPickupDays_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProductPickupDaysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosPickupWindowServiceServer).GetProductPickupDays(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosPickupWindowService/GetProductPickupDays",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosPickupWindowServiceServer).GetProductPickupDays(ctx, req.(*GetProductPickupDaysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosPickupWindowService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosPickupWindowService",
	HandlerType: (*LcosPickupWindowServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetArrangedPickupDays",
			Handler:    _LcosPickupWindowService_GetArrangedPickupDays_Handler,
		},
		{
			MethodName: "GetArrangedPickupDaysOpenLogistic",
			Handler:    _LcosPickupWindowService_GetArrangedPickupDaysOpenLogistic_Handler,
		},
		{
			MethodName: "GetReturnPickupDays",
			Handler:    _LcosPickupWindowService_GetReturnPickupDays_Handler,
		},
		{
			MethodName: "CheckPickupTime",
			Handler:    _LcosPickupWindowService_CheckPickupTime_Handler,
		},
		{
			MethodName: "GetPickupTimeslot",
			Handler:    _LcosPickupWindowService_GetPickupTimeslot_Handler,
		},
		{
			MethodName: "QueryPickupTimeslots",
			Handler:    _LcosPickupWindowService_QueryPickupTimeslots_Handler,
		},
		{
			MethodName: "GetValidPickupConf",
			Handler:    _LcosPickupWindowService_GetValidPickupConf_Handler,
		},
		{
			MethodName: "GetHolidays",
			Handler:    _LcosPickupWindowService_GetHolidays_Handler,
		},
		{
			MethodName: "GetAllPickupGroups",
			Handler:    _LcosPickupWindowService_GetAllPickupGroups_Handler,
		},
		{
			MethodName: "GetProductPickupDays",
			Handler:    _LcosPickupWindowService_GetProductPickupDays_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_pickup_window.proto",
}
