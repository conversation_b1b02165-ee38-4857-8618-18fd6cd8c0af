// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_parcel_lib.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ParcelLibraryQuery struct {
	UniqueId             *string    `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	Region               *string    `protobuf:"bytes,2,req,name=region" json:"region,omitempty"`
	SkuList              []*SkuInfo `protobuf:"bytes,3,rep,name=sku_list,json=skuList" json:"sku_list,omitempty"`
	BuyerPurchaseTime    *uint32    `protobuf:"varint,4,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ParcelLibraryQuery) Reset()         { *m = ParcelLibraryQuery{} }
func (m *ParcelLibraryQuery) String() string { return proto.CompactTextString(m) }
func (*ParcelLibraryQuery) ProtoMessage()    {}
func (*ParcelLibraryQuery) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{0}
}

func (m *ParcelLibraryQuery) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ParcelLibraryQuery.Unmarshal(m, b)
}
func (m *ParcelLibraryQuery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ParcelLibraryQuery.Marshal(b, m, deterministic)
}
func (m *ParcelLibraryQuery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ParcelLibraryQuery.Merge(m, src)
}
func (m *ParcelLibraryQuery) XXX_Size() int {
	return xxx_messageInfo_ParcelLibraryQuery.Size(m)
}
func (m *ParcelLibraryQuery) XXX_DiscardUnknown() {
	xxx_messageInfo_ParcelLibraryQuery.DiscardUnknown(m)
}

var xxx_messageInfo_ParcelLibraryQuery proto.InternalMessageInfo

func (m *ParcelLibraryQuery) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *ParcelLibraryQuery) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *ParcelLibraryQuery) GetSkuList() []*SkuInfo {
	if m != nil {
		return m.SkuList
	}
	return nil
}

func (m *ParcelLibraryQuery) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

type ParcelLibraryInfo struct {
	UniqueId              *string  `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	AccurateLength        *float64 `protobuf:"fixed64,2,req,name=accurate_length,json=accurateLength" json:"accurate_length,omitempty"`
	AccurateWidth         *float64 `protobuf:"fixed64,3,req,name=accurate_width,json=accurateWidth" json:"accurate_width,omitempty"`
	AccurateHeight        *float64 `protobuf:"fixed64,4,req,name=accurate_height,json=accurateHeight" json:"accurate_height,omitempty"`
	AccurateWeight        *float64 `protobuf:"fixed64,5,req,name=accurate_weight,json=accurateWeight" json:"accurate_weight,omitempty"`
	WeightFinAccuracy     *float64 `protobuf:"fixed64,6,req,name=weight_fin_accuracy,json=weightFinAccuracy" json:"weight_fin_accuracy,omitempty"`
	VolumetricFinAccuracy *float64 `protobuf:"fixed64,7,req,name=volumetric_fin_accuracy,json=volumetricFinAccuracy" json:"volumetric_fin_accuracy,omitempty"`
	IsBulkyParcel         *int32   `protobuf:"varint,8,req,name=is_bulky_parcel,json=isBulkyParcel" json:"is_bulky_parcel,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *ParcelLibraryInfo) Reset()         { *m = ParcelLibraryInfo{} }
func (m *ParcelLibraryInfo) String() string { return proto.CompactTextString(m) }
func (*ParcelLibraryInfo) ProtoMessage()    {}
func (*ParcelLibraryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{1}
}

func (m *ParcelLibraryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ParcelLibraryInfo.Unmarshal(m, b)
}
func (m *ParcelLibraryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ParcelLibraryInfo.Marshal(b, m, deterministic)
}
func (m *ParcelLibraryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ParcelLibraryInfo.Merge(m, src)
}
func (m *ParcelLibraryInfo) XXX_Size() int {
	return xxx_messageInfo_ParcelLibraryInfo.Size(m)
}
func (m *ParcelLibraryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ParcelLibraryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ParcelLibraryInfo proto.InternalMessageInfo

func (m *ParcelLibraryInfo) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *ParcelLibraryInfo) GetAccurateLength() float64 {
	if m != nil && m.AccurateLength != nil {
		return *m.AccurateLength
	}
	return 0
}

func (m *ParcelLibraryInfo) GetAccurateWidth() float64 {
	if m != nil && m.AccurateWidth != nil {
		return *m.AccurateWidth
	}
	return 0
}

func (m *ParcelLibraryInfo) GetAccurateHeight() float64 {
	if m != nil && m.AccurateHeight != nil {
		return *m.AccurateHeight
	}
	return 0
}

func (m *ParcelLibraryInfo) GetAccurateWeight() float64 {
	if m != nil && m.AccurateWeight != nil {
		return *m.AccurateWeight
	}
	return 0
}

func (m *ParcelLibraryInfo) GetWeightFinAccuracy() float64 {
	if m != nil && m.WeightFinAccuracy != nil {
		return *m.WeightFinAccuracy
	}
	return 0
}

func (m *ParcelLibraryInfo) GetVolumetricFinAccuracy() float64 {
	if m != nil && m.VolumetricFinAccuracy != nil {
		return *m.VolumetricFinAccuracy
	}
	return 0
}

func (m *ParcelLibraryInfo) GetIsBulkyParcel() int32 {
	if m != nil && m.IsBulkyParcel != nil {
		return *m.IsBulkyParcel
	}
	return 0
}

type GetParcelSizeDetailBySkuRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	SkuInfo              []*SkuInfo `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	Region               *string    `protobuf:"bytes,3,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetParcelSizeDetailBySkuRequest) Reset()         { *m = GetParcelSizeDetailBySkuRequest{} }
func (m *GetParcelSizeDetailBySkuRequest) String() string { return proto.CompactTextString(m) }
func (*GetParcelSizeDetailBySkuRequest) ProtoMessage()    {}
func (*GetParcelSizeDetailBySkuRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{2}
}

func (m *GetParcelSizeDetailBySkuRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetParcelSizeDetailBySkuRequest.Unmarshal(m, b)
}
func (m *GetParcelSizeDetailBySkuRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetParcelSizeDetailBySkuRequest.Marshal(b, m, deterministic)
}
func (m *GetParcelSizeDetailBySkuRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetParcelSizeDetailBySkuRequest.Merge(m, src)
}
func (m *GetParcelSizeDetailBySkuRequest) XXX_Size() int {
	return xxx_messageInfo_GetParcelSizeDetailBySkuRequest.Size(m)
}
func (m *GetParcelSizeDetailBySkuRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetParcelSizeDetailBySkuRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetParcelSizeDetailBySkuRequest proto.InternalMessageInfo

func (m *GetParcelSizeDetailBySkuRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetParcelSizeDetailBySkuRequest) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func (m *GetParcelSizeDetailBySkuRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type GetParcelSizeDetailBySkuResponse struct {
	ParcelSizeInfo       *ParcelSizeInfo `protobuf:"bytes,1,opt,name=parcel_size_info,json=parcelSizeInfo" json:"parcel_size_info,omitempty"`
	RespHeader           *RespHeader     `protobuf:"bytes,2,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetParcelSizeDetailBySkuResponse) Reset()         { *m = GetParcelSizeDetailBySkuResponse{} }
func (m *GetParcelSizeDetailBySkuResponse) String() string { return proto.CompactTextString(m) }
func (*GetParcelSizeDetailBySkuResponse) ProtoMessage()    {}
func (*GetParcelSizeDetailBySkuResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{3}
}

func (m *GetParcelSizeDetailBySkuResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetParcelSizeDetailBySkuResponse.Unmarshal(m, b)
}
func (m *GetParcelSizeDetailBySkuResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetParcelSizeDetailBySkuResponse.Marshal(b, m, deterministic)
}
func (m *GetParcelSizeDetailBySkuResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetParcelSizeDetailBySkuResponse.Merge(m, src)
}
func (m *GetParcelSizeDetailBySkuResponse) XXX_Size() int {
	return xxx_messageInfo_GetParcelSizeDetailBySkuResponse.Size(m)
}
func (m *GetParcelSizeDetailBySkuResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetParcelSizeDetailBySkuResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetParcelSizeDetailBySkuResponse proto.InternalMessageInfo

func (m *GetParcelSizeDetailBySkuResponse) GetParcelSizeInfo() *ParcelSizeInfo {
	if m != nil {
		return m.ParcelSizeInfo
	}
	return nil
}

func (m *GetParcelSizeDetailBySkuResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

type ParcelSizeInfo struct {
	Length                   *string  `protobuf:"bytes,1,req,name=length" json:"length,omitempty"`
	Width                    *string  `protobuf:"bytes,2,req,name=width" json:"width,omitempty"`
	Height                   *string  `protobuf:"bytes,3,req,name=height" json:"height,omitempty"`
	Weight                   *string  `protobuf:"bytes,4,req,name=weight" json:"weight,omitempty"`
	ActualWeightAccuracy     *string  `protobuf:"bytes,5,req,name=actual_weight_accuracy,json=actualWeightAccuracy" json:"actual_weight_accuracy,omitempty"`
	VolumetricWeightAccuracy *string  `protobuf:"bytes,6,req,name=volumetric_weight_accuracy,json=volumetricWeightAccuracy" json:"volumetric_weight_accuracy,omitempty"`
	IsBulkyParcel            *int32   `protobuf:"varint,8,opt,name=is_bulky_parcel,json=isBulkyParcel" json:"is_bulky_parcel,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *ParcelSizeInfo) Reset()         { *m = ParcelSizeInfo{} }
func (m *ParcelSizeInfo) String() string { return proto.CompactTextString(m) }
func (*ParcelSizeInfo) ProtoMessage()    {}
func (*ParcelSizeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{4}
}

func (m *ParcelSizeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ParcelSizeInfo.Unmarshal(m, b)
}
func (m *ParcelSizeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ParcelSizeInfo.Marshal(b, m, deterministic)
}
func (m *ParcelSizeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ParcelSizeInfo.Merge(m, src)
}
func (m *ParcelSizeInfo) XXX_Size() int {
	return xxx_messageInfo_ParcelSizeInfo.Size(m)
}
func (m *ParcelSizeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ParcelSizeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ParcelSizeInfo proto.InternalMessageInfo

func (m *ParcelSizeInfo) GetLength() string {
	if m != nil && m.Length != nil {
		return *m.Length
	}
	return ""
}

func (m *ParcelSizeInfo) GetWidth() string {
	if m != nil && m.Width != nil {
		return *m.Width
	}
	return ""
}

func (m *ParcelSizeInfo) GetHeight() string {
	if m != nil && m.Height != nil {
		return *m.Height
	}
	return ""
}

func (m *ParcelSizeInfo) GetWeight() string {
	if m != nil && m.Weight != nil {
		return *m.Weight
	}
	return ""
}

func (m *ParcelSizeInfo) GetActualWeightAccuracy() string {
	if m != nil && m.ActualWeightAccuracy != nil {
		return *m.ActualWeightAccuracy
	}
	return ""
}

func (m *ParcelSizeInfo) GetVolumetricWeightAccuracy() string {
	if m != nil && m.VolumetricWeightAccuracy != nil {
		return *m.VolumetricWeightAccuracy
	}
	return ""
}

func (m *ParcelSizeInfo) GetIsBulkyParcel() int32 {
	if m != nil && m.IsBulkyParcel != nil {
		return *m.IsBulkyParcel
	}
	return 0
}

type GroupParcelSizeInfo struct {
	AccurateLength        *string  `protobuf:"bytes,1,req,name=accurate_length,json=accurateLength" json:"accurate_length,omitempty"`
	AccurateWidth         *string  `protobuf:"bytes,2,req,name=accurate_width,json=accurateWidth" json:"accurate_width,omitempty"`
	AccurateHeight        *string  `protobuf:"bytes,3,req,name=accurate_height,json=accurateHeight" json:"accurate_height,omitempty"`
	AccurateWeight        *string  `protobuf:"bytes,4,req,name=accurate_weight,json=accurateWeight" json:"accurate_weight,omitempty"`
	WeightFinAccuracy     *string  `protobuf:"bytes,5,req,name=weight_fin_accuracy,json=weightFinAccuracy" json:"weight_fin_accuracy,omitempty"`
	VolumetricFinAccuracy *string  `protobuf:"bytes,6,req,name=volumetric_fin_accuracy,json=volumetricFinAccuracy" json:"volumetric_fin_accuracy,omitempty"`
	GroupId               *string  `protobuf:"bytes,7,req,name=group_id,json=groupId" json:"group_id,omitempty"`
	IsBulkyParcel         *int32   `protobuf:"varint,8,opt,name=is_bulky_parcel,json=isBulkyParcel" json:"is_bulky_parcel,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *GroupParcelSizeInfo) Reset()         { *m = GroupParcelSizeInfo{} }
func (m *GroupParcelSizeInfo) String() string { return proto.CompactTextString(m) }
func (*GroupParcelSizeInfo) ProtoMessage()    {}
func (*GroupParcelSizeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{5}
}

func (m *GroupParcelSizeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupParcelSizeInfo.Unmarshal(m, b)
}
func (m *GroupParcelSizeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupParcelSizeInfo.Marshal(b, m, deterministic)
}
func (m *GroupParcelSizeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupParcelSizeInfo.Merge(m, src)
}
func (m *GroupParcelSizeInfo) XXX_Size() int {
	return xxx_messageInfo_GroupParcelSizeInfo.Size(m)
}
func (m *GroupParcelSizeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupParcelSizeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GroupParcelSizeInfo proto.InternalMessageInfo

func (m *GroupParcelSizeInfo) GetAccurateLength() string {
	if m != nil && m.AccurateLength != nil {
		return *m.AccurateLength
	}
	return ""
}

func (m *GroupParcelSizeInfo) GetAccurateWidth() string {
	if m != nil && m.AccurateWidth != nil {
		return *m.AccurateWidth
	}
	return ""
}

func (m *GroupParcelSizeInfo) GetAccurateHeight() string {
	if m != nil && m.AccurateHeight != nil {
		return *m.AccurateHeight
	}
	return ""
}

func (m *GroupParcelSizeInfo) GetAccurateWeight() string {
	if m != nil && m.AccurateWeight != nil {
		return *m.AccurateWeight
	}
	return ""
}

func (m *GroupParcelSizeInfo) GetWeightFinAccuracy() string {
	if m != nil && m.WeightFinAccuracy != nil {
		return *m.WeightFinAccuracy
	}
	return ""
}

func (m *GroupParcelSizeInfo) GetVolumetricFinAccuracy() string {
	if m != nil && m.VolumetricFinAccuracy != nil {
		return *m.VolumetricFinAccuracy
	}
	return ""
}

func (m *GroupParcelSizeInfo) GetGroupId() string {
	if m != nil && m.GroupId != nil {
		return *m.GroupId
	}
	return ""
}

func (m *GroupParcelSizeInfo) GetIsBulkyParcel() int32 {
	if m != nil && m.IsBulkyParcel != nil {
		return *m.IsBulkyParcel
	}
	return 0
}

type BatchGetParcelLibraryDataBySkuInfosRequest struct {
	ReqHeader            *ReqHeader         `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	Orders               []*CalculateDetail `protobuf:"bytes,2,rep,name=orders" json:"orders,omitempty"`
	Region               *string            `protobuf:"bytes,3,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetParcelLibraryDataBySkuInfosRequest) Reset() {
	*m = BatchGetParcelLibraryDataBySkuInfosRequest{}
}
func (m *BatchGetParcelLibraryDataBySkuInfosRequest) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetParcelLibraryDataBySkuInfosRequest) ProtoMessage() {}
func (*BatchGetParcelLibraryDataBySkuInfosRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{6}
}

func (m *BatchGetParcelLibraryDataBySkuInfosRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosRequest.Unmarshal(m, b)
}
func (m *BatchGetParcelLibraryDataBySkuInfosRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetParcelLibraryDataBySkuInfosRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosRequest.Merge(m, src)
}
func (m *BatchGetParcelLibraryDataBySkuInfosRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosRequest.Size(m)
}
func (m *BatchGetParcelLibraryDataBySkuInfosRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosRequest proto.InternalMessageInfo

func (m *BatchGetParcelLibraryDataBySkuInfosRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetParcelLibraryDataBySkuInfosRequest) GetOrders() []*CalculateDetail {
	if m != nil {
		return m.Orders
	}
	return nil
}

func (m *BatchGetParcelLibraryDataBySkuInfosRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type BatchGetParcelLibraryDataBySkuInfosResponse struct {
	OrderResult          []*CalculateResult `protobuf:"bytes,1,rep,name=order_result,json=orderResult" json:"order_result,omitempty"`
	RespHeader           *RespHeader        `protobuf:"bytes,2,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetParcelLibraryDataBySkuInfosResponse) Reset() {
	*m = BatchGetParcelLibraryDataBySkuInfosResponse{}
}
func (m *BatchGetParcelLibraryDataBySkuInfosResponse) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetParcelLibraryDataBySkuInfosResponse) ProtoMessage() {}
func (*BatchGetParcelLibraryDataBySkuInfosResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{7}
}

func (m *BatchGetParcelLibraryDataBySkuInfosResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosResponse.Unmarshal(m, b)
}
func (m *BatchGetParcelLibraryDataBySkuInfosResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetParcelLibraryDataBySkuInfosResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosResponse.Merge(m, src)
}
func (m *BatchGetParcelLibraryDataBySkuInfosResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosResponse.Size(m)
}
func (m *BatchGetParcelLibraryDataBySkuInfosResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetParcelLibraryDataBySkuInfosResponse proto.InternalMessageInfo

func (m *BatchGetParcelLibraryDataBySkuInfosResponse) GetOrderResult() []*CalculateResult {
	if m != nil {
		return m.OrderResult
	}
	return nil
}

func (m *BatchGetParcelLibraryDataBySkuInfosResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

type CalculateResult struct {
	OrderResult          []*GroupParcelSizeInfo `protobuf:"bytes,1,rep,name=order_result,json=orderResult" json:"order_result,omitempty"`
	CalculateId          *string                `protobuf:"bytes,2,req,name=calculate_id,json=calculateId" json:"calculate_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *CalculateResult) Reset()         { *m = CalculateResult{} }
func (m *CalculateResult) String() string { return proto.CompactTextString(m) }
func (*CalculateResult) ProtoMessage()    {}
func (*CalculateResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{8}
}

func (m *CalculateResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculateResult.Unmarshal(m, b)
}
func (m *CalculateResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculateResult.Marshal(b, m, deterministic)
}
func (m *CalculateResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculateResult.Merge(m, src)
}
func (m *CalculateResult) XXX_Size() int {
	return xxx_messageInfo_CalculateResult.Size(m)
}
func (m *CalculateResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculateResult.DiscardUnknown(m)
}

var xxx_messageInfo_CalculateResult proto.InternalMessageInfo

func (m *CalculateResult) GetOrderResult() []*GroupParcelSizeInfo {
	if m != nil {
		return m.OrderResult
	}
	return nil
}

func (m *CalculateResult) GetCalculateId() string {
	if m != nil && m.CalculateId != nil {
		return *m.CalculateId
	}
	return ""
}

type CalculateDetail struct {
	CalculateId          *string         `protobuf:"bytes,1,req,name=calculate_id,json=calculateId" json:"calculate_id,omitempty"`
	SkuGroup             []*SkuGroupInfo `protobuf:"bytes,2,rep,name=sku_group,json=skuGroup" json:"sku_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CalculateDetail) Reset()         { *m = CalculateDetail{} }
func (m *CalculateDetail) String() string { return proto.CompactTextString(m) }
func (*CalculateDetail) ProtoMessage()    {}
func (*CalculateDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{9}
}

func (m *CalculateDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculateDetail.Unmarshal(m, b)
}
func (m *CalculateDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculateDetail.Marshal(b, m, deterministic)
}
func (m *CalculateDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculateDetail.Merge(m, src)
}
func (m *CalculateDetail) XXX_Size() int {
	return xxx_messageInfo_CalculateDetail.Size(m)
}
func (m *CalculateDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculateDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CalculateDetail proto.InternalMessageInfo

func (m *CalculateDetail) GetCalculateId() string {
	if m != nil && m.CalculateId != nil {
		return *m.CalculateId
	}
	return ""
}

func (m *CalculateDetail) GetSkuGroup() []*SkuGroupInfo {
	if m != nil {
		return m.SkuGroup
	}
	return nil
}

type SkuGroupInfo struct {
	GroupId              *string    `protobuf:"bytes,1,req,name=group_id,json=groupId" json:"group_id,omitempty"`
	SkuInfo              []*SkuInfo `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SkuGroupInfo) Reset()         { *m = SkuGroupInfo{} }
func (m *SkuGroupInfo) String() string { return proto.CompactTextString(m) }
func (*SkuGroupInfo) ProtoMessage()    {}
func (*SkuGroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3d29b2582e493dcb, []int{10}
}

func (m *SkuGroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuGroupInfo.Unmarshal(m, b)
}
func (m *SkuGroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuGroupInfo.Marshal(b, m, deterministic)
}
func (m *SkuGroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuGroupInfo.Merge(m, src)
}
func (m *SkuGroupInfo) XXX_Size() int {
	return xxx_messageInfo_SkuGroupInfo.Size(m)
}
func (m *SkuGroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuGroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SkuGroupInfo proto.InternalMessageInfo

func (m *SkuGroupInfo) GetGroupId() string {
	if m != nil && m.GroupId != nil {
		return *m.GroupId
	}
	return ""
}

func (m *SkuGroupInfo) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*ParcelLibraryQuery)(nil), "lcos_protobuf.ParcelLibraryQuery")
	proto.RegisterType((*ParcelLibraryInfo)(nil), "lcos_protobuf.ParcelLibraryInfo")
	proto.RegisterType((*GetParcelSizeDetailBySkuRequest)(nil), "lcos_protobuf.GetParcelSizeDetailBySkuRequest")
	proto.RegisterType((*GetParcelSizeDetailBySkuResponse)(nil), "lcos_protobuf.GetParcelSizeDetailBySkuResponse")
	proto.RegisterType((*ParcelSizeInfo)(nil), "lcos_protobuf.ParcelSizeInfo")
	proto.RegisterType((*GroupParcelSizeInfo)(nil), "lcos_protobuf.GroupParcelSizeInfo")
	proto.RegisterType((*BatchGetParcelLibraryDataBySkuInfosRequest)(nil), "lcos_protobuf.BatchGetParcelLibraryDataBySkuInfosRequest")
	proto.RegisterType((*BatchGetParcelLibraryDataBySkuInfosResponse)(nil), "lcos_protobuf.BatchGetParcelLibraryDataBySkuInfosResponse")
	proto.RegisterType((*CalculateResult)(nil), "lcos_protobuf.CalculateResult")
	proto.RegisterType((*CalculateDetail)(nil), "lcos_protobuf.CalculateDetail")
	proto.RegisterType((*SkuGroupInfo)(nil), "lcos_protobuf.SkuGroupInfo")
}

func init() {
	proto.RegisterFile("lcos_parcel_lib.proto", fileDescriptor_3d29b2582e493dcb)
}

var fileDescriptor_3d29b2582e493dcb = []byte{
	// 862 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xdb, 0x8e, 0x1b, 0x45,
	0x10, 0xa5, 0xc7, 0x7b, 0x73, 0x79, 0x2f, 0x6c, 0xef, 0x66, 0x99, 0x38, 0x02, 0xcc, 0x44, 0x80,
	0x05, 0x92, 0x11, 0x16, 0x0a, 0x10, 0xf1, 0x92, 0x4d, 0x60, 0x63, 0x69, 0x1f, 0xc2, 0x18, 0x29,
	0x2f, 0x48, 0xa3, 0xf6, 0xb8, 0x6d, 0xb7, 0x3c, 0x9e, 0x19, 0xf7, 0x25, 0x2b, 0x87, 0x1f, 0x40,
	0x7c, 0x04, 0x6f, 0x88, 0x97, 0xbc, 0xf2, 0x03, 0x7c, 0x4a, 0xbe, 0x04, 0x4d, 0x77, 0xfb, 0x32,
	0xe3, 0xcb, 0x3a, 0xec, 0xdb, 0x54, 0xd5, 0xa9, 0xea, 0xea, 0xea, 0x73, 0xca, 0x86, 0x7b, 0x51,
	0x98, 0x88, 0x20, 0x25, 0x3c, 0xa4, 0x51, 0x10, 0xb1, 0x4e, 0x23, 0xe5, 0x89, 0x4c, 0xf0, 0x91,
	0x71, 0x67, 0xdf, 0x1d, 0xd5, 0xab, 0x9e, 0x68, 0xb3, 0x43, 0x04, 0x35, 0x71, 0xef, 0x0d, 0x02,
	0xfc, 0x42, 0x27, 0x5d, 0xb3, 0x0e, 0x27, 0x7c, 0xf2, 0xb3, 0xa2, 0x7c, 0x82, 0x1f, 0x40, 0x59,
	0xc5, 0x6c, 0xac, 0x68, 0xc0, 0xba, 0x2e, 0xaa, 0x39, 0xf5, 0xb2, 0x7f, 0x60, 0x1c, 0xad, 0x2e,
	0xbe, 0x80, 0x3d, 0x4e, 0xfb, 0x2c, 0x89, 0x5d, 0x47, 0x47, 0xac, 0x85, 0xbf, 0x86, 0x03, 0x31,
	0x54, 0x41, 0xc4, 0x84, 0x74, 0x4b, 0xb5, 0x52, 0xbd, 0xd2, 0xbc, 0x68, 0xe4, 0x8e, 0x6f, 0xb4,
	0x87, 0xaa, 0x15, 0xf7, 0x12, 0x7f, 0x5f, 0x0c, 0xd5, 0x35, 0x13, 0x12, 0x37, 0xe0, 0xac, 0xa3,
	0x26, 0x94, 0x07, 0xa9, 0xe2, 0xe1, 0x80, 0x08, 0x1a, 0x48, 0x36, 0xa2, 0xee, 0x4e, 0x0d, 0xd5,
	0x8f, 0xfc, 0x53, 0x1d, 0x7a, 0x61, 0x23, 0xbf, 0xb0, 0x11, 0xf5, 0xde, 0x3a, 0x70, 0x9a, 0x6b,
	0x37, 0x2b, 0xb7, 0xb9, 0xdb, 0xcf, 0xe1, 0x84, 0x84, 0xa1, 0xe2, 0x44, 0xd2, 0x20, 0xa2, 0x71,
	0x5f, 0x0e, 0x74, 0xdb, 0xc8, 0x3f, 0x9e, 0xba, 0xaf, 0xb5, 0x17, 0x7f, 0x0a, 0x33, 0x4f, 0x70,
	0xc3, 0xba, 0x72, 0xe0, 0x96, 0x34, 0xee, 0x68, 0xea, 0x7d, 0x99, 0x39, 0x73, 0xf5, 0x06, 0x94,
	0xf5, 0x07, 0xd2, 0xdd, 0xc9, 0xd7, 0x7b, 0xae, 0xbd, 0x39, 0xe0, 0x8d, 0x01, 0xee, 0xe6, 0x81,
	0x2f, 0x0d, 0xb0, 0x01, 0x67, 0x26, 0x1e, 0xf4, 0x58, 0x1c, 0x98, 0x60, 0x38, 0x71, 0xf7, 0x34,
	0xf8, 0xd4, 0x84, 0x7e, 0x62, 0xf1, 0x13, 0x1b, 0xc0, 0x8f, 0xe0, 0x83, 0x57, 0x49, 0xa4, 0x46,
	0x54, 0x72, 0x16, 0xe6, 0x73, 0xf6, 0x75, 0xce, 0xbd, 0x79, 0x78, 0x31, 0xef, 0x33, 0x38, 0x61,
	0x22, 0xe8, 0xa8, 0x68, 0x38, 0xb1, 0x44, 0x71, 0x0f, 0x6a, 0x4e, 0x7d, 0xd7, 0x3f, 0x62, 0xe2,
	0x32, 0xf3, 0x9a, 0xc9, 0x7a, 0x7f, 0x21, 0xf8, 0xf8, 0x8a, 0x4a, 0x63, 0xb5, 0xd9, 0x6b, 0xfa,
	0x8c, 0x4a, 0xc2, 0xa2, 0xcb, 0x49, 0x7b, 0xa8, 0x7c, 0x3a, 0x56, 0x54, 0x48, 0xfc, 0x2d, 0x00,
	0xa7, 0xe3, 0x60, 0x40, 0x49, 0x97, 0x72, 0x3d, 0xf3, 0x4a, 0xd3, 0x2d, 0xbc, 0xb6, 0x4f, 0xc7,
	0xcf, 0x75, 0xdc, 0x2f, 0xf3, 0xe9, 0xe7, 0x94, 0x24, 0x2c, 0xee, 0x25, 0xae, 0x73, 0x2b, 0x49,
	0xf4, 0xf3, 0xce, 0xf9, 0x56, 0x5a, 0xe4, 0x9b, 0xf7, 0x37, 0x82, 0xda, 0xfa, 0x3e, 0x45, 0x9a,
	0xc4, 0x82, 0xe2, 0x2b, 0x78, 0xdf, 0x8a, 0x42, 0xb0, 0xd7, 0xd4, 0x9c, 0x8b, 0x6a, 0xa8, 0x5e,
	0x69, 0x7e, 0x58, 0x38, 0x77, 0x5e, 0x47, 0x1f, 0x7f, 0x9c, 0xe6, 0x6c, 0xfc, 0x18, 0x2a, 0x9c,
	0x8a, 0x74, 0x7a, 0x65, 0x47, 0x5f, 0xf9, 0xfe, 0xd2, 0x95, 0x45, 0x6a, 0xef, 0x0c, 0x7c, 0xf6,
	0xed, 0xfd, 0xe1, 0xc0, 0x71, 0xbe, 0x7c, 0x76, 0x29, 0xcb, 0x46, 0x43, 0x58, 0x6b, 0xe1, 0x73,
	0xd8, 0x35, 0xe4, 0x33, 0xda, 0x32, 0x46, 0x86, 0xb6, 0x5c, 0xb3, 0x23, 0x30, 0x56, 0xe6, 0xbf,
	0x99, 0x73, 0xb0, 0xec, 0x5b, 0x0b, 0x7f, 0x03, 0x17, 0x24, 0x94, 0x8a, 0x44, 0x96, 0x79, 0x73,
	0x86, 0xec, 0x6a, 0xdc, 0xb9, 0x89, 0x1a, 0x02, 0xce, 0x08, 0xf2, 0x03, 0x54, 0x17, 0x88, 0x55,
	0xcc, 0xdc, 0xd3, 0x99, 0xee, 0x1c, 0x51, 0xc8, 0x5e, 0x49, 0x2f, 0xb4, 0x4c, 0xaf, 0xb7, 0x0e,
	0x9c, 0x5d, 0xf1, 0x44, 0xa5, 0x85, 0x89, 0xac, 0x10, 0xaa, 0x19, 0xcd, 0xed, 0x42, 0x35, 0xb3,
	0xba, 0x5d, 0xa8, 0xa5, 0x7c, 0xbd, 0xf5, 0x42, 0xdd, 0xc9, 0x03, 0x37, 0x0b, 0xd5, 0x8c, 0xf4,
	0xdd, 0x84, 0x6a, 0x86, 0xb9, 0x46, 0xa8, 0xf7, 0xe1, 0xa0, 0x9f, 0x0d, 0x28, 0x5b, 0x67, 0xfb,
	0x1a, 0xb8, 0xaf, 0xed, 0x56, 0x77, 0xeb, 0x21, 0xff, 0x83, 0xe0, 0x8b, 0x4b, 0x22, 0xc3, 0xc1,
	0x4c, 0x20, 0x76, 0x61, 0x3e, 0x23, 0x92, 0x68, 0x85, 0x64, 0x33, 0x17, 0x77, 0x96, 0xf3, 0x23,
	0xd8, 0x4b, 0x78, 0x97, 0x72, 0x61, 0xc5, 0xfc, 0x51, 0x21, 0xe9, 0x29, 0x89, 0x42, 0x15, 0x11,
	0x69, 0xb5, 0xe9, 0x5b, 0xf4, 0x5a, 0x4d, 0xbf, 0x41, 0xf0, 0xe5, 0x56, 0x7d, 0x5b, 0x79, 0x3f,
	0x81, 0x43, 0x5d, 0x31, 0xe0, 0x54, 0xa8, 0x48, 0xba, 0x68, 0x73, 0x17, 0xbe, 0x46, 0xf9, 0x15,
	0x9d, 0x63, 0x8c, 0x3b, 0x09, 0xfb, 0x37, 0x38, 0x29, 0xd4, 0xc6, 0x3f, 0xae, 0xec, 0xc8, 0x2b,
	0xd4, 0x5b, 0x21, 0x80, 0x7c, 0x57, 0x9f, 0xc0, 0x61, 0x38, 0xad, 0x9c, 0xf1, 0xc0, 0x50, 0xbc,
	0x32, 0xf3, 0xb5, 0xba, 0x5e, 0xbc, 0x70, 0xb8, 0x19, 0xef, 0x52, 0x16, 0x5a, 0xca, 0xc2, 0xdf,
	0x41, 0x39, 0x5b, 0xc0, 0x9a, 0x50, 0xf6, 0xd1, 0x1e, 0x2c, 0x6f, 0x60, 0xdd, 0x9f, 0xee, 0x2a,
	0x5b, 0xd7, 0xda, 0xf2, 0x7e, 0x85, 0xc3, 0xc5, 0x48, 0x8e, 0xa6, 0x28, 0x4f, 0xd3, 0x77, 0xdf,
	0xf2, 0xcd, 0x7f, 0x1d, 0x38, 0xcf, 0xbd, 0x78, 0x9b, 0xf2, 0x57, 0x2c, 0xa4, 0xf8, 0x77, 0x04,
	0x0f, 0xd7, 0xad, 0xf9, 0xa7, 0xc9, 0xa8, 0xc3, 0x62, 0x22, 0xb3, 0xbf, 0x1f, 0x8d, 0xe2, 0x88,
	0x37, 0xff, 0x84, 0x55, 0xbf, 0xda, 0x1a, 0x6f, 0xb8, 0xe6, 0xbd, 0x87, 0xff, 0x44, 0xf0, 0x70,
	0x0b, 0x76, 0xe2, 0xef, 0x0b, 0xa5, 0xb7, 0x57, 0x62, 0xf5, 0xf1, 0xff, 0x49, 0x9d, 0x36, 0xf8,
	0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x11, 0x49, 0x4e, 0xc9, 0x06, 0x0a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// ParcelLibraryServiceClient is the client API for ParcelLibraryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ParcelLibraryServiceClient interface {
	GetParcelSizeDetailBySkuCombination(ctx context.Context, in *GetParcelSizeDetailBySkuRequest, opts ...grpc.CallOption) (*GetParcelSizeDetailBySkuResponse, error)
	BatchGetParcelLibraryDataBySkuInfos(ctx context.Context, in *BatchGetParcelLibraryDataBySkuInfosRequest, opts ...grpc.CallOption) (*BatchGetParcelLibraryDataBySkuInfosResponse, error)
}

type parcelLibraryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewParcelLibraryServiceClient(cc grpc.ClientConnInterface) ParcelLibraryServiceClient {
	return &parcelLibraryServiceClient{cc}
}

func (c *parcelLibraryServiceClient) GetParcelSizeDetailBySkuCombination(ctx context.Context, in *GetParcelSizeDetailBySkuRequest, opts ...grpc.CallOption) (*GetParcelSizeDetailBySkuResponse, error) {
	out := new(GetParcelSizeDetailBySkuResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.ParcelLibraryService/GetParcelSizeDetailBySkuCombination", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parcelLibraryServiceClient) BatchGetParcelLibraryDataBySkuInfos(ctx context.Context, in *BatchGetParcelLibraryDataBySkuInfosRequest, opts ...grpc.CallOption) (*BatchGetParcelLibraryDataBySkuInfosResponse, error) {
	out := new(BatchGetParcelLibraryDataBySkuInfosResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.ParcelLibraryService/BatchGetParcelLibraryDataBySkuInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ParcelLibraryServiceServer is the server API for ParcelLibraryService service.
type ParcelLibraryServiceServer interface {
	GetParcelSizeDetailBySkuCombination(context.Context, *GetParcelSizeDetailBySkuRequest) (*GetParcelSizeDetailBySkuResponse, error)
	BatchGetParcelLibraryDataBySkuInfos(context.Context, *BatchGetParcelLibraryDataBySkuInfosRequest) (*BatchGetParcelLibraryDataBySkuInfosResponse, error)
}

// UnimplementedParcelLibraryServiceServer can be embedded to have forward compatible implementations.
type UnimplementedParcelLibraryServiceServer struct {
}

func (*UnimplementedParcelLibraryServiceServer) GetParcelSizeDetailBySkuCombination(ctx context.Context, req *GetParcelSizeDetailBySkuRequest) (*GetParcelSizeDetailBySkuResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParcelSizeDetailBySkuCombination not implemented")
}
func (*UnimplementedParcelLibraryServiceServer) BatchGetParcelLibraryDataBySkuInfos(ctx context.Context, req *BatchGetParcelLibraryDataBySkuInfosRequest) (*BatchGetParcelLibraryDataBySkuInfosResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetParcelLibraryDataBySkuInfos not implemented")
}

func RegisterParcelLibraryServiceServer(s *grpc.Server, srv ParcelLibraryServiceServer) {
	s.RegisterService(&_ParcelLibraryService_serviceDesc, srv)
}

func _ParcelLibraryService_GetParcelSizeDetailBySkuCombination_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetParcelSizeDetailBySkuRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParcelLibraryServiceServer).GetParcelSizeDetailBySkuCombination(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.ParcelLibraryService/GetParcelSizeDetailBySkuCombination",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParcelLibraryServiceServer).GetParcelSizeDetailBySkuCombination(ctx, req.(*GetParcelSizeDetailBySkuRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParcelLibraryService_BatchGetParcelLibraryDataBySkuInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetParcelLibraryDataBySkuInfosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParcelLibraryServiceServer).BatchGetParcelLibraryDataBySkuInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.ParcelLibraryService/BatchGetParcelLibraryDataBySkuInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParcelLibraryServiceServer).BatchGetParcelLibraryDataBySkuInfos(ctx, req.(*BatchGetParcelLibraryDataBySkuInfosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ParcelLibraryService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.ParcelLibraryService",
	HandlerType: (*ParcelLibraryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetParcelSizeDetailBySkuCombination",
			Handler:    _ParcelLibraryService_GetParcelSizeDetailBySkuCombination_Handler,
		},
		{
			MethodName: "BatchGetParcelLibraryDataBySkuInfos",
			Handler:    _ParcelLibraryService_BatchGetParcelLibraryDataBySkuInfos_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_parcel_lib.proto",
}
